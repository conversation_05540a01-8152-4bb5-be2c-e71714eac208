<?php


namespace app\controller;

use app\BaseController;
use app\ErrorCode;
use app\Request;
use app\service\es\Es;
use app\service\Tripartite as TripartiteService;
use app\service\WeChat as WeChatService;
use app\validate\ListPagination;
use think\Exception;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\Log;
use think\facade\Validate;

class Tripartite extends BaseController
{
    /**
     * Description:三方订单录入
     * Author: zrc
     * Date: 2022/5/11
     * Time: 9:20
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function orderInput(Request $request)
    {
        $params = $request->param();
        //数据验证
        $validate = Validate::rule([
            'platform|三方平台标识' => 'require|in:1,2,3,4,13,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31',
            'owner_id|店铺ID'   => 'require',
            'goodsOrder|订单信息' => 'require',
            'address|收货地址信息'  => 'require',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $tripartiteService = new TripartiteService();
        $result            = $tripartiteService->orderInput($params);
        return $this->success($result);
    }

    public function import(Request $request)
    {
        $express_type = array_column(config('config.express_type'), 'value', 'label');
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        $mime_type          = pathinfo($params['file'] ?? '', PATHINFO_EXTENSION);

        if ($mime_type == 'xlsx') {
            $path = env('ALIURL') . urldecode($params['file']);
            //$path = 'D:\线上-销售退货批量导入-单个.xlsx';
            try {
                #下载文件
                $local_path = download_image($path, $mime_type);

                #解析文件
                $eData = readExcelData2($local_path);
                if ($eData['error_code'] != 0) $this->throwError('excel解析失败');
                if (count($eData['data']) == 0) $this->throwError('未获取到excel内容');
                $excelData = $eData['data'];
                unset($excelData[0]);

                $excelDataGroup = [];
                foreach ($excelData as $excelDatumItem) {
                    $excelDataGroup[$excelDatumItem['0'] ?? ''] [] = $excelDatumItem;
                }
                $read_data       = [];
                $read_data_group = [];
                $items           = [];
                $now = date('Y-m-d H:i:s');
                $whs = [
                    '松鸽待入仓' => '335',
                    '松鸽酒业南通仓' => '328',
                ];
                $owner_ids = ['517978682'];
                foreach ($excelDataGroup as $datum) {
                    $datum_product  = [];
                    $datum_pay_money  = 0;
                    foreach ($datum as $datum_item){
                        $datum_pay_money = bcadd($datum_pay_money, $datum_item['3']??0, 2);
                        $datum_product[] = "{$datum_item['1']}*".($datum_item['2'] ?? '1');
                    }
                    $owner_id = '517978682';
                    if (!empty($datum[0][12]) && !in_array($datum[0][12], $owner_ids)) {
                        $owner_id = trim($datum[0][12]);
                        $owner_ids[] = $owner_id;
                    }
                    $storename = '快团团惺松的Soul List';
                    if (!empty($datum[0][13])) {
                        $storename = trim(str_replace(['(', ')'], ['（', '）'], $datum[0][13]));
                    }
                    $read_data[] = [
                        'platform'             => 27,
                        'storename'            => $storename,
                        'owner_id'             => $owner_id,
                        'main_orderno'         => $datum[0][0] ?? '', //【导入】
                        'main_pay_money'       => $datum_pay_money, // 子订单金额一样
                        'payment_time'         => $now,
                        'create_time'          => $now,
                        'main_goodsname'       => '',
                        'remark'               => '',
                        'sub_goodsname'        => '',
                        'orderno'              => $datum[0][0] ?? '',
                        'pay_number'           => 1,
                        'pay_money'            => $datum_pay_money,
                        'status'               => 1,
                        'refund_status'        => 0,
                        'product'              => implode("+",$datum_product),
                        'company_id'           => 4,
                        'is_supplier_delivery' => 0,
                        'related_order_no'     => '',
                        'store_code'           => $whs[$datum[0][11] ?? ''] ?? '',
                        'province'             => $datum[0][7] ?? '',
                        'city'                 => $datum[0][8] ?? '',
                        'district'             => $datum[0][9] ?? '',
                        'address'              => $datum[0][10] ?? '',
                        'consignee'            => $datum[0][5] ?? '',
                        'cellphone'            => $datum[0][6] ?? '',
                        'express_type'         => $express_type[$datum[0][4]] ?? '',
                        'express_fee'          => 0,
                    ];
                }
                $rules = [
                    'platform|三方平台来源'         => 'require',
                    'storename|店铺名称'            => 'require',
                    'owner_id|店铺Id'               => 'require',
                    'main_orderno|主订单号'         => 'require',
                    'main_pay_money|主订单支付金额' => 'require',
                    'payment_time|付款时间'         => 'require',
                    'create_time|下单时间'          => 'require',
//                    'main_goodsname|主订单商品名称' => 'require',
//                    'remark|主订单商品名称' => 'require',
//                    'sub_goodsname|子订单商品名称' => 'require',
                    'orderno|子订单编号'            => 'require',
                    'pay_number|购买数量'           => 'require',
                    'pay_money|子订单支付金额'      => 'require',
                    'status|订单状态'               => 'require',
                    'refund_status|退款状态'        => 'require',
                    'product|产品'                  => 'require',
                    'company_id|公司ID'             => 'require',
                    'is_supplier_delivery|是否代发' => 'require',
//                    'related_order_no|关联订单号'   => 'require',
                    'store_code|仓库编号'           => 'require',
                    'province|省'                   => 'require',
                    'city|市'                       => 'require',
                    'district|区'                   => 'require',
                    'address|详细地址'              => 'require',
                    'consignee|收货人姓名'          => 'require',
                    'cellphone|收货人联系电话'      => 'require',
                    'express_type|快递方式'         => 'require',
                    'express_fee|快递费'            => 'require',
                ];
                $all_sub_ods = [];

                // 查询店铺ID是否存在
                $tripartite_owner = Db::table('vh_push_t_plus.vh_customer_code')
                    ->where('company_id', 4)
                    ->whereIn('owner_id', $owner_ids)
                    ->column('id,name,vh_platform', 'owner_id');
                foreach ($read_data as $read_datum) {
                    try {
                        $owner_info = $tripartite_owner[$read_datum['owner_id']] ?? [];
                        if (empty($owner_info)) {
                            $this->throwError("【{$read_datum['owner_id']}】店铺ID填写错误，请检查与系统配置是否一致。");
                        }
                        if ($owner_info['name'] != $read_datum['storename']) {
                            $this->throwError("【{$read_datum['storename']}】店铺名称与系统配置不一致，系统店铺名称为【{$owner_info['name']}】。");
                        }
                        if (!empty($owner_info['vh_platform'])) {
                            $read_datum['platform'] = $owner_info['vh_platform'];
                        }
                        
                        validate()->rule($rules)->check($read_datum);
                        $read_data_group[$read_datum['main_orderno']][$read_datum['orderno']] = $read_datum;
                        $all_sub_ods[] = $read_datum['orderno'];
                    } catch (ValidateException $e) {
                        // 验证失败 输出错误信息
                        $this->throwError('验证错误: ' . ($read_datum['orderno'] ?? '') . $e->getError());
                    }
                }

                $uniq_all_sub_ods = array_values(array_unique($all_sub_ods));
                if (count($uniq_all_sub_ods) != count($all_sub_ods)) {
                    $this->throwError('验证错误: Excel文件包含 重复的子订单号');
                }
                $has_order_no = Db::name('tripartite_order')->where('sub_order_no', 'in', $uniq_all_sub_ods)->value('sub_order_no');
                if ($has_order_no) {
                    $this->throwError('验证错误: 子订单号' . $has_order_no . '已存在');
                }

                foreach ($read_data_group as $main) {
                    $item    = [];
                    $sub_ods = [];
                    foreach ($main as $sub) {
                        if (empty($item)) {
                            $item = [
                                "platform"   => $sub['platform'],
                                "storename"  => $sub['storename'],
                                "owner_id"   => $sub['owner_id'],
                                "address"    => [
                                    "province"     => $sub['province'],
                                    "city"         => $sub['city'],
                                    "district"     => $sub['district'],
                                    "address"      => $sub['address'],
                                    "consignee"    => $sub['consignee'],
                                    "cellphone"    => $sub['cellphone'],
                                    "express_type" => $sub['express_type'],
                                    "express_fee"  => $sub['express_fee'],
                                ],
                                "goodsOrder" => [
                                    "main" => [
                                        "pay_money"    => $sub['main_pay_money'],
                                        "payment_time" => strtotime($sub['payment_time']),
                                        "create_time"  => strtotime($sub['create_time']),
                                        "goodsname"    => $sub['main_goodsname'],
                                        "orderno"      => $sub['main_orderno'],
                                        "remark"       => $sub['remark'],
                                    ],
                                ],
                            ];
                        }

                        $i_prod     = explode('+', $sub['product']);
                        $i_prod_arr = [];
                        foreach ($i_prod as $i_pro) {
                            $i_pro_arr    = explode('*', $i_pro);
                            $i_prod_arr[] = [
                                'short_code' => $i_pro_arr[0],
                                'number'     => $i_pro_arr[1] ?? 1
                            ];
                        }
                        $sub_ods[] = [
                            "goodsname"            => $sub['sub_goodsname'],
                            "orderno"              => $sub['orderno'],
                            "pay_number"           => $sub['pay_number'],
                            "pay_money"            => $sub['pay_money'],
                            "status"               => $sub['status'],
                            "refund_status"        => $sub['refund_status'],
                            "product"              => $i_prod_arr,
                            "company_id"           => $sub['company_id'],
                            "is_supplier_delivery" => $sub['is_supplier_delivery'],
                            "related_order_no"     => $sub['related_order_no'],
                            "store_code"           => $sub['store_code'],
                        ];
                    }
                    $item['goodsOrder']['son'] = $sub_ods;
                    $items[]                   = $item;
                }
                if (empty($items)) $this->throwError('未获取到excel有效导入数据');


                //获取企微用户信息
                $userInfo = (new \app\service\SalesReturn())->httpGet(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info', ['admin_id' => $params['admin_id'], 'field' => 'userid,dept_id']);
                if ($userInfo['error_code'] != 0 || !isset($userInfo['data'][$params['admin_id']])) $this->throwError('发起企业微信批失败：未获取到企业微信用户信息');
                $userid = $userInfo['data'][$params['admin_id']]['userid'];//企业微信userid
                //创建销售退货数据组装

                $pushData = array(
                    'namespace' => 'orders',
                    'key'       => 'tripartiteImport_' . time(),
                    'data'      => base64_encode(json_encode([
                        'user_id'  => $userid,
                        'admin_id' => $params['admin_id'],
                        'items'    => $items,
                    ], JSON_UNESCAPED_UNICODE)),
                    'callback'  => env('ITEM.ORDERS_URL') . '/orders/v3/tripartite/importDeal',
                    'timeout'   => '10s',
                );
                $result   = curlRequest(env('ITEM.TIMEING_SERVICE_URL') . '/services/v3/timing/add', json_encode($pushData));
                if (!isset($result['error_code'])) $this->throwError('导入失败：秒级计划任务模块访问异常');
                if ($result['error_code'] != 0) $this->throwError('导入失败：' . $result['error_msg']);
            } catch (\Exception $e) {
                @unlink($local_path);
                $this->throwError($e->getMessage());
            }
        } else {
            $this->throwError('请用模板文件制作excel进行上传');
        }
        return $this->success((object)[], "提交成功，导入结果处理中，请稍后刷新列表查看结果");
    }

    public function importDeal(Request $request)
    {
        $param = $request->param();
        Log::write('importDeal : ' . json_encode($param));
        $items = $param['items'];

        $err_arr = $success = [];
        foreach ($items as $item) {
            try {
                $main_order = '';
                $main_order = $item['goodsOrder']['main']['orderno'];
                //数据验证
                $validate = Validate::rule([
                    'platform|三方平台标识' => 'require|in:1,2,3,4,13,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31',
                    'owner_id|店铺ID'       => 'require',
                    'goodsOrder|订单信息'   => 'require',
                    'address|收货地址信息'  => 'require',
                ]);
                if (!$validate->check($item)) {
                    $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
                }
                $tripartiteService = new TripartiteService();
                $result            = $tripartiteService->orderInput($item);
                $success[]         = $main_order;
            } catch (\Exception $e) {
                $err_arr[] = $main_order . ' ' . $e->getMessage();
            }
        }

        $weChatService = new WeChatService();
        $msg           = '三方订单批量导入结果：成功' . count($success) . '条,失败:' . count($err_arr) . '条.';
        if (!empty($err_arr)) {
            $msg .= ' 失败主订单号: ' . implode(',', $err_arr);
        }
        $weChatService->weChatSendText($param['user_id'], $msg);
        return $this->success();
    }

    /**
     * Description:三方订单列表
     * Author: zrc
     * Date: 2023/2/2
     * Time: 17:01
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function orderList(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        $h_referer   = request()->header('referer', '');
        $referer_arr = parse_url($h_referer);
        $h_host      = $referer_arr['host'] ?? '';
        if ($h_host == 'os.mulando.cn') $params['company_id'] = 3;
        if (empty($params['admin_id'])) $this->throwError('未获取到后台用户');
        $validate = new ListPagination();
        if (!$validate->goCheck($params)) {//验证参数
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $result            = [];
        $p_export_type     = $params['export_type'] ?? 0;
        $tripartiteService = new TripartiteService();
        if ($p_export_type == 1) {
            $tripartiteService->orderListExport($params);
        } else {
            $result = $tripartiteService->orderList($params);
        }
        return $this->success($result);
    }

    /**
     * Description:终止萌牙发货
     * Author: zrc
     * Date: 2023/2/3
     * Time: 15:11
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function stopPushOrder(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        //参数验证
        $validate = Validate::rule([
            'admin_id|后台用户ID'  => 'require|number',
            'sub_order_no|订单号' => 'require',
            'store_name|店铺名称'  => 'require',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $tripartiteService = new TripartiteService();
        $result            = $tripartiteService->stopPushOrder($params);
        return $this->success($result);
    }

    /**
     * Description:添加赠品
     * Author: zrc
     * Date: 2023/2/20
     * Time: 9:57
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function addGift(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        $validate           = Validate::rule([
            'admin_id|后台用户ID'  => 'require|number',
            'sub_order_no|订单号' => 'require',
            'short_code|简码'    => 'require',
            'nums|数量'          => 'require|number|>:0',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $tripartiteService = new TripartiteService();
        $result            = $tripartiteService->addGift($params);
        return $this->success($result);
    }

    /**
     * Description:合并订单
     * Author: zrc
     * Date: 2023/2/20
     * Time: 14:17
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function mergeOrder(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        $validate           = Validate::rule([
            'admin_id|后台用户ID'        => 'require|number',
            'sub_order_no_str|合并订单号' => 'require',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $tripartiteService = new TripartiteService();
        $result            = $tripartiteService->mergeOrder($params);
        return $this->success($result);
    }

    public function bathUpdateStatus(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        //参数验证
        $validate = Validate::rule([
            'ids|推送萌芽状态'   => 'require',
            'push_wms_status|推送萌芽状态'   => 'require|in:0',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }

        $SalesReturnService = new TripartiteService();
        $result = $SalesReturnService->bathUpdateStatus($params);
        return $this->success($result);
    }


    public function trOrderPushTPlus(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        //参数验证
        $validate = Validate::rule([
            'sub_order_no|子订单号' => 'require',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }

        $SalesReturnService = new TripartiteService();
        $result             = $SalesReturnService->trOrderPushTPlus($params);
        if ($result === true) {
            return $this->success($result);
        } else {
            return $this->throwError($result);
        }
    }


    public function productUpdate(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        Log::write(__FUNCTION__ . ' 简码换绑: ' . json_encode($params));

        //参数验证
        $validate = Validate::rule([
            'sub_order_no|子订单号' => 'require',
            'items_info|商品信息'   => 'require|array',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }

        $SalesReturnService = new TripartiteService();
        $result             = $SalesReturnService->productUpdate($params);
        if ($result === true) {
            return $this->success($result);
        } else {
            return $this->throwError($result);
        }
    }

    public function bathUpdateAddress(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid',0);
        $params['province_id'] = $params['province_id'] ?? 0;
        $params['city_id'] = $params['city_id'] ?? 0;
        $params['district_id'] = $params['district_id'] ?? 0;

        //参数验证
        $validate = Validate::rule([
            'sub_order_nos|子订单号'       => 'require',
            'province_id|省ID'             => 'require',
            'city_id|市ID'                 => 'require',
            'district_id|区ID'             => 'require',
            'province_name|省名'           => 'require',
            'city_name|市名'               => 'require',
            'district_name|区名'           => 'require',
            'address|详细地址'             => 'require',
            'consignee|收货人姓名'         => 'require',
            'consignee_phone|收货人手机号' => 'require',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }

        $sub_orders = explode(',', $params['sub_order_nos']);
        $tr_orders  = Db::name('tripartite_order')->alias('t1')
            ->join('order_main t2', 't1.main_order_id=t2.id')
            ->whereIn('t1.sub_order_no', $sub_orders)
            ->column('t1.province,t1.city,t1.district,t1.address,t1.sub_order_no,t2.consignee,t2.consignee_phone,t2.main_order_no,t1.push_wms_status', 't1.sub_order_no');
        if (count($tr_orders) != count($sub_orders)) $this->throwError("订单号有误", ErrorCode::PARAM_ERROR);
        if (count(array_unique(array_column($tr_orders, 'main_order_no'))) != 1) $this->throwError("所选订单只能属于同一个主单", ErrorCode::PARAM_ERROR);
        $enc_data                    = \Curl::cryptionDeal([$params['consignee'], $params['consignee_phone']], "E");
        $params['n_consignee']       = $enc_data[$params['consignee']] ?? $params['consignee'];
        $params['n_consignee_phone'] = $enc_data[$params['consignee_phone']] ?? $params['consignee_phone'];
        $uid                         = 0;
        $main_order_no               = array_values($tr_orders)[0]['main_order_no'];

        $exists_address = Db::name('sub_order_receive_information')->where([
            ['sub_order_no', 'in', $sub_orders],
            ['uid', '=', $uid],
            ['main_order_no', '=', $main_order_no],
        ])->column('*', 'sub_order_no');


        $remarks =[];
        Db::startTrans();
        try {

            $now                        = time();
            $insert_data                = $update_orders = $push_wms_orders = [];
            $updata                     = $insert_item = [
                'uid'             => 0,
                'main_order_no'   => $main_order_no,
                'order_type'      => 7,
                'province_id'     => $params['province_id'],
                'city_id'         => $params['city_id'],
                'district_id'     => $params['district_id'],
                'address'         => $params['address'],
                'consignee'       => $params['n_consignee'],
                'consignee_phone' => $params['n_consignee_phone'],
                'province_name'   => $params['province_name'],
                'city_name'       => $params['city_name'],
                'district_name'   => $params['district_name'],
                'update_time'     => $now,
            ];
            $insert_item['update_time'] = $now;

            foreach ($sub_orders as $sub_order_no) {
                $tro = $tr_orders[$sub_order_no];
                if ($tro['push_wms_status'] == 1) {
                    $push_wms_orders[] = $sub_order_no;
                }
                $exists = $exists_address[$sub_order_no] ?? [];
                if (!empty($exists)) {
                    $enc_data                    = \Curl::cryptionDeal([$exists['consignee'], $exists['consignee_phone']]);
                    $d_consignee       = $enc_data[$exists['consignee']] ?? $exists['consignee'];
                    $d_consignee_phone = $enc_data[$exists['consignee_phone']] ?? $exists['consignee_phone'];

                    //添加订单备注
                    $remarks[] = [
                        'sub_order_no' => $sub_order_no,
                        'admin_id'     => $params['admin_id'],
                        'remarks'      => "原地址: {$d_consignee} {$d_consignee_phone} {$exists['province_name']}{$exists['city_name']}{$exists['district_name']}{$exists['address']} ,修改为:{$params['consignee']} {$params['consignee_phone']} {$params['province_name']}{$params['city_name']}{$params['district_name']}{$params['address']} ",
                        'created_time' => time(),
                    ];
                    $update_orders[] = $sub_order_no;
                } else {
                    //添加订单备注
                    $enc_data                    = \Curl::cryptionDeal([$tro['consignee'], $tro['consignee_phone']]);
                    $d_consignee       = $enc_data[$tro['consignee']] ?? $tro['consignee'];
                    $d_consignee_phone = $enc_data[$tro['consignee_phone']] ?? $tro['consignee_phone'];

                    $remarks[] = [
                        'sub_order_no' => $sub_order_no,
                        'admin_id'     => $params['admin_id'],
                        'remarks'      => "原地址: {$d_consignee} {$d_consignee_phone} {$tro['province']}{$tro['city']}{$tro['district']}{$tro['address']} ,修改为:{$params['consignee']} {$params['consignee_phone']} {$params['province_name']}{$params['city_name']}{$params['district_name']}{$params['address']} ",
                        'created_time' => time(),
                    ];
                    $insert_item['sub_order_no'] = $sub_order_no;
                    $insert_data[]               = $insert_item;
                }
            }

            if (!empty($update_orders)) {
                Db::name('sub_order_receive_information')->where([
                    ['sub_order_no', 'in', $update_orders],
                    ['uid', '=', $uid],
                    ['main_order_no', '=', $main_order_no],
                ])->update($updata);
            }

            if (!empty($insert_data)) {
                Db::name('sub_order_receive_information')->insertAll($insert_data);
            }

            if (!empty($remarks)) {
                Db::name('order_remarks')->insertAll($remarks);
            }

            //同步最新收货信息到子订单es
            $consignee_encrypt       = hidestr($params['consignee'], 1);
            $consignee_phone_encrypt = hidestr($params['consignee_phone'], 3, 4);
            $updateEsData            = [
                "province_id"             => $params['province_id'],
                "city_id"                 => $params['city_id'],
                "district_id"             => $params['district_id'],
                "address"                 => $params['address'],
                "province_name"           => $params['province_name'],
                "city_name"               => $params['city_name'],
                "district_name"           => $params['district_name'],
                "consignee"               => $params['consignee'],
                "consignee_phone"         => $params['consignee_phone'],
                "consignee_encrypt"       => $consignee_encrypt,
                "consignee_phone_encrypt" => $consignee_phone_encrypt,
            ];

            Es::name(Es::ORDERS)->where([['_id', 'in', $sub_orders]])->save($updateEsData);

            if (!empty($push_wms_orders)) {
                //同步更新萌芽暂存状态
                $wms_up_data = [
                    "address"        => $params['address'],
                    "town"           => $params['district_name'],
                    "city"           => $params['city_name'],
                    "province"       => $params['province_name'],
                    "receiver_name"  => $params['consignee'],
                    "receiver_phone" => $params['consignee_phone'],
                ];

                foreach ($push_wms_orders as $push_wms_order_no) {
                    $wms_up_data['orderno'] = $push_wms_order_no;
                    \Curl::receiptInfo($wms_up_data);
                }
            }

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            return $this->throwError($e->getMessage() . $e->getLine());
        }
        return $this->success(true);
    }

    public function bathUpdateWhCode(Request $request)
    {
        $params               = $request->param();
        $params['admin_id']   = $request->header('vinehoo-uid', 0);
        $params['admin_name'] = base64_decode($request->header('vinehoo-vos-name'));

        //参数验证
        $validate = Validate::rule([
            'sub_order_nos|子订单号' => 'require',
            'warehouse_id|仓库ID'    => 'require',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $sub_orders = explode(',', $params['sub_order_nos']);

        $is_supplier_delivery = Db::table('vh_commodities.vh_virtual_warehouse')->where([
            ['erp_id', 'in', $params['warehouse_id']],
        ])->value('is_supplier_delivery');
        if ($is_supplier_delivery === null) {
            $this->throwError("未查询到仓库!");
        }

        $remarks = [];
        Db::startTrans();
        try {
            $now         = time();
            $insert_data = $update_orders = $push_wms_orders = [];
            $updata      = [
                'is_supplier_delivery' => $is_supplier_delivery,
                'warehouse_id'         => $params['warehouse_id'],
                'update_time'          => $now,
            ];

            foreach ($sub_orders as $sub_order_no) {
                $remarks[] = [
                    'sub_order_no' => $sub_order_no,
                    'admin_id'     => $params['admin_id'],
                    'remarks'      => "{$params['admin_name']} 修改" . ($is_supplier_delivery ? '代发' : "") . "仓库: {$params['warehouse_id']}",
                    'created_time' => time(),
                ];
            }

            Db::name('tripartite_order')->where([
                ['sub_order_no', 'in', $sub_orders],
            ])->update($updata);

            if (!empty($remarks)) {
                Db::name('order_remarks')->insertAll($remarks);
            }
            $updata['update_time'] = date('Y-m-d H:i:s');
            Es::name(Es::ORDERS)->where([['_id', 'in', $sub_orders]])->save($updata);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            return $this->throwError($e->getMessage() . $e->getLine());
        }
        return $this->success(true);
    }

    public function export(Request $request)
    {
        $param = $request->param();

        Log::write(__FUNCTION__ . ' 三方订单导出: ' . json_encode($param));

        try {
            $wechat_status = $param['process_instance']['status'] ?? null;
            if (!in_array($wechat_status, ['COMPLETED', 'TERMINATED'])) {
                throw new Exception("审批未完成");
            }
            if ($wechat_status == 'COMPLETED') {
                $wechat_result = $param['process_instance']['result'] ?? null;
                if (!$wechat_result) {
                    throw new Exception('审批状态错误');
                }
            } else {
                $wechat_result = $wechat_status;
            }
            $user_id = $param['process_instance']['originator_userid'];

            if ($wechat_result == 'agree') {
                $limit                   = 10000;
                $form_component_values   = array_column($param['process_instance']['form_component_values'], 'value', 'name');
                $where                   = function ($query) use ($form_component_values) {
                    if (!empty($form_component_values['开始日期'])) {
                        $start_time = date('Y-m-d 00:00:00', strtotime($form_component_values['开始日期']));
                        $query->whereTime('o.created_time', '>=', $start_time);
                    }
                    if (!empty($form_component_values['结束日期'])) {
                        $end_time = date('Y-m-d 23:59:59', strtotime($form_component_values['结束日期']));
                        $query->whereTime('o.created_time', '<=', $end_time);
                    }
                    if (!empty($form_component_values['多选'])) {
                        $dx = json_decode($form_component_values['多选'], true);
                        $store_ids = [];
                        $config    = config('config');
                        $tr_stores = array_column(array_merge($config['tripartite_store'], $config['tripartite_sg_store']), 'value', 'label');
                        foreach ($dx as $dx_item) {
                            if (!empty($tr_stores[$dx_item])) {
                                $store_ids[] = $tr_stores[$dx_item];
                            }
                        }

                        if (!in_array('全部店铺', $dx)) {
                            $query->where('o.store_id', 'in', $store_ids);
                        }
                    }
                };
                $total                   = Db::name('tripartite_order')->alias('o')->join("order_main m", 'o.main_order_id=m.id')->where($where)->count();
                $total_page              = ceil($total / $limit);
                $trorder_payment_subject = [
                    '0' => '未知',
                    '1' => '重庆云酒佰酿电子商务有限公司',
                    '2' => '佰酿云酒（重庆）科技有限公司',
                    '3' => '木兰朵',
                    '4' => '松鸽酒业有限公司',
                ]; //订单收款公司
                $config                  = config('config');
                $sub_order_status_arr    = array_column($config['sub_order_status'], 'label', 'value');
                $warehouse_code_arr      = array_column($config['warehouse_code'], 'label', 'value');
                $push_t_status_arr       = array_column($config['push_t_status'], 'label', 'value');
                $data[]                  = [
                    '主体',
                    '主订单号',
                    '子订单号',
                    '店铺名称',
                    '简码',
                    '数量',
                    '金额',//需要按照订单推送逻辑拆分
                    '支付时间',
                    '发货时间',
                    '订单状态',
                    '仓库名称', //仓库名称
                    '萌芽实际发货虚拟仓',
                    '萌芽发货简码',
                    '萌芽发货数量',
                    '差异',
                    '萌芽发货时间',
                    'ERP推送状态',
                ];
                if ($total_page > 0) {
                    foreach (range(1, $total_page) as $page) {
                        $start = ($page - 1) * $limit;

                        $orders = Db::name('tripartite_order')->alias('o')
                            ->join("order_main m", 'o.main_order_id=m.id')->where($where)
                            ->limit($start, $limit)
                            ->field('m.main_order_no,o.order_qty,o.company_id,o.push_t_status,o.warehouse_id,o.payment_amount,o.sub_order_no,o.store_id,o.delivery_time,o.payment_time,o.remarks,o.express_number,o.province,o.city,o.district,o.created_time,o.title,o.sub_order_status,o.items_info,o.store_name')->select()->toArray();
                        $sub_order_nos = array_column($orders,'sub_order_no');

                        $wmsOrders      = [];
                        $sub_order_nosg = array_chunk($sub_order_nos, 500);
                        foreach ($sub_order_nosg as $sub_order_nosgi) {
                            $wmsOrdersTmp = \Curl::queryOrderStatus(['orderno' => implode(',', $sub_order_nosgi)]);
                            $wmsOrders    = array_merge($wmsOrders, ($wmsOrdersTmp ?? []));
                        }

                        $wmsOrders = array_column($wmsOrders,null,'orderno');

                        foreach ($orders as $order) {
                            $p_items_info_arr = $wms_order_goods = [];
                            $wms_order = $wmsOrders[$order['sub_order_no']] ?? [];
                            $wms_order_goods = array_column(($wms_order['goods'] ?? []),null,'short_code');

                            $p_items_infos = explode(',', $order['items_info']);
                            foreach ($p_items_infos as $pii) {
                                $pii  = explode('*', $pii);
                                if (empty($p_items_info_arr[$pii[0]])) {
                                    $p_items_info_arr[$pii[0]] = $pii;
                                } else {
                                    $p_items_info_arr[$pii[0]]['1'] = bcadd(($p_items_info_arr[$pii[0]]['1'] ?? '1'), ($pii['1'] ?? '1'), 2);
                                    $p_items_info_arr[$pii[0]]['2'] = bcadd(($p_items_info_arr[$pii[0]]['2'] ?? '0'), ($pii['2'] ?? '0'), 2);
                                }
                            }

                            ksort($p_items_info_arr);
                            ksort($wms_order_goods);

                            $p_items_info_arr = array_values($p_items_info_arr);
                            $wms_order_goods = array_values($wms_order_goods);

                            $nums = max(count($p_items_info_arr),count($wms_order_goods));

                            if($nums > 0){
                                foreach (range(0,($nums -1)) as $i){
                                    $data[]  = [
                                        $trorder_payment_subject[$order['company_id']] ?? $order['company_id'],
                                        $order['main_order_no'],
                                        $order['sub_order_no'],
                                        $order['store_name'],
                                        $p_items_info_arr[$i]['0'] ?? '',
                                        bcmul(($p_items_info_arr[$i]['1'] ?? '1'),$order['order_qty']),
                                        $p_items_info_arr[$i]['2'] ?? '',
                                        $order['payment_time'] > 0 ? date('Y-m-d H:i:s', $order['payment_time']) : '',
                                        $order['delivery_time'] > 0 ? date('Y-m-d H:i:s', $order['delivery_time']) : '',
                                        $sub_order_status_arr[$order['sub_order_status']] ?? $order['sub_order_status'],
                                        $warehouse_code_arr[$order['warehouse_id']] ?? $order['warehouse_id'],

                                        $wms_order['fictitious_id'] ?? '',
                                        $wms_order_goods[$i]['short_code'] ?? '',
                                        $wms_order_goods[$i]['nums'] ?? '',
                                        bcsub(bcmul(($p_items_info_arr[$i]['1'] ?? '1'),$order['order_qty']), ($wms_order_goods[$i]['nums'] ?? 0)),
                                        $wms_order['complete_time'] ?? '',
                                        $push_t_status_arr[$order['push_t_status']] ?? $order['push_t_status'],
                                    ];

                                }
                            }
                        }


                        $path      = app()->getRootPath() . "public/storage/";
                        $excel     = new \Vtiful\Kernel\Excel(['path' => $path]);
                        $file_name = time() . "三方订单导出_{$page}.xlsx";
                        $filePath  = $excel->fileName($file_name, 'sheet1')->data($data)->output();

                        $temp_file_info = \Curl::upTempFileToWechat($filePath);
                        unlink($filePath);
                        \Curl::wecomSend($temp_file_info['media_id'], $user_id, 'file');

                        $data                 = [
                            [
                                '主体',
                                '主订单号',
                                '子订单号',
                                '店铺名称',
                                '简码',
                                '数量',
                                '金额',//需要按照订单推送逻辑拆分
                                '支付时间',
                                '发货时间',
                                '订单状态',
                                '仓库名称', //仓库名称
                                '萌芽实际发货虚拟仓',
                                '萌芽发货简码',
                                '萌芽发货数量',
                                '差异',
                                '萌芽发货时间',
                                'ERP推送状态',
                            ]
                        ];
                    }
                }
//
//                $path      = app()->getRootPath() . "public/storage/";
//                $excel     = new \Vtiful\Kernel\Excel(['path' => $path]);
//                $file_name = time() . '三方订单导出.xlsx';
//                $filePath  = $excel->fileName($file_name, 'sheet1')->data($data)->output();
//
//                $temp_file_info = \Curl::upTempFileToWechat($filePath);
//                unlink($filePath);
//                \Curl::wecomSend($temp_file_info['media_id'], $user_id, 'file');
            }
        } catch (\Exception $exception) {
            Log::write(__FUNCTION__ . ' 三方订单导出 ERROR: ' . $exception->getMessage() . $exception->getLine());
            if (!empty($user_id)) {
                \Curl::wecomSend('三方订单导出失败' . $exception->getMessage() . ' ' . $exception->getLine(), $user_id, 'text');
            }
            if (!empty($filePath)) {
                unlink($filePath);
            }
            throw new Exception(' 三方订单导出 ERROR: ' . $exception->getMessage() . $exception->getLine());
        }

        return $this->success();
    }

    public function changeItemsInfo(Request $request)
    {
        $params = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        $params['admin_name'] = base64_decode($request->header('vinehoo-vos-name'));

        // 修改后的验证规则
        $validate = Validate::rule([
            'admin_id|后台用户ID' => 'require|number',
            'sub_order_no|订单号' => 'require',
            'type|类型'           => 'require|in:1,2,3',
            'product|商品信息'    => 'require|array',
            'full_address|收货地址'               => 'requireIf:type,3',
            'consignee|收货人姓名'           => 'requireIf:type,3',
            'consignee_phone|收货人电话号码' => 'requireIf:type,3',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $validate_sub = Validate::rule([
            'short_code|简码' => 'require',
            'nums|数量'       => 'require|integer|>:0',
            'price|金额'      => 'require|float|>=:0'
        ]);
        foreach ($params['product'] as $p_product) {
            if (!$validate_sub->check($p_product)) {
                return $this->throwError($validate_sub->getError());
            }
        }

        $tripartiteService = new TripartiteService();
        $result            = $tripartiteService->changeItemsInfo($params);
        return $this->success($result);
    }

    public function salesList(Request $request)
    {
        $params = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');

        try {
            $orders_nos = Db::name('sales_data')->whereOr('corp_code', "")->whereOr('corp_code', null)->column('order_id');
            if (count($orders_nos) > 0) {
                $corps   = Db::name('collecting_company')->column('name', 'corp');
                $trcorps = Db::name('offline_order')->where('sub_order_no', "in", $orders_nos)->column('corp,sub_order_no');
                $trcorps = array_group($trcorps, 'corp');
                foreach ($trcorps as $corp_code => $trcorp) {
                    $corp = $corps[$corp_code] ?? '';
                    Db::name('sales_data')->where('order_id', 'in', array_column($trcorp, 'sub_order_no'))->update(compact('corp', 'corp_code'));
                }
                $trcorps = \app\service\SalesReturn::getTripartiteOrderCorp($orders_nos);
                $trcorps = array_group($trcorps, 'corp');
                foreach ($trcorps as $corp_code => $trcorp) {
                    $corp = $corps[$corp_code] ?? '';
                    Db::name('sales_data')->where('order_id', 'in', array_column($trcorp, 'sub_order_no'))->update(compact('corp', 'corp_code'));
                }
                $res =Db::name('sales_data')->whereOr('corp_code', "")->whereOr('corp_code', null)->update([
                    'corp'      => '未知',
                    'corp_code' => '未知',
                ]);
            }
        } catch (\Exception $trExcept) {
        }

        // 验证分页参数
        $validate = new ListPagination();
        if (!$validate->goCheck($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        
        // 构建查询条件
        $where = [];
        
        // 订单号筛选
        if (!empty($params['order_id'])) {
            $where[] = ['order_id|main_order_id', 'like', '%' . $params['order_id'] . '%'];
        }

        // 平台类型筛选
        if (!empty($params['platform_type'])) {
            $where[] = ['platform_type', '=', $params['platform_type']];
        }

        // 平台类型筛选
        if (!empty($params['corp_code'])) {
            $where[] = ['corp_code', '=', $params['corp_code']];
        }

        // 平台名称筛选
        if (!empty($params['corp'])) {
            $where[] = ['corp', 'like', '%' . $params['corp'] . '%'];
        }
        
        // 平台名称筛选
        if (!empty($params['platform_name'])) {
            $where[] = ['platform_name', 'like', '%' . $params['platform_name'] . '%'];
        }
        
        // 月份筛选
        if (!empty($params['month'])) {
            $where[] = ['month', '=', $params['month']];
        }

        // 月份范围筛选
        if (!empty($params['start_month']) && !empty($params['end_month'])) {
            $where[] = ['month', 'between', [$params['start_month'], $params['end_month']]];
        }
        
        // 订单状态筛选
        if (!empty($params['order_status'])) {
            $where[] = ['order_status', '=', $params['order_status']];
        }
        
        // 发货时间范围筛选
        if (!empty($params['shipping_start_time'])) {
            $where[] = ['shipping_time', '>=', strtotime($params['shipping_start_time'])];
        }
        if (!empty($params['shipping_end_time'])) {
            $where[] = ['shipping_time', '<=', strtotime($params['shipping_end_time'])];
        }
        
        // 创建时间范围筛选
        if (!empty($params['created_start_time'])) {
            $where[] = ['created_at', '>=', $params['created_start_time']];
        }
        if (!empty($params['created_end_time'])) {
            $where[] = ['created_at', '<=', $params['created_end_time']];
        }
        
        // 计算分页参数
        $page = isset($params['page']) ? max(1, intval($params['page'])) : 1;
        $limit = isset($params['limit']) ? max(1, intval($params['limit'])) : 10;
        $offset = ($page - 1) * $limit;

        $p_export = $params['export'] ?? 0;
        if ($p_export == 1) {
            $list = Db::name('sales_data')
                ->where($where)
                ->order('created_at', 'desc')
//                ->order('platform_type', 'desc')
//                ->order('month', 'desc')
                ->column('month,end_time,platform_type,corp,platform_name,main_order_id,order_id,order_status,order_amount,merchant_receivable,merchant_receivable,receivable_amount,shipping_time,payment_channel');

            $list = $this->salesListExtend($list);

            $header = array(
                array('column' => 'month', 'name' => '所属月份', 'width' => 30),
                array('column' => 'platform_type', 'name' => '平台类型', 'width' => 30),
                array('column' => 'platform_name', 'name' => '平台名称', 'width' => 30),
                array('column' => 'corp', 'name' => '所属主体', 'width' => 30),
                array('column' => 'main_order_id', 'name' => '主订单号', 'width' => 30),
                array('column' => 'order_id', 'name' => '子订单号', 'width' => 30),
                array('column' => 'order_status', 'name' => '订单状态', 'width' => 30),
                array('column' => 'merchant_receivable', 'name' => '商家应收金额', 'width' => 30),
                array('column' => 'refund_amount', 'name' => '退款金额', 'width' => 30),
                array('column' => 'order_amount', 'name' => '订单金额', 'width' => 30),
                array('column' => 'receivable_amount', 'name' => '应收金额', 'width' => 30),
                array('column' => 'shipping_time', 'name' => '发货时间', 'width' => 30),
                array('column' => 'end_time', 'name' => '订单完成时间', 'width' => 30),
                array('column' => 'u8c_029_amount', 'name' => 'U8C 029金额', 'width' => 30),
                array('column' => 'u8c_515_amount', 'name' => 'U8C 515金额', 'width' => 30),
                array('column' => 't_plus_002_amount', 'name' => 'T+002金额', 'width' => 30),
                array('column' => 't_plus_008_amount', 'name' => 'T+008金额', 'width' => 30),
                array('column' => 'sales_order_total', 'name' => '销货单合计', 'width' => 30),
                array('column' => 'sales_order_date', 'name' => '销货单日期', 'width' => 30),
                array('column' => 'unshipped_amount', 'name' => '本月未发货金额', 'width' => 30),
                array('column' => 'received_amount', 'name' => '已回款金额', 'width' => 30),
                array('column' => 'payment_date', 'name' => '回款日期', 'width' => 30),
                array('column' => 'payment_channel', 'name' => '收款渠道', 'width' => 30),
                array('column' => 'pending_payment', 'name' => '待回款', 'width' => 30),
            );
            $file_name = time().'三方销售数据';
            $uploadUrl = exportSheelExcels2($list, $header, $file_name,'Xlsx');

//            $header = [
//                "所属月份",
//                "平台类型",
//                "平台名称",
//                "主订单号",
//                "子订单号",
//                "订单状态",
//                "商家应收金额",
//                "退款金额",
//                "订单金额",
//                "应收金额",
//                "发货时间",
//                "U8C 029金额",
//                "U8C 515金额",
//                "T+002金额",
//                "T+008金额",
//                "销货单合计",
//                "销货单日期",
//                "本月未发货金额",
//                "已回款金额",
//                "回款日期",
//                "收款渠道",
//                "待回款"
//            ];
            $filePath      = app()->getRootPath() . "public/{$uploadUrl}";
//            $temp_file_info = \Curl::upTempFileToWechat($filePath);
//            unlink($filePath);
//            \Curl::wecomSend($temp_file_info['media_id'], "ChenChaoTao", 'file');

            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="' . $file_name . '.xlsx"');
            header('Cache-Control: max-age=0');
            echo file_get_contents($filePath);
            unlink($filePath);
            die;

            $excel     = new \Vtiful\Kernel\Excel(['path' => $path]);
            $filePath  = $excel->fileName($file_name, 'sheet1')->header($header)->data($list)->output();
        } else {
            // 查询数据
            $list = Db::name('sales_data')
                ->where($where)
                ->order('created_at', 'desc')
                ->limit($offset, $limit)
                ->column('month,end_time,platform_type,corp,platform_name,main_order_id,order_id,order_status,order_amount,merchant_receivable,merchant_receivable,receivable_amount,shipping_time,payment_channel');

            $list = $this->salesListExtend($list);

            // 查询总数
            $total = Db::name('sales_data')
                ->where($where)
                ->count();

            return $this->success([
                'list'  => $list,
                'total' => $total,
                'page'  => $page,
                'limit' => $limit
            ]);
        }
    }

    private function salesListExtend($list)
    {
        $sub_order_nos = array_column($list, 'order_id');
        #region  回款数据 settlement
        $settlements      = [];
        $temp_settlements = Db::name('tripartite_settlement')
            ->where('sub_order_no', 'in', $sub_order_nos)
            ->where('business', 'in', ['退款', '收入'])//'退款','收入'
            ->where('status', '=', 2)//已审核
            ->order('create_time', 'desc')
            ->column('sub_order_no,amount,business,create_time');

        foreach ($temp_settlements as $item) {
            if (empty($settlements[$item['sub_order_no']])) {
                $tmp_settlement = [
                    'refund_amount'   => 0, //退款金额
                    'received_amount' => 0, //已回款金额
                    'payment_date'    => ($item['create_time'] > 0) ? date('Y-m-d', $item['create_time']) : "", //回款日期
                ];
            } else {
                $tmp_settlement = $settlements[$item['sub_order_no']];
            }
            if (in_array($item['business'], ['退款'])) {
                $tmp_settlement['refund_amount'] = bcadd($tmp_settlement['refund_amount'], $item['amount'], 2);
            }
            if (in_array($item['business'], ['收入'])) {
                $tmp_settlement['received_amount'] = bcadd($tmp_settlement['received_amount'], $item['amount'], 2);
            }
            $settlements[$item['sub_order_no']] = $tmp_settlement;
        }
        unset($temp_settlements);
        #endregion $settlements
        #region ERP销售数据 $erp_datas
        $erp_datas         = [];
        $sales_order_dates = Db::name('tripartite_order')
            ->where('sub_order_no', 'in', $sub_order_nos)
            ->where('delivery_time', '>', 0)
            ->column('delivery_time', 'sub_order_no');
        $sales_order_dates = array_merge($sales_order_dates, Db::name('offline_order')
            ->where('sub_order_no', 'in', $sub_order_nos)
            ->where('delivery_time', '>', 0)
            ->column('delivery_time', 'sub_order_no'));
        $erp_amounts = [];
        if (!empty($sub_order_nos)) {
            $erp_amounts = \Curl::erpGetAmounts(['sub_order_no' => implode(',', $sub_order_nos)]);
        }

        foreach ($sub_order_nos as $sub_order_no) {
            $erp_datas[$sub_order_no]                      = $erp_amounts[$sub_order_no] ?? [
                'u8c_029_amount'    => 0,
                'u8c_515_amount'    => 0,
                't_plus_002_amount' => 0,
                't_plus_008_amount' => 0,
            ];
            $erp_datas[$sub_order_no]['sales_order_total'] = round(array_sum(array_values($erp_datas[$sub_order_no])), 2);
            $erp_datas[$sub_order_no]['sales_order_date']  = !empty($sales_order_dates[$sub_order_no]) ? date('Y-m-d', $sales_order_dates[$sub_order_no]) : '';
        }
        unset($erp_amounts, $sales_order_dates);
        #endregion ERP销售数据 $erp_datas

        foreach ($list as &$order) {
            $settlement                = $settlements[$order['order_id']] ?? [
                'refund_amount'   => 0,
                'received_amount' => 0,
                'payment_date'    => ''
            ];
            $erp_data                  = $erp_datas[$order['order_id']] ?? [
                'u8c_029_amount'    => 0,
                'u8c_515_amount'    => 0,
                't_plus_002_amount' => 0,
                't_plus_008_amount' => 0,
                'sales_order_total' => 0,
                'sales_order_date'  => '',
            ];
            $order                     = array_merge($order, $settlement, $erp_data);
            $order['pending_payment']  = bcsub($order['receivable_amount'], $order['received_amount'], 2); // 待回款
            $order['unshipped_amount'] = bcsub($order['receivable_amount'], $order['sales_order_total'], 2); // 本月未发货金额
            $order['shipping_time']    = ($order['shipping_time'] > 0) ? date('Y-m-d', $order['shipping_time']) : ""; //回款日期
            $order['end_time']    = ($order['end_time'] > 0) ? date('Y-m-d H:i:s', $order['end_time']) : ""; //回款日期
        }
        return $list;
    }

    public function orderMonitorDeal(Request $request)
    {
        $param = $request->param();
        Log::write("orderMonitorDeal : " . json_encode($param));
        if ($param['order_from_thirdparty'] == 31) {
            //木兰朵·命蕴大美
            Db::table('vh_mulando.vh_order')->where('sub_order_no', $param['sub_order_no'])->update([
                'push_wms_status' => $param['push_wms_status'],
                'push_t_status'   => $param['push_t_status'],
            ]);
        }
    }

}