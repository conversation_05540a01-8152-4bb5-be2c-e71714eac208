<?php


namespace app\command;


use think\console\Command;
use think\console\Input;
use think\console\Output;

class CollectionInformationQueryCommand extends Command
{
    protected $service;

    protected function configure()
    {
        // 指令配置
        $this->setName('CollectionInformationQueryCommand')
            ->setDescription('the CollectionInformationQueryCommand command');
    }

    protected function execute(Input $input, Output $output)
    {
        // 指令输出
        $this->init();
        $this->service->exec();
    }

    protected function init(){
        $this->service = new \app\service\command\CollectionInformationQueryCommand();
    }
}