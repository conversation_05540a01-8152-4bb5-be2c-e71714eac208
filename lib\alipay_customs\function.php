<?php
/**
 * Created by PhpStorm.
 * User: oldSmokeGun
 * Date: 2019/7/6
 * Time: 11:34
 */

/**
 * @param $outRequestNo 商户生成的用于唯一标识一次报关操作的业务编号（商户端报关请求号）
 * @param $tradeNo 交易流水号
 * @param $amount 报关金额，单位为人民币“元”，精确到小数点后 2 位
 * @param $store_ids 仓库编码
 * @return 支付宝处理结果
 */
function custom_declare($outRequestNo, $tradeNo, $amount)
{
    require_once("alipay.config.php");
    require_once("lib/alipay_submit.class.php");

    // 构造要请求的参数数组，无需改动
    $parameter = array(
        "service"               => "alipay.acquire.customs",
        "partner"               => trim($alipay_config['partner']),
        "out_request_no"        => $outRequestNo,
        "trade_no"              => $tradeNo,
        "merchant_customs_code" => $alipay_config['merchant_customs_code'],
        "merchant_customs_name" => $alipay_config['merchant_customs_name'],
        "amount"                => $amount,
        "customs_place"         => $alipay_config['customs_place'],
        "_input_charset"        => trim(strtolower($alipay_config['input_charset']))
    );

    // 建立请求
    $alipaySubmit = new AlipaySubmit($alipay_config);

    $response = $alipaySubmit->buildRequestHttp($parameter);

    return $response;
}

/**
 * @param $outRequestNos 需要查询的商户端报关请求号，支持批量查询，多个值用英文半角逗号分隔，单次最多 10 个报关请求号
 * @return 支付宝处理结果
 */
function custom_declare_query($outRequestNos)
{
    require_once("alipay.config.php");
    require_once("lib/alipay_submit.class.php");

    // 构造要请求的参数数组，无需改动
    $parameter = array(
        "service"        => "alipay.overseas.acquire.customs.query",
        "partner"        => trim($alipay_config['partner']),
        "_input_charset" => trim(strtolower($alipay_config['input_charset'])),
        "out_request_nos"=> $outRequestNos
    );

    // 建立请求
    $alipaySubmit = new AlipaySubmit($alipay_config);

    $response = $alipaySubmit->buildRequestHttp($parameter);

    return $response;
}