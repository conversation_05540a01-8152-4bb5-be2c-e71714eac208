<?php


namespace app\controller;


use app\BaseController;
use app\ErrorCode;
use app\Request;
use app\service\Notify as NotifyService;

class Notify extends BaseController
{
    /**
     * Description:银联支付异步回调
     * Author: zrc
     * Date: 2021/8/16
     * Time: 10:23
     * @param Request $request
     */
    public function notify(Request $request)
    {
        $params = file_get_contents('php://input');
        if (empty($params)) {
            $this->throwError('未获取到回调参数', ErrorCode::PARAM_ERROR);
        }
        $notifyService = new NotifyService();
        $result        = $notifyService->notify($params);
        return $result;
    }

    /**
     * Description:队列处理异步回调处理抽奖记录+抽奖记录+补差价订单+已购数量
     * Author: zrc
     * Date: 2021/8/16
     * Time: 12:08
     * @param Request $request
     * @return \think\Response
     */
    public function notifyDealOrderStatus(Request $request)
    {
        $params = file_get_contents('php://input');
        //$params = $request->param();
        if (empty($params)) {
            $this->throwError('未获取到队列数据', ErrorCode::PARAM_ERROR);
        }
        $notifyService = new NotifyService();
        $result        = $notifyService->notifyDealOrderStatus($params);
        return $this->success($result);
    }

    /**
     * Description:队列处理异步回调订单添加开票记录
     * Author: zrc
     * Date: 2022/4/11
     * Time: 16:47
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function notifyDealInvoice(Request $request)
    {
        $params = file_get_contents('php://input');
        //$params = $request->param();
        if (empty($params)) {
            $this->throwError('未获取到队列数据', ErrorCode::PARAM_ERROR);
        }
        $notifyService = new NotifyService();
        $result        = $notifyService->notifyDealInvoice($params);
        return $this->success($result);
    }

    /**
     * Description:队列处理异步回调订单拼团
     * Author: zrc
     * Date: 2022/4/12
     * Time: 11:58
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function notifyDealGroup(Request $request)
    {
        $params = file_get_contents('php://input');
        //$params = $request->param();
        if (empty($params)) {
            $this->throwError('未获取到队列数据', ErrorCode::PARAM_ERROR);
        }
        $notifyService = new NotifyService();
        $result        = $notifyService->notifyDealGroup($params);
        return $this->success($result);
    }

    /**
     * Description:队列处理异步回调订单满赠
     * Author: zrc
     * Date: 2022/4/12
     * Time: 11:58
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function notifyDealFullGift(Request $request)
    {
        $params = file_get_contents('php://input');
        //$params = $request->param();
        if (empty($params)) {
            $this->throwError('未获取到队列数据', ErrorCode::PARAM_ERROR);
        }
        $notifyService = new NotifyService();
        $result        = $notifyService->notifyDealFullGift($params);
        return $this->success($result);
    }

    /**
     * Description:队列处理异步回调订单推送萌牙+订金膨胀处理
     * Author: zrc
     * Date: 2022/4/12
     * Time: 11:58
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function notifyDealPushWMS(Request $request)
    {
        $params = file_get_contents('php://input');
        //$params = $request->param();
        if (empty($params)) {
            $this->throwError('未获取到队列数据', ErrorCode::PARAM_ERROR);
        }
        $notifyService = new NotifyService();
        $result        = $notifyService->notifyDealPushWMS($params);
        return $this->success($result);
    }
}