<?php
declare (strict_types = 1);

namespace app\model;

use app\service\elasticsearch\ElasticSearchService;
use think\facade\Db;
use think\Model;

/**
 * @mixin \think\Model
 */
class OrderZdmRecord extends Model
{
    public $api_key = '23dd810c763d2cfebd51a199e13fb225';
    /**
     * @param $prams
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function list($prams)
    {
        $page = $prams['page']??1;
        $limit = $prams['limit']??10;
        $offset = ($page - 1) * $limit;
        $where=[];
        $where[] = ['is_del','=',0];
        if (isset($params['start_time']) && !empty($params['start_time']) && isset($params['end_time']) && !empty($params['end_time'])) {
            $where[] = ['created_time', 'between',[strtotime($params['start_time']),strtotime($params['end_time'])] ];
        }
        if ( isset($prams['uid']) && !empty($params['uid'])) {
            $where[] = ['uid' ,'=', $params['uid']];
        }
        if ( isset($prams['sub_order_no']) && !empty($params['sub_order_no'])) {
            $where[] = ['sub_order_no' ,'=', $params['sub_order_no']];
        }
        if ( isset($prams['period']) && !empty($params['period'])) {
            $where[] = ['period' ,'=', $params['period']];
        }
        if ( isset($prams['source_event']) && !empty($params['source_event'])) {
            $where[] = ['source_event' ,'=', $params['source_event']];
        }
        $list= Db::name("order_zdm_record")->where($where)->limit($offset,$limit)->select()->toArray();
        $result['list']=$list;
        $result['count']=count($list);
        return $result;
    }

    /**
     * @param $params
     * @return int|string
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function recordCreate($params)
    {
        $filed='uid,nickname,telephone,created_time';
        $userInfo = getUserInfoByUids($params['uid'],$filed);
        $es = new ElasticSearchService();
        $where[] = ['id' => $params['period']];
        $arr = array(
            'index'  => ['periods'],
            'match'        => $where,
            'source' => ['id','title'],
        );
        $periodsData = $es->getDocumentList($arr);
        $periodsInfo=[];
        if (!empty($periodsData['data'][0])){
            $periodsInfo = $periodsData['data'][0];
        }
        $recordData = array(
            'sub_order_no'    => $params['sub_order_no'],
            'main_order_id'   => $params['main_order_id'],
            'period'          => $params['period'],
            'title'          => empty($periodsInfo)?'':$periodsInfo['title'],
            'period_type'     => $params['period_type'],
            'package_id'      => $params['package_id'],
            'order_qty'       => $params['order_qty'],
            'payment_amount'  => $params['payment_amount'],
            'uid'             => $params['uid'],
            'source_event'    => $params['source_event'],
            'consignee'    => $params['consignee'],
            'consignee_phone'    => $params['consignee_phone'],
            'created_time'    => $params['created_time'],
            'user_created_time'    => isset($userInfo[$params['uid']]) ? $userInfo[$params['uid']]['created_time']  : '',
            'nickname'    => isset($userInfo[$params['uid']]) ? $userInfo[$params['uid']]['nickname'] : '',
            'telephone'    => isset($userInfo[$params['uid']]) ? $userInfo[$params['uid']]['telephone'] : '',
        );
        return  Db::name('order_zdm_record')->insert($recordData);
    }

    /**
     * Description:验证来源标识
     * Author: gangh
     * @param string $source_event 来源标识
     * @param array $periods 期数
     * @return bool|string
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function VerifySourceEvent($source_event, $periods)
    {
        if (strpos(strval($source_event), 'smzdm') === false) {
            return $source_event;
        }

        $source_event_arr = explode('_', trim($source_event));
        if (empty($source_event_arr[1]) || !in_array($source_event_arr[1], $periods)) {
            return false;
        }

        return 'smzdm';
    }

    /**
     * Description:添加记录
     * Author: gangh
     * @param array $sub_order 子订单数据
     * @param array $params 请求参数
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function addRecord($sub_order, $params)
    {
        if (empty($params['source_event'])) {
            return true;
        }

        # 验证什么值得买来源标识
        $source_event = $this->VerifySourceEvent($params['source_event'], [$sub_order['period']]);
        if (empty($source_event) || $source_event != 'smzdm') {
            return true;
        }

        $recordData = array(
            'sub_order_no'    => $sub_order['sub_order_no'],
            'main_order_id'   => $sub_order['main_order_id'],
            'period'          => $sub_order['period'],
            'period_type'     => $sub_order['periods_type'],
            'package_id'      => $sub_order['package_id'],
            'order_qty'       => $sub_order['order_qty'],
            'payment_amount'  => $sub_order['payment_amount'],
            'uid'             => $sub_order['uid'],
            'source_event'    => $params['source_event'],
            'consignee'       => $params['consignee'],
            'consignee_phone' => $params['consignee_phone'],
            'created_time'    => $sub_order['created_time']
        );
        $this->recordCreate($recordData);
    }

    /**
     * Description:推送订单信息到值得买
     * Author: gangh
     * @param array $info 记录信息
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function pushZdmOrder($info)
    {
        // 设置时区
        $timezone = new \DateTimeZone('Asia/Shanghai'); // 选择你需要的时区
        $date = new \DateTime('@' . $info['created_time']);
        $date->setTimezone($timezone);
        // 格式化时间
        $order_time = $date->format('Y-m-d\TH:i:sP');
        // 佣金比例
        $commission_rate = 0.05;
        // 订单佣金
        $commission = bcmul(strval($info['payment_amount']), strval($commission_rate), 2);
        $order_json = json_encode([
            // 广告计划以及网站主渠道的标识。
            "feedback" => "1229_0_184_0_smzdm",
            // 订单编号
            "order_number" => $info['sub_order_no'],
            // 下单时间
            "order_time" => $order_time,
            // 订单金额
            "order_price" => $info['payment_amount'],
            // 订单状态。0=待定（默认值）；-1=无效订单； 1=有效订单； 2=订单已支付
            "order_status" => 0,
            // 该订单是否为新注册的用户首次下单的标识：1 = 是；其他值或不填写 = 不是
            "is_new_customer" => 0,
            // 订单佣金。发布的商品产生的销售额的5%
            "order_commission" => $commission,
            "goods" => [[
                // 商品ID
                "goods_id" => $info['period'],
                // 商品名称
                "goods_name" => $info['title'],
                // 单个商品的价格
                "goods_price" => bcdiv(strval($info['payment_amount']), strval($info['order_qty']), 2),
                // 本件商品的佣金类目。如果采用固定佣金制，此处固定填 1。
                "goods_commission_type" => 1,
                // 该商品的佣金比例。小数。0.05 代表 5%。
                "goods_commission_rate" => $commission_rate,
                // 商品数量。至少为 1。
                "goods_count" => $info['order_qty'],
                // 该商品总佣金（不是单件的佣金）。
                "goods_commission" => $commission,
            ]],
        ]);
        $body = [
            'key'   => md5($this->api_key . $order_json),
            'order' => $order_json
        ];
        $url = 'https://www.linkstars.com/api/adv/cps/order';
        $res = curlRequest($url, $body, ['Content-Type:application/x-www-form-urlencoded']);

        // 记录日志
        $log = [
            'sub_order_no' => $info['sub_order_no'],
            'push_status'  => 1,
            'push_body'    => json_encode($body),
            'push_resp'    => json_encode($res),
            'created_time' => time(),
        ];
        $push_zdm_status = 2;
        if (!empty($res['code']) && intval($res['code']) === 1) {// 推送成功
            $log['push_status'] = 0;
            $push_zdm_status = 1;
        }
        Db::name('order_zdm_push_log')->insert($log);

        // 更新推送状态
        Db::name('order_zdm_record')
            ->where('id', $info['id'])
            ->update(['push_zdm_status' => $push_zdm_status]);
    }

    /**
     * Description:推送订单状态到值得买
     * Author: gangh
     * @param array $info 记录信息
     * @param int $status 订单状态
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function pushZdmOrderStatus($info, $status)
    {
        // 更新订单状态
        Db::name('order_zdm_record')
            ->where('id', $info['id'])
            ->update(['sub_order_status' => $status]);

        switch (intval($status)) {
            case 1://已支付
            case 2://已发货
                $order_status = 2;
                break;
            case 3://已完成
                $order_status = 1;
                break;
            case 4://已取消
                $order_status = -1;
                break;
            default:
                $order_status = 0;
        }

        $order_json = json_encode([[
            // 广告计划以及网站主渠道的标识。
            "feedback" => "1229_0_184_0_smzdm",
            // 订单编号
            "order_number" => $info['sub_order_no'],
            // 订单状态。0=待定（默认值）；-1=无效订单； 1=有效订单； 2=订单已支付
            "order_status" => $order_status,
        ]]);
        $body = [
            'key'   => md5($this->api_key . $order_json),
            'order' => $order_json
        ];
        $url = 'https://www.linkstars.com/api/adv/cps/effect';
        $res = curlRequest($url, $body, ['Content-Type:application/x-www-form-urlencoded']);
        // 记录日志
        $log = [
            'sub_order_no' => $info['sub_order_no'],
            'push_type'    => 1,
            'push_status'  => 1,
            'push_body'    => json_encode($body),
            'push_resp'    => json_encode($res),
            'created_time' => time(),
        ];
        if (!empty($res['data'][0]['result']) && intval($res['data'][0]['result']) === 1) {// 推送成功
            $log['push_status'] = 0;
        }
        Db::name('order_zdm_push_log')->insert($log);
    }
}
