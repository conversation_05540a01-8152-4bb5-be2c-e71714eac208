<?php
declare (strict_types = 1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;

/**
 * 订单15天自动确认收货
 * Class AutomaticGoodsReceipt
 * @package app\command
 */
class AutomaticGoodsReceiptCommand extends Command
{
    protected $service;

    protected function configure()
    {
        // 指令配置
        $this->setName('AutomaticGoodsReceiptCommand')
            ->setDescription('the AutomaticGoodsReceiptCommand command');
    }

    protected function execute(Input $input, Output $output)
    {
    	// 指令输出
        $this->init();
        $this->service->exec();
    }

    protected function init(){
      $this->service = new \app\service\command\AutomaticGoodsReceiptCommand();
    }
}
