# 收款退款统计接口文档

## 接口地址
`POST /orders/v3/payment-statistics/write`

## 接口说明
该接口支持新旧两种调用方式，系统会根据参数自动识别调用方式。

### 新版本接口（推荐）

#### 1. 收款接口
**请求参数：**
```json
{
    "main_order_no": "VHM202412040001",
    "amount": 100.50
}
```

**参数说明：**
- `main_order_no`: 主订单号（必填）
- `amount`: 收款金额（必填，大于0）

**处理逻辑：**
1. 根据主订单号查询所有子订单
2. 根据子订单的期数查询ES获取收款商户ID
3. 按子订单金额比例分配收款金额到不同商户
4. 防重复处理（主订单号+商户ID+操作类型唯一）

#### 2. 退款接口
**请求参数：**
```json
{
    "refund_no": "GD202412040001",
    "amount": 50.25
}
```

**参数说明：**
- `refund_no`: 退款单号（必填）
- `amount`: 退款金额（必填，大于0）

**处理逻辑：**
1. 判断退款单号类型：
   - 以"GD"开头：查询工单表 `vh_customer_service.vh_work_order` 获取期数
   - 其他：查询退款表 `vh_refund_order` 获取子订单号，再查询ES获取期数
2. 根据期数查询ES获取收款商户ID
3. 防重复处理（退款单号+商户ID+操作类型唯一）

### 旧版本接口（向后兼容）

**请求参数：**
```json
{
    "company_code": "001",
    "operation_type": 1,
    "amount": 100.50
}
```

**参数说明：**
- `company_code`: 公司编码（001,002,008,032）
- `operation_type`: 操作类型（1-收款，2-退款）
- `amount`: 金额

## 收款商户ID映射

| 收款商户ID | 公司名称 |
|-----------|---------|
| 1 | 重庆云酒佰酿电子商务有限公司 |
| 2 | 佰酿云酒（重庆）科技有限公司 |
| 5 | 渝中区微醺酒业商行 |
| 10 | 海南一花一世界科技有限公司 |

## 响应格式

**成功响应：**
```json
{
    "code": 200,
    "msg": "收款数据写入成功",
    "data": true
}
```

**错误响应：**
```json
{
    "code": 400,
    "msg": "主订单不存在",
    "data": null
}
```

## 防重复机制

系统通过 `vh_payment_statistics_log` 表记录处理日志，防止重复处理：
- 收款：主订单号 + 收款商户ID + 操作类型(1) 唯一
- 退款：退款单号 + 收款商户ID + 操作类型(2) 唯一

## 注意事项

1. 新版本接口会自动根据订单信息查询收款商户，无需手动指定公司编码
2. 一个主订单可能包含多个不同商户的子订单，系统会按金额比例分配
3. 系统会自动过滤无效的收款商户ID，只处理配置中的4个公司
4. 所有操作都会记录日志，便于问题排查和数据审计
