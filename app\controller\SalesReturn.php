<?php

namespace app\controller;

use app\BaseController;
use app\ErrorCode;
use app\Request;
use app\service\SalesReturn as SalesReturnService;
use think\facade\Db;
use think\facade\Validate;

class SalesReturn extends BaseController
{

    public function list(Request $request)
    {
        $params = $request->param();
        $params['operator_id'] = $request->header('vinehoo-uid');
        $result = SalesReturnService::list($params);
        return $this->success($result);
    }

    public function create(Request $request)
    {
        $params = $request->param();
        $params['operator_id'] = $request->header('vinehoo-uid');
        $params['dingtalk_uid'] = $request->header('dingtalk-uid');
        if (!empty($params['return_courier_no'])) $params['return_courier_no'] = str_replace('，', ',', $params['return_courier_no']);
        //验证参数
        validate(\app\validate\SalesReturn::class)->check($params);
        if (strpos($params['sub_order_no'], ',') !== false) $this->throwError("批量导入的销售退货请重新导入进行编辑！");
        $params['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));
        if (!empty($params['sub_order_no']) && !empty($params['bill_no']) && ($params['bill_no'] == $params['sub_order_no'])) {
            $this->throwError("销售退货单的单号不能和销售订单一样！");
        }
        $result = SalesReturnService::create($params);
        return $this->success((object)[]);
    }

    public function update(Request $request)
    {
        $params = $request->param();
        $params['operator_id'] = $request->header('vinehoo-uid');
        $params['dingtalk_uid'] = $request->header('dingtalk-uid');
        if (!empty($params['return_courier_no'])) $params['return_courier_no'] = str_replace('，', ',', $params['return_courier_no']);
        if(!isset($params['id'])) $this->throwError("id主键不能为空");
        //验证参数
        validate(\app\validate\SalesReturn::class)->check($params);
        if(strpos($params['sub_order_no'], ',') !== false)$this->throwError("批量导入的销售退货请修改后重新导入！");
        $params['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));
        if (!empty($params['sub_order_no']) && !empty($params['bill_no']) && ($params['bill_no'] == $params['sub_order_no'])) {
            $this->throwError("销售退货单的单号不能和销售订单一样！");
        }
        $result = SalesReturnService::update($params);
        return $this->success((object)[]);
    }

    public function approval(Request $request)
    {
        $params = $request->param();
        $params['operator_id'] = $request->header('vinehoo-uid');
        $params['dingtalk_uid'] = $request->header('dingtalk-uid');
        $params['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));
        $result = SalesReturnService::approval($params);

        if(is_string($result)){
            return $this->throwError($result);
        }else if($result === true){
            return $this->success((object)[]);
        }else{
            return $this->throwError("修改失败,未改动");
        }
    }

    /**
     * 筛选需要开票的销售单据
     * @param Request $request
     * @return \think\response\Json
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getSalesDocumentsList(Request $request)
    {
        $params = $request->param();
        if (!isset($params['sales_type']) ||empty($params['sales_type'])) {
            $this->throwError('销售单据类型必传', ErrorCode::PARAM_ERROR);
        }
        $orderService = new SalesReturnService();
        $result  = $orderService->searchSalesDocuments($params);
        return $this->success($result);
    }

    public function getCorpByOrderNo(Request $request)
    {
        $params = $request->param();
        if(!is_array($params)) $this->throwError("请参数数组");
        foreach ($params as $v){
            if(!isset($v['sub_order_no']) || strlen($v['sub_order_no'])<=0)  $this->throwError("订单号为必传");
            if(!isset($v['order_type']) || strlen($v['order_type'])<=0)  $this->throwError("订单类型为必传");
        }
        $data = SalesReturnService::getCorpByOrderNo($params);
        return $this->success($data);
    }

    public function export(Request $request)
    {
        $params = $request->param();
        if(!isset($params['errcode'])) $this->throwError('非法参数');
        if($params['errcode'] != 0) $this->throwError('失败回调');

        $data = SalesReturnService::export($params);
        return $this->success($data);
    }

    /**
     * 从跑数据 审批时间
     * @return void
     */
    public function rerundata()
    {
        SalesReturnService::rerundata();
    }

    /**
     * Description:批量导入销售退货
     * Author: zrc
     * Date: 2023/6/29
     * Time: 15:51
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function salesReturnBatchImport(Request $request)
    {
        $params                  = $request->param();
        $params['admin_id']      = $request->header('vinehoo-uid');
        $params['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));
        if (empty($params['file'])) $this->throwError('未获取到OSS文件地址');
        $SalesReturnService = new SalesReturnService();
        $result             = $SalesReturnService->salesReturnBatchImport($params);
        return $this->success($result);
    }

    /**
     * Description:销售退货批量导入异步处理
     * Author: zrc
     * Date: 2023/6/29
     * Time: 18:01
     * @param Request $request
     * @return \think\response\Json
     */
    public function salesReturnBatchImportDeal(Request $request)
    {
        $params             = $request->param();
        $SalesReturnService = new SalesReturnService();
        $result             = $SalesReturnService->salesReturnBatchImportDeal($params);
        return $this->success($result);
    }

    /**
     * Description:销售退货推送ERP
     * Author: ggh
     * Date: 2023/8/8
     * @param Request $request
     * @return \think\response\Json
     */
    public function SalesReturnPushErp(Request $request)
    {
        $params = $request->param();
        $params['operator_id'] = $request->header('vinehoo-uid');
        $params['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));
        //数据验证
        $validate = Validate::rule([
            'bill_no|单据编号'       => 'require|max:100',
            'operator_id|操作人ID' => 'require|number|>:0',
            'operator_name|操作人'    => 'require|max:100'
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }

        $SalesReturnService = new SalesReturnService();
        $result = $SalesReturnService->SalesReturnPushErp($params);
        if ($result !== true) {
            $this->throwError($result, ErrorCode::PARAM_ERROR);
        }
        
        return $this->success($result);
    }

    /**
     * Description:销售退货弃审
     * Author: zrc
     * Date: 2023/8/24
     * Time: 11:11
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function rejectSalesReturn(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        //参数验证
        $validate = Validate::rule([
            'admin_id|后台用户ID'   => 'require|number',
            'bill_no|单据编号' => 'require',
            'reason|弃审原因'       => 'require|max:900',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $SalesReturnService = new SalesReturnService();
        $result = $SalesReturnService->rejectSalesReturn($params);
        return $this->success($result);
    }

    /**
     * Description:销售退货修改推送ERP时间
     * Author: gangh
     * Date: 2023/12/05
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function updatePushErpTime(Request $request)
    {
        $params             = $request->param();
        $params['operator_id'] = $request->header('vinehoo-uid');
        //数据验证
        $validate = Validate::rule([
            'bill_no|单据编号'       => 'require|max:100',
            'operator_id|操作人ID' => 'require|number|>:0',
            'push_erp_time|推送ERP时间' => 'require|dateFormat:Y-m-d H:i:s',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }

        $SalesReturnService = new SalesReturnService();
        $result = $SalesReturnService->updatePushErpTime($params);
        if ($result !== true) {
            $this->throwError($result, ErrorCode::PARAM_ERROR);
        }

        return $this->success();
    }

    /**
     * Description:销售退货推送萌牙WMS
     * Author: ggh
     * Date: 2023/12/8
     * @param Request $request
     * @return \think\response\Json
     */
    public function SalesReturnPushWms(Request $request)
    {
        $params = $request->param();
        $params['operator_id'] = $request->header('vinehoo-uid');
        $params['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));
        //数据验证
        $validate = Validate::rule([
            'bill_no|单据编号'       => 'require|max:100',
            'operator_id|操作人ID' => 'require|number|>:0',
            'operator_name|操作人'    => 'require|max:100'
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }

        $SalesReturnService = new SalesReturnService();
        $result = $SalesReturnService->SalesReturnPushWms($params);
        if ($result !== true) {
            $this->throwError($result, ErrorCode::PARAM_ERROR);
        }
        
        return $this->success($result);
    }

    /**
     * Description:WMS更新销售退货状态
     * Author: ggh
     * Date: 2024/05/16
     * @param Request $request
     * @return \think\response\Json
     */
    public function updateStatusUp(Request $request)
    {
        $params = $request->param();
        //数据验证
        $validate = Validate::rule([
            'bill_no|单据编号'       => 'require',
            'wms_order_status|操作人ID' => 'require|number|>:0',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }

        $SalesReturnService = new SalesReturnService();
        $result = $SalesReturnService->updateStatusUp($params);
        if ($result !== true) {
            $this->throwError($result, ErrorCode::PARAM_ERROR);
        }

        return $this->success($result);

    }

    public function updateByInventory(Request $request)
    {
        $params = $request->param();
        if (!empty($params['return_courier_no'])) $params['return_courier_no'] = str_replace('，', ',', $params['return_courier_no']);
        //数据验证
        $validate = Validate::rule([
            'return_courier_no|退货快递单号' => 'require',
            'detail_json|清点详情'           => 'require',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }

        $SalesReturnService = new SalesReturnService();
        $result             = $SalesReturnService->updateByInventory($params);
        return $this->success($result);
    }

    public function revokeReturns(Request $request)
    {
        $params = $request->param();
        //数据验证
        $validate = Validate::rule([
            'bill_no|退货单号' => 'require',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }

        $SalesReturnService = new SalesReturnService();
        $result             = $SalesReturnService->revokeReturns($params);
        return $this->success($result);
    }

    public function priceSplit(Request $request)
    {
        $params = $request->param();
        //数据验证
        $validate = Validate::rule([
            'sub_order_no|退货单号' => 'require',
            'products|产品详情'     => 'array',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }

        $SalesReturnService = new SalesReturnService();
        $result             = $SalesReturnService->priceSplit($params);
        return $this->success($result);
    }

    public function checkWaybill(Request $request)
    {
        //检查运单
        $params = $request->param();

        $ret = ['tip_show' => false, 'tip_message' => ''];

        //数据验证
        $validate = Validate::rule([
            'sub_order_no|订单号'        => 'require',
            'return_courier_no|快递单号' => 'require',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }

        $order_nos = Db::name('sales_return')
            ->where('sub_order_no', '<>', $params['sub_order_no'])
            ->where('return_courier_no', $params['return_courier_no'])
            ->group('sub_order_no')
            ->column('sub_order_no');


        if (!empty($order_nos)) {
            $ret = ['tip_show' => true, 'tip_message' => implode(',', $order_nos) . "已使用{$params['return_courier_no']}运单号，请确认退货数量及来源。"];
        }
        return $this->success($ret);
    }


}