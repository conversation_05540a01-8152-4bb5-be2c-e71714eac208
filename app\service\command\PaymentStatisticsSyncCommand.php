<?php
declare (strict_types = 1);

namespace app\service\command;

use app\service\PaymentStatistics;

class PaymentStatisticsSyncCommand
{
    /**
     * 执行收款统计数据同步
     * @return bool
     */
    public function exec()
    {
        try {
            $service = new PaymentStatistics();
            $service->syncRedisToDatabase();
            return true;
        } catch (\Exception $e) {
            \think\facade\Log::error('收款统计数据同步失败: ' . $e->getMessage());
            return false;
        }
    }
}
