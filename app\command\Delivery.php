<?php
declare (strict_types = 1);

namespace app\command;

use app\service\Order;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;

class Delivery extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('delivery')
            ->setDescription('the delivery command');
    }

    protected function execute(Input $input, Output $output)
    {
        // 指令输出
        (new Order())->scheduledDelivery([]);
        $output->writeln('success');
    }
}
