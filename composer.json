{"name": "topthink/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "http://thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=7.1.0", "topthink/framework": "6.0.10", "topthink/think-orm": "2.0.47", "elasticsearch/elasticsearch": "7.14", "symfony/var-dumper": "4.4.36", "phpoffice/phpspreadsheet": "1.15.0", "aliyuncs/oss-sdk-php": "^2.6", "phpoffice/phpexcel": "^1.8", "phpmailer/phpmailer": "^6.9"}, "require-dev": {"topthink/think-trace": "1.4", "guzzlehttp/guzzle": "6.5.5"}, "autoload": {"psr-4": {"app\\": "app"}, "psr-0": {"": "extend/"}}, "config": {"platform-check": false, "preferred-install": "dist"}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}}