<?php


namespace app\service\command;


use app\service\elasticsearch\ElasticSearchService;
use app\service\Notify as NotifyService;
use think\facade\Db;

class DepositGrantCouponCompensateCommand
{
    /**
     * Description:定金订单发放优惠券补偿机制
     * Author: zrc
     * Date: 2023/8/28
     * Time: 9:54
     * @return bool
     */
    public function exec()
    {
        $depositRecord = Db::name('deposit_inflation_record')
            ->field('id,uid,sub_order_no,status')
            ->where(['status' => 1])
            ->order('id desc')
            ->limit(100)
            ->select()->toArray();
        if (!empty($depositRecord)) {
            $sub_order_no_arr  = array_column($depositRecord, 'sub_order_no');
            $es                = new ElasticSearchService();
            $terms[]           = ['sub_order_no.keyword' => $sub_order_no_arr];
            $arr               = array(
                'index'  => ['orders'],
                'terms'  => $terms,
                'source' => ['sub_order_no', 'main_order_no'],
                'limit'  => 100
            );
            $esData            = $es->getDocumentList($arr);
            $main_order_no_arr = array_column($esData['data'], 'main_order_no');
            foreach ($main_order_no_arr as &$val) {
                $queue_distribute_param          = Db::name('order_deal_log')->where(['main_order_no' => $val])->value('queue_distribute_param');
                $data['notify_deal_status->wms'] = 0;
                Db::name('order_deal_log')->where(['main_order_no' => $val])->json(['notify_deal_status', true])->update($data);
                $notifyService = new NotifyService();
                $notifyService->notifyDealPushWMS($queue_distribute_param);
                echo '补偿订单：' . $val . PHP_EOL;
            }
        }
        echo '执行完成' . PHP_EOL;
        exit;
    }
}