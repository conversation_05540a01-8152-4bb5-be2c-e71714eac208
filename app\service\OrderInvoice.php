<?php


namespace app\service;


use app\BaseService;
use app\service\elasticsearch\ElasticSearchService;
use app\service\es\Es;
use think\facade\Db;

class OrderInvoice extends BaseService
{
    const InvoiceGoodsCodesJiu = '1030305000000000000';
    const InvoiceGoodsCodesYL = '1030307020000000000';
    const InvoiceGoodsCodesSP = '1030299000000000000';
    const InvoiceGoodsCodesWL = '1050106020000000000';
    const InvoiceGoodsCodesXN = '3070401000000000000';
    const InvoiceGoodsCodesJJ = '1060508010000000000';
    const InvoiceGoodsCodesQT = '1030305000000000000';

    const InvoiceGoodsTaxAlcohol = 0.13;
    const InvoiceGoodsTaxFarmer = 0.13;
    const InvoiceGoodsTaxCook = 0.06;

    const InvoiceGoodsCodes = [
        1 => self::InvoiceGoodsCodesJiu,
        2 => self::InvoiceGoodsCodesYL,
        3 => self::InvoiceGoodsCodesSP,
        4 => self::InvoiceGoodsCodesWL,
        5 => self::InvoiceGoodsCodesXN,
        6 => self::InvoiceGoodsCodesJJ,
        7 => self::InvoiceGoodsCodesQT,
    ];

    const InvoiceGoodsCodesTax = [
        1 => self::InvoiceGoodsTaxAlcohol,
        2 => self::InvoiceGoodsTaxAlcohol,
        3 => self::InvoiceGoodsTaxFarmer,
        4 => self::InvoiceGoodsTaxAlcohol,
        5 => self::InvoiceGoodsTaxCook,
        6 => self::InvoiceGoodsTaxAlcohol,
        7 => self::InvoiceGoodsTaxAlcohol,
    ];

    /**
     * Description:添加/修改订单开票记录
     * Author: zrc
     * Date: 2023/4/7
     * Time: 17:19
     * @param $params
     * @return int|string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function addInvoiceRecord($params)
    {
        $invoiceRecord = Db::name('invoice_record')->where(['sub_order_no' => $params['sub_order_no']])->find();
        if (empty($invoiceRecord)) {//添加开票记录
            $es   = new ElasticSearchService();
            $arr  = array(
                'index'  => ['orders'],
                'match'  => [['sub_order_no.keyword' => $params['sub_order_no']]],
                'source' => ['uid', 'main_order_no', 'sub_order_no', 'order_type'],
                'limit'  => 1,
            );
            $data = $es->getDocumentList($arr);
            if (!isset($data['data'][0])) $this->throwError('未获取到订单信息');
            $orderInfo  = $data['data'][0];
            $insertData = array(
                'uid'              => $orderInfo['uid'],
                'main_order_no'    => $orderInfo['main_order_no'],
                'sub_order_no'     => $orderInfo['sub_order_no'],
                'order_type'       => $orderInfo['order_type'],
                'invoice_type'     => $params['invoice_type'],
                'type_id'          => $params['type_id'],
                'invoice_name'     => $params['invoice_name'],
                'taxpayer'         => isset($params['taxpayer']) ? $params['taxpayer'] : '',
                'telephone'        => isset($params['telephone']) ? $params['telephone'] : '',
                'email'            => $params['email'],
                'company_address'  => isset($params['company_address']) ? $params['company_address'] : '',
                'company_tel'      => isset($params['company_tel']) ? $params['company_tel'] : '',
                'opening_bank'     => isset($params['opening_bank']) ? $params['opening_bank'] : '',
                'bank_account'     => isset($params['bank_account']) ? $params['bank_account'] : '',
                'invoice_progress' => 1,
                'created_time'     => time(),
            );
            $result     = Db::name('invoice_record')->insertGetId($insertData);
            $order_type = config('config')['order_type'];//订单频道获取
            Db::name($order_type[intval($orderInfo['order_type'])]['table'])->where(['sub_order_no' => $orderInfo['sub_order_no']])->update(['invoice_progress' => 1, 'invoice_id' => $result, 'update_time' => time()]);
        } else {//修改开票记录
            $updateData = array(
                'invoice_type'    => $params['invoice_type'],
                'type_id'         => $params['type_id'],
                'invoice_name'    => $params['invoice_name'],
                'taxpayer'        => isset($params['taxpayer']) ? $params['taxpayer'] : '',
                'telephone'       => isset($params['telephone']) ? $params['telephone'] : '',
                'email'           => $params['email'],
                'company_address' => isset($params['company_address']) ? $params['company_address'] : '',
                'company_tel'     => isset($params['company_tel']) ? $params['company_tel'] : '',
                'opening_bank'    => isset($params['opening_bank']) ? $params['opening_bank'] : '',
                'bank_account'    => isset($params['bank_account']) ? $params['bank_account'] : '',
                'update_time'     => time(),
            );
            Db::name('invoice_record')->where(['id' => $invoiceRecord['id']])->update($updateData);
            $result = $invoiceRecord['id'];
        }
        return $result;
    }

    /**
     * Description:订单开票
     * Author: zrc
     * Date: 2023/4/11
     * Time: 17:49
     * @param $params
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function orderInvoice($params)
    {
        $es = new ElasticSearchService();
        //获取订单信息
        $sub_order_no_arr                 = array_values(array_column($params['order_info'], 'sub_order_no'));
        $orderEs                          = array(
            'index' => ['orders'],
            'terms' => [['sub_order_no.keyword' => $sub_order_no_arr]],
            'limit' => 10000
        );
        $orderData                        = $es->getDocumentList($orderEs);
        $merchant_second_order            = [];
        $merchant_second_order_no         = [];
        $merchant_second_order_uid        = 0;
        $merchant_second_order_invoice_id = 0;
        $productIdArr                     = [];
        $result                           = [];
        if (count($orderData['data']) > 0) {
            foreach ($orderData['data'] as $key => $order) {
                #region 兼容
                $orderData['data'][$key]['recharge_balance']        = $order['recharge_balance'] ?? 0;
                $orderData['data'][$key]['bonus_balance']           = $order['bonus_balance'] ?? 0;
                $orderData['data'][$key]['refund_recharge_balance'] = $order['refund_recharge_balance'] ?? 0;
                $orderData['data'][$key]['refund_bonus_balance']    = $order['refund_bonus_balance'] ?? 0;
                $orderData['data'][$key]['cash_amount']             = $order['cash_amount'] ?? ($order['payment_amount'] ?? 0);
                #endregion 兼容
            }
            $created_time = time();
            foreach ($orderData['data'] as $key => $order) {
                if (in_array($order['refund_status'], [1, 2])) $this->throwError($order['sub_order_no'] . '订单已售后，不能开票');
                if ($order['invoice_progress'] == 2) $this->throwError($order['sub_order_no'] . '订单已开票，请勿重复操作'); // todo 取消注释
                if ($order['order_type'] == 9) {
                    $merchantInfo = curlRequest(env('ITEM.VMALL_URL') . '/vmall/v3/merchant/detail', ['id' => $order['merchant_id']], [], 'GET');
                    //商家秒发(非直营商家，产品渠道为酒云采购的由商家自己开票)
                    if (isset($merchantInfo['data']['is_self_support']) && $merchantInfo['data']['is_self_support'] == 0 && $order['product_channel'] == 3) {
                        $merchant_second_order[]          = [
                            'order_sn'    => $order['sub_order_no'],
                            'amount'      => $order['cash_amount'] - $order['refund_money'],
                            'finish_time' => $order['goods_receipt_time'],
                            'mid'         => $order['merchant_id']
                        ];
                        $merchant_second_order_uid        = $order['uid'];
                        $merchant_second_order_invoice_id = $order['invoice_id'];
                        $merchant_second_order_no[]       = $order['sub_order_no'];
                        unset($orderData['data'][$key]);
                        continue;
                    }
                }
                //写入开票记录
                if ($order['invoice_progress'] == 0) {
                    $invoiceId  = Db::name('invoice_record')->where(['sub_order_no' => $order['sub_order_no']])->value('id');
                    $insertData = array(
                        'uid'              => $order['uid'],
                        'main_order_no'    => $order['main_order_no'],
                        'sub_order_no'     => $order['sub_order_no'],
                        'order_type'       => $order['order_type'],
                        'invoice_type'     => $params['invoice_type'],
                        'type_id'          => $params['type_id'],
                        'invoice_name'     => $params['invoice_name'],
                        'taxpayer'         => isset($params['taxpayer']) ? str_replace(" ", "", $params['taxpayer']) : '',
                        'telephone'        => isset($params['telephone']) ? $params['telephone'] : '',
                        'email'            => $params['email'],
                        'company_address'  => isset($params['company_address']) ? $params['company_address'] : '',
                        'company_tel'      => isset($params['company_tel']) ? $params['company_tel'] : '',
                        'opening_bank'     => isset($params['opening_bank']) ? $params['opening_bank'] : '',
                        'bank_account'     => isset($params['bank_account']) ? $params['bank_account'] : '',
                        'remark'           => $params['remark'] ?? '',
                        'invoice_progress' => 1,
                        'created_time'     => $created_time,
                    );
                    if ($invoiceId > 0) {
                        Db::name('invoice_record')->where(['id' => $invoiceId])->update($insertData);
                    } else {
                        Db::name('invoice_record')->insert($insertData);
                    }
                }
                //获取产品成本信息
                $costpriceInfo = Db::table("vh_commodities.vh_periods_product_inventory")->field('product_id,costprice')->where([['period', '=', $order['period']], ['costprice', '>', 0]])->select()->toArray();
                if (empty($costpriceInfo)) {
                    $queueData = array(
                        'access_token' => 'b3670fb7-a6aa-4353-b3fa-71da6d4e9765',
                        'type'         => 'text',
                        'at'           => '***********',
                        'content'      => base64_encode('订单号：' . $order['sub_order_no'] . '，期数：' . $order['period'] . ',产品成本为0开票失败，请手动处理'),
                    );
                    $data      = base64_encode(json_encode($queueData));
                    $pushData  = array(
                        'exchange_name' => 'dingtalk',
                        'routing_key'   => 'dingtalk_sender',
                        'data'          => $data,
                    );
                    httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
                    $this->throwError($order['sub_order_no'] . '订单信息异常，开票失败，请联系客服手动处理');
                }
                $productIdArr[]                            = array_values(array_unique(array_column($costpriceInfo, 'product_id')));
                $orderData['data'][$key]['costprice_info'] = $costpriceInfo;
            }
            //商家秒发开票
            if (!empty($merchant_second_order)) {
                $merchant_receipt = [
                    'uid'              => $merchant_second_order_uid,
                    'invoice_type_id'  => $params['invoice_type'],
                    'invoice_title_id' => $merchant_second_order_invoice_id,
                    'type_id'          => $params['type_id'],
                    'invoice_name'     => $params['invoice_name'] ?? '',
                    'taxpayer'         => str_replace(" ", "", $params['taxpayer']) ?? '',
                    'email'            => $params['email'] ?? '',
                    'orders'           => $merchant_second_order,
                    'company_address'  => isset($params['company_address']) ? $params['company_address'] : '',
                    'company_tel'      => isset($params['company_tel']) ? $params['company_tel'] : '',
                    'opening_bank'     => isset($params['opening_bank']) ? $params['opening_bank'] : '',
                    'bank_account'     => isset($params['bank_account']) ? $params['bank_account'] : '',
                    'consignee'        => isset($params['consignee']) ? $params['consignee'] : '',
                    'province_id'      => isset($params['province_id']) ? $params['province_id'] : '',
                    'city_id'          => isset($params['city_id']) ? $params['city_id'] : '',
                    'town_id'          => isset($params['town_id']) ? $params['town_id'] : '',
                    'province_name'    => isset($params['province_name']) ? $params['province_name'] : '',
                    'city_name'        => isset($params['city_name']) ? $params['city_name'] : '',
                    'town_name'        => isset($params['town_name']) ? $params['town_name'] : '',
                    'address'          => isset($params['address']) ? $params['address'] : '',
                ];
                $makeOutInvoice   = curlRequest(env('ITEM.VMALL_URL') . '/vmall/v3/invoice/invoice/creates', $merchant_receipt, []);
                if (!isset($makeOutInvoice['error_code']) || $makeOutInvoice['error_code'] != 0) {
                    $this->throwError('增加商家的待开票记录失败！' . isset($makeOutInvoice['error_msg']) ? $makeOutInvoice['error_msg'] : '商家秒发模块请求异常');
                }
                $backData = array(
                    'order_nos' => $merchant_second_order_no,
                    'status'    => 1
                );
                $this->orderInvoiceCallBack($backData);
                $result['data'] = true;
            }
        }
        if (count($orderData['data']) > 0) {
            //获取期数信息
            $periodId   = array_values(array_unique(array_column($orderData['data'], 'period')));
            $periodEs   = array(
                'index'  => ['periods'],
                'terms'  => [['id' => $periodId]],
                'source' => ['id', 'payee_merchant_id'],
                'limit'  => 10000
            );
            $periodData = $es->getDocumentList($periodEs);
            $periodArr  = [];
            foreach ($periodData['data'] as &$v) {
                $periodArr[$v['id']]['payee_merchant_id'] = isset($v['payee_merchant_id']) ? $v['payee_merchant_id'] : 2;
            }
            //获取套餐信息
            $packageId   = array_values(array_unique(array_column($orderData['data'], 'package_id')));
            $packageEs   = array(
                'index'  => ['periods_set'],
                'terms'  => [['id' => $packageId]],
                'source' => ['id', 'associated_products'],
                'limit'  => 10000
            );
            $packageData = $es->getDocumentList($packageEs);
            $packageArr  = [];
            foreach ($packageData['data'] as &$vv) {
                $associated_products                          = json_decode($vv['associated_products'], true);
                $packageArr[$vv['id']]['associated_products'] = $associated_products;
            }
            //获取磐石产品信息
            $productId   = array_values(array_unique(call_user_func_array('array_merge', $productIdArr)));
            $productEs   = array(
                'index'  => ['panshi.products'],
                'terms'  => [['id' => $productId]],
                'source' => ['id', 'short_code', 'cn_product_name', 'product_category', 'product_unit_name', 'tax_rate', 'tax_code', 'invoice_name', 'capacity', 'is_taxfree', 'invoice_value'],
                'limit'  => 10000
            );
            $productData = $es->getDocumentList($productEs);
            $productArr  = [];
            foreach ($productData['data'] as &$vvv) {
                $vvv['is_taxfree_txt'] = in_array(($vvv['is_taxfree'] ?? '0'), ['1', '03']) ? "03" : "0";
                $vvv['invoice_value'] = floatval($vvv['invoice_value'] ?? 0.00);
                $productArr[$vvv['id']] = $vvv;
            }
            //订单产品信息组装
            $orders = [];
            foreach ($orderData['data'] as &$val) {
                //产品含税单价计算
                $totalcostprice = 0;
                foreach ($val['costprice_info'] as $kk => $value) {
                    foreach ($packageArr[$val['package_id']]['associated_products'] as &$as) {
                        if ($as['product_id'] == $value['product_id']) $val['costprice_info'][$kk]['nums'] = $as['nums'];
                    }
                    if (isset($val['costprice_info'][$kk]['nums'])) {
                        //单个产品总成本
                        $val['costprice_info'][$kk]['totalcostprice'] = $value['costprice'] * $val['costprice_info'][$kk]['nums'] * $val['order_qty'];
                        //套餐总成本
                        $totalcostprice += $val['costprice_info'][$kk]['totalcostprice'];
                    } else {
                        unset($val['costprice_info'][$kk]);
                    }
                }
                if (count($val['costprice_info']) <= 0) {
                    $queueData = array(
                        'access_token' => 'b3670fb7-a6aa-4353-b3fa-71da6d4e9765',
                        'type'         => 'text',
                        'at'           => '***********',
                        'content'      => base64_encode('订单号：' . $val['sub_order_no'] . '，期数：' . $val['period'] . ',产品成本为0开票失败，请手动处理'),
                    );
                    $data      = base64_encode(json_encode($queueData));
                    $pushData  = array(
                        'exchange_name' => 'dingtalk',
                        'routing_key'   => 'dingtalk_sender',
                        'data'          => $data,
                    );
                    httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
                    $this->throwError($val['sub_order_no'] . '订单信息异常，开票失败，请联系客服处理');
                }
                //产品信息组装
                $products     = [];
                $invoiceMoney = $val['cash_amount'] - $val['refund_money'];
                $is_support_invoicing = 1;
                $period_es = Es::name(Es::PERIODS)->where([['id', '==', $val['period']]])->field('id,is_support_invoicing,periods_type')->find();
                if (isset($period_es['is_support_invoicing'])) {
                    $is_support_invoicing = $period_es['is_support_invoicing'];
                } else {
                    if (in_array($period_es['periods_type'], [0, 1, 2, 3])) {
                        $table                = [0 => 'vh_periods_flash', 1 => 'vh_periods_second', 2 => 'vh_periods_cross', 3 => 'vh_periods_leftover'];
                        $is_support_invoicing = Db::table('vh_commodities.' . $table[$period_es['periods_type']])->where('id', $period_es['id'])->value('is_support_invoicing');
                    }
                }
                if ($invoiceMoney <= 0) $this->throwError($val['sub_order_no'] . '开票失败，订单已全额退款，无可开票金额');
                $totalMoney = $val['cash_amount'] - $val['refund_money'];
                $totalZk = $invoiceZk = max(0, (($val['bonus_balance'] + $val['recharge_balance']) - ($val['refund_bonus_balance'] + $val['refund_recharge_balance'])));
                $totalMoney +=$totalZk;
                $invoiceMoney +=$invoiceZk;
                $sort_price = array_column($val['costprice_info'], 'costprice');
                array_multisort($sort_price, SORT_ASC, $val['costprice_info']);
                foreach ($val['costprice_info'] as $ka => $va) {
                    $difference = 0;
                    $nums       = 0;
                    $nums       = $va['nums'] * $val['order_qty'];

                    $zk_difference = 0;
                    $zk_nums       = $nums;
                    //科技公司订单不拆分产品数量来保证含税单价必须保留2位小数
                    if (isset($periodArr[$val['period']]['payee_merchant_id']) && $periodArr[$val['period']]['payee_merchant_id'] == 2 && strtotime($val['created_time']) > '1664467200') {
                        if (count($val['costprice_info']) == 1) {
                            $price = $totalMoney / $nums;
                            $zk_price = $totalZk / $nums;
                        } else {
                            $price = bcdiv($va['totalcostprice'] / $totalcostprice * $invoiceMoney, $nums, 2) + 0;
                            $zk_price = bcdiv($va['totalcostprice'] / $totalcostprice * $invoiceZk, $nums, 2) + 0;
                            if (count($val['costprice_info']) > $ka + 1) {
                                if ($price == 0) {
                                    $price      = 0.01;
                                    $totalMoney -= 0.01 * $nums;
                                } else {
                                    $totalMoney -= ($price * $nums);
                                    $totalMoney = strval($totalMoney) + 0;
                                }
                                if ($zk_price == 0 && $invoiceZk > 0) {
                                    $zk_price      = 0.01;
                                    $totalZk -= 0.01 * $nums;
                                } else {
                                    $totalZk -= ($zk_price * $nums);
                                    $totalZk = strval($totalZk) + 0;
                                }
                            } else {
                                $price = $totalMoney / $nums;
                                $zk_price = $totalZk / $nums;
                            }
                        }
                    } else {
                        if (count($val['costprice_info']) == 1) {
                            $price = bcdiv($totalMoney, $nums, 2) + 0;
                            if (strval($totalMoney / $nums) != strval($price) && $nums > 1) {
                                $price      = bcdiv($totalMoney, $nums, 2) + 0;
                                $difference = bcsub($totalMoney, $price * ($nums - 1), 2) + 0;
                                $nums       = $nums - 1;
                            }

                            $zk_price = bcdiv($totalZk, $zk_nums, 2) + 0;
                            if (strval($totalZk / $zk_nums) != strval($zk_price) && $zk_nums > 1) {
                                $zk_price   = bcdiv($totalZk, $zk_nums, 2) + 0;
                                $difference = bcsub($totalZk, $zk_price * ($zk_nums - 1), 2) + 0;
                                $zk_nums    = $zk_nums - 1;
                            }
                        } else {
                            $price = intval($va['totalcostprice'] / $totalcostprice * $invoiceMoney / $nums);
                            if (count($val['costprice_info']) > $ka + 1) {
                                if ($price == 0) {
                                    $price      = 0.01;
                                    $totalMoney -= 0.01 * $nums;
                                } else {
                                    $totalMoney -= $price * $nums;
                                    $totalMoney = strval($totalMoney) + 0;
                                }
                            } else {
                                $price = bcdiv($totalMoney, $nums, 2) + 0;
                                if (strval($totalMoney / $nums) != strval($price) && $nums > 1) {
                                    $price      = bcdiv($totalMoney, $nums, 2) + 0;
                                    $difference = bcsub($totalMoney, $price * ($nums - 1), 2) + 0;
                                    $nums       = $nums - 1;
                                }
                            }

                            $zk_price = intval($va['totalcostprice'] / $totalcostprice * $invoiceZk / $zk_nums);
                            if (count($val['costprice_info']) > $ka + 1) {
                                if ($zk_price == 0 && $invoiceZk > 0) {
                                    $zk_price      = 0.01;
                                    $totalZk -= 0.01 * $zk_nums;
                                } else {
                                    $totalZk -= $zk_price * $zk_nums;
                                    $totalZk = strval($totalZk) + 0;
                                }
                            } else {
                                $zk_price = bcdiv($totalZk, $zk_nums, 2) + 0;
                                if (strval($totalZk / $zk_nums) != strval($zk_price) && $zk_nums > 1) {
                                    $zk_price      = bcdiv($totalZk, $zk_nums, 2) + 0;
                                    $zk_difference = bcsub($totalZk, $zk_price * ($zk_nums - 1), 2) + 0;
                                    $zk_nums       = $zk_nums - 1;
                                }
                            }
                        }
                    }
                    if ($totalZk <= 0) {
                        $zk_price = 0;
                    }
                    //税率、税收编码处理
                    if (isset($productArr[$va['product_id']]['tax_rate']) && $productArr[$va['product_id']]['tax_rate'] > 0 && isset($productArr[$va['product_id']]['tax_code']) && !empty($productArr[$va['product_id']]['tax_code'])) {
                        if (strpos($productArr[$va['product_id']]['tax_code'], '-') !== false) {
                            $productArr[$va['product_id']]['tax_code'] = substr($productArr[$va['product_id']]['tax_code'], 0, strrpos($productArr[$va['product_id']]['tax_code'], '-'));
                        }
                        $tax                = floatval($productArr[$va['product_id']]['tax_rate']);
                        $invoice_goods_code = $productArr[$va['product_id']]['tax_code'];
                    } else {
                        $tax                = self::InvoiceGoodsCodesTax[isset($productArr[$va['product_id']]['product_category']) ? $productArr[$va['product_id']]['product_category'] : 1];
                        $invoice_goods_code = self::InvoiceGoodsCodes[isset($productArr[$va['product_id']]['product_category']) ? $productArr[$va['product_id']]['product_category'] : 1];
                    }
                    $spec_type = $productArr[$va['product_id']]['capacity'] ?? '';
                    //$tax                = self::InvoiceGoodsCodesTax[isset($productArr[$va['product_id']]['product_category']) ? $productArr[$va['product_id']]['product_category'] : 1];
                    //$invoice_goods_code = self::InvoiceGoodsCodes[isset($productArr[$va['product_id']]['product_category']) ? $productArr[$va['product_id']]['product_category'] : 1];
                    if (($productArr[$va['product_id']]['invoice_value'] <= $price)) {
                        if ($zk_price <= 0) {
                            //正常行
                            $products[] = array(
                                'code'                 => isset($productArr[$va['product_id']]['short_code']) ? $productArr[$va['product_id']]['short_code'] : '',
                                'name'                 => isset($productArr[$va['product_id']]['invoice_name']) ? $productArr[$va['product_id']]['invoice_name'] : $productArr[$va['product_id']]['cn_product_name'],
                                'price'                => floatval(strval(isset($periodArr[$val['period']]['payee_merchant_id']) && $periodArr[$val['period']]['payee_merchant_id'] == 2 ? $price : strval($price) + 0)),
                                'num'                  => $nums,
                                'unit'                 => isset($productArr[$va['product_id']]['product_unit_name']) ? $productArr[$va['product_id']]['product_unit_name'] : '',
                                'tax'                  => ($tax > 0 && ($productArr[$va['product_id']]['is_taxfree_txt'] ?? '0') == '03') ? 0 : $tax,
                                'favoured_policy_flag' => $productArr[$va['product_id']]['is_taxfree_txt'] ?? '0',
                                'spec_type'            => strval($spec_type),
                                'invoice_goods_code'   => $invoice_goods_code,
                            );
                        } else {
                            //被折扣行
                            $products[] = array(
                                'code'                 => isset($productArr[$va['product_id']]['short_code']) ? $productArr[$va['product_id']]['short_code'] : '',
                                'name'                 => isset($productArr[$va['product_id']]['invoice_name']) ? $productArr[$va['product_id']]['invoice_name'] : $productArr[$va['product_id']]['cn_product_name'],
                                'price'                => floatval(strval($price)),
                                'num'                  => $nums,
                                'unit'                 => isset($productArr[$va['product_id']]['product_unit_name']) ? $productArr[$va['product_id']]['product_unit_name'] : '',
                                'tax'                  => ($tax > 0 && ($productArr[$va['product_id']]['is_taxfree_txt'] ?? '0') == '03') ? 0 : $tax,
                                'favoured_policy_flag' => $productArr[$va['product_id']]['is_taxfree_txt'] ?? '0',
                                'spec_type'            => strval($spec_type),
                                'invoice_goods_code'   => $invoice_goods_code,
                                'property'             => 2,//发票行性质（0正常行(默认),1折扣行,2被折扣行
                            );
                            //折扣行
                            $products[] = array(
                                'code'                 => isset($productArr[$va['product_id']]['short_code']) ? $productArr[$va['product_id']]['short_code'] : '',
                                'name'                 => isset($productArr[$va['product_id']]['invoice_name']) ? $productArr[$va['product_id']]['invoice_name'] : $productArr[$va['product_id']]['cn_product_name'],
                                'price'                => floatval(strval(-$zk_price)),
                                'num'                  => $zk_nums,
                                'unit'                 => isset($productArr[$va['product_id']]['product_unit_name']) ? $productArr[$va['product_id']]['product_unit_name'] : '',
                                'tax'                  => ($tax > 0 && ($productArr[$va['product_id']]['is_taxfree_txt'] ?? '0') == '03') ? 0 : $tax,
                                'favoured_policy_flag' => $productArr[$va['product_id']]['is_taxfree_txt'] ?? '0',
                                'spec_type'            => strval($spec_type),
                                'invoice_goods_code'   => $invoice_goods_code,
                                'property'             => 1,//发票行性质（0正常行(默认),1折扣行,2被折扣行
                            );
                        }
                    } else {
                        //被折扣行
                        $products[] = array(
                            'code'                 => isset($productArr[$va['product_id']]['short_code']) ? $productArr[$va['product_id']]['short_code'] : '',
                            'name'                 => isset($productArr[$va['product_id']]['invoice_name']) ? $productArr[$va['product_id']]['invoice_name'] : $productArr[$va['product_id']]['cn_product_name'],
                            'price'                => floatval(strval(($productArr[$va['product_id']]['invoice_value']))),
                            'num'                  => $nums,
                            'unit'                 => isset($productArr[$va['product_id']]['product_unit_name']) ? $productArr[$va['product_id']]['product_unit_name'] : '',
                            'tax'                  => ($tax > 0 && ($productArr[$va['product_id']]['is_taxfree_txt'] ?? '0') == '03') ? 0 : $tax,
                            'favoured_policy_flag' => $productArr[$va['product_id']]['is_taxfree_txt'] ?? '0',
                            'spec_type'            => strval($spec_type),
                            'invoice_goods_code'   => $invoice_goods_code,
                            'property'             => 2,//发票行性质（0正常行(默认),1折扣行,2被折扣行
                        );

                        if (floatval(bcsub($productArr[$va['product_id']]['invoice_value'], $price, 2)) <= 0) {
                            $this->throwError($val['sub_order_no'] . '开票失败！' . "折扣行金额必须大于0");
                        }
                        //折扣行
                        $products[] = array(
                            'code'                 => isset($productArr[$va['product_id']]['short_code']) ? $productArr[$va['product_id']]['short_code'] : '',
                            'name'                 => isset($productArr[$va['product_id']]['invoice_name']) ? $productArr[$va['product_id']]['invoice_name'] : $productArr[$va['product_id']]['cn_product_name'],
                            'price'                => floatval(strval($price- $productArr[$va['product_id']]['invoice_value'] - $zk_price)),
                            'num'                  => $nums,
                            'unit'                 => isset($productArr[$va['product_id']]['product_unit_name']) ? $productArr[$va['product_id']]['product_unit_name'] : '',
                            'tax'                  => ($tax > 0 && ($productArr[$va['product_id']]['is_taxfree_txt'] ?? '0') == '03') ? 0 : $tax,
                            'favoured_policy_flag' => $productArr[$va['product_id']]['is_taxfree_txt'] ?? '0',
                            'spec_type'            => strval($spec_type),
                            'invoice_goods_code'   => $invoice_goods_code,
                            'property'             => 1,//发票行性质（0正常行(默认),1折扣行,2被折扣行
                        );
                    }

                    if ($difference > 0 || $zk_difference > 0) {
                        if($zk_difference > 0){
                            //被折扣行
                            $products[] = array(
                                'code'                 => isset($productArr[$va['product_id']]['short_code']) ? $productArr[$va['product_id']]['short_code'] : '',
                                'name'                 => isset($productArr[$va['product_id']]['invoice_name']) ? $productArr[$va['product_id']]['invoice_name'] : $productArr[$va['product_id']]['cn_product_name'],
                                'price'                => floatval(strval($difference+$zk_difference)),
                                'num'                  => 1,
                                'unit'                 => isset($productArr[$va['product_id']]['product_unit_name']) ? $productArr[$va['product_id']]['product_unit_name'] : '',
                                'tax'                  => ($tax > 0 && ($productArr[$va['product_id']]['is_taxfree_txt'] ?? '0') == '03') ? 0 : $tax,
                                'favoured_policy_flag' => $productArr[$va['product_id']]['is_taxfree_txt'] ?? '0',
                                'spec_type'            => strval($spec_type),
                                'invoice_goods_code'   => $invoice_goods_code,
                                'property'             => 2,//发票行性质（0正常行(默认),1折扣行,2被折扣行
                            );
                            //折扣行
                            $products[] = array(
                                'code'               => isset($productArr[$va['product_id']]['short_code']) ? $productArr[$va['product_id']]['short_code'] : '',
                                'name'               => isset($productArr[$va['product_id']]['invoice_name']) ? $productArr[$va['product_id']]['invoice_name'] : $productArr[$va['product_id']]['cn_product_name'],
                                'price'                => floatval(strval($zk_difference)),
                                'num'                => 1,
                                'unit'               => isset($productArr[$va['product_id']]['product_unit_name']) ? $productArr[$va['product_id']]['product_unit_name'] : '',
                                'tax' => ($tax > 0 && ($productArr[$va['product_id']]['is_taxfree_txt'] ?? '0') == '03') ? 0 : $tax,
                                'favoured_policy_flag' => $productArr[$va['product_id']]['is_taxfree_txt'] ?? '0',
                                'spec_type'          => strval($spec_type),
                                'invoice_goods_code' => $invoice_goods_code,
                                'property'             => 1,//发票行性质（0正常行(默认),1折扣行,2被折扣行
                            );
                        } else {
                            $products[] = array(
                                'code'                 => isset($productArr[$va['product_id']]['short_code']) ? $productArr[$va['product_id']]['short_code'] : '',
                                'name'                 => isset($productArr[$va['product_id']]['invoice_name']) ? $productArr[$va['product_id']]['invoice_name'] : $productArr[$va['product_id']]['cn_product_name'],
                                'price'                => floatval(strval(strval($difference) + 0)),
                                'num'                  => 1,
                                'unit'                 => isset($productArr[$va['product_id']]['product_unit_name']) ? $productArr[$va['product_id']]['product_unit_name'] : '',
                                'tax'                  => ($tax > 0 && ($productArr[$va['product_id']]['is_taxfree_txt'] ?? '0') == '03') ? 0 : $tax,
                                'favoured_policy_flag' => $productArr[$va['product_id']]['is_taxfree_txt'] ?? '0',
                                'spec_type'            => strval($spec_type),
                                'invoice_goods_code'   => $invoice_goods_code,
                            );
                        }
                    }
                }
                $affiliation = isset($periodArr[$val['period']]['payee_merchant_id']) ? $periodArr[$val['period']]['payee_merchant_id'] : 2;
                //2022-9-30号之前默认云酒公司
                if (strtotime($val['created_time']) <= '1664467200') {
                    $affiliation = 1;
                }
                $orders[] = array(
                    'order_no'    => $val['sub_order_no'],
                    'affiliation' => $affiliation,
                    'is_support_invoicing' => $is_support_invoicing,
                    'products'    => $products
                );
            }
            //开票信息组装
            $data   = array(
                'uid'      => intval($params['uid']),
                'genre_id' => 2,
                'remarks' => $params['remark'] ?? '',
                'genre'    => $params['invoice_type'],
                'receipt'  => array(
                    'genre'           => intval($params['type_id']),
                    'name'            => $params['invoice_name'],
                    'tel'             => isset($params['telephone']) ? $params['telephone'] : '',
                    'email'           => isset($params['email']) ? $params['email'] : '',
                    'taxpayer'        => isset($params['taxpayer']) ? str_replace(" ", "", $params['taxpayer']) : '',
                    'company_address' => isset($params['company_address']) ? $params['company_address'] : '',
                    'company_tel'     => isset($params['company_tel']) ? $params['company_tel'] : '',
                    'opening_bank'    => isset($params['opening_bank']) ? $params['opening_bank'] : '',
                    'bank_account'    => isset($params['bank_account']) ? $params['bank_account'] : '',
                ),
                'orders'   => $orders
            );
            $result = curlRequest(env('ITEM.GO_INVOICE_URL') . '/go-invoice/v3/create/createD', json_encode($data), [], 'POST');
            if (!isset($result['error_code'])) $this->throwError('发票模块请求异常');
            $sub_order_no = implode(',', array_column($orders, 'order_no'));
            if ($result['error_code'] != 0) {
                $nmsg = '订单号：' . $sub_order_no . '，开票失败！错误信息：' . $result['error_msg'];
                $at = '***********';
                if (strpos($nmsg, '商编') !== false || strpos($nmsg, '商品') !== false) {
                    $at = '***********';
                }
                if (isset($is_support_invoicing) && $is_support_invoicing == 0) {
                    $at = '***********';
                }
                if(strpos($nmsg,'开票失败，请联系客服。') !== false){
                    $at = '***********';
                }
                $queueData = array(
                    'access_token' => 'b3670fb7-a6aa-4353-b3fa-71da6d4e9765',
                    'type'         => 'text',
                    'at'           => $at,
                    'content'      => base64_encode($nmsg)
                );
                $data      = base64_encode(json_encode($queueData));
                $pushData  = array(
                    'exchange_name' => 'dingtalk',
                    'routing_key'   => 'dingtalk_sender',
                    'data'          => $data,
                );
                httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
                $backData = array(
                    'order_nos'    => array_column($orders, 'order_no'),
                    'status'       => 3,
                    'msg'          => $result['error_msg'],
                    'invoice_code' => '无',
                );
                $this->orderInvoiceCallBack($backData);
                $this->throwError($sub_order_no . '开票失败！' . $result['error_msg']);
            }
            //开票同步结果status=1和2和3的进行回调处理开票状态
            foreach ($result['data']['invoice_by_order'] as &$values) {
                if (isset($values['invoices'][0]['status']) && in_array($values['invoices'][0]['status'], [1, 2, 3])) {
                    $backData = array(
                        'order_nos'    => [$values['order_no']],
                        'status'       => $values['invoices'][0]['status'],
                        'msg'          => $values['invoices'][0]['msg'],
                        'invoice_code' => isset($values['invoices'][0]['invoice_code']) ? $values['invoices'][0]['invoice_code'] : '无',
                    );
                    $this->orderInvoiceCallBack($backData);
                }
            }
        }
        return $result['data'];
    }

    public function orderInvoiceBAK($params)
    {
        $es = new ElasticSearchService();
        //获取订单信息
        $sub_order_no_arr                 = array_values(array_column($params['order_info'], 'sub_order_no'));
        $orderEs                          = array(
            'index' => ['orders'],
            'terms' => [['sub_order_no.keyword' => $sub_order_no_arr]],
            'limit' => 10000
        );
        $orderData                        = $es->getDocumentList($orderEs);
        $merchant_second_order            = [];
        $merchant_second_order_no         = [];
        $merchant_second_order_uid        = 0;
        $merchant_second_order_invoice_id = 0;
        $productIdArr                     = [];
        $result                           = [];
        if (count($orderData['data']) > 0) {
            $created_time = time();
            foreach ($orderData['data'] as $key => $order) {
                if (in_array($order['refund_status'], [1, 2])) $this->throwError($order['sub_order_no'] . '订单已售后，不能开票');
//                if ($order['invoice_progress'] == 2) $this->throwError($order['sub_order_no'] . '订单已开票，请勿重复操作');
                if ($order['order_type'] == 9) {
                    $merchantInfo = curlRequest(env('ITEM.VMALL_URL') . '/vmall/v3/merchant/detail', ['id' => $order['merchant_id']], [], 'GET');
                    //商家秒发(非直营商家，产品渠道为酒云采购的由商家自己开票)
                    if (isset($merchantInfo['data']['is_self_support']) && $merchantInfo['data']['is_self_support'] == 0 && $order['product_channel'] == 3) {
                        $merchant_second_order[]          = [
                            'order_sn'    => $order['sub_order_no'],
                            'amount'      => $order['cash_amount'] - $order['refund_money'],
                            'finish_time' => $order['goods_receipt_time'],
                            'mid'         => $order['merchant_id']
                        ];
                        $merchant_second_order_uid        = $order['uid'];
                        $merchant_second_order_invoice_id = $order['invoice_id'];
                        $merchant_second_order_no[]       = $order['sub_order_no'];
                        unset($orderData['data'][$key]);
                        continue;
                    }
                }
                //写入开票记录
                if ($order['invoice_progress'] == 0) {
                    $invoiceId  = Db::name('invoice_record')->where(['sub_order_no' => $order['sub_order_no']])->value('id');
                    $insertData = array(
                        'uid'              => $order['uid'],
                        'main_order_no'    => $order['main_order_no'],
                        'sub_order_no'     => $order['sub_order_no'],
                        'order_type'       => $order['order_type'],
                        'invoice_type'     => $params['invoice_type'],
                        'type_id'          => $params['type_id'],
                        'invoice_name'     => $params['invoice_name'],
                        'taxpayer'         => isset($params['taxpayer']) ? str_replace(" ", "", $params['taxpayer']) : '',
                        'telephone'        => isset($params['telephone']) ? $params['telephone'] : '',
                        'email'            => $params['email'],
                        'company_address'  => isset($params['company_address']) ? $params['company_address'] : '',
                        'company_tel'      => isset($params['company_tel']) ? $params['company_tel'] : '',
                        'opening_bank'     => isset($params['opening_bank']) ? $params['opening_bank'] : '',
                        'bank_account'     => isset($params['bank_account']) ? $params['bank_account'] : '',
                        'remark'           => $params['remark'] ?? '',
                        'invoice_progress' => 1,
                        'created_time'     => $created_time,
                    );
                    if ($invoiceId > 0) {
                        Db::name('invoice_record')->where(['id' => $invoiceId])->update($insertData);
                    } else {
                        Db::name('invoice_record')->insert($insertData);
                    }
                }
                //获取产品成本信息
                $costpriceInfo = Db::table("vh_commodities.vh_periods_product_inventory")->field('product_id,costprice')->where([['period', '=', $order['period']], ['costprice', '>', 0]])->select()->toArray();
                if (empty($costpriceInfo)) {
                    $queueData = array(
                        'access_token' => 'b3670fb7-a6aa-4353-b3fa-71da6d4e9765',
                        'type'         => 'text',
                        'at'           => '***********',
                        'content'      => base64_encode('订单号：' . $order['sub_order_no'] . '，期数：' . $order['period'] . ',产品成本为0开票失败，请手动处理'),
                    );
                    $data      = base64_encode(json_encode($queueData));
                    $pushData  = array(
                        'exchange_name' => 'dingtalk',
                        'routing_key'   => 'dingtalk_sender',
                        'data'          => $data,
                    );
                    httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
                    $this->throwError($order['sub_order_no'] . '订单信息异常，开票失败，请联系客服手动处理');
                }
                $productIdArr[]                            = array_values(array_unique(array_column($costpriceInfo, 'product_id')));
                $orderData['data'][$key]['costprice_info'] = $costpriceInfo;
            }
            //商家秒发开票
            if (!empty($merchant_second_order)) {
                $merchant_receipt = [
                    'uid'              => $merchant_second_order_uid,
                    'invoice_type_id'  => $params['invoice_type'],
                    'invoice_title_id' => $merchant_second_order_invoice_id,
                    'type_id'          => $params['type_id'],
                    'invoice_name'     => $params['invoice_name'] ?? '',
                    'taxpayer'         => str_replace(" ", "", $params['taxpayer']) ?? '',
                    'email'            => $params['email'] ?? '',
                    'orders'           => $merchant_second_order,
                    'company_address'  => isset($params['company_address']) ? $params['company_address'] : '',
                    'company_tel'      => isset($params['company_tel']) ? $params['company_tel'] : '',
                    'opening_bank'     => isset($params['opening_bank']) ? $params['opening_bank'] : '',
                    'bank_account'     => isset($params['bank_account']) ? $params['bank_account'] : '',
                    'consignee'        => isset($params['consignee']) ? $params['consignee'] : '',
                    'province_id'      => isset($params['province_id']) ? $params['province_id'] : '',
                    'city_id'          => isset($params['city_id']) ? $params['city_id'] : '',
                    'town_id'          => isset($params['town_id']) ? $params['town_id'] : '',
                    'province_name'    => isset($params['province_name']) ? $params['province_name'] : '',
                    'city_name'        => isset($params['city_name']) ? $params['city_name'] : '',
                    'town_name'        => isset($params['town_name']) ? $params['town_name'] : '',
                    'address'          => isset($params['address']) ? $params['address'] : '',
                ];
                $makeOutInvoice   = curlRequest(env('ITEM.VMALL_URL') . '/vmall/v3/invoice/invoice/creates', $merchant_receipt, []);
                if (!isset($makeOutInvoice['error_code']) || $makeOutInvoice['error_code'] != 0) {
                    $this->throwError('增加商家的待开票记录失败！' . isset($makeOutInvoice['error_msg']) ? $makeOutInvoice['error_msg'] : '商家秒发模块请求异常');
                }
                $backData = array(
                    'order_nos' => $merchant_second_order_no,
                    'status'    => 1
                );
                $this->orderInvoiceCallBack($backData);
                $result['data'] = true;
            }
        }
        if (count($orderData['data']) > 0) {
            //获取期数信息
            $periodId   = array_values(array_unique(array_column($orderData['data'], 'period')));
            $periodEs   = array(
                'index'  => ['periods'],
                'terms'  => [['id' => $periodId]],
                'source' => ['id', 'payee_merchant_id'],
                'limit'  => 10000
            );
            $periodData = $es->getDocumentList($periodEs);
            $periodArr  = [];
            foreach ($periodData['data'] as &$v) {
                $periodArr[$v['id']]['payee_merchant_id'] = isset($v['payee_merchant_id']) ? $v['payee_merchant_id'] : 2;
            }
            //获取套餐信息
            $packageId   = array_values(array_unique(array_column($orderData['data'], 'package_id')));
            $packageEs   = array(
                'index'  => ['periods_set'],
                'terms'  => [['id' => $packageId]],
                'source' => ['id', 'associated_products'],
                'limit'  => 10000
            );
            $packageData = $es->getDocumentList($packageEs);
            $packageArr  = [];
            foreach ($packageData['data'] as &$vv) {
                $associated_products                          = json_decode($vv['associated_products'], true);
                $packageArr[$vv['id']]['associated_products'] = $associated_products;
            }
            //获取磐石产品信息
            $productId   = array_values(array_unique(call_user_func_array('array_merge', $productIdArr)));
            $productEs   = array(
                'index'  => ['panshi.products'],
                'terms'  => [['id' => $productId]],
                'source' => ['id', 'short_code', 'cn_product_name', 'product_category', 'product_unit_name', 'tax_rate', 'tax_code', 'invoice_name', 'capacity', 'is_taxfree', 'invoice_value'],
                'limit'  => 10000
            );
            $productData = $es->getDocumentList($productEs);
            $productArr  = [];
            foreach ($productData['data'] as &$vvv) {
                $vvv['is_taxfree_txt'] = in_array(($vvv['is_taxfree'] ?? '0'), ['1', '03']) ? "03" : "0";
                $vvv['invoice_value'] = floatval($vvv['invoice_value'] ?? 0.00);
                $productArr[$vvv['id']] = $vvv;
            }
            //订单产品信息组装
            $orders = [];
            foreach ($orderData['data'] as &$val) {
                //产品含税单价计算
                $totalcostprice = 0;
                foreach ($val['costprice_info'] as $kk => $value) {
                    foreach ($packageArr[$val['package_id']]['associated_products'] as &$as) {
                        if ($as['product_id'] == $value['product_id']) $val['costprice_info'][$kk]['nums'] = $as['nums'];
                    }
                    if (isset($val['costprice_info'][$kk]['nums'])) {
                        //单个产品总成本
                        $val['costprice_info'][$kk]['totalcostprice'] = $value['costprice'] * $val['costprice_info'][$kk]['nums'] * $val['order_qty'];
                        //套餐总成本
                        $totalcostprice += $val['costprice_info'][$kk]['totalcostprice'];
                    } else {
                        unset($val['costprice_info'][$kk]);
                    }
                }
                if (count($val['costprice_info']) <= 0) {
                    $queueData = array(
                        'access_token' => 'b3670fb7-a6aa-4353-b3fa-71da6d4e9765',
                        'type'         => 'text',
                        'at'           => '***********',
                        'content'      => base64_encode('订单号：' . $val['sub_order_no'] . '，期数：' . $val['period'] . ',产品成本为0开票失败，请手动处理'),
                    );
                    $data      = base64_encode(json_encode($queueData));
                    $pushData  = array(
                        'exchange_name' => 'dingtalk',
                        'routing_key'   => 'dingtalk_sender',
                        'data'          => $data,
                    );
                    httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
                    $this->throwError($val['sub_order_no'] . '订单信息异常，开票失败，请联系客服处理');
                }
                //产品信息组装
                $products     = [];
                $invoiceMoney = $val['cash_amount'] - $val['refund_money'];
                $is_support_invoicing = 1;
                $period_es = Es::name(Es::PERIODS)->where([['id', '==', $val['period']]])->field('id,is_support_invoicing,periods_type')->find();
                if (isset($period_es['is_support_invoicing'])) {
                    $is_support_invoicing = $period_es['is_support_invoicing'];
                } else {
                    if (in_array($period_es['periods_type'], [0, 1, 2, 3])) {
                        $table                = [0 => 'vh_periods_flash', 1 => 'vh_periods_second', 2 => 'vh_periods_cross', 3 => 'vh_periods_leftover'];
                        $is_support_invoicing = Db::table('vh_commodities.' . $table[$period_es['periods_type']])->where('id', $period_es['id'])->value('is_support_invoicing');
                    }
                }
                if ($invoiceMoney <= 0) $this->throwError($val['sub_order_no'] . '开票失败，订单已全额退款，无可开票金额');
                $totalMoney = $val['cash_amount'] - $val['refund_money'];
                $sort_price = array_column($val['costprice_info'], 'costprice');
                array_multisort($sort_price, SORT_ASC, $val['costprice_info']);
                foreach ($val['costprice_info'] as $ka => $va) {
                    $difference = 0;
                    $nums       = 0;
                    $nums       = $va['nums'] * $val['order_qty'];
                    //科技公司订单不拆分产品数量来保证含税单价必须保留2位小数
                    if (isset($periodArr[$val['period']]['payee_merchant_id']) && $periodArr[$val['period']]['payee_merchant_id'] == 2 && strtotime($val['created_time']) > '1664467200') {
                        if (count($val['costprice_info']) == 1) {
                            $price = $totalMoney / $nums;
                        } else {
                            $price = bcdiv($va['totalcostprice'] / $totalcostprice * $invoiceMoney, $nums, 2) + 0;
                            if (count($val['costprice_info']) > $ka + 1) {
                                if ($price == 0) {
                                    $price      = 0.01;
                                    $totalMoney -= 0.01 * $nums;
                                } else {
                                    $totalMoney -= ($price * $nums);
                                    $totalMoney = strval($totalMoney) + 0;
                                }
                            } else {
                                $price = $totalMoney / $nums;
                            }
                        }
                    } else {
                        if (count($val['costprice_info']) == 1) {
                            $price = bcdiv($totalMoney, $nums, 2) + 0;
                            if (strval($totalMoney / $nums) != strval($price) && $nums > 1) {
                                $price      = bcdiv($totalMoney, $nums, 2) + 0;
                                $difference = bcsub($totalMoney, $price * ($nums - 1), 2) + 0;
                                $nums       = $nums - 1;
                            }
                        } else {
                            $price = intval($va['totalcostprice'] / $totalcostprice * $invoiceMoney / $nums);
                            if (count($val['costprice_info']) > $ka + 1) {
                                if ($price == 0) {
                                    $price      = 0.01;
                                    $totalMoney -= 0.01 * $nums;
                                } else {
                                    $totalMoney -= $price * $nums;
                                    $totalMoney = strval($totalMoney) + 0;
                                }
                            } else {
                                $price = bcdiv($totalMoney, $nums, 2) + 0;
                                if (strval($totalMoney / $nums) != strval($price) && $nums > 1) {
                                    $price      = bcdiv($totalMoney, $nums, 2) + 0;
                                    $difference = bcsub($totalMoney, $price * ($nums - 1), 2) + 0;
                                    $nums       = $nums - 1;
                                }
                            }
                        }
                    }
                    //税率、税收编码处理
                    if (isset($productArr[$va['product_id']]['tax_rate']) && $productArr[$va['product_id']]['tax_rate'] > 0 && isset($productArr[$va['product_id']]['tax_code']) && !empty($productArr[$va['product_id']]['tax_code'])) {
                        if (strpos($productArr[$va['product_id']]['tax_code'], '-') !== false) {
                            $productArr[$va['product_id']]['tax_code'] = substr($productArr[$va['product_id']]['tax_code'], 0, strrpos($productArr[$va['product_id']]['tax_code'], '-'));
                        }
                        $tax                = floatval($productArr[$va['product_id']]['tax_rate']);
                        $invoice_goods_code = $productArr[$va['product_id']]['tax_code'];
                    } else {
                        $tax                = self::InvoiceGoodsCodesTax[isset($productArr[$va['product_id']]['product_category']) ? $productArr[$va['product_id']]['product_category'] : 1];
                        $invoice_goods_code = self::InvoiceGoodsCodes[isset($productArr[$va['product_id']]['product_category']) ? $productArr[$va['product_id']]['product_category'] : 1];
                    }
                    $spec_type = $productArr[$va['product_id']]['capacity'] ?? '';
                    //$tax                = self::InvoiceGoodsCodesTax[isset($productArr[$va['product_id']]['product_category']) ? $productArr[$va['product_id']]['product_category'] : 1];
                    //$invoice_goods_code = self::InvoiceGoodsCodes[isset($productArr[$va['product_id']]['product_category']) ? $productArr[$va['product_id']]['product_category'] : 1];
                    if (($productArr[$va['product_id']]['invoice_value'] <= $price)) {
                        //正常行
                        $products[] = array(
                            'code'               => isset($productArr[$va['product_id']]['short_code']) ? $productArr[$va['product_id']]['short_code'] : '',
                            'name'               => isset($productArr[$va['product_id']]['invoice_name']) ? $productArr[$va['product_id']]['invoice_name'] : $productArr[$va['product_id']]['cn_product_name'],
                            'price'              => floatval(strval(isset($periodArr[$val['period']]['payee_merchant_id']) && $periodArr[$val['period']]['payee_merchant_id'] == 2 ? $price : strval($price) + 0)),
                            'num'                => $nums,
                            'unit'               => isset($productArr[$va['product_id']]['product_unit_name']) ? $productArr[$va['product_id']]['product_unit_name'] : '',
                            'tax' => ($tax > 0 && ($productArr[$va['product_id']]['is_taxfree_txt'] ?? '0') == '03') ? 0 : $tax,
                            'favoured_policy_flag' => $productArr[$va['product_id']]['is_taxfree_txt'] ?? '0',
                            'spec_type'          => strval($spec_type),
                            'invoice_goods_code' => $invoice_goods_code,
                        );
                    } else {
                        //被折扣行
                        $products[] = array(
                            'code'                 => isset($productArr[$va['product_id']]['short_code']) ? $productArr[$va['product_id']]['short_code'] : '',
                            'name'                 => isset($productArr[$va['product_id']]['invoice_name']) ? $productArr[$va['product_id']]['invoice_name'] : $productArr[$va['product_id']]['cn_product_name'],
                            'price'                => floatval(strval(floatval($productArr[$va['product_id']]['invoice_value']))),
                            'num'                  => $nums,
                            'unit'                 => isset($productArr[$va['product_id']]['product_unit_name']) ? $productArr[$va['product_id']]['product_unit_name'] : '',
                            'tax'                  => ($tax > 0 && ($productArr[$va['product_id']]['is_taxfree_txt'] ?? '0') == '03') ? 0 : $tax,
                            'favoured_policy_flag' => $productArr[$va['product_id']]['is_taxfree_txt'] ?? '0',
                            'spec_type'            => strval($spec_type),
                            'invoice_goods_code'   => $invoice_goods_code,
                            'property'   => 2,//发票行性质（0正常行(默认),1折扣行,2被折扣行
                        );

                        if (floatval(bcsub($productArr[$va['product_id']]['invoice_value'], $price, 2)) <= 0) {
                            $this->throwError($val['sub_order_no'] . '开票失败！' . "折扣行金额必须大于0");
                        }
                        //折扣行
                        $products[] = array(
                            'code'                 => isset($productArr[$va['product_id']]['short_code']) ? $productArr[$va['product_id']]['short_code'] : '',
                            'name'                 => isset($productArr[$va['product_id']]['invoice_name']) ? $productArr[$va['product_id']]['invoice_name'] : $productArr[$va['product_id']]['cn_product_name'],
                            'price'                => floatval(strval(floatval(bcsub($price,$productArr[$va['product_id']]['invoice_value'], 2)))),
                            'num'                  => $nums,
                            'unit'                 => isset($productArr[$va['product_id']]['product_unit_name']) ? $productArr[$va['product_id']]['product_unit_name'] : '',
                            'tax'                  => ($tax > 0 && ($productArr[$va['product_id']]['is_taxfree_txt'] ?? '0') == '03') ? 0 : $tax,
                            'favoured_policy_flag' => $productArr[$va['product_id']]['is_taxfree_txt'] ?? '0',
                            'spec_type'            => strval($spec_type),
                            'invoice_goods_code'   => $invoice_goods_code,
                            'property'   => 1,//发票行性质（0正常行(默认),1折扣行,2被折扣行
                        );
                    }

                    if ($difference > 0) {
                        $products[] = array(
                            'code'               => isset($productArr[$va['product_id']]['short_code']) ? $productArr[$va['product_id']]['short_code'] : '',
                            'name'               => isset($productArr[$va['product_id']]['invoice_name']) ? $productArr[$va['product_id']]['invoice_name'] : $productArr[$va['product_id']]['cn_product_name'],
                            'price'              => floatval(strval(strval($difference) + 0)),
                            'num'                => 1,
                            'unit'               => isset($productArr[$va['product_id']]['product_unit_name']) ? $productArr[$va['product_id']]['product_unit_name'] : '',
                            'tax' => ($tax > 0 && ($productArr[$va['product_id']]['is_taxfree_txt'] ?? '0') == '03') ? 0 : $tax,
                            'favoured_policy_flag' => $productArr[$va['product_id']]['is_taxfree_txt'] ?? '0',
                            'spec_type'          => strval($spec_type),
                            'invoice_goods_code' => $invoice_goods_code,
                        );
                    }
                }
                $affiliation = isset($periodArr[$val['period']]['payee_merchant_id']) ? $periodArr[$val['period']]['payee_merchant_id'] : 2;
                //2022-9-30号之前默认云酒公司
                if (strtotime($val['created_time']) <= '1664467200') {
                    $affiliation = 1;
                }
                $orders[] = array(
                    'order_no'    => $val['sub_order_no'],
                    'affiliation' => $affiliation,
                    'is_support_invoicing' => $is_support_invoicing,
                    'products'    => $products
                );
            }
            //开票信息组装
            $data   = array(
                'uid'      => intval($params['uid']),
                'genre_id' => 2,
                'remarks' => $params['remark'] ?? '',
                'genre'    => $params['invoice_type'],
                'receipt'  => array(
                    'genre'           => intval($params['type_id']),
                    'name'            => $params['invoice_name'],
                    'tel'             => isset($params['telephone']) ? $params['telephone'] : '',
                    'email'           => isset($params['email']) ? $params['email'] : '',
                    'taxpayer'        => isset($params['taxpayer']) ? str_replace(" ", "", $params['taxpayer']) : '',
                    'company_address' => isset($params['company_address']) ? $params['company_address'] : '',
                    'company_tel'     => isset($params['company_tel']) ? $params['company_tel'] : '',
                    'opening_bank'    => isset($params['opening_bank']) ? $params['opening_bank'] : '',
                    'bank_account'    => isset($params['bank_account']) ? $params['bank_account'] : '',
                ),
                'orders'   => $orders
            );
            $result = curlRequest(env('ITEM.GO_INVOICE_URL') . '/go-invoice/v3/create/createD', json_encode($data), [], 'POST');
            if (!isset($result['error_code'])) $this->throwError('发票模块请求异常');
            $sub_order_no = implode(',', array_column($orders, 'order_no'));
            if ($result['error_code'] != 0) {
                $nmsg = '订单号：' . $sub_order_no . '，开票失败！错误信息：' . $result['error_msg'];
                $at = '***********';
                if (strpos($nmsg, '商编') !== false || strpos($nmsg, '商品') !== false) {
                    $at = '***********';
                }
                if (isset($is_support_invoicing) && $is_support_invoicing == 0) {
                    $at = '***********';
                }
                if(strpos($nmsg,'开票失败，请联系客服。') !== false){
                    $at = '***********';
                }
                $queueData = array(
                    'access_token' => 'b3670fb7-a6aa-4353-b3fa-71da6d4e9765',
                    'type'         => 'text',
                    'at'           => $at,
                    'content'      => base64_encode($nmsg)
                );
                $data      = base64_encode(json_encode($queueData));
                $pushData  = array(
                    'exchange_name' => 'dingtalk',
                    'routing_key'   => 'dingtalk_sender',
                    'data'          => $data,
                );
                httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
                $backData = array(
                    'order_nos'    => array_column($orders, 'order_no'),
                    'status'       => 3,
                    'msg'          => $result['error_msg'],
                    'invoice_code' => '无',
                );
                $this->orderInvoiceCallBack($backData);
                $this->throwError($sub_order_no . '开票失败！' . $result['error_msg']);
            }
            //开票同步结果status=1和2和3的进行回调处理开票状态
            foreach ($result['data']['invoice_by_order'] as &$values) {
                if (isset($values['invoices'][0]['status']) && in_array($values['invoices'][0]['status'], [1, 2, 3])) {
                    $backData = array(
                        'order_nos'    => [$values['order_no']],
                        'status'       => $values['invoices'][0]['status'],
                        'msg'          => $values['invoices'][0]['msg'],
                        'invoice_code' => isset($values['invoices'][0]['invoice_code']) ? $values['invoices'][0]['invoice_code'] : '无',
                    );
                    $this->orderInvoiceCallBack($backData);
                }
            }
        }
        return $result['data'];
    }

    /**
     * Description:订单开票回调处理
     * Author: zrc
     * Date: 2023/4/12
     * Time: 13:20
     * @param $params
     * @return bool
     * @throws \think\db\exception\DbException
     */
    public function orderInvoiceCallBack($params)
    {
        $updateData                = [];
        $updateData['update_time'] = time();
        switch ($params['status']) {
            case 1:
                $updateData['invoice_progress'] = 1;
                break;
            case 2://开票成功
                $updateData['invoice_progress'] = 2;
                break;
            case 3://开票失败
                $nmsg = '订单号：' . implode(',', $params['order_nos']) . '，发票号：' . $params['invoice_code'] . '，开票失败！错误信息：' . $params['msg'];
                $at = '***********';
                if (strpos($nmsg, '商编') !== false || strpos($nmsg, '商品') !== false) {
                    $at = '***********';
                }
                if(strpos($nmsg,'开票失败，请联系客服。') !== false){
                    $at = '***********';
                }
                $queueData = array(
                    'access_token' => 'b3670fb7-a6aa-4353-b3fa-71da6d4e9765',
                    'type'         => 'text',
                    'at'           => $at,
                    'content'      => base64_encode($nmsg)
                );
                $data      = base64_encode(json_encode($queueData));
                $pushData  = array(
                    'exchange_name' => 'dingtalk',
                    'routing_key'   => 'dingtalk_sender',
                    'data'          => $data,
                );
                httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
                $updateData['invoice_progress'] = 3;
                break;
            case 4://作废发票
                $updateData['invoice_progress'] = 0;
                $is_cancel                      = 1;
                break;
        }
        if (!empty($updateData)) {
            $es         = new ElasticSearchService();
            $orderEs    = array(
                'index'  => ['orders'],
                'terms'  => [['sub_order_no.keyword' => $params['order_nos']]],
                'source' => ['order_type', 'sub_order_no', 'invoice_progress'],
                'limit'  => 10000
            );
            $orderData  = $es->getDocumentList($orderEs);
            $order_type = config('config')['order_type'];//订单频道获取
            if (isset($is_cancel)) {
                foreach ($orderData['data'] as &$val) {
                    Db::name($order_type[intval($val['order_type'])]['table'])->where(['sub_order_no' => $val['sub_order_no']])->update($updateData);
                }
            } else {
                foreach ($orderData['data'] as &$val) {
                    Db::name($order_type[intval($val['order_type'])]['table'])->where(['sub_order_no' => $val['sub_order_no']])->update($updateData);
                    Db::name('invoice_record')->where(['sub_order_no' => $val['sub_order_no']])->update($updateData);
                }
            }
        }
        return true;
    }

    /**
     * Description:获取订单开票信息
     * Author: zrc
     * Date: 2023/4/23
     * Time: 13:43
     * @param $params
     * @return array|mixed|Db|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getOrderInvoiceInfo($params)
    {
        $invoiceInfo = Db::name('invoice_record')->where(['sub_order_no' => $params['sub_order_no']])->find();
        if (empty($invoiceInfo)) $this->throwError('未获取到开票信息');
        $invoiceInfo['pdf_url'] = '';
        if ($invoiceInfo['invoice_progress'] == 2) {
            $invoice                = curlRequest(env('ITEM.GO_INVOICE_URL') . '/go-invoice/v3/query/infoByOrderNo', json_encode(['genre_id' => 2, 'order_nos' => [$params['sub_order_no']], 'return_product' => false], true), [], 'POST');
            $invoiceInfo['pdf_url'] = isset($invoice['data']['orders'][0]['invoices'][0]['pdf_url']) ? $invoice['data']['orders'][0]['invoices'][0]['pdf_url'] : '';
        }
        return $invoiceInfo;
    }

    /**
     * Description:获取开票中订单列表
     * Author: zrc
     * Date: 2023/7/26
     * Time: 11:19
     * @param $params
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getInvoicingOrderList($params)
    {
        $where = [];
        if (!empty($params['uid'])) {
            $where[] = ['uid' => $params['uid']];
        }
        if (!empty($params['sub_order_no'])) {
            $where[] = ['sub_order_no' => $params['sub_order_no']];
        }
        $where[] = ['sub_order_status' => 3];
        $where[] = ['invoice_progress' => 1];
        $order   = [['created_time' => 'desc']];
        $es      = new ElasticSearchService();
        $arr     = array(
            'index'  => ['orders'],
            'match'  => $where,
            'terms'  => [['order_type' => [0, 1, 3, 9]]],
            'source' => ['id', 'uid', 'sub_order_no', 'order_type', 'order_qty', 'period', 'package_id', 'cash_amount', 'payment_amount', 'title', 'package_name', 'banner_img', 'refund_status', 'invoice_progress', 'created_time', 'sub_order_status'],
            'page'   => $params['page'],
            'limit'  => $params['limit'],
            'sort'   => $order
        );
        $data    = $es->getDocumentList($arr);
        if (count($data['data']) > 0) {
            $sub_order_no_arr = array_column($data['data'], 'sub_order_no');
            $invoiceRecord    = Db::name('invoice_record')->where([['sub_order_no', 'in', $sub_order_no_arr]])->select()->toArray();
            foreach ($data['data'] as &$val) {
                foreach ($invoiceRecord as &$v) {
                    if ($val['sub_order_no'] == $v['sub_order_no']) {
                        $val['invoice_info'] = array(
                            'invoice_type'    => $v['invoice_type'],
                            'type_id'         => $v['type_id'],
                            'invoice_name'    => $v['invoice_name'],
                            'taxpayer'        => $v['taxpayer'],
                            'telephone'       => $v['telephone'],
                            'email'           => $v['email'],
                            'company_address' => $v['company_address'],
                            'company_tel'     => $v['company_tel'],
                            'opening_bank'    => $v['opening_bank'],
                            'bank_account'    => $v['bank_account']
                        );
                    }
                }
                $val['banner_img'] = imagePrefix($val['banner_img']);
                $val['cash_amount'] = $val['cash_amount'] ?? ($val['payment_amount'] ?? 0);
            }
        }
        $totalNum        = $data['total']['value'];
        $result['list']  = $data['data'];
        $result['total'] = $totalNum;
        return $result;
    }
}