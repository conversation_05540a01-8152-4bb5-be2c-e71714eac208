<?php


namespace app\service;


use app\BaseService;
use app\ErrorCode;
use app\service\elasticsearch\ElasticSearchService;
use app\service\WeChat as WeChatService;
use think\facade\Db;
use think\facade\Log;

class Logistics extends BaseService
{
    /**
     * Description:添加/编辑未发货提醒
     * Author: zrc
     * Date: 2023/2/15
     * Time: 14:14
     * @param $params
     * @return int|string
     * @throws \think\db\exception\DbException
     */
    public function addEditReason($params)
    {
        if (!isset($params['id'])) {//添加
            $isset = Db::name('unshipped_reason')->where(['reason' => $params['reason']])->count();
            if ($isset > 0) $this->throwError('已存在该未发货原因，请勿重复添加');
            $data   = array(
                'reason'               => $params['reason'],
                'content'              => $params['content'],
                'is_set_delivery_time' => $params['is_set_delivery_time'],
                'operator'             => $params['admin_id'],
                'created_time'         => time(),
            );
            $result = Db::name('unshipped_reason')->insert($data);
        } else {//修改
            $isset = Db::name('unshipped_reason')->where(['id' => $params['id']])->count();
            if ($isset == 0) $this->throwError('数据不存在，修改失败');
            $updateData = array(
                'operator'    => $params['admin_id'],
                'update_time' => time()
            );
            if (!empty($params['reason'])) $updateData['reason'] = $params['reason'];
            if (!empty($params['content'])) $updateData['content'] = $params['content'];
            if (isset($params['is_set_delivery_time']) && in_array($params['is_set_delivery_time'], [0, 1])) $updateData['is_set_delivery_time'] = $params['is_set_delivery_time'];
            if (isset($params['status']) && in_array($params['status'], [0, 1])) $updateData['status'] = $params['status'];
            $result = Db::name('unshipped_reason')->where(['id' => $params['id']])->update($updateData);
        }
        return $result;
    }

    /**
     * Description:未发货提醒列表
     * Author: zrc
     * Date: 2023/2/16
     * Time: 10:33
     * @param $params
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function reasonList($params)
    {
        $offset = ($params['page'] - 1) * $params['limit'];
        $where  = [];
        if (!empty($params['reason'])) {
            $where[] = ['reason', 'like', '%' . $params['reason'] . '%'];
        }
        if (isset($params['status']) && in_array($params['status'], [0, 1])) {
            $where[] = ['status', '=', $params['status']];
        }
        $totalNum = Db::name('unshipped_reason')->where($where)->count();
        $lists    = Db::name('unshipped_reason')
            ->where($where)
            ->limit($offset, $params['limit'])
            ->order('id desc')
            ->select()->toArray();
        $result   = [];
        if (count($lists) > 0) {
            $operator  = array_unique(array_column($lists, 'operator'));
            $adminInfo = $this->httpGet(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info', ['admin_id' => implode(',', $operator), 'field' => 'realname']);
            foreach ($lists as &$val) {
                $val['created_time'] = date('Y-m-d H:i:s', $val['created_time']);
                if (isset($adminInfo['data'][$val['operator']])) $val['operator'] = $adminInfo['data'][$val['operator']];
            }
        }
        $result['list']  = $lists;
        $result['total'] = $totalNum;
        return $result;
    }

    /**
     * Description:未发货提醒获取期数信息
     * Author: zrc
     * Date: 2023/2/17
     * Time: 13:26
     * @param $params
     * @return array|mixed
     * @throws \Exception
     */
    public function getPeriodInfo($params)
    {
        $list = [];
        switch ($params['type']) {
            case 0:
                $periods_arr = explode(',', str_replace('，',',', $params['period']));
                foreach ($periods_arr as $pa_i){
                    $periodInfo = esGetOne($pa_i, 'vinehoo.periods');
                if (empty($periodInfo)) $this->throwError($pa_i. '未获取到期数信息');
                $list[] = array(
                    'period'                => $pa_i,
                    'periods_type'          => $periodInfo['periods_type'],
                    'title'                 => $periodInfo['title'],
                    'predict_shipment_time' => $periodInfo['predict_shipment_time'],
                    'buyer_name'            => $periodInfo['buyer_name'],
                );
                }
                break;
            case 1:
                if (!empty($params['sub_order_no'])) {
                    $orderInfo = esGetOne($params['sub_order_no'], 'vinehoo.orders');
                    if (empty($orderInfo)) $this->throwError('未获取到订单信息');
                    $periodInfo = esGetOne($orderInfo['period'], 'vinehoo.periods');
                    if (empty($periodInfo)) $this->throwError('未获取到期数信息');
                    $list[] = array(
                        'period'                => $params['sub_order_no'],
                        'periods_type'          => $periodInfo['periods_type'],
                        'title'                 => $periodInfo['title'],
                        'predict_shipment_time' => $orderInfo['predict_time'],
                        'buyer_name'            => $periodInfo['buyer_name'],
                    );
                } else if (!empty($params['file'])) {
                    //获取文件路径
                    $path   = env('ALIURL') . $params['file'];
                    //$path   = 'D:\未发货提醒模板.xls';
                    $startI = 1;
                    #下载文件
                    $local_path = download_image($path, 'xls');
                    #解析文件
                    $data = getExcelData($local_path, $startI);
                    @unlink($local_path);
                    if ($data['error_code'] != 0) $this->throwError('excel解析失败');
                    if (count($data['data']) == 0) $this->throwError('未获取到excel内容');
                    $excelData = $data['data'];
                    if ($excelData[$startI][0] != '子订单号') {
                        $this->throwError('格式有误，请下载模板操作');
                    }
                    unset($excelData[$startI]);
                    $orderNoArr = array_column($excelData, 0);
                    $es         = new ElasticSearchService();
                    $source     = ['sub_order_no', 'period', 'predict_time'];
                    $where      = ['index' => ['orders'], 'terms' => [['_id' => $orderNoArr]], 'source' => $source,'limit'=>10000];
                    $orderInfo  = $es->getDocumentList($where);
                    if (!isset($orderInfo['data']) || count($orderInfo['data']) == 0) $this->throwError('未获取到订单信息');
                    $periodArr  = array_values(array_unique(array_column($orderInfo['data'], 'period')));
                    $periodInfo = $es->getDocumentList(['index' => ['periods'], 'terms' => [['id' => $periodArr]], 'source' => ['id', 'periods_type', 'title', 'predict_shipment_time', 'buyer_name'],'limit'=>10000]);
                    if (!isset($periodInfo['data']) || count($periodInfo['data']) == 0) $this->throwError('未获取到期数信息');
                    foreach ($orderInfo['data'] as &$val) {
                        foreach ($periodInfo['data'] as &$v) {
                            if ($v['id'] == $val['period']) {
                                $list[] = array(
                                    'period'                => $val['sub_order_no'],
                                    'periods_type'          => $v['periods_type'],
                                    'title'                 => $v['title'],
                                    'predict_shipment_time' => $val['predict_time'],
                                    'buyer_name'            => $v['buyer_name'],
                                );
                            }
                        }
                    }
                }
                break;
        }
        $offset          = ($params['page'] - 1) * $params['limit'];
        $result['list']  = array_slice($list, $offset, $params['limit']);
        $result['total'] = count($list);
        return $result;
    }

    /**
     * Description:提交未发货提醒审批
     * Author: zrc
     * Date: 2023/2/21
     * Time: 14:28
     * @param $params
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function submitUnShipApproval($params)
    {
        $reasonInfo = Db::name('unshipped_reason')->field('content,status,is_set_delivery_time')->where(['id' => $params['reason_id']])->find();
        if (empty($reasonInfo)) $this->throwError('未获取到未发货原因信息');
        if ($reasonInfo['status'] != 0) $this->throwError('当前原因未启用');
        if ($reasonInfo['is_set_delivery_time'] == 1 && empty($params['predict_time'])) $this->throwError('未获取到预计发货时间', ErrorCode::PARAM_ERROR);
        $periodArr = array_values(array_unique(array_column($params['periodInfo'], 'period')));
        if (count($periodArr) == 0) {
            $this->throwError('未获取到期数/订单信息');
            //        } else if (count($periodArr) == 1) {
            //            if (strpos($periodArr[0], 'VH') !== false || strpos($periodArr[0], 'WYS') !== false) {
            //                $sub_order_no_arr = $periodArr;
            //            }
            //        } else {
        } else  if (strpos($periodArr[0], 'VH') !== false || strpos($periodArr[0], 'WYS') !== false) {
            $sub_order_no_arr = $periodArr;
        }
        $es = new ElasticSearchService();
        if (isset($sub_order_no_arr)) {//订单号处理
            $orderInfo = $es->getDocumentList(['index' => ['orders'], 'terms' => [['_id' => $sub_order_no_arr]], 'source' => ['sub_order_no', 'period', 'predict_time'],'limit'=>10000]);
            if (!isset($orderInfo['data']) || count($orderInfo['data']) == 0) $this->throwError('未获取到订单信息');
            $periodArr  = array_values(array_unique(array_column($orderInfo['data'], 'period')));
            $periodInfo = $es->getDocumentList(['index' => ['periods'], 'terms' => [['id' => $periodArr]], 'source' => ['id', 'periods_type', 'title', 'predict_shipment_time', 'buyer_name'],'limit'=>10000]);
            if (!isset($periodInfo['data']) || count($periodInfo['data']) == 0) $this->throwError('未获取到期数信息');
            $periodData = [];
            foreach ($orderInfo['data'] as &$val) {
                foreach ($periodInfo['data'] as &$v) {
                    if ($v['id'] == $val['period']) {
                        switch ($v['periods_type']) {
                            case 0:
                                $v['periods_type'] = '闪购';
                                break;
                            case 1:
                                $v['periods_type'] = '秒发';
                                break;
                            case 2:
                                $v['periods_type'] = '跨境';
                                break;
                            case 3:
                                $v['periods_type'] = '尾货';
                                break;
                            case 4:
                                $v['periods_type'] = '兔头';
                                break;
                            case 9:
                                $v['periods_type'] = '商家秒发';
                                break;
                        }
                        $periodData[] = array(
                            array(
                                'name'  => '期数/订单',
                                'value' => strval($val['sub_order_no']),
                            ),
                            array(
                                'name'  => '频道',
                                'value' => $v['periods_type'],
                            ),
                            array(
                                'name'  => '商品名称',
                                'value' => $v['title'],
                            ),
                            array(
                                'name'  => '原发货时间',
                                'value' => $val['predict_time'],
                            ),
                            array(
                                'name'  => '采购人',
                                'value' => $v['buyer_name'],
                            ),
                        );
                    }
                }
            }
        } else {//期数处理
            $periodInfo = $es->getDocumentList(['index' => ['periods'], 'terms' => [['id' => $periodArr]], 'source' => ['id', 'periods_type', 'title', 'predict_shipment_time', 'buyer_name'],'limit'=>10000]);
            if (!isset($periodInfo['data']) || count($periodInfo['data']) == 0) $this->throwError('未获取到期数信息');
            $periodData = [];
            foreach ($periodInfo['data'] as &$val) {
                switch ($val['periods_type']) {
                    case 0:
                        $val['periods_type'] = '闪购';
                        break;
                    case 1:
                        $val['periods_type'] = '秒发';
                        break;
                    case 2:
                        $val['periods_type'] = '跨境';
                        break;
                    case 3:
                        $val['periods_type'] = '尾货';
                        break;
                    case 4:
                        $val['periods_type'] = '兔头';
                        break;
                    case 9:
                        $val['periods_type'] = '商家秒发';
                        break;
                }
                $periodData[] = array(
                    array(
                        'name'  => '期数/订单',
                        'value' => strval($val['id']),
                    ),
                    array(
                        'name'  => '频道',
                        'value' => $val['periods_type'],
                    ),
                    array(
                        'name'  => '商品名称',
                        'value' => $val['title'],
                    ),
                    array(
                        'name'  => '原发货时间',
                        'value' => $val['predict_shipment_time'],
                    ),
                    array(
                        'name'  => '采购人',
                        'value' => $val['buyer_name'],
                    ),
                );
            }
        }
        //获取发起人信息
        $userInfo = $this->httpGet(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info', ['admin_id' => $params['admin_id'], 'field' => 'userid,dept_id']);
        if ($userInfo['error_code'] != 0 || !isset($userInfo['data'][$params['admin_id']])) $this->throwError('发起企业微信批失败：未获取到企业微信用户信息');
        $userid = $userInfo['data'][$params['admin_id']]['userid'];//企业微信userid

        $form_component_values = array(
            array(
                'name'  => '未发货原因',
                'value' => $reasonInfo['content'],
            ),
            array(
                'name'  => '最新预计发货时间',
                'value' => $reasonInfo['is_set_delivery_time'] == 1 ? $params['predict_time'] : '不修改',
            ),
            array(
                'name'  => '期数信息',
                'value' => $periodData,
            ),
        );
        $pushData              = array(
            'form_component_values' => $form_component_values,
            'process_code'          => env('ORDERS.un_ship_verify_id'),
            'dept_id'               => 0,//部门传0默认获取自己主部门
            'originator_user_id'    => $userid
        );
        $result                = curlRequest(env('ITEM.WECHART_URL') . '/wechat/v3/wecom/approval/create', json_encode($pushData,true), [], 'POST');
        if (!isset($result['error_code'])) $this->throwError('发起企业微信审批失败：微信服务模块请求异常！' . $result);
        if ($result['error_code'] != 0) $this->throwError('发起企业微信审批失败：' . $result['error_msg']);
        return true;
    }

    /**
     * Description:未发货提醒审批回调处理
     * Author: zrc
     * Date: 2023/2/22
     * Time: 9:12
     * @param $params
     * @return bool
     * @throws \Exception
     */
    public function unShipCallBack($params)
    {
        $params           = $params['process_instance'];
        $weChatService    = new WeChatService();
        $periodData       = '';
        $reason           = '';
        $new_predict_time = '';

        $admin_id = 0;
        $admin_name = '系统';
        try {
            $admin = \Curl::adminInfo(['admin_id' => $params['originator_userid']]) ?? [];
            if (!empty($admin)) {
                $admin_info = array_column($admin, null, 'userid')[$params['originator_userid']] ?? [];
                $admin_id = $admin_info['id'] ?? $admin_id;
                $admin_name = $admin_info['realname'] ?? $admin_name;
            }
        } catch (\Exception $e) {}

        foreach ($params['form_component_values'] as &$val) {
            if (isset($val['name'])) {
                if ($val['name'] == '期数信息') $periodData = $val['value'];
                if ($val['name'] == '未发货原因') $reason = $val['value'];
                if ($val['name'] == '最新预计发货时间') $new_predict_time = $val['value'];
            }
        }
        if (empty($periodData) || empty($reason) || empty($new_predict_time)) {
            $weChatService->weChatSendText($params['originator_userid'], '未获取到审批数据,未发货提醒审批流处理失败!');
            $this->throwError('未获取到审批数据,未发货提醒审批流处理失败!');
        }
        //审批通过处理
        if ($params['result'] == 'agree') {
            $dataStr   = [];
            $periodArr = [];
            $orderArr  = [];
            foreach ($periodData as &$val) {
                if (!isset($val['rowValue'][0]['value']) || !isset($val['rowValue'][1]['value'])) {
                    $weChatService->weChatSendText($params['originator_userid'], '未获取到审批数据,未发货提醒审批流处理失败!');
                    $this->throwError('未获取到审批数据,审批流处理失败!');
                }
                if (strpos($val['rowValue'][0]['value'], 'VH') !== false || strpos($val['rowValue'][0]['value'], 'WYS') !== false) {//订单
                    $orderArr[] = $val['rowValue'][0]['value'];
                } else {//期数
                    $period      = $val['rowValue'][0]['value'];
                    $periodArr[] = $period;
                    switch ($val['rowValue'][1]['value']) {
                        case '闪购':
                            $period_type = 0;
                            break;
                        case '秒发':
                            $period_type = 1;
                            break;
                        case '跨境':
                            $period_type = 2;
                            break;
                        case '尾货':
                            $period_type = 3;
                            break;
                        case '兔头':
                            $period_type = 4;
                            break;
                        case '商家秒发':
                            $period_type = 9;
                            break;
                        default:
                            $weChatService->weChatSendText($params['originator_userid'], '期数频道异常,未发货提醒审批流处理失败!');
                            $this->throwError('期数频道异常,未发货提醒审批流处理失败!');
                            break;
                    }
                    //拼接修改商品预计发货时间队列数据
                    if (!empty($new_predict_time) && $new_predict_time != '不修改' && isset($period_type)) {
                        $dataStr[] = base64_encode(json_encode(['period' => $period, 'periods_type' => $period_type, 'p_not_update' => 1, 'p_admin_id' => $admin_id, 'p_admin_name' => $admin_name, 'predict_shipment_time' => date('Y-m-d 23:59:59', strtotime($new_predict_time))]));
                    }
                }
            }
            //查询已支付未发货订单
            $es = new ElasticSearchService();
            if (!empty($orderArr)) {//订单
                $orderInfo = $es->getDocumentList(['index' => ['orders'], 'terms' => [['_id' => $orderArr]], 'match' => [['sub_order_status' => 1], ['express_number' => '']], 'source' => ['sub_order_no', 'order_type', 'predict_time', 'uid', 'title'],'limit'=>10000]);
            } else {//期数
                $orderInfo = $es->getDocumentList(['index' => ['orders'], 'terms' => [['period' => $periodArr]], 'match' => [['sub_order_status' => 1], ['express_number' => '']], 'source' => ['sub_order_no', 'order_type', 'predict_time', 'uid', 'title'],'limit'=>10000]);
            }
            $order_type = config('config')['order_type'];//订单频道获取
            Db::startTrans();
            try {
                if (count($orderInfo['data']) > 0) {
                    $uids     = array_values(array_unique(array_column($orderInfo['data'], 'uid')));
                    $enc_data = Db::table('vh_user.vh_user')
                        ->where('uid', 'in', $uids)
                        ->column('telephone', 'uid');
                    $dec_data = \Curl::cryptionDeal(array_values($enc_data));
                    foreach ($enc_data as $uid => &$enc_phone) {
                        $enc_phone = $dec_data[$enc_phone] ?? '';
                    }
                    foreach ($orderInfo['data'] as &$val) {
                        $unshipped_reason = $reason;
                        //订单预计发货时间处理
                        if ($new_predict_time != '不修改') {
                            $predict_time_str     = strtotime($val['predict_time']);
                            $new_predict_time_str = strtotime(date('Y-m-d 23:59:59', strtotime($new_predict_time)));
                            if ($predict_time_str != $new_predict_time_str) {
                                if ($new_predict_time_str > $predict_time_str) {
                                    try {
                                        $phone = $enc_data[$val['uid']];
                                        if ($phone) {
//                                            $sms_content = str_replace('此订单', $val['sub_order_no'], ($reason . "，最新预计发货时间调整为:{$new_predict_time}，感谢您对酒云网的支持！"));
                                            $sms_content = str_replace('此订单', "您购买的{$val['title']}", ($reason . "，最新预计发货时间调整为:{$new_predict_time}，感谢您对酒云网的支持！"));
                                            \Curl::sendSms([
                                                    'type'      => 1,
                                                    'telephone' => strval($phone),
                                                    'content'   => $sms_content
                                                ]
                                            );
                                        }
                                    } catch (\Exception $e) {
                                        Log::write('unShipCallBack 发送短信失败' . $e->getMessage());
                                    }
                                }
                                $order_remark = '原发货时间：'.$val['predict_time'].'，修改后发货时间：'.$new_predict_time;
                                Db::name($order_type[intval($val['order_type'])]['table'])->where(['sub_order_no' => $val['sub_order_no']])->update(['update_time' => time(), 'remarks' => $order_remark]); //'predict_time' => $new_predict_time_str,
                                Db::name('order_remarks')->insert(['sub_order_no' => $val['sub_order_no'], 'admin_id' => $admin_id, 'remarks' => $order_remark, 'created_time' => time()]);
                            }
                            $unshipped_reason = $reason . '最新预计发货时间调整为：' . $new_predict_time;
                        }
                        //未发货提示添加处理
                        $isset = Db::name('sub_order_extend')->where(['sub_order_no' => $val['sub_order_no']])->count();
                        if ($isset == 0) {
                            Db::name('sub_order_extend')->insert(['sub_order_no' => $val['sub_order_no'], 'order_type' => $val['order_type'], 'unshipped_reason' => $unshipped_reason, 'created_time' => time()]);
                        } else {
                            Db::name('sub_order_extend')->where(['sub_order_no' => $val['sub_order_no']])->update(['unshipped_reason' => $unshipped_reason, 'update_time' => time()]);
                        }
                    }
                }
                //推送处理队列
                if (!empty($dataStr)) {
                    $pushData = array(
                        'exchange_name' => 'orders',
                        'routing_key'   => 'un_ship_edit_period_predict_time',
                        'data'          => $dataStr,
                    );
                    $result   = httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
                    if (!isset($result['error_code']) || $result['error_code'] != 0) {
                        $this->throwError('推送期数预计发货时间修改队列数据失败');
                    }
                }
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                $weChatService->weChatSendText($params['originator_userid'], '未发货提醒审批流处理失败!' . $e->getMessage());
                $this->throwError($e->getMessage());
            }
        }
        return true;
    }
}