<?php
/**
 * 代码质量检查工具
 * 用于检查收款退款统计接口的代码质量
 */

class CodeQualityChecker
{
    private $basePath;
    private $issues = [];
    
    public function __construct($basePath)
    {
        $this->basePath = $basePath;
    }
    
    /**
     * 运行所有检查
     */
    public function runAllChecks()
    {
        echo "🔍 开始代码质量检查...\n\n";
        
        $this->checkSyntax();
        $this->checkComplexity();
        $this->checkSecurity();
        $this->checkPerformance();
        $this->checkBestPractices();
        
        $this->generateReport();
    }
    
    /**
     * 语法检查
     */
    private function checkSyntax()
    {
        echo "📝 检查语法错误...\n";
        
        $files = [
            'app/service/PaymentStatistics.php',
            'app/controller/PaymentStatistics.php',
            'app/model/PaymentStatisticsLog.php',
            'app/model/DailyPaymentStatistics.php'
        ];
        
        foreach ($files as $file) {
            $fullPath = $this->basePath . '/' . $file;
            if (!file_exists($fullPath)) {
                $this->addIssue('error', "文件不存在: $file");
                continue;
            }
            
            $output = shell_exec("php -l \"$fullPath\" 2>&1");
            if (strpos($output, 'No syntax errors') === false) {
                $this->addIssue('error', "语法错误: $file - $output");
            }
        }
        
        echo "✅ 语法检查完成\n\n";
    }
    
    /**
     * 复杂度检查
     */
    private function checkComplexity()
    {
        echo "🧮 检查代码复杂度...\n";
        
        $serviceFile = $this->basePath . '/app/service/PaymentStatistics.php';
        if (file_exists($serviceFile)) {
            $content = file_get_contents($serviceFile);
            
            // 检查方法长度
            preg_match_all('/function\s+(\w+)\s*\([^)]*\)\s*{/', $content, $matches, PREG_OFFSET_CAPTURE);
            foreach ($matches[1] as $index => $match) {
                $methodName = $match[0];
                $startPos = $matches[0][$index][1];
                
                // 简单的大括号匹配来计算方法长度
                $braceCount = 0;
                $lines = 1;
                for ($i = $startPos; $i < strlen($content); $i++) {
                    if ($content[$i] === '{') $braceCount++;
                    if ($content[$i] === '}') $braceCount--;
                    if ($content[$i] === "\n") $lines++;
                    if ($braceCount === 0) break;
                }
                
                if ($lines > 50) {
                    $this->addIssue('warning', "方法过长: $methodName ($lines 行)");
                }
            }
            
            // 检查嵌套深度
            $maxNesting = $this->calculateMaxNesting($content);
            if ($maxNesting > 4) {
                $this->addIssue('warning', "嵌套层级过深: $maxNesting 层");
            }
        }
        
        echo "✅ 复杂度检查完成\n\n";
    }
    
    /**
     * 安全检查
     */
    private function checkSecurity()
    {
        echo "🔒 检查安全问题...\n";
        
        $files = glob($this->basePath . '/app/**/*.php');
        foreach ($files as $file) {
            $content = file_get_contents($file);
            
            // 检查SQL注入风险
            if (preg_match('/\$.*\s*\.\s*["\'].*SELECT|INSERT|UPDATE|DELETE/i', $content)) {
                $this->addIssue('high', "可能的SQL注入风险: " . basename($file));
            }
            
            // 检查XSS风险
            if (preg_match('/echo\s+\$_[GET|POST|REQUEST]/i', $content)) {
                $this->addIssue('high', "可能的XSS风险: " . basename($file));
            }
            
            // 检查敏感信息泄露
            if (preg_match('/(password|secret|key)\s*=\s*["\'][^"\']+["\']/i', $content)) {
                $this->addIssue('medium', "可能的敏感信息泄露: " . basename($file));
            }
        }
        
        echo "✅ 安全检查完成\n\n";
    }
    
    /**
     * 性能检查
     */
    private function checkPerformance()
    {
        echo "⚡ 检查性能问题...\n";
        
        $serviceFile = $this->basePath . '/app/service/PaymentStatistics.php';
        if (file_exists($serviceFile)) {
            $content = file_get_contents($serviceFile);
            
            // 检查循环中的数据库查询
            if (preg_match('/foreach.*{[^}]*Db::|for.*{[^}]*Db::/s', $content)) {
                $this->addIssue('warning', "循环中存在数据库查询，考虑批量操作");
            }
            
            // 检查N+1查询问题
            if (preg_match('/foreach.*{[^}]*->find\(\)|for.*{[^}]*->find\(\)/s', $content)) {
                $this->addIssue('warning', "可能存在N+1查询问题");
            }
            
            // 检查内存使用
            if (preg_match('/array_merge.*foreach|.*\[\]\s*=.*foreach/s', $content)) {
                $this->addIssue('info', "注意大数组操作的内存使用");
            }
        }
        
        echo "✅ 性能检查完成\n\n";
    }
    
    /**
     * 最佳实践检查
     */
    private function checkBestPractices()
    {
        echo "📋 检查最佳实践...\n";
        
        $files = [
            'app/service/PaymentStatistics.php',
            'app/controller/PaymentStatistics.php'
        ];
        
        foreach ($files as $file) {
            $fullPath = $this->basePath . '/' . $file;
            if (!file_exists($fullPath)) continue;
            
            $content = file_get_contents($fullPath);
            
            // 检查注释覆盖率
            $totalLines = substr_count($content, "\n");
            $commentLines = substr_count($content, "//") + substr_count($content, "/*") + substr_count($content, "*");
            $commentRatio = $totalLines > 0 ? ($commentLines / $totalLines) * 100 : 0;
            
            if ($commentRatio < 10) {
                $this->addIssue('info', "注释覆盖率较低: " . basename($file) . " (" . round($commentRatio, 1) . "%)");
            }
            
            // 检查异常处理
            $tryCount = substr_count($content, 'try {');
            $catchCount = substr_count($content, 'catch (');
            if ($tryCount > $catchCount) {
                $this->addIssue('warning', "存在未处理的异常: " . basename($file));
            }
            
            // 检查日志记录
            if (!preg_match('/Log::|->log\(/', $content)) {
                $this->addIssue('info', "缺少日志记录: " . basename($file));
            }
        }
        
        echo "✅ 最佳实践检查完成\n\n";
    }
    
    /**
     * 计算最大嵌套深度
     */
    private function calculateMaxNesting($content)
    {
        $maxNesting = 0;
        $currentNesting = 0;
        
        $tokens = token_get_all("<?php " . $content);
        foreach ($tokens as $token) {
            if (is_array($token)) {
                switch ($token[0]) {
                    case T_IF:
                    case T_FOREACH:
                    case T_FOR:
                    case T_WHILE:
                    case T_SWITCH:
                        $currentNesting++;
                        $maxNesting = max($maxNesting, $currentNesting);
                        break;
                }
            } else {
                if ($token === '}') {
                    $currentNesting = max(0, $currentNesting - 1);
                }
            }
        }
        
        return $maxNesting;
    }
    
    /**
     * 添加问题
     */
    private function addIssue($level, $message)
    {
        $this->issues[] = [
            'level' => $level,
            'message' => $message,
            'time' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * 生成报告
     */
    private function generateReport()
    {
        echo "📊 代码质量报告\n";
        echo str_repeat("=", 50) . "\n\n";
        
        $levelCounts = [
            'error' => 0,
            'high' => 0,
            'warning' => 0,
            'medium' => 0,
            'info' => 0
        ];
        
        foreach ($this->issues as $issue) {
            $levelCounts[$issue['level']]++;
        }
        
        // 统计信息
        echo "问题统计:\n";
        echo "🔴 错误: " . $levelCounts['error'] . "\n";
        echo "🟠 高风险: " . $levelCounts['high'] . "\n";
        echo "🟡 警告: " . $levelCounts['warning'] . "\n";
        echo "🟡 中风险: " . $levelCounts['medium'] . "\n";
        echo "🔵 信息: " . $levelCounts['info'] . "\n\n";
        
        // 详细问题列表
        if (!empty($this->issues)) {
            echo "详细问题:\n";
            foreach ($this->issues as $issue) {
                $icon = $this->getLevelIcon($issue['level']);
                echo "$icon {$issue['message']}\n";
            }
        } else {
            echo "🎉 恭喜！没有发现代码质量问题。\n";
        }
        
        // 质量评分
        $totalIssues = count($this->issues);
        $criticalIssues = $levelCounts['error'] + $levelCounts['high'];
        
        if ($criticalIssues > 0) {
            $score = max(0, 60 - $criticalIssues * 10);
        } elseif ($totalIssues > 10) {
            $score = max(70, 90 - ($totalIssues - 10) * 2);
        } else {
            $score = max(80, 100 - $totalIssues * 2);
        }
        
        echo "\n📈 代码质量评分: $score/100\n";
        
        if ($score >= 90) {
            echo "🌟 优秀！代码质量很高。\n";
        } elseif ($score >= 80) {
            echo "👍 良好！有一些小问题需要注意。\n";
        } elseif ($score >= 70) {
            echo "⚠️  一般！建议修复一些问题。\n";
        } else {
            echo "❌ 需要改进！存在较多问题需要修复。\n";
        }
    }
    
    /**
     * 获取级别图标
     */
    private function getLevelIcon($level)
    {
        $icons = [
            'error' => '🔴',
            'high' => '🟠',
            'warning' => '🟡',
            'medium' => '🟡',
            'info' => '🔵'
        ];
        
        return $icons[$level] ?? '⚪';
    }
}

// 主执行
if (php_sapi_name() === 'cli') {
    $basePath = dirname(__DIR__);
    $checker = new CodeQualityChecker($basePath);
    $checker->runAllChecks();
} else {
    echo "请在命令行环境下运行此脚本\n";
}
?>
