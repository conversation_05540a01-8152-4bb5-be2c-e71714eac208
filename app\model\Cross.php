<?php


namespace app\model;


use app\service\elasticsearch\ElasticSearchService;
use app\service\es\Es;
use think\facade\Db;
use think\Model;

class Cross extends Model
{
    protected $name = 'cross_order';

    protected function getTableName()
    {
        return Db::name($this->name);
    }

    /**
     * Description:获取订单列表
     * Author: zrc
     * Date: 2021/8/2
     * Time: 20:37
     * @param $params
     * @param int $page
     * @param int $limit
     */
    public function getOrderList($params, $page = 1, $limit = 10)
    {
        $where[] = ['order_type' => 2];
        $range   = [];
        if (!empty($params['order_no'])) {
            if (strpos($params['order_no'], 'VHM') !== false || strpos($params['order_no'], 'WYM') !== false) {
                $where[] = ['main_order_no' => $params['order_no']];
            } else {
                $where[] = ['sub_order_no' => $params['order_no']];
            }
        }
        if (isset($params['order_status']) && is_numeric($params['order_status'])) {
            $where[] = ['sub_order_status' => $params['order_status']];
        }
        if (isset($params['refund_status']) && is_numeric($params['refund_status'])) {
            $where[] = ['refund_status' => $params['refund_status']];
        }
        if (!empty($params['period'])) {
            $where[] = ['period' => $params['period']];
        }
        if (!empty($params['title'])) {
            $where[] = ['title' => $params['title']];
        }
        if (!empty($params['nickname'])) {
            $where[] = ['nickname' => $params['nickname']];
        }
        if (!empty($params['consignee'])) {
            //收件人加密查询
            $encrypt             = cryptionDeal(1, [$params['consignee']], '15736175219', '宗仁川');
            $params['consignee'] = isset($encrypt[$params['consignee']]) ? $encrypt[$params['consignee']] : '';
            $where[]             = ['consignee' => $params['consignee']];
        }
        if (!empty($params['consignee_phone'])) {
            //收件人加密查询
            $encrypt                   = cryptionDeal(1, [$params['consignee_phone']], '15736175219', '宗仁川');
            $params['consignee_phone'] = isset($encrypt[$params['consignee_phone']]) ? $encrypt[$params['consignee_phone']] : '';
            $where[]                   = ['consignee_phone' => $params['consignee_phone']];
        }
        if (isset($params['express_type']) && is_numeric($params['express_type'])) {
            $where[] = ['express_type' => $params['express_type']];
        }
        if (!empty($params['express_number'])) {
            $where[] = ['express_number' => $params['express_number']];
        }
        if (isset($params['order_from']) && is_numeric($params['order_from'])) {
            $where[] = ['order_from' => $params['order_from']];
        }
        if (isset($params['payment_method']) && is_numeric($params['payment_method'])) {
            $where[] = ['payment_method' => $params['payment_method']];
        }
        if (isset($params['is_ts']) && is_numeric($params['is_ts'])) {
            $where[] = ['is_ts' => $params['is_ts']];
        }
        if (isset($params['invoice_progress']) && is_numeric($params['invoice_progress'])) {
            $where[] = ['invoice_progress' => $params['invoice_progress']];
        }
        if (!empty($params['stime'])) {
            $range[] = ['created_time' => ['gte' => $params['stime']]];
        }
        if (!empty($params['etime'])) {
            $range[] = ['created_time' => ['lte' => $params['etime']]];
        }
        $order           = [['id' => 'desc']];
        $es              = new ElasticSearchService();
        $arr             = array(
            'index' => ['orders'],
            'match' => $where,
            'range' => $range,
            'page'  => $page,
            'limit' => $limit,
            'sort'  => $order
        );
        $data            = $es->getDocumentList($arr);
        $totalNum        = $data['total']['value'];
        $result['list']  = $data['data'];
        $result['total'] = $totalNum;
        return $result;
    }

    /**
     * Description:获取订单详情
     * Author: zrc
     * Date: 2021/8/3
     * Time: 9:59
     * @param $params
     */
    public function getOrderDetail($params)
    {
        $data = esGetOne($params['sub_order_no'], 'vinehoo.orders');
        return $data;
    }

    /**
     * Description:跨境订单推送池列表
     * Author: zrc
     * Date: 2022/4/24
     * Time: 14:33
     * @param $params
     * @param int $page
     * @param int $limit
     * @return mixed
     */
    public function pushPoolList($params, $page = 1, $limit = 10)
    {
        $where[] = ['match' => ['order_type' => 2]];
        $where[] = ['match' => ['sub_order_status' => 1]];
        $where[] = ['match' => ['refund_status' => 0]];
        $where[] = ['bool' => ['should' => [['match_phrase' => ['push_store_status' => 0]], ['match_phrase' => ['push_store_status' => 2]]]]];
        if (!empty($params['main_order_no'])) {
            $where[] = ['match' => ['main_order_no' => $params['main_order_no']]];
        }
        if (!empty($params['period'])) {
            $where[] = ['match' => ['period' => $params['period']]];
        }
        if (!empty($params['title'])) {
            $where[] = ['match' => ['title' => $params['title']]];
        }
        if (isset($params['is_ts']) && is_numeric($params['is_ts'])) {
            $where[] = ['match' => ['is_ts' => $params['is_ts']]];
        }
        if (isset($params['push_store_status']) && is_numeric($params['push_store_status'])) {
            $where[] = ['match' => ['push_store_status' => $params['push_store_status']]];
        }
        if (isset($params['is_auto_push']) && is_numeric($params['is_auto_push'])) {
            $where[] = ['match' => ['is_auto_push' => $params['is_auto_push']]];
        }
        $data = esGetList('vinehoo.orders', $where, [['created_time' => 'desc']], $page - 1, $limit);
        $list = [];
        if (isset($data['hits']['hits']) && count($data['hits']['hits']) > 0) {
            foreach ($data['hits']['hits'] as $key => $val) {
                $orderData               = $val['_source'];
                $orderData['banner_img'] = imagePrefix($orderData['banner_img']);
                $list[$key]              = $orderData;
            }
        }
        $totalNum        = isset($data['hits']['total']['value']) ? $data['hits']['total']['value'] : 0;
        $result['list']  = $list;
        $result['total'] = $totalNum;
        return $result;
    }

    /**
     * Description:跨境商品备案信息列表
     * Author: zrc
     * Date: 2022/4/26
     * Time: 10:27
     * @param $params
     * @param int $page
     * @param int $limit
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function goodsRecordInformationList($params, $page = 1, $limit = 10)
    {
        $offset = ($page - 1) * $limit;
        $where  = [];
        if (!empty($params['goods_item_name'])) {
            $where[] = ["goods_item_name", "like", "%{$params['goods_item_name']}%"];
        }
        if (!empty($params['goods_itemno'])) {
            $where[] = ['goods_itemno', '=', $params['goods_itemno']];
        }
        if (!empty($params['hs_code'])) {
            $where[] = ['hs_code', '=', $params['hs_code']];
        }
        $totalNum        = Db::name('cross_record_information')->where($where)->count();
        $lists           = Db::name('cross_record_information')
            ->where($where)
            ->limit($offset, $limit)
            ->order('id desc')
            ->select()->toArray();
        $result['list']  = $lists;
        $result['total'] = $totalNum;
        return $result;
    }

    /**
     * Description:跨境订单推送代发仓记录列表
     * Author: zrc
     * Date: 2022/4/29
     * Time: 14:36
     * @param $params
     * @param int $page
     * @param int $limit
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function pushWarehouseLogList($params, $page = 1, $limit = 10)
    {
        $offset = ($page - 1) * $limit;
        $where  = [];
        if (!empty($params['main_order_no'])) {
            $where[] = ["main_order_no", "=", $params['main_order_no']];
        }
        if (isset($params['store_type']) && is_numeric($params['store_type'])) {
            $where[] = ["store_type", "=", $params['store_type']];
        }
        if (isset($params['push_result']) && is_numeric($params['push_result'])) {
            $where[] = ["push_result", "=", $params['push_result']];
        }
        if (!empty($params['created_stime'])) {
            $where[] = ["created_time", ">", strtotime($params['created_stime'])];
        }
        if (!empty($params['created_etime'])) {
            $where[] = ["created_time", "<", strtotime($params['created_etime'])];
        }
        $totalNum = Db::name('cross_push_warehouse_log')->where($where)->count();
        $lists    = Db::name('cross_push_warehouse_log')
            ->field('id,main_order_no,store_type,push_result,result_msg,created_time')
            ->where($where)
            ->limit($offset, $limit)
            ->order('id desc')
            ->select()->toArray();
        foreach ($lists as &$val) {
            $val['created_time'] = date('Y-m-d H:i:s', $val['created_time']);
        }
        $result['list']  = $lists;
        $result['total'] = $totalNum;
        return $result;
    }

    /**
     * Description:海关申报异常记录列表
     * Author: zrc
     * Date: 2022/4/29
     * Time: 15:40
     * @param $params
     * @param int $page
     * @param int $limit
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function declareRecordList($params, $page = 1, $limit = 10)
    {
        $offset = ($page - 1) * $limit;
        $where  = [];
        if (!empty($params['main_order_no'])) {
            $where[] = ["main_order_no", "=", $params['main_order_no']];
        }
        if (isset($params['store_type']) && is_numeric($params['store_type'])) {
            $where[] = ["store_type", "=", $params['store_type']];
        }
        if (isset($params['abnormal_node']) && is_numeric($params['abnormal_node'])) {
            $where[] = ["abnormal_node", "=", $params['abnormal_node']];
        }
        if (isset($params['customs_status']) && is_numeric($params['customs_status'])) {
            $where[] = ["customs_status", "=", $params['customs_status']];
        }
        if (!empty($params['push_warehouse_stime'])) {
            $where[] = ["push_warehouse_time", ">", strtotime($params['push_warehouse_stime'])];
        }
        if (!empty($params['push_warehouse_etime'])) {
            $where[] = ["push_warehouse_time", "<", strtotime($params['push_warehouse_etime'])];
        }
        $totalNum = Db::name('cross_customs_declare_record')->where($where)->count();
        $lists    = Db::name('cross_customs_declare_record')
            ->where($where)
            ->limit($offset, $limit)
            ->order('id desc')
            ->select()->toArray();
        foreach ($lists as &$val) {
            $val['push_warehouse_time'] = date('Y-m-d H:i:s', $val['push_warehouse_time']);
        }
        $result['list']  = $lists;
        $result['total'] = $totalNum;
        return $result;
    }

    /**
     * Description:支付单记录列表
     * Author: zrc
     * Date: 2022/5/9
     * Time: 14:18
     * @param $params
     * @param int $page
     * @param int $limit
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function declareLogList($params, $page = 1, $limit = 10)
    {
        $offset = ($page - 1) * $limit;
        $where  = [];
        if (!empty($params['main_order_no'])) {
            $where[] = ["main_order_no", "=", $params['main_order_no']];
        }
        $totalNum = Db::name('cross_declare_log')->where($where)->count();
        $lists    = Db::name('cross_declare_log')
            ->field('platform,main_order_no,trade_no,ver_dept,request_status,result_code,request_msg,notify_msg,identity_check,created_time')
            ->where($where)
            ->limit($offset, $limit)
            ->order('id desc')
            ->select()->toArray();
        foreach ($lists as &$val) {
            $val['created_time'] = date('Y-m-d H:i:s', $val['created_time']);
            $val['result_msg']   = $val['platform'] == 3 ? $val['notify_msg'] : $val['request_msg'];
        }
        $result['list']  = $lists;
        $result['total'] = $totalNum;
        return $result;
    }

    /**
     * Description:库存管理列表
     * Author: zrc
     * Date: 2022/9/19
     * Time: 14:01
     * @param $params
     * @param int $page
     * @param int $limit
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function stockManagementList($params, $page = 1, $limit = 10)
    {
        $offset = ($page - 1) * $limit;
        $where  = [];
        if (!empty($params['goods_barcode'])) {
            $where[] = ["goods_barcode", "=", $params['goods_barcode']];
        }
        if (!empty($params['goods_name'])) {
            $where[] = ["goods_name", "like", "%{$params['goods_name']}%"];
        }
        if (!empty($params['supplier_name'])) {
            $where[] = ["supplier_name", "like", "%{$params['supplier_name']}%"];
        }
        if (!empty($params['purchase_time_s'])) {
            $where[] = ["purchase_time", ">=", strtotime($params['purchase_time_s'])];
        }
        if (!empty($params['purchase_time_e'])) {
            $where[] = ["purchase_time", "<", strtotime($params['purchase_time_e'])];
        }
        if (!empty($params['predict_time_s'])) {
            $where[] = ["predict_time", ">=", strtotime($params['predict_time_s'])];
        }
        if (!empty($params['predict_time_e'])) {
            $where[] = ["predict_time", "<", strtotime($params['predict_time_e'])];
        }
        if (!empty($params['last_takedown_time_s'])) {
            $where[] = ["last_takedown_time", ">=", strtotime($params['last_takedown_time_s'])];
        }
        if (!empty($params['last_takedown_time_e'])) {
            $where[] = ["last_takedown_time", "<", strtotime($params['last_takedown_time_e'])];
        }
        if (isset($params['goods_status']) && is_numeric($params['goods_status'])) {
            $where[] = ["goods_status", "=", $params['goods_status']];
        }
        if (isset($params['store_type']) && is_numeric($params['store_type'])) {
            $where[] = ["store_type", "=", $params['store_type']];
        }
        if (!empty($params['pickup_warehouse'])) {
            $where[] = ["pickup_warehouse", "like", "%{$params['pickup_warehouse']}%"];
        }
        $where[] = ["is_delete", "=", 0];
        $sort    = 'created_time desc';
        if (isset($params['sort']) && is_numeric($params['sort'])) {
            switch ($params['sort']) {
                case 1:
                    $sort = 'enter_nums desc';
                    break;
                case 2:
                    $sort = 'enter_nums asc';
                    break;
                case 3:
                    $sort = 'sold_nums desc';
                    break;
                case 4:
                    $sort = 'sold_nums asc';
                    break;
                case 5:
                    $sort = 'ts_nums desc';
                    break;
                case 6:
                    $sort = 'ts_nums asc';
                    break;
                case 7:
                    $sort = 'available_nums desc';
                    break;
                case 8:
                    $sort = 'available_nums asc';
                    break;
                case 9:
                    $sort = 'defective_nums desc';
                    break;
                case 10:
                    $sort = 'defective_nums asc';
                    break;
                case 11:
                    $sort = 'real_nums desc';
                    break;
                case 12:
                    $sort = 'real_nums asc';
                    break;
            }
        }
        if (!empty($sort)) {
            $sort = $sort . ' created_time desc';
        }
        $totalNum     = Db::name('cross_inventory')->where($where)->count();
        $lists        = Db::name('cross_inventory')->where($where)->limit($offset, $limit)->order($sort)->select()->toArray();
        $admin_id_arr = array_unique(array_column($lists, 'operator'));
        $admin_id_str = implode(',', $admin_id_arr);
        $adminInfo    = httpGet(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info', ['admin_id' => $admin_id_str, 'field' => 'realname']);

        $goods_barcodes           = array_values(array_unique(array_column($lists, 'goods_barcode')));
        $has_short_periods        = Es::name(Es::PERIODS)->where([
            ['short_code', 'in', $goods_barcodes]
        ])->field('id,short_code')->order(['id' => 'desc'])->select()->toArray();
        $goods_barcode_period_ids = [];
        foreach ($has_short_periods as $has_short_period) {
            foreach ($has_short_period['short_code'] as $goods_barcode) {
                $goods_barcode_period_ids[$goods_barcode][] = $has_short_period['id'];
            }
        }
        $pids                  = Db::table('vh_wiki.vh_products')->where('short_code', 'in', $goods_barcodes)->column('short_code', 'id');
        $has_short_pkgs        = Db::table('vh_commodities.vh_periods_cross_set')
            ->where('period_id', 'in', array_column($has_short_periods, 'id'))
            ->where(function ($query) use ($pids) {
                foreach ($pids as $pid => $sc) {
                    $query->whereOr('associated_products', 'LIKE', "%:{$pid},%");
                }
            })->column('associated_products', 'id');
        $goods_barcode_pkg_ids = [];
        foreach ($has_short_pkgs as $pkg_id => $associated_products) {
            $associated_products = json_decode($associated_products, true);
            foreach ($associated_products as $ap_info) {
                if (!is_array($ap_info['product_id']) && !empty($pids[$ap_info['product_id']])) {
                    if (!empty($ap_info['sub_package_id']))
                        $goods_barcode_pkg_ids[$pids[$ap_info['product_id']]][] = $ap_info['sub_package_id'];
                    $goods_barcode_pkg_ids[$pids[$ap_info['product_id']]][] = $pkg_id;
                }
            }
        }

        $inv_nums     = Db::name('cross_excess')->where('log_id', 'in', array_column($lists, 'id'))->where('status', 1)->group('log_id')->column('SUM(num)', 'log_id');
        $excess       = Db::name('cross_excess')->where('status', 1)->where('goods_barcode', 'in', $goods_barcodes)->column('id,goods_barcode,store_type,num');
        $excess_group = [];
        foreach ($excess as $exces_item) {
            $excess_group[$exces_item['goods_barcode']][$exces_item['store_type']][] = $exces_item['num'];
        }
        $excess_nums = [];
        foreach ($excess_group as $egik => $egiv) {
            foreach ($egiv as $egisk => $egisv) {
                $excess_nums[$egik][$egisk] = round(array_sum($egisv));
            }
        }

        $temp_quantitys = [];
        $wh_list        = range(1, 2);
        foreach ($goods_barcode_period_ids as $i_goods_barcode => $i_goods_barcode_period_id) {
            if (in_array($i_goods_barcode, $goods_barcodes)) {

                $ts_where = $wd_where = [
                    ['period', 'in', $i_goods_barcode_period_id], //简码包含的全部期数ID
                    ['package_id', 'in', $goods_barcode_pkg_ids[$i_goods_barcode] ?? []], //简码包含的全部套餐ID
                    ['is_delete', '=', 0], //是否删除 0-正常 1-已删除
                    ['order_type', '=', 2], //是否删除 2-跨境
                    ['store_type', 'in', $wh_list],
                    ['sub_order_status', '=', 1], //订单状态=1 订单状态：0-待支付 1-已支付 2-已发货 3-已完成 4-已取消
                    ['refund_status', '=', 0], //退款状态=0  0-未退款 1-退款中 2-退款成功 3-退款失败
                ];

                $ts_where[] = ['is_ts', '=', 1]; //是否暂存：0否 1是
                $ts_where[] = ['push_store_status', '!=', 1]; //代发仓推送状态：0-未推送 1-推送成功 2-推送失败 3-不推送
                $wd_where[] = ['push_store_status', '=', 1]; //代发仓推送状态：0-未推送 1-推送成功 2-推送失败 3-不推送

                $temp_orders  = Es::name(Es::ORDERS)->where($ts_where)->field('id,store_type,order_qty')->select()->toArray();
                $wait_deliver = Es::name(Es::ORDERS)->where($wd_where)->field('id,store_type,order_qty')->select()->toArray();

                $temp_order_group   = array_group($temp_orders, 'store_type');
                $wait_deliver_group = array_group($wait_deliver, 'store_type');

                foreach ($wh_list as $wh_id) {
                    //代发仓：1-古斯缇 2-南沙仓
                    $temp_quantitys[$i_goods_barcode][$wh_id][0] = array_sum(array_column($wait_deliver_group[$wh_id] ?? [], 'order_qty')); //已支付未发货
                    $temp_quantitys[$i_goods_barcode][$wh_id][1] = array_sum(array_column($temp_order_group[$wh_id] ?? [], 'order_qty')); //暂存
                }
            }
        }

        #推单时需要【实物库存-次品数量-暂存数量-已推送未发货数量】
        $entry_nums             = [];
        $temp_cross_inventorys  = Db::name('cross_inventory')->where([
            ['goods_barcode', 'in', $goods_barcodes],
            ['store_type', 'in', $wh_list],
            ['is_delete', '=', 0]
        ])->column('id,goods_barcode,store_type,real_nums,defective_nums');
        $group_cross_inventorys = array_group($temp_cross_inventorys, 'goods_barcode');
        foreach ($group_cross_inventorys as &$group_cross_inventory_format) {
            $group_cross_inventory_format = array_group($group_cross_inventory_format, 'store_type');
        }

        foreach ($group_cross_inventorys as $b_code => $group_cross_inventory) {
            foreach ($wh_list as $wh_id) {
                $wh_gci                        = $group_cross_inventory[$wh_id] ?? [];
//                $entry_nums[$b_code][$wh_id] = bcsub(array_sum(array_column($wh_gci, 'real_nums')), array_sum(array_column($wh_gci, 'defective_nums'))); //实物库存 - 次品库存之和
                $entry_nums[$b_code][$wh_id] = bcsub(array_sum(array_column($wh_gci, 'real_nums')), 0); //实物库存 - 次品库存之和
            }
        }

        foreach ($lists as $key => $val) {
            $item_excess_nums = $excess_nums[$val['goods_barcode']][$val['store_type']] ?? 0;
            $inv_num     = $inv_nums[$val['id']] ?? 0;

            $lists[$key]['excess_nums']    = $item_excess_nums;
            $lists[$key]['available_nums'] = bcsub($lists[$key]['available_nums'], $inv_num);

            //是否暂存：0否 1是
            $i_temp_quantity        = $temp_quantitys[$val['goods_barcode']][$val['store_type']][1] ?? 0; //暂存数
            $i_normal_temp_quantity = $temp_quantitys[$val['goods_barcode']][$val['store_type']][0] ?? 0; //已推送未发货数量
            $i_quantity        = bcadd(strval($i_temp_quantity), strval($i_normal_temp_quantity));

            $lists[$key]['temp_quantity']        = $i_temp_quantity;
            $lists[$key]['normal_temp_quantity'] = $i_normal_temp_quantity;
            $lists[$key]['pushable']             = bcsub(strval($entry_nums[$val['goods_barcode']][$val['store_type']] ?? 0), strval($i_quantity)); //可推送数量

            $lists[$key]['entry_days']       = $val['entry_time'] == 0 ? 0 : bcdiv(bcsub(time(), $val['entry_time']),86400 );
            $lists[$key]['entry_time']       = $val['entry_time'] == 0 ? '-' : date('Y-m-d', $val['entry_time']);
            $lists[$key]['last_out_time']       = $val['last_out_time'] == 0 ? '-' : date('Y-m-d', $val['last_out_time']);

            $lists[$key]['predict_time']       = $val['predict_time'] == 0 ? '-' : date('Y-m-d', $val['predict_time']);
            $lists[$key]['last_takedown_time'] = $val['last_takedown_time'] == 0 ? '-' : date('Y-m-d H:i:s', $val['last_takedown_time']);
            $lists[$key]['update_time']        = $val['update_time'] == 0 ? '-' : date('Y-m-d H:i:s', $val['update_time']);
            $lists[$key]['created_time']       = date('Y-m-d H:i:s', $val['created_time']);
            $lists[$key]['purchase_time']      = date('Y-m-d', $val['purchase_time']);
            $lists[$key]['operator']           = isset($adminInfo['data'][$val['operator']]) ? $adminInfo['data'][$val['operator']] : '系统';
        }
        $result['list']  = $lists;
        $result['total'] = $totalNum;
        return $result;
    }
}