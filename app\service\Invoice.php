<?php

namespace app\service;

use app\BaseService;
use app\model\Invoice as InvoiceModel;
use app\service\elasticsearch\ElasticSearchService;
use think\exception\ValidateException;
use think\facade\Db;

class Invoice extends BaseService
{

    /**
     * 开票列表
     * @param $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function list($params)
    {
        $err_ret       = ['list' => [], 'total' => 0];
        $p_admin_id    = request()->header('vinehoo_uid');
        $preparedsInfo = (new self)->httpGet(env('ITEM.ERP_PREPARED_URL') . '/prepared/v3/prepareds/detail', ['prepared_uid' => $p_admin_id]);
        if (!isset($preparedsInfo['error_code']) || $preparedsInfo['error_code'] != 0 || empty($preparedsInfo['data'])) return $err_ret;
        if ($preparedsInfo['data']['status'] == 0) return $err_ret;

        //查询条件
        $where = function ($query) use ($params, $preparedsInfo) {
            //客户名称
            if (isset($params['customer_name']) && strlen($params['customer_name']) > 0) {
                $query->where('customer_name', 'like', '%' . $params['customer_name'] . '%');
            }

            //单据编号
            if (isset($params['bill_no']) && strlen($params['bill_no']) > 0) {
                $query->where('bill_no', '=', $params['bill_no']);
            }

            //开票公司
            if (isset($params['incoic_form']) && strlen($params['incoic_form']) > 0) {
                $query->where('incoic_form', '=', $params['incoic_form']);
            }

            //发票状态
            if (isset($params['invoic_status']) && strlen($params['invoic_status']) > 0) {
                $query->where('invoic_status', '=', $params['invoic_status']);
            }


            if (isset($params['start_time']) && strlen($params['start_time']) > 0) {
                $query->where('create_time', '>=', strtotime($params['start_time']));
            }

            if (isset($params['end_time']) && strlen($params['end_time']) > 0) {
                $query->where('create_time', '<=', strtotime($params['end_time']));
            }

            if (isset($params['approver_start_time']) && strlen($params['approver_start_time']) > 0) {
                $query->where('approver_time', '>=', strtotime($params['approver_start_time']));
            }

            if (isset($params['approver_end_time']) && strlen($params['approver_end_time']) > 0) {
                $query->where('approver_time', '<=', strtotime($params['approver_end_time']));
            }

            if (isset($params['approver']) && strlen($params['approver']) > 0) {
                $query->where('approver', 'like', "%" . trim($params['approver']) . "%");
            }

            if ($preparedsInfo['data']['is_all_inquire_prepared'] == 0) {
                $prepared_inquire_uid = array_column($preparedsInfo['data']['prepared_inquire_items'], 'uid');
                $query->where('operator_id', 'in', $prepared_inquire_uid);
            }
            if (isset($params['order_no']) && strlen($params['order_no']) > 0) {
                $query->where('order_no', 'like', "%" . trim($params['order_no']) . "%");
            }

            if (isset($params['invoice_no']) && strlen($params['invoice_no']) > 0) {
                $query->where('invoice_no', '=', trim($params['invoice_no']));
            }

        };

        $page      = $params['page'] ?? 1;
        $limit     = $params['limit'] ?? 10;
        $pagestrat = ($page - 1) * $limit;
        $result    = \app\model\Invoice::where($where)->limit($pagestrat, $limit)->order('id', 'desc')->select()->toArray();
        $result    = array_map(function ($v) {
            $v['detail_json'] = array_values((array)$v['detail_json']);
            return $v;
        }, $result);
        $total     = \app\model\Invoice::where($where)->count();

        return ['list' => $result, 'total' => $total];
    }

    /**
     * 更新数据
     * @param $params
     * @return bool
     */
    public static function save($params)
    {

        Db::startTrans();
        try {
            unset($params['create_time']);
            unset($params['update_time']);
            unset($params['bill_no']);
            $genre = $params['recipient_type'];//发票抬头类型 1个人 2公司
            if ($genre == 1 && $params['invoic_type'] == 2) {//个人不能开专票
                throw new ValidateException("个人不能开专票");
            }
            //发票名称处理
            if (isset($params['detail_json'])) {
                //在创建发票前,添加退货/退款验证
                foreach ($params['detail_json'] as $item) {
                    // 1. 获取原订单信息和退货信息
                    $order_no = $item['order_no'];

                    // 获取退货记录
                    $refunds = Db::name('sales_return')
                        ->where('sub_order_no', $order_no)
                        ->where('dingtalk_status', '<>', 3) // 排除已拒绝的退款
                        ->select()
                        ->toArray();

                    $original_qty   = 0;
                    // 2. 获取原订单金额和商品数量
                    if ($params['sale_bill_type'] == 1) { // 酒云线上
                        $es    = new ElasticSearchService();
                        $order = $es->getDocumentList([
                            'index'  => ['orders'],
                            'match'  => [['_id' => $order_no]],
                            'source' => ['cash_amount','payment_amount', 'items_info']
                        ])['data'][0];

                        $payment_amount = $order['cash_amount'] ?? ($order['payment_amount'] ?? 0);
                        foreach ($order['items_info'] as $oi) {
                            if ($oi['short_code'] == $item['short_code']) {
                                $original_qty += $oi['nums'];
                            }
                        }

                    } else if ($params['sale_bill_type'] == 2) { // 线下
                        $order          = Db::name("offline_order")
                            ->where('sub_order_no', $order_no)
                            ->field('payment_amount,items_info')
                            ->find();
                        $payment_amount = $order['payment_amount'];

                        $goods_list = explode(',', $order['items_info']);
                        foreach ($goods_list as $goods) {
                            $goods_info = explode('*', $goods);
                            if ($goods_info[1] == $item['short_code']) {
                                $original_qty += intval($goods_info[2]);
                            }
                        }

                    } else if ($params['sale_bill_type'] == 3) { // 三方
                        $order          = Db::name("tripartite_order")
                            ->where('sub_order_no', $order_no)
                            ->field('payment_amount,items_info,order_qty')
                            ->find();
                        $payment_amount = $order['payment_amount'];

                        $goods_list = explode(',', $order['items_info']);
                        foreach ($goods_list as $goods) {
                            $goods_info = explode('*', $goods);
                            if ($goods_info[0] == $item['short_code']) {
                                $original_qty += intval($goods_info[1]) * $order['order_qty'];
                            }
                        }
                    }

                    // 3. 计算已退款金额和退货数量
                    $refund_amount = 0;
                    $refund_qty    = 0;
                    foreach ($refunds as $refund) {
                        $refund_amount += $refund['return_amount'];
                        $refund_detail = json_decode($refund['detail_json'], true);
                        foreach ($refund_detail as $rd) {
                            if ($rd['short_code'] == $item['short_code']) {
                                $refund_qty += $rd['nums'];
                            }
                        }
                    }

                    // 3.1 计算已开票的数量和金额
                    $invoiced_data = Db::name('invoice')
                        ->where('invoic_status', 'in', [1,2]) // 开票中,已开票
                        ->where('order_no', 'like', '%'.$order_no.'%') // 模糊匹配订单号
                        ->select()
                        ->toArray();

                    $invoiced_amount = 0; // 已开票金额
                    $invoiced_qty = 0;    // 已开票数量

                    // 如果是修改,排除当前发票
                    if(isset($params['id'])) {
                        $invoiced_data = array_filter($invoiced_data, function($item) use ($params) {
                            return $item['id'] != $params['id'];
                        });
                    }

                    // 计算已开票数量和金额
                    foreach($invoiced_data as $invoice) {
                        $detail_json = json_decode($invoice['detail_json'], true);
                        foreach($detail_json as $detail) {
                            if($detail['short_code'] == $item['short_code']) {
                                $invoiced_qty += $detail['nums'];
                                $invoiced_amount += $detail['tax_total_price'];
                            }
                        }
                    }

                    // 验证金额和数量
                    $total_available_amount = bcsub($payment_amount, bcadd($refund_amount, $invoiced_amount, 2), 2);
                    if($item['tax_total_price'] > $total_available_amount) {
                        throw new ValidateException("商品{$item['short_code']}开票金额超过可开票金额,可开票金额为:{$total_available_amount}");
                    }

                    // 1. 首先计算相同简码商品的总申请数量
                    $total_requested_qty = 0;
                    foreach($params['detail_json'] as $detail_item) {
                        if($detail_item['short_code'] == $item['short_code'] && $detail_item['order_no'] == $item['order_no']) {
                            $total_requested_qty += $detail_item['nums'];
                        }
                    }

                    // 2. 与可开票数量比较
                    $total_available_qty = $original_qty - $refund_qty - $invoiced_qty;

                    if($total_requested_qty > $total_available_qty) {
                        throw new ValidateException("商品{$item['short_code']}开票数量超过可开票数量,实际申请:{$total_requested_qty},可开票数量为:{$total_available_qty}");
                    }
                }

                foreach ($params['detail_json'] as &$val) {
                    $val['invoice_name'] = Db::table('vh_wiki.vh_products')->where(['short_code' => $val['short_code']])->value('invoice_name');
                    if (empty($val['invoice_name'])) $val['invoice_name'] = $val['cn_product_name'];
                }
            }
            if (isset($params['id'])) {
                unset($params['operator_id']);
                unset($params['operator_name']);
                $where                   = ['id' => $params['id']];
                $params['update_time']   = time();
                $params['order_no']      = implode(',', array_column($params['detail_json'], 'order_no'));
                $params['short_codes']   = implode(',', array_column($params['detail_json'], 'short_code'));
                $params['invoic_status'] = 1;
                $params['approver_id']   = 0;
                $params['approver']      = '';
                $params['callbackmsg']   = '';
                $Invoice                 = InvoiceModel::where($where)->save($params);
                //修改订单状态 开票中
                if ($params['sale_bill_type'] == 1) {//酒云线上
                    $orders           = $params['order_no'];
                    $invoice_progress = 1;
                    self::saveonlineorderInvoiceProgress($orders, $invoice_progress);
                } else if ($params['sale_bill_type'] == 2) {//线下 vh_offline_order
                    $sub_order_no = $params['order_no'];
                    Db::name('offline_order')->where('sub_order_no', 'in', $sub_order_no)->update(['invoice_progress' => 1]);
                } else if ($params['sale_bill_type'] == 3) {//三方 vh_tripartite_orde
                    $sub_order_no = $params['order_no'];
                    Db::name('tripartite_order')->where('sub_order_no', 'in', $sub_order_no)->update(['invoice_progress' => 1]);
                }
            } else {
                $params['bill_no']       = self::getBillNo();
                $params['invoic_status'] = 1;
                $params['create_time']   = time();
                $params['update_time']   = time();
                $params['order_no']      = implode(',', array_column($params['detail_json'], 'order_no'));
                $params['short_codes']   = implode(',', array_column($params['detail_json'], 'short_code'));
                //验证已存在单据
                $order_no_arr = [];
//                foreach ($params['detail_json'] as $v) {
//                    array_push($order_no_arr, "find_in_set('" . $v['order_no'] . "',order_no)");
//                }
//                $user = Db::name('invoice')->where('invoic_status', '<>', 4)->whereRaw(implode(' or ', $order_no_arr))->find();
//                if ($user) throw new ValidateException("单据编号：" . $user['bill_no'] . "订单已存在." . $user['order_no'] . ",请核对后再申请");
                $Invoice = InvoiceModel::create($params);
                //修改订单状态 开票中
                if ($params['sale_bill_type'] == 1) {//酒云线上
                    $orders           = $params['order_no'];
                    $invoice_progress = 1;
                    self::saveonlineorderInvoiceProgress($orders, $invoice_progress);
                } else if ($params['sale_bill_type'] == 2) {//线下 vh_offline_order
                    $sub_order_no = $params['order_no'];
                    Db::name('offline_order')->where('sub_order_no', 'in', $sub_order_no)->update(['invoice_progress' => 1]);
                } else if ($params['sale_bill_type'] == 3) {//三方 vh_tripartite_orde
                    $sub_order_no = $params['order_no'];
                    Db::name('tripartite_order')->where('sub_order_no', 'in', $sub_order_no)->update(['invoice_progress' => 1]);
                }
            }
            Db::commit();
            if (!$Invoice) return false;
            return true;
        } catch (\Exception $exception) {
            Db::rollback();
            throw new ValidateException($exception->getMessage());
        }

    }

    public static function detail($params)
    {
        $where  = ['id' => $params['id']];
        $result = \app\model\Invoice::where($where)->find();
        return $result;
    }

    /**
     * 审核
     * @param $params
     * @return bool|string
     */
    public static function approval($params)
    {
        $time = time();
        Db::startTrans();
        try {
            $where   = ['id' => $params['id']];
            $data    = ['invoic_status' => $params['invoic_status'], 'finance_remarks' => $params['finance_remarks'], 'approver_id' => $params['approver_id'], 'approver' => $params['approver'], 'approver_time' => $time];
            $Invoice = InvoiceModel::where($where)->save($data);
            if ($Invoice) {
                if ($params['invoic_status'] == 2) {//开票已完成

                    //修改第三方订单  vh_tripartite_orde  vh_offline_order
                    $Invoice_data = InvoiceModel::field('id,order_no,sale_bill_type')->find($params['id']);
                    if ($Invoice_data['sale_bill_type'] == 2) {//线下 vh_offline_order
                        $sub_order_no = $Invoice_data['order_no'];
                        Db::name('offline_order')->where('sub_order_no', 'in', $sub_order_no)->update(['invoice_progress' => 2,'invoicing_date' => $time]);
                    } else if ($Invoice_data['sale_bill_type'] == 3) {//三方 vh_tripartite_orde
                        $sub_order_no = $Invoice_data['order_no'];
                        $result       = Db::name('tripartite_order')->where('sub_order_no', 'in', $sub_order_no)->update(['invoice_progress' => 2]);

                    } else if ($Invoice_data['sale_bill_type'] == 1) {//三方 vh_tripartite_orde
                        $sub_order_no = $Invoice_data['order_no'];
                        self::saveonlineorderInvoiceProgress($sub_order_no, 2);
                    } else {
                        throw new ValidateException("销售单据类型有误，请检查");
                    }

                } else if ($params['invoic_status'] == 3) {//开票拒绝
                    //修改第三方订单  vh_tripartite_orde  vh_offline_order
                    $Invoice_data = InvoiceModel::field('id,order_no,sale_bill_type')->find($params['id']);
                    if ($Invoice_data['sale_bill_type'] == 2) {//线下 vh_offline_order
                        $sub_order_no = $Invoice_data['order_no'];
                        Db::name('offline_order')->where('sub_order_no', 'in', $sub_order_no)->update(['invoice_progress' => 3]);
                    } else if ($Invoice_data['sale_bill_type'] == 3) {//三方 vh_tripartite_orde
                        $sub_order_no = $Invoice_data['order_no'];
                        Db::name('tripartite_order')->where('sub_order_no', 'in', $sub_order_no)->update(['invoice_progress' => 3]);

                    } else if ($Invoice_data['sale_bill_type'] == 1) {//三方 vh_tripartite_orde
                        $sub_order_no = $Invoice_data['order_no'];
                        self::saveonlineorderInvoiceProgress($sub_order_no, 3);
                    } else {
                        throw new ValidateException("销售单据类型有误，请检查");
                    }
                } else if ($params['invoic_status'] == 4) {//开票作废
                    //修改第三方订单  vh_tripartite_orde  vh_offline_order
                    $Invoice_data = InvoiceModel::field('id,order_no,sale_bill_type,invoice_no')->find($params['id']);
                    if ($Invoice_data['sale_bill_type'] == 2) {//线下 vh_offline_order
                        $sub_order_no = $Invoice_data['order_no'];
                        Db::name('offline_order')->where('sub_order_no', 'in', $sub_order_no)->update(['invoice_progress' => 0]);
                    } else if ($Invoice_data['sale_bill_type'] == 3) {//三方 vh_tripartite_orde
                        $sub_order_no = $Invoice_data['order_no'];
                        Db::name('tripartite_order')->where('sub_order_no', 'in', $sub_order_no)->update(['invoice_progress' => 0]);

                    } else if ($Invoice_data['sale_bill_type'] == 1) {//三方 vh_tripartite_orde
                        $sub_order_no = $Invoice_data['order_no'];
                        self::saveonlineorderInvoiceProgress($sub_order_no, 0);
                    } else {
                        throw new ValidateException("销售单据类型有误，请检查");
                    }
                    //作废电子开票
                    $result = self::abandonmentByInvoiceCodes($Invoice_data['invoice_no']);
                    if ($result !== true) {
                        throw new ValidateException($result);
                    }
                } else if ($params['invoic_status'] == 1) {//开票中
                    $Invoice = InvoiceModel::where($where)->find()->toArray();
                    //公司 只有科技公司开票
                    $incoic_form = 2;
                    //税收编码
                    $tar_code = [
                        1 => 1030305000000000000,
                        2 => 1030307020000000000,
                        3 => 1030299000000000000,
                        4 => 1050106020000000000,
                        5 => 3070401000000000000,
                        6 => 1060508010000000000,
                        7 => 1030305000000000000,
                    ];

                    $genre = $Invoice['recipient_type'];//发票抬头类型 1个人 2公司
                    if ($genre == 1 && $Invoice['invoic_type'] == 2) {//个人不能开专票
                        throw new ValidateException("个人不能开专票");
                    }
                    $receipt = [
                        "genre"    => $genre,
                        "name"     => $Invoice['invoice_to_detail_name'],//开票人
                        "tel"      => "",//电话
                        "email"    => $genre == 1 ? $Invoice['customer_email'] : $Invoice['invoice_to_detail_email'],//邮箱
                        "taxpayer" => $Invoice['invoice_to_detail_tax_no'],//税号
//                        "company_address"=>$Invoice['invoice_to_detail_addr'],//公司地址
//                        "company_tel"=>$Invoice['invoice_to_detail_phone'],//公司电话
//                        "opening_bank"=>$Invoice['invoice_to_detail_bank_name'],//开户银行
//                        "bank_account"=>$Invoice['invoice_to_detail_bank_no']//开户银行账号
                    ];
                    //获取产品大类

                    $orders              = [];
                    $Invoice_detail_json = (array)$Invoice['detail_json'];

                    foreach ($Invoice_detail_json as $k => $v) {
                        $orders[$v['order_no']][] = $v;
                    }

                    $dpordersdata = [];
                    foreach ($orders as $k => $v) {
                        $products = [];
                        foreach ($v as $vv) {
                            $difference = 0;
                            $is_gift = $vv['is_gift'] ?? 0;
//                            $p_rate = $vv['rate'] ?? 0;
//                            if ($is_gift == 1) {
//                                $vv['rate'] = 1;
//                            } elseif ($p_rate == 'undefined') {
//                                $vv['rate'] = 0;
//                            } else {
//                                $vv['rate'] = $vv['rate'] ?? 0;
//                            }
//                            $vv_rate = bcmul(($vv['rate'] ?? '0'), '-1',8);
//                            $property = $vv_rate == 0 ? 0 : 2;
                            $vv['rate'] = $vv['rate'] ?? 0;
                            if ($is_gift == 1) {
                                $vv['rate'] = 0;
                                $property   = 2;
                            } else {
                                if ($vv['rate'] == 'undefined') {
                                    $vv['rate'] = 0;
                                }
                                $property = $vv['rate'] == 0 ? 0 : 2;
                            }
                            $vv_productsitem = [];
                            //产品大类编码
                            $invoice_goods_code = $tar_code[$vv['product_category']];
                            $productData        = Db::table('vh_wiki.vh_products')->where(['short_code' => $vv['short_code']])->field('tax_rate,tax_code,capacity,is_taxfree')->find();
                            //税率、税收编码处理
                            if (isset($productData['tax_rate']) && $productData['tax_rate'] > 0 && isset($productData['tax_code']) && !empty($productData['tax_code'])) {
                                if (strpos($productData['tax_code'], '-') !== false) {
                                    $productData['tax_code'] = substr($productData['tax_code'], 0, strrpos($productData['tax_code'], '-'));
                                }
                                $tax                = floatval($productData['tax_rate']);
                                $invoice_goods_code = $productData['tax_code'];
                            } else {
                                $tax                = (float)$vv['tax_rate'];
                                $invoice_goods_code = (string)$invoice_goods_code;
                            }
                            $spec_type = $productData['capacity'] ?? '';
                            $is_taxfree = in_array(($productData['is_taxfree'] ?? '0'), ['1', '03']) ? "03" : "0";
                            $vv['tax_total_price'] = $vv['tax_total_price'] + $vv['rate'];
                            $productsitem = [
                                "code"               => $vv['short_code'],
                                "name"               => isset($vv['invoice_name']) ? $vv['invoice_name'] : $vv['cn_product_name'],
                                "price"              => $vv['tax_total_price'] / $vv['nums'],//$vv['tax_unit_price']
                                "num"                => $vv['nums'],
                                "unit"               => $vv['product_unit_name'],
                                "tax"                => ($tax > 0 && $is_taxfree == '03') ? 0 : $tax,
                                "favoured_policy_flag" => $is_taxfree,
                                "spec_type"          => strval($spec_type),
                                "invoice_goods_code" => $invoice_goods_code,
                                'property' => $property
                            ];
                            array_push($products, $productsitem);
                            if($property == 2){
                                $vv_productsitem = $productsitem;
                                $vv_productsitem['property'] = 1;
                                if ($is_gift != 1) {
                                    $vv_productsitem['price'] = ($vv['rate'] / $vv['nums']);
                                }
                                $vv_productsitem['price'] = -1 * $vv_productsitem['price'];
                                array_push($products, $vv_productsitem);
                            }
                        }
                        $ordersitem = ["order_no" => (string)$k, "affiliation" => $incoic_form, "products" => $products];
                        array_push($dpordersdata, $ordersitem);
                    }

                    //订单详情
                    $data = [
                        "uid"      => $Invoice["operator_id"],
                        "genre_id" => 3,//销售单
                        "receipt"  => $receipt,
                        "genre"    => $Invoice['invoic_type'],//发票类型
                        "orders"   => $dpordersdata
                    ];
                    //备注开关
                    if ($Invoice['remarks_sysn_incoice'] == 1) {
                        $data['remarks'] = $Invoice['remarks'];
                    }
                    $goinvoice = self::approvalGoInvoice($data);
                    if (is_string($goinvoice)) throw new ValidateException("电子开票请求失败，原因：" . $goinvoice);

                    $invoice_by_order = $goinvoice['invoice_by_order'];
                    //去重所有状态
                    $invoice_status_array = array_column($invoice_by_order, 'invoices');

                    //开票状态     //开票号
                    $invoice_status_array_data = [];
                    $invoice_no_array          = [];
                    $msg                       = [];
                    foreach ($invoice_status_array as $v) {
                        foreach ($v as $vv) {
                            array_push($invoice_status_array_data, $vv['status']);
                            array_push($invoice_no_array, $vv['invoice_code']);
                            if ($vv['status'] == 3) {
                                array_push($msg, $vv['msg']);
                            }

                        }
                    }
                    if (in_array(4, $invoice_status_array_data)) {//作废发票
                        $invoic_status = 4;
                        self::saveInvoiceStatus($params['id'], 4);
                    } else if (in_array(3, $invoice_status_array_data)) {//开票失败
                        $invoic_status = 3;
                        self::saveInvoiceStatus($params['id'], 3);
                    } else if (in_array(1, $invoice_status_array_data)) {//开票中
                        $invoic_status = 1;
                        self::saveInvoiceStatus($params['id'], 1);
                    } else if (in_array(2, $invoice_status_array_data)) {//开票成功
                        $invoic_status = 2;
                        self::saveInvoiceStatus($params['id'], 2);
                    }
                    //记录开票返回数据

                    if ($invoic_status == 3) {
                        $invoic_status = 5;//开票失败
                        $invoice_no    = '';
                    } else {
                        $invoice_no = implode(',', array_unique($invoice_no_array));
                    }


                    if ($msg == []) {
                        $msg_err = '';
                    } else {
                        $msg_err = implode(",", array_unique($msg));
                    }
                    $Invoice_sava_data = ["callbackmsg" => $msg_err, 'invoic_status' => $invoic_status, 'invoice_no' => $invoice_no, 'goinvoicejson' => $invoice_by_order];

                    $result = InvoiceModel::where('id', '=', $params['id'])->save($Invoice_sava_data);
                    if (!$result) throw new ValidateException("数据存储失败");
                }
                $code = true;
            } else {
                $code = false;
            }
            Db::commit();
            return $code;
        } catch (\Exception $exception) {
            Db::rollback();
            return $exception->getMessage();
        }

    }

    public static function getBillNo()
    {
        $bill_no_param = "FP-" . date("Y-m-d");
        $time          = strtotime(date("Y-m-d", time()) . " 00:00:00");
        $bill_no       = InvoiceModel::where('create_time', '>=', $time)->order('id', 'desc')->value('bill_no');

        if ($bill_no) {
            $bill_no_unit_nums = substr($bill_no, -4) + 1;
            $bill_no_param     .= "-" . str_pad($bill_no_unit_nums, 4, 0, STR_PAD_LEFT);
        } else {
            $bill_no_param = "FP-" . date("Y-m-d") . "-0001";
        }

        return $bill_no_param;
    }

    /**
     * 筛选需要开票的销售单据
     * @param $params
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function searchSalesDocuments($params)
    {
        $where = [];
        if (isset($params['sub_order_no']) && !empty($params['sub_order_no'])) {
            $sub_order_no = explode(',', trim($params['sub_order_no']));
            $where[]      = ['sub_order_no', 'in', array_unique($sub_order_no)];
        }
        if (isset($params['main_order_no']) && !empty($params['main_order_no'])) {
            $p_main_order_no  = explode(',', trim($params['main_order_no']));
            $p_main_order_ids = Db::name('order_main')->where('main_order_no', 'in', $p_main_order_no)->column('id');
            $where[]          = ['main_order_id', 'in', $p_main_order_ids];
        }
        $where[] = ['invoice_progress', 'in', [0, 3]];
        #三方和线下仅查询2022年11月1日之后的订单数据
        $where[] = ['created_time', '>=', 1635696000];
        $field   = "sub_order_no,items_info,order_qty,payment_amount,items_info,created_time";
        $list    = [];
        $count   = 0;
        $page    = $params['page'] ? $params['page'] : 1;
        $limit   = $params['limit'] ? $params['limit'] : 10;
        #分页起始值
        $offset = ($page - 1) * $limit;
        if ($params['sales_type'] == 2) { #线下销售
            $where[] = [['dingtalk_status', '=', 2]];
            $where[] = [['is_reject', '=', 0]];
            $where[] = [['document_type', '<>', 0]];//过滤样酒 单据类型：0-样酒 1-销售单 2-T+销售单
            if (isset($params['customer']) && !empty($params['customer'])) {
                $where[] = ['customer', '=', trim($params['customer'])];
            }
            if (isset($params['settle_customer']) && !empty($params['settle_customer'])) {
                $where[] = ['settle_customer', '=', trim($params['settle_customer'])];
            }
            if (isset($params['customer_abbreviation']) && !empty($params['customer_abbreviation'])) {
                $where[] = ['customer_abbreviation', '=', trim($params['customer_abbreviation'])];
            }
            $list  = Db::name("offline_order")->field($field)->where($where)->limit(intval($offset), intval($limit))->select()->toArray();
            $count = Db::name("offline_order")->where($where)->count('sub_order_no');

            $short_code_arr = [];
            foreach ($list as $key => &$val) {
                $item       = explode(",", $val['items_info']);
                $items_info = [];
                foreach ($item as $k => $v) {
                    $goods                            = explode('*', $v);
                    $short_code                       = isset($goods[1]) ? $goods[1] : '';
                    $items_info[$k]['bar_code']       = isset($goods[0]) ? $goods[0] : '';
                    $items_info[$k]['short_code']     = $short_code;
                    $items_info[$k]['is_gift']         = isset($goods[4]) ? intval($goods[4]) : 0;
                    $items_info[$k]['nums']           = isset($goods[2]) ? intval($goods[2]) : 0;
                    $items_info[$k]['nums']           = isset($goods[2]) ? intval($goods[2]) : 0;
                    $items_info[$k]['tax_unit_price'] = isset($goods[3]) ? $goods[3] : 0;
                    $items_info[$k]['product_name']   = isset($goods[6]) ? $goods[6] : '';
                    $items_info[$k]['unit']           = isset($goods[8]) ? $goods[8] : '';
                    $items_info[$k]['year']           = isset($goods[7]) ? $goods[7] : '';
                    $items_info[$k]['capacity']       = isset($goods[11]) ? $goods[11] : '';
                    $items_info[$k]['tax_rate']       = 0.13;
                    array_push($short_code_arr, $short_code);
                }
                $list[$key]['items_info'] = $items_info;
                $val['created_time']      = date("Y-m-d H:i:s", $val['created_time']);
                $val['documents_type']    = 1;
            }

            //产品大类
            $product              = self::productIdSearchProduct($short_code_arr, 'short_code');
            $product_category_key = array_column($product, 'product_category', 'short_code');
            foreach ($list as &$val) {
                foreach ($val['items_info'] as &$v) {
                    if (!isset($product_category_key[$v['short_code']])) throw new ValidateException($v['short_code'] . ":产品数据不存在");
                    $v['product_category'] = $product_category_key[$v['short_code']];
                }
            }
        } elseif ($params['sales_type'] == 3) { #三方订单
            // $where[]=['push_wms_status','=',1];
            $where[]        = ['items_info', '<>', ""];
            $list           = Db::name("tripartite_order")->field($field)->where($where)->limit(intval($offset), intval($limit))->select()->toArray();
            $count          = Db::name("tripartite_order")->where($where)->count('sub_order_no');
            $short_code_arr = [];
            foreach ($list as $key => &$val) {
                $item       = explode(",", $val['items_info']);
                $items_info = [];
                foreach ($item as $k => $v) {
                    $goods                            = explode('*', $v);
                    $short_code                       = isset($goods[0]) ? $goods[0] : '';
                    $items_info[$k]['short_code']     = $short_code;
                    $nums                             = isset($goods[1]) && $goods[1] > 0 ? intval($goods[1]) : 1;
                    $nums                             = $nums * $val['order_qty'];
                    $price                            = isset($goods[2]) ? $goods[2] : 0;
                    $items_info[$k]['tax_unit_price'] = $price == 0 ? 0 : bcdiv($price, strval($nums), 2);
                    $items_info[$k]['nums']           = $nums;
                    $product                          = self::shortSearchProduct($items_info[$k]['short_code']);
                    $items_info[$k]['product_name']   = $product['product_name'];
                    $items_info[$k]['capacity']       = $product['capacity'];
                    $items_info[$k]['unit']           = $product['product_unit'];
                    $items_info[$k]['year']           = $product['year'];
                    $items_info[$k]['tax_rate']       = 0.13;
                    array_push($short_code_arr, $short_code);
                }
                $list[$key]['items_info'] = $items_info;
                $val['created_time']      = date("Y-m-d H:i:s", $val['created_time']);
                $val['documents_type']    = 1;
            }

            //产品大类
            $product              = self::productIdSearchProduct($short_code_arr, 'short_code');
            $product_category_key = array_column($product, 'product_category', 'short_code');
            foreach ($list as &$val) {
                foreach ($val['items_info'] as &$v) {
                    if (!isset($product_category_key[$v['short_code']])) throw new ValidateException($v['short_code'] . ":产品数据不存在");
                    $v['product_category'] = $product_category_key[$v['short_code']];
                }
            }
        } elseif ($params['sales_type'] == 1) {
            $list = $this->invoiceOrderList($params);
            return $list;
        }
        $result['list']  = $list;
        $result['total'] = $count;
        return $result;
    }

    /**
     * es通过简码查询商品名称
     * @param $short_code
     * @return array|mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public static function shortSearchProduct($short_code)
    {
        $es      = new ElasticSearchService();
        $data    = $es->getDocumentList(['index' => ['panshi.products'], 'match' => [['short_code' => $short_code]], 'source' => ['cn_product_name', 'bar_code', 'short_code', 'capacity', 'product_unit_name', 'products_years'], 'limit' => 100]);
        $product = ['capacity' => '', 'product_name' => '', 'product_unit' => "瓶", 'year' => 0];
        if (count($data['data'])) {
            $product = [
                "capacity"     => isset($data['data'][0]['capacity']) ? $data['data'][0]['capacity'] : '',
                "product_name" => isset($data['data'][0]['cn_product_name']) ? $data['data'][0]['cn_product_name'] : '',
                "product_unit" => isset($data['data'][0]['product_unit_name']) ? $data['data'][0]['product_unit_name'] : '瓶',
                "year"         => isset($data['data'][0]['products_years']) ? $data['data'][0]['products_years'] : '瓶',
            ];
        }
        return $product;
    }

    public static function export($params)
    {
        $where = function ($query) use ($params) {
            //客户名称
            if (isset($params['customer_name']) && strlen($params['customer_name']) > 0) {
                $query->where('customer_name', 'like', '%' . $params['customer_name'] . '%');
            }

            //单据编号
            if (isset($params['bill_no']) && strlen($params['bill_no']) > 0) {
                $query->where('bill_no', '=', $params['bill_no']);
            }

            //开票公司
            if (isset($params['incoic_form']) && strlen($params['incoic_form']) > 0) {
                $query->where('incoic_form', '=', $params['incoic_form']);
            }

            //发票状态
            if (isset($params['invoic_status']) && strlen($params['invoic_status']) > 0) {
                $query->where('invoic_status', '=', $params['invoic_status']);
            }


            if (isset($params['start_time']) && strlen($params['start_time']) > 0) {
                $query->where('create_time', '>=', strtotime($params['start_time']));
            }

            if (isset($params['end_time']) && strlen($params['end_time']) > 0) {
                $query->where('create_time', '<=', strtotime($params['end_time']));
            }

            if (isset($params['invoice_no']) && strlen($params['invoice_no']) > 0) {
                $query->where('invoice_no', '=', $params['invoice_no']);
            }

        };

        $page              = $params['page'] ?? 1;
        $limit             = $params['limit'] ?? 10;
        $pagestrat         = ($page - 1) * $limit;
        $result            = \app\model\Invoice::where($where)->order('id', 'desc')->select()->toArray();
        $invoic_status_arr = [1 => '待开票', 2 => '已开票3', 3 => '开票作废'];
        $invoic_type_arr   = [1 => '普通发票', 2 => '专用发票', 3 => '红字专用发票', 4 => '红字普通发票'];//发票类型 2，专用发票，1，普通发票，3红字专用发票，4，红字普通发票',
        $incoic_form_arr   = [1 => '酒云线上', 2 => '线下销售', 3 => '三方线上', 4 => '兔子星球', 5 => '木兰朵'];
        $put_data = [];
        $result            = array_map(function ($v) use ($invoic_status_arr, $invoic_type_arr, $incoic_form_arr,&$put_data) {
            $v['detail_json']   = json_encode((array)$v['detail_json']);
            $v['invoic_status'] = $invoic_status_arr[$v['invoic_status']] ?? "未知状态";
            $v['invoic_type']   = $invoic_type_arr[$v['invoic_type']] ?? "未知状态";
            $v['incoic_form']   = $incoic_form_arr[$v['incoic_form']] ?? "未知公司";
            foreach (json_decode($v['detail_json'],true) as $detail){
                $put_data[] = array_merge($v,$detail);
            }
            return $v;
        }, $result);
//        $header            = array(
//            array('column' => 'create_time', 'name' => '单据时间', 'width' => 15),
//            array('column' => 'bill_no', 'name' => '单据编号', 'width' => 15),
//            array('column' => 'order_no', 'name' => '销售单据编号', 'width' => 15),
//            array('column' => 'short_codes', 'name' => '产品简码', 'width' => 30),
//            array('column' => 'invoic_status', 'name' => '开票状态', 'width' => 30),
//            array('column' => 'invoic_type', 'name' => '发票类型', 'width' => 30),
//            array('column' => 'customer_name', 'name' => '客户名称', 'width' => 30),
//            array('column' => 'customer_bill_receiv_info', 'name' => '客户收票信息', 'width' => 30),
//            array('column' => 'incoic_form', 'name' => '开票公司', 'width' => 30),
//            array('column' => 'invoic_courier_no', 'name' => '发票快递单号', 'width' => 30),
//            array('column' => 'remarks', 'name' => '备注', 'width' => 30),
//            array('column' => 'finance_remarks', 'name' => '财务备注', 'width' => 30),
//            array('column' => 'invoice_no', 'name' => '发票单号', 'width' => 30),
//            array('column' => 'invoice_to_detail_name', 'name' => '收票公司收件信息名称', 'width' => 30),
//            array('column' => 'invoice_to_detail_tax_no', 'name' => '收票公司收件信息纳税号', 'width' => 30),
//            array('column' => 'invoice_to_detail_addr', 'name' => '收票公司收件信息地址', 'width' => 30),
//            array('column' => 'invoice_to_detail_phone', 'name' => '收票公司收件信息电话', 'width' => 30),
//            array('column' => 'invoice_to_detail_bank_name', 'name' => '收款公司收件信息银行名称', 'width' => 30),
//            array('column' => 'invoice_to_detail_bank_code', 'name' => '收款公司收件信息银行code', 'width' => 30),
//            array('column' => 'invoice_to_detail_bank_no', 'name' => '收款公司收件信息银行账号', 'width' => 30),
//            array('column' => 'approver', 'name' => '审核人名称', 'width' => 30),
//            array('column' => 'detail_json', 'name' => '详情', 'width' => 30),
//            array('column' => 'invoice_no', 'name' => '发票单号', 'width' => 30),
//        );
        $header            = array(
            array('column' => 'create_time', 'name' => '单据时间', 'width' => 15),
            array('column' => 'order_no', 'name' => '订单号', 'width' => 15),
            array('column' => 'invoice_no', 'name' => '发票号', 'width' => 30),
            array('column' => 'short_code', 'name' => '简码', 'width' => 15),
            array('column' => 'invoice_name', 'name' => '名称', 'width' => 15),
            array('column' => 'nums', 'name' => '数量', 'width' => 15),
            array('column' => 'product_unit_name', 'name' => '单位', 'width' => 15),
            array('column' => 'product_capacity', 'name' => '规格', 'width' => 15),
            array('column' => 'tax_unit_price', 'name' => '含税单价', 'width' => 15),
            array('column' => 'invoic_status', 'name' => '开票状态', 'width' => 30),
            array('column' => 'invoic_type', 'name' => '发票类型', 'width' => 30),
            array('column' => 'invoice_to_detail_name', 'name' => '收票公司收件信息名称', 'width' => 30),
            array('column' => 'invoice_to_detail_tax_no', 'name' => '收票公司收件信息纳税号', 'width' => 30),
            array('column' => 'customer_email', 'name' => '收票邮箱', 'width' => 30),
            array('column' => 'approver_time', 'name' => '审批时间', 'width' => 30),
        );
        $filename          = "开票导出数据.xlsx";
        $uploadUrl         = exportSheelExcel($put_data, $header, $filename);
        if (empty($uploadUrl)) throw new ValidateException('生成excel文件异常');
        $file = app()->getRootPath() . "public/storage/" . $uploadUrl;
        return [$file, $filename];
    }

    /**
     * 修改订单开票状态
     * @param array $order
     * @return bool
     */
    public static function saveonlineorderInvoiceProgress($order, int $invoice_progress)
    {
        $es     = new ElasticSearchService();
        $source = ['sub_order_no', 'order_type'];
        $where  = ['index' => ['orders'], 'terms' => [['_id' => explode(',', $order)]], 'source' => $source];
        $data   = $es->getDocumentList($where);
        if (count($data['data']) == 0) throw new ValidateException('订单查询为空');
        foreach ($data['data'] as $v) {
            self::saveOrderInvoiceProgress($v['sub_order_no'], $v['order_type'], $invoice_progress);
        }
        return true;
    }

    public static function saveOrderInvoiceProgress($sub_order_no, $order_type, $status)
    {
        $name = '';
        //0-闪购 1-秒发 2-跨境 3-尾货
        switch ($order_type) {
            case 0://闪购
                $name = 'flash_order';
                break;
            case 1://秒发
                $name = 'second_order';
                break;
            case 2://跨境 待定
                $name = 'cross_order';
                throw new ValidateException('订单类型不正确,跨境暂不支持开票。请检查.');
                break;
            case 3://尾货 tail_order
                $name = 'tail_order';
                break;
            default:
                throw new ValidateException('订单类型不正确,（闪购 1-秒发 2-跨境 3-尾货）请检查.');
                break;
        }
        Db::name($name)->where('sub_order_no', 'in', $sub_order_no)->update(['invoice_progress' => $status]);
    }


    /**
     * 酒云线上可开票订单
     * @param $params
     * @return mixed
     */
    public function invoiceOrderList($params)
    {
        #获取套餐 通过套餐 拿到产品id 再去磐石查询产品信息
        $page         = !empty($params['page']) ? $params['page'] : 1;
        $limit        = !empty($params['limit']) ? $params['limit'] : 10;
        $match_phrase = [];
        $where[]      = ['sub_order_status' => 3];
        $where[]      = ['is_virtual' => 0]; #是否虚拟
        $where[]      = ['is_gift' => 0];
        $where[]      = ['refund_status' => 0];
        $range[]      = ['cash_amount' => ['gt' => 0]];
        $terms        = [];
        if (isset($params['sub_order_no']) && !empty($params['sub_order_no'])) {
            $sub_order_no = explode(',', trim($params['sub_order_no']));
            $terms[]      = ['sub_order_no.keyword' => $sub_order_no];
        }
        if (isset($params['order_type']) && is_numeric($params['order_type'])) {
            $where[] = ['order_type' => $params['order_type']];
        }
        if (!empty($params['stime'])) {
            $range[] = ['created_time' => ['gte' => $params['stime']]];
        }
        if (!empty($params['etime'])) {
            $range[] = ['created_time' => ['lte' => $params['etime']]];
        }
        if (!empty($params['period'])) {
            $where[] = ['period' => $params['period']];
        }
        if (!empty($params['nickname'])) {
            $match_phrase[] = ['nickname' => $params['nickname']];
        }

        $order       = [['created_time' => 'desc']];
        $terms[]     = ['invoice_progress' => [0, 3]];
        $terms[]     = ['order_type' => [0, 1, 2, 3]];
        $es          = new ElasticSearchService();
        $arr         = array(
            'index'        => ['orders'],
            'match'        => $where,
            'match_phrase' => $match_phrase,
            'range'        => $range,
            'terms'        => $terms,
            'source'       => ['id', 'uid', 'nickname', 'invoice_progress', 'sub_order_no', 'order_type', 'order_qty', 'period', 'package_id', 'cash_amount', 'payment_amount', 'created_time'],
            'page'         => $page,
            'limit'        => $limit,
            'sort'         => $order
        );
        $data        = $es->getDocumentList($arr);
        $packageList = [];
        if (count($data['data']) > 0) {
            $packageId  = array_column($data['data'], 'package_id');
            $packageArr = [
                'index'  => ['periods_set'],
                'terms'  => [['id' => $packageId]],
                'source' => ["id", 'period_id', 'associated_products', 'price'],
                'limit'  => 10000,
            ];
            #获取套餐信息
            $periodsSet = $es->getDocumentList($packageArr);
            if (count($periodsSet['data']) > 0) {
                foreach ($periodsSet['data'] as $temp) {
                    #解析产品信息 获取产品数据
                    $associated_products = json_decode($temp['associated_products'], true);
                    $product_id          = array_column($associated_products, 'product_id');
                    $product             = self::productIdSearchProduct($product_id);
                    foreach ($associated_products as &$ap) {
                        $ap['short_code']   = isset($product[$ap['product_id']]['short_code']) ? $product[$ap['product_id']]['short_code'] : "";
                        $ap['product_name'] = isset($product[$ap['product_id']]['cn_product_name']) ? $product[$ap['product_id']]['cn_product_name'] : "";
                        $ap['capacity']     = isset($product[$ap['product_id']]['capacity']) ? $product[$ap['product_id']]['capacity'] : "";
                        $ap['unit']         = isset($product[$ap['product_id']]['product_unit_name']) ? $product[$ap['product_id']]['product_unit_name'] : "";
                        $ap['year']         = isset($product[$ap['product_id']]['products_years']) ? $product[$ap['product_id']]['products_years'] : "";
                        $ap['tax_rate']     = "13%";
                    }
                    $packageList[$temp['id']] = $associated_products;
                }
            }
            #数据整合
            foreach ($data['data'] as &$orderTemp) {
                #region 兼容
                $orderTemp['recharge_balance']        = $orderTemp['recharge_balance'] ?? 0;
                $orderTemp['bonus_balance']           = $orderTemp['bonus_balance'] ?? 0;
                $orderTemp['refund_recharge_balance'] = $orderTemp['refund_recharge_balance'] ?? 0;
                $orderTemp['refund_bonus_balance']    = $orderTemp['refund_bonus_balance'] ?? 0;
                $orderTemp['cash_amount']             = $orderTemp['cash_amount'] ?? ($orderTemp['payment_amount'] ?? 0);
                #endregion 兼容

                $items_info = isset($packageList[$orderTemp['package_id']]) ? $packageList[$orderTemp['package_id']] : [];
                $total_nums = array_sum(array_column($items_info, 'nums'));
                $total_nums = $total_nums * $orderTemp['order_qty']; #总数量=套餐总数*购买总数量
                foreach ($items_info as &$v) {
                    $v['nums'] = $v['nums'] * $orderTemp['order_qty'];
                    @$v['tax_unit_price'] = bcdiv($orderTemp['cash_amount'], $total_nums, 2);
                }
                $orderTemp['items_info']     = $items_info;
                $orderTemp['documents_type'] = 1;
            }
        }
        $result['list']  = $data['data'];
        $result['total'] = $data['total']['value'];
        return $result;
    }

    /**
     * 通过产品id查询产品数据
     * @param $product_id
     * @return array
     */
    public static function productIdSearchProduct($product_id, $field = 'id')
    {
        $es      = new ElasticSearchService();
        $data    = $es->getDocumentList(['index' => ['panshi.products'], 'terms' => [[$field => $product_id]], 'source' => ['id', 'cn_product_name', 'bar_code', 'short_code', 'capacity', 'product_unit_name', 'products_years', 'product_category'], 'limit' => 200]);
        $product = [];
        if (count($data['data']) > 0) {
            foreach ($data['data'] as $val) {
                $product[$val['id']] = $val;
            }
        }
        return $product;
    }

    public function getSalesDocumentsAllList($params)
    {
        $where = [];
        if (isset($params['sub_order_no']) && !empty($params['sub_order_no'])) {
            $sub_order_no = explode(',', trim($params['sub_order_no']));
            $where[]      = ['sub_order_no', 'in', array_unique($sub_order_no)];
        }
//        $where[] = ['invoice_progress', 'in', [0, 3]];
        #三方和线下仅查询2022年11月1日之后的订单数据
        $where[] = ['created_time', '>=', 1635696000];
        $field   = "sub_order_no,items_info,order_qty,payment_amount,items_info,created_time";
        $list    = [];
        $count   = 0;
        $page    = $params['page'] ? $params['page'] : 1;
        $limit   = $params['limit'] ? $params['limit'] : 10;
        #分页起始值
        $offset = ($page - 1) * $limit;
        if ($params['sales_type'] == 2) { #线下销售
//            $where[] =[['dingtalk_status','=',2]];
//            $where[] =[['is_reject','=',0]];
            if (isset($params['customer']) && !empty($params['customer'])) {
                $where[] = ['customer', '=', trim($params['customer'])];
            }
            if (isset($params['settle_customer']) && !empty($params['settle_customer'])) {
                $where[] = ['settle_customer', '=', trim($params['settle_customer'])];
            }
            if (isset($params['customer_abbreviation']) && !empty($params['customer_abbreviation'])) {
                $where[] = ['customer_abbreviation', '=', trim($params['customer_abbreviation'])];
            }
            $list  = Db::name("offline_order")->field($field)->where($where)->limit(intval($offset), intval($limit))->select()->toArray();
            $count = Db::name("offline_order")->where($where)->count('sub_order_no');
            foreach ($list as $key => &$val) {
                $item       = explode(",", $val['items_info']);
                $items_info = [];
                foreach ($item as $k => $v) {
                    $goods                            = explode('*', $v);
                    $items_info[$k]['bar_code']       = isset($goods[0]) ? $goods[0] : '';
                    $items_info[$k]['short_code']     = isset($goods[1]) ? $goods[1] : '';
                    $items_info[$k]['nums']           = isset($goods[2]) ? intval($goods[2]) : 0;
                    $items_info[$k]['tax_unit_price'] = isset($goods[3]) ? $goods[3] : 0;
                    $items_info[$k]['product_name']   = isset($goods[6]) ? $goods[6] : '';
                    $items_info[$k]['unit']           = isset($goods[8]) ? $goods[8] : '';
                    $items_info[$k]['year']           = isset($goods[7]) ? $goods[7] : '';
                    $items_info[$k]['capacity']       = isset($goods[11]) ? $goods[11] : '';
                    $items_info[$k]['is_gift']       = isset($goods[4]) ? intval($goods[4]) : 0;
                    $items_info[$k]['tax_rate']       = 0.13;
                }
                $list[$key]['items_info'] = $items_info;
                $val['created_time']      = date("Y-m-d H:i:s", $val['created_time']);
                $val['documents_type']    = 1;
            }
        } elseif ($params['sales_type'] == 3) { #三方订单
            // $where[]=['push_wms_status','=',1];
            $where[] = ['items_info', '<>', ""];
            $list    = Db::name("tripartite_order")->field($field)->where($where)->limit(intval($offset), intval($limit))->select()->toArray();
            $count   = Db::name("tripartite_order")->where($where)->count('sub_order_no');
            foreach ($list as $key => &$val) {
                $item       = explode(",", $val['items_info']);
                $items_info = [];
                foreach ($item as $k => $v) {
                    $goods                            = explode('*', $v);
                    $items_info[$k]['short_code']     = isset($goods[0]) ? $goods[0] : '';
                    $nums                             = isset($goods[1]) && $goods[1] > 0 ? intval($goods[1]) : 1;
                    $nums                             = $nums * $val['order_qty'];
                    $price                            = isset($goods[2]) ? $goods[2] : 0;
                    $items_info[$k]['tax_unit_price'] = $price == 0 ? 0 : bcdiv($price, strval($nums), 2);
                    $items_info[$k]['nums']           = $nums;
                    $product                          = self::shortSearchProduct($items_info[$k]['short_code']);
                    $items_info[$k]['product_name']   = $product['product_name'];
                    $items_info[$k]['capacity']       = $product['capacity'];
                    $items_info[$k]['unit']           = $product['product_unit'];
                    $items_info[$k]['year']           = $product['year'];
                    $items_info[$k]['tax_rate']       = 0.13;
                }
                $list[$key]['items_info'] = $items_info;
                $val['created_time']      = date("Y-m-d H:i:s", $val['created_time']);
                $val['documents_type']    = 1;
            }
        } elseif ($params['sales_type'] == 1) {
            $list = $this->invoiceOrderAllList($params);
            return $list;
        }
        $result['list']  = $list;
        $result['total'] = $count;
        return $result;
    }

    public function invoiceOrderAllList($params)
    {
        #获取套餐 通过套餐 拿到产品id 再去磐石查询产品信息
        $page         = !empty($params['page']) ? $params['page'] : 1;
        $limit        = !empty($params['limit']) ? $params['limit'] : 10;
        $match_phrase = [];
//        $where = [];
//        $where[]      = ['sub_order_status' => 3];
//        $where[]      = ['is_virtual' => 0]; #是否虚拟
//        $where[]      = ['is_gift' => 0];
//        $where[]      = ['refund_status' => 0];
        $range[] = ['cash_amount' => ['gt' => 0]];
        $terms   = [];
        if (isset($params['sub_order_no']) && !empty($params['sub_order_no'])) {
            $sub_order_no = explode(',', trim($params['sub_order_no']));
            $terms[]      = ['sub_order_no.keyword' => $sub_order_no];
        }
        if (isset($params['order_type']) && is_numeric($params['order_type'])) {
            $where[] = ['order_type' => $params['order_type']];
        }
        if (!empty($params['stime'])) {
            $range[] = ['created_time' => ['gte' => $params['stime']]];
        }
        if (!empty($params['etime'])) {
            $range[] = ['created_time' => ['lte' => $params['etime']]];
        }
        if (!empty($params['period'])) {
            $where[] = ['period' => $params['period']];
        }
        if (!empty($params['nickname'])) {
            $match_phrase[] = ['nickname' => $params['nickname']];
        }

        $order = [['created_time' => 'desc']];
//        $terms[] = ['invoice_progress' => [0, 3]];
        $terms[] = ['order_type' => [0, 1, 2, 3]];
        $es      = new ElasticSearchService();
        $arr     = array(
            'index'        => ['orders'],
//            'match'        => $where,
            'match_phrase' => $match_phrase,
            'range'        => $range,
            'terms'        => $terms,
            'source'       => ['id', 'uid', 'nickname', 'invoice_progress', 'sub_order_no', 'order_type', 'order_qty', 'period', 'package_id', 'cash_amount','payment_amount', 'created_time'],
            'page'         => $page,
            'limit'        => $limit,
            'sort'         => $order
        );

        $data        = $es->getDocumentList($arr);
        $packageList = [];
        if (count($data['data']) > 0) {
            $packageId  = array_column($data['data'], 'package_id');
            $packageArr = [
                'index'  => ['periods_set'],
                'terms'  => [['id' => $packageId]],
                'source' => ["id", 'period_id', 'associated_products', 'price'],
                'limit'  => 10000,
            ];
            #获取套餐信息
            $periodsSet = $es->getDocumentList($packageArr);
            if (count($periodsSet['data']) > 0) {
                foreach ($periodsSet['data'] as $temp) {
                    #解析产品信息 获取产品数据
                    $associated_products = json_decode($temp['associated_products'], true);
                    $product_id          = array_column($associated_products, 'product_id');
                    $total_nums          = array_sum(array_column($associated_products, 'nums'));
                    $product             = self::productIdSearchProduct($product_id);
                    foreach ($associated_products as &$ap) {
                        $ap['short_code']   = isset($product[$ap['product_id']]['short_code']) ? $product[$ap['product_id']]['short_code'] : "";
                        $ap['product_name'] = isset($product[$ap['product_id']]['cn_product_name']) ? $product[$ap['product_id']]['cn_product_name'] : "";
                        $ap['capacity']     = isset($product[$ap['product_id']]['capacity']) ? $product[$ap['product_id']]['capacity'] : "";
                        $ap['unit']         = isset($product[$ap['product_id']]['product_unit_name']) ? $product[$ap['product_id']]['product_unit_name'] : "";
                        $ap['year']         = isset($product[$ap['product_id']]['products_years']) ? $product[$ap['product_id']]['products_years'] : "";
                        $ap['tax_rate']     = 0.13;
                        @$ap['tax_unit_price'] = bcdiv($temp['price'], $total_nums, 2);
                    }
                    $packageList[$temp['id']] = $associated_products;
                }
            }
            #数据整合
            foreach ($data['data'] as &$orderTemp) {
                $orderTemp['items_info']     = isset($packageList[$orderTemp['package_id']]) ? $packageList[$orderTemp['package_id']] : [];
                $orderTemp['documents_type'] = 1;
            }
        }
        $result['list']  = $data['data'];
        $result['total'] = $data['total']['value'];
        return $result;
    }

    //发起电子开票
    public static function approvalGoInvoice($params)
    {
        $method = env("ITEM.GO_INVOICE_URL") . "/go-invoice/v3/create/createD";
        $result = (new \app\CommonHttpRequest())->httpPost($method, $params, [], 'json');
//        $result = $this->httpPost($method,$params);
        if (isset($result['error_code']) && $result['error_code'] == 0) {
            return $result['data'];
        } else {
            return $result['error_msg'];
        }
    }

    //修改发票状态
    public static function saveInvoiceStatus($id, $invoice_progress)
    {
        //修改第三方订单  vh_tripartite_orde  vh_offline_order
        $Invoice_data = InvoiceModel::field('id,order_no,sale_bill_type,approver_time')->find($id);
        if ($Invoice_data['sale_bill_type'] == 2) {//线下 vh_offline_order
            $sub_order_no = $Invoice_data['order_no'];
            $update = ['invoice_progress' => $invoice_progress];
            if ($invoice_progress == 2) {
                $update['invoicing_date'] = $Invoice_data['approver_time'];
            }
            Db::name('offline_order')->where('sub_order_no', 'in', $sub_order_no)->update($update);
        } else if ($Invoice_data['sale_bill_type'] == 3) {//三方 vh_tripartite_orde
            $sub_order_no = $Invoice_data['order_no'];
            Db::name('tripartite_order')->where('sub_order_no', 'in', $sub_order_no)->update(['invoice_progress' => $invoice_progress]);

        } else if ($Invoice_data['sale_bill_type'] == 1) {//线上 vh_tripartite_orde
            $sub_order_no = $Invoice_data['order_no'];
            self::saveonlineorderInvoiceProgress($sub_order_no, $invoice_progress);
        } else {
            throw new ValidateException("销售单据类型有误，请检查");
        }
    }

    //发票回调
    public static function callback($params)
    {
        $invoices_status    = $params['data']['status'];
        $invoices_order_nos = $params['data']['order_nos'];

        $invoice_no = $invoices_order_nos[0];
        $result     = InvoiceModel::whereFindInSet('order_no', $invoice_no)->where('invoic_status', '<>', 4)->find();
        if (!$result) throw new ValidateException("订单不存在");
        $id = $result->id;

        //回调
        if ($invoices_status == 4) {//作废发票
            $invoic_status = 4;
            self::saveInvoiceStatus($id, 4);
        } else if ($invoices_status == 3) {//开票失败
            $invoic_status = 3;
            self::saveInvoiceStatus($id, 3);
        } else if ($invoices_status == 1) {//开票中
            $invoic_status = 1;
            self::saveInvoiceStatus($id, 1);
        } else if ($invoices_status == 2) {//开票成功
            $invoic_status = 2;
            self::saveInvoiceStatus($id, 2);
        }
        //记录开票返回数据

        if ($invoic_status == 3) {
            $invoic_status = 5;//开票失败
            $invoice_no    = '';
        } else {
            $invoice_no = $params['data']['invoice_code'];
        }
        $Invoice_sava_data = ['invoic_status' => $invoic_status, 'invoice_no' => $invoice_no, 'goinvoicejson' => $params, 'callbackmsg' => $params['data']['msg']];
        $result            = InvoiceModel::where('id', $id)->save($Invoice_sava_data);
        if (!$result) throw new ValidateException("数据存储失败");
    }

    public static function abandonmentByInvoiceCodes($invoiceCodes)
    {
        $params = [
            'invoice_codes' => [$invoiceCodes]
        ];
        $method = env("ITEM.GO_INVOICE_URL") . "/go-invoice/v3/update/abandonmentByInvoiceCodes";
        $result = (new \app\CommonHttpRequest())->httpPost($method, $params, [], 'json');
        if (isset($result['error_code']) && $result['error_code'] == 0) {
            return true;
        } else {
            return "电子开票" . $result['error_msg'];
        }
    }
}