<?php
declare (strict_types = 1);

namespace app\service;

use app\BaseService;
use app\model\FreightManagement as FreightManagementModel;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\Log;

class FreightManagement extends BaseService
{

    /**
     * Description：添加快递运费管理模板
     * Author: zjl
     * Date: 2022/5/5
     * @param $params
     * @return mixed
     */
    public function freightList($params)
    {
        $page = !empty($params['page']) ? $params['page'] : 1;
        $limit  = !empty($params['limit']) ? $params['limit'] : 10;
        $result = (new FreightManagementModel)->getFreightList($params,$page,$limit);
        return $result;

    }

    /**
     * Description：添加快递运费管理模板
     * Author: zjl
     * Date: 2022/5/5
     * @param $params
     * @return array|bool|string
     * @throws \Exception
     */
    public function createFreight($params)
    {

        if (empty($params['name'])) {
            throw new ValidateException('模板名称不能为空');
        }
        if (empty($params['calculate_type'])) {
            throw new ValidateException('计价方式不能为空');
        }
        if (empty($params['base_quantity'])) {
            throw new ValidateException('默认运费件数不能为空');
        }
        if (empty($params['base_price'])) {
            throw new ValidateException('默认运费价格不能为空');
        }
        if (empty($params['add_quantity'])) {
            throw new ValidateException('默认运费增加件数不能为空');
        }
        if (empty($params['add_price'])) {
            throw new ValidateException('默认运费增加价格不能为空');
        }
        try {
            $result = (new FreightManagementModel())->createFreight($params);
        } catch (\Exception $e) {
            Log::error('运费模板添加失败：' . $e->getMessage());
            throw new ValidateException($e->getMessage());
        }
        return $result;
   }

    /**
     * Description：修改快递运费管理模板
     * Author: zjl
     * Date: 2022/5/5
     * @param $params
     * @return bool|string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     *
     */
    public function updateFreight($params)
    {
        if (!isset($params['id']) || empty($params['id'])) {
            $this->throwError("模板id不能为空");
        }
        $res = (new  FreightManagementModel())->getOne($params['id']);
        if (empty($res)) {
            $this->throwError("未检测到运费管理模板数据");
        }
        $oldRegion=$res['region'];
        $oldCondition=$res['condition'];
        $editData =[
            'name'=>$params['name'],
            'calculate_type'=>$params['calculate_type'],
            'base_quantity'=>$params['base_quantity'],
            'base_price'=>$params['base_price'],
            'add_quantity'=>$params['add_quantity'],
            'add_price'=>$params['add_price'],
            'update_time'=>time(),
        ];
        $region = $params['region'];
        $condition = $params['condition'];
        $regionData =[];
        $conditionData=[];
        foreach ($oldRegion as $oldTempRegion){
            $regionData[$oldTempRegion['id']]=$oldTempRegion;
        }
        foreach ($oldCondition as $oldTempCondition){
            $conditionData[$oldTempCondition['id']]=$oldTempCondition;
        }
        Db::startTrans();
        try {
            $freight=   Db::name('freight_management')->where('id','=',$params['id'])->update($editData);
            if (empty($freight)){
                Db::rollback();
                return false;
            }
            //指定地区包邮
            foreach ($region as &$tempRegion){
                if ($tempRegion['region'] == '' || empty($tempRegion['base_quantity']) ||empty($tempRegion['base_price']) || empty($tempRegion['add_quantity']) || empty($tempRegion['add_price']) ) {
                    $this->throwError('指定地区参数配置参数不能为空');
                }
                if (isset($tempRegion['region_name'])) {
                    unset($tempRegion['region_name']);
                }
                //是否有新增 没有就添加
                if (!isset($tempRegion['id']) || empty($tempRegion['id'])) {
                    $tempRegion['freight_id']=$params['id'];
                    $tempRegion['update_time']=time();
                    $tempRegion['created_time']=time();
                    $tempRegion['status']=$res['status'];
                    $add =  Db::name('freight_region')->insert($tempRegion);
                    if (!$add) {
                        Db::rollback();
                        $this->throwError("操作失败");
                    }

                }else if(isset($regionData[$tempRegion['id']])) {
                    $tempRegion['update_time']=time();
                    $edit =  Db::name('freight_region')->where('id','=',$tempRegion['id'])->update($tempRegion);
                    if (!$edit) {
                        Db::rollback();
                        $this->throwError("修改失败");
                    }
                    unset($regionData[$tempRegion['id']]);
                }
            }
            foreach ($regionData as $tempData){
                Db::name('freight_region')->where('id','=',$tempData['id'])->update(['status'=>3,'update_time'=>time()]);
            }
            //指定条件包邮
            foreach ($condition as &$tempCondition){
                if ($tempCondition['condition'] == '' || empty($tempCondition['base_quantity']) ||empty($tempCondition['base_price'])) {
                    $this->throwError('条件包邮配置参数不能为空');
                }
                //是否有新增 没有就添加
                if (!isset($tempCondition['id']) || empty($tempCondition['id'])) {

                    $tempCondition['freight_id']=$params['id'];
                    $tempCondition['update_time']=time();
                    $tempCondition['created_time']=time();
                    $tempCondition['status']=$res['status'];
                    $add =  Db::name('freight_condition')->insert($tempCondition);
                    if (!$add) {
                        Db::rollback();
                        $this->throwError("条件包邮添加失败失败");
                    }

                }else if(isset($conditionData[$tempCondition['id']])) {
                    $tempCondition['update_time']=time();
                    $edit =  Db::name('freight_condition')->where('id','=',$tempCondition['id'])->update($tempCondition);
                    if (!$edit) {
                        Db::rollback();
                        $this->throwError("修改失败");
                    }
                    unset($conditionData[$tempCondition['id']]);
                }
            }
            foreach ($conditionData as $tempCondition){
                Db::name('freight_condition')->where('id','=',$tempCondition['id'])->update(['status'=>3,'update_time'=>time()]);
            }
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Log::error('运费模板修改失败：' . $e->getMessage());
            Db::rollback();
            throw new ValidateException($e->getMessage());
        }

    }

    /**
     * 状态变更
     * @param $params
     * @return bool|string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function setStatus($params)
    {
        if (!isset($params['id']) || empty($params['id'])) {
            $this->throwError("快递费用id不能为空");
        }
        $list =  (new FreightManagementModel())->where('id','=',$params['id'])->find();
        if (empty($list)) {
            $this->throwError("未检测到快递方式数据");
        }
        return   (new FreightManagementModel())->setStatus($params);

    }
}
