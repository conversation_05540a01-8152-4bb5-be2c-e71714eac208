<?php

namespace app\validate;


use think\facade\Db;

class InviceValidate extends BaseValidate
{

    protected $rule = [
        'id'=>'number',
        'invoic_status|开票状态'=>'number|in:1,2,3',
        'invoic_type|开票类型'=>'require|number|in:1,2,3,4',
        'customer_name|客户名'=>'require',
        'sale_bill_type|销售单据类型'=>'require|in:1,2,3',
        'incoic_form|开票公司'=>'require|in:1,2,3,4,5',
        'tax_total_price|含税总金额'=>'float',
        'detail_json|明细'=>'array|detailjson',
        'operator_id|用户id'=>'require|number',
        'recipient_type|发票类型'=>'require|number',
    ];

    protected $message = [
        'operator_id.require'=>"请先登录",
        'recipient_type.require'=>"请刷新升级当前系统页面。"
    ];


    /**
     * 验证json
     * @param $value
     * @param $rule
     * @param $data
     * @return bool|string
     */
    public function detailjson($value, $rule, $data=[])
    {

        if($value == []) return "明细不能为空";
        foreach ($value as $v){
            if(!is_array($v)) return '明细：数据格式错误';

            $validate = \think\facade\Validate::rule([
                'short_code|明细简码' => 'require',
                'cn_product_name|产品名称' => 'require',
//                'grape_picking_years|年份' => 'require',
                'product_capacity|容量' => 'require',
                'product_unit_name|单位名称' => 'require',
                'nums|数量' => 'require|number',
                'tax_rate|税率' => 'require',
                'tax_unit_price|含税单价' => 'require|float',
                'order_no|订单号' => 'require',
            ]);

            if (!$validate->check($v)) {
                return '明细:'.$validate->getError();
            }
        }
        $sub_order_no = array_column($value,'order_no');
        $sale_bill_type = &$data['sale_bill_type'];
        if($sale_bill_type == 2){//线下
            $table = 'offline_order';
            return $this->checkorderno(implode(",",$sub_order_no),$table);
        }else if($sale_bill_type == 3){//三方
            $table = 'tripartite_order';
            return $this->checkorderno(implode(",",$sub_order_no),$table);
        }

        return true;
    }

    /**
     * 验证已提交的订单状态
     * @param $order_no
     * @param $table
     * @return void
     */
    public function checkorderno($sub_order_no,$table)
    {
        $result = Db::name($table)->where('sub_order_no','in',$sub_order_no)->where('invoice_progress',"=",2)->find();
        if($result) return $result['sub_order_no']."：订单已开票";
        return true;
    }

}