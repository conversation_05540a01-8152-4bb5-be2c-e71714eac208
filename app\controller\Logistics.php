<?php


namespace app\controller;


use app\BaseController;
use app\ErrorCode;
use app\Request;
use think\facade\Validate;
use app\service\Logistics as LogisticsService;

class Logistics extends BaseController
{
    /**
     * Description:添加/编辑未发货提醒
     * Author: zrc
     * Date: 2023/2/15
     * Time: 13:54
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function addEditReason(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        //添加数据验证
        if (!isset($params['id'])) {
            $validate = Validate::rule([
                'admin_id|后台用户ID'                 => 'require|number',
                'reason|原因'                       => 'require|max:30',
                'content|未发货文案内容'                 => 'require|max:1000',
                'is_set_delivery_time|是否重新设置发货时间' => 'require|in:0,1',
            ]);
            if (!$validate->check($params)) {
                $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
            }
        }
        $logisticsService = new LogisticsService();
        $result           = $logisticsService->addEditReason($params);
        return $this->success($result);
    }

    /**
     * Description:未发货提醒列表
     * Author: zrc
     * Date: 2023/2/16
     * Time: 10:12
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function reasonList(Request $request)
    {
        $params = $request->param();
        //参数验证
        $validate = Validate::rule([
            'page|页码'      => 'require|number|>:0',
            'limit|每页显示条数' => 'require|number|>:0',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $logisticsService = new LogisticsService();
        $result           = $logisticsService->reasonList($params);
        return $this->success($result);
    }

    /**
     * Description:未发货提醒获取期数信息
     * Author: zrc
     * Date: 2023/2/16
     * Time: 14:33
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function getPeriodInfo(Request $request)
    {
        $params   = $request->param();
        $validate = Validate::rule([
            'type|类型'      => 'require|in:0,1',
            'page|页码'      => 'require|number|>:0',
            'limit|每页显示条数' => 'require|number|>:0',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        switch ($params['type']) {
            case 0:
                if (empty($params['period'])) $this->throwError('未获取到期数信息', ErrorCode::PARAM_ERROR);
                break;
            case 1:
                if (empty($params['sub_order_no']) && empty($params['file'])) $this->throwError('未获取到订单号或导入文件', ErrorCode::PARAM_ERROR);
                break;
        }
        $logisticsService = new LogisticsService();
        $result           = $logisticsService->getPeriodInfo($params);
        return $this->success($result);
    }

    /**
     * Description:提交未发货提醒审批
     * Author: zrc
     * Date: 2023/2/17
     * Time: 14:04
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function submitUnShipApproval(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        $validate           = Validate::rule([
            'admin_id|后台用户ID'    => 'require|number',
            'periodInfo|期数/订单信息' => 'require',
            'reason_id|原因ID'     => 'require|number|>:0',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $logisticsService = new LogisticsService();
        $result           = $logisticsService->submitUnShipApproval($params);
        return $this->success($result);
    }

    /**
     * Description:未发货提醒审批回调处理
     * Author: zrc
     * Date: 2023/2/21
     * Time: 14:32
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function unShipCallBack(Request $request)
    {
        $params = $request->param();
        file_put_contents(root_path() . "/runtime/log/" . date('Ym') . "/" . date('Ymd') . 'weChatCallBackLog' . '.log', json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL, FILE_APPEND);
        if (empty($params)) {
            $this->throwError('未获取到参数');
        }
        if ($params['errcode'] != 0) {
            $this->throwError($params['errmsg']);
        }
        $weChatService = new LogisticsService();
        $result        = $weChatService->unShipCallBack($params);
        return $this->success($result);
    }
}