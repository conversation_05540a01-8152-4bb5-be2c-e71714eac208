<?php


namespace app\model;


use app\service\elasticsearch\ElasticSearchService;
use think\facade\Db;
use think\Model;

class Second extends Model
{
    protected $name = 'second_order';

    protected function getTableName()
    {
        return Db::name($this->name);
    }

    /**
     * Description:获取订单列表
     * Author: zrc
     * Date: 2021/8/2
     * Time: 20:37
     * @param $params
     * @param int $page
     * @param int $limit
     */
    public function getOrderList($params, $page = 1, $limit = 10)
    {
        $where[] = ['order_type' => 1];
        $range   = [];
        if (!empty($params['order_no'])) {
            if (strpos($params['order_no'], 'VHM') !== false || strpos($params['order_no'], 'WYM') !== false) {
                $where[] = ['main_order_no' => $params['order_no']];
            } else {
                $where[] = ['sub_order_no' => $params['order_no']];
            }
        }
        if (isset($params['order_status']) && is_numeric($params['order_status'])) {
            $where[] = ['sub_order_status' => $params['order_status']];
        }
        if (isset($params['refund_status']) && is_numeric($params['refund_status'])) {
            $where[] = ['refund_status' => $params['refund_status']];
        }
        if (!empty($params['period'])) {
            $where[] = ['period' => $params['period']];
        }
        if (!empty($params['title'])) {
            $where[] = ['title' => $params['title']];
        }
        if (!empty($params['nickname'])) {
            $where[] = ['nickname' => $params['nickname']];
        }
        if (!empty($params['consignee'])) {
            //收件人加密查询
            $encrypt             = cryptionDeal(1, [$params['consignee']], '15736175219', '宗仁川');
            $params['consignee'] = isset($encrypt[$params['consignee']]) ? $encrypt[$params['consignee']] : '';
            $where[]             = ['consignee' => $params['consignee']];
        }
        if (!empty($params['consignee_phone'])) {
            //收件人加密查询
            $encrypt                   = cryptionDeal(1, [$params['consignee_phone']], '15736175219', '宗仁川');
            $params['consignee_phone'] = isset($encrypt[$params['consignee_phone']]) ? $encrypt[$params['consignee_phone']] : '';
            $where[]                   = ['consignee_phone' => $params['consignee_phone']];
        }
        if (isset($params['express_type']) && is_numeric($params['express_type'])) {
            $where[] = ['express_type' => $params['express_type']];
        }
        if (!empty($params['express_number'])) {
            $where[] = ['express_number' => $params['express_number']];
        }
        if (isset($params['order_from']) && is_numeric($params['order_from'])) {
            $where[] = ['order_from' => $params['order_from']];
        }
        if (isset($params['payment_method']) && is_numeric($params['payment_method'])) {
            $where[] = ['payment_method' => $params['payment_method']];
        }
        if (isset($params['is_ts']) && is_numeric($params['is_ts'])) {
            $where[] = ['is_ts' => $params['is_ts']];
        }
        if (isset($params['invoice_progress']) && is_numeric($params['invoice_progress'])) {
            $where[] = ['invoice_progress' => $params['invoice_progress']];
        }
        if (!empty($params['stime'])) {
            $range[] = ['created_time' => ['gte' => $params['stime']]];
        }
        if (!empty($params['etime'])) {
            $range[] = ['created_time' => ['lte' => $params['etime']]];
        }
        $order           = [['created_time' => 'desc']];
        $es              = new ElasticSearchService();
        $arr             = array(
            'index' => ['orders'],
            'match' => $where,
            'range' => $range,
            'page'  => $page,
            'limit' => $limit,
            'sort'  => $order
        );
        $data            = $es->getDocumentList($arr);
        $totalNum        = $data['total']['value'];
        $result['list']  = $data['data'];
        $result['total'] = $totalNum;
        return $result;
    }

    /**
     * Description:获取订单详情
     * Author: zrc
     * Date: 2021/8/3
     * Time: 9:59
     * @param $params
     */
    public function getOrderDetail($params)
    {
        $fields = 'o.id,o.main_order_id,o.sub_order_no,om.main_order_no,o.sub_order_status,o.order_from,o.order_qty,o.payment_amount,o.express_fee,o.money_off_split_value,o.coupon_split_value,o.rabbit_payment_amount,o.remarks,o.invoice_progress,o.refund_status,o.express_type,o.express_number,o.is_gift,o.is_ts,o.payment_time,o.goods_receipt_time,o.created_time,o.period,p.title,s.package_name,o.uid,om.payment_amount as main_order_money,om.province_id,om.city_id,om.district_id,om.address,om.consignee,om.consignee_phone,o.return_number,o.push_t_status,o.push_wms_status,o.latest_routing_msg';
        $data   = $this->getTableName()->alias('o')
            ->field($fields)
            ->leftJoin('order_main om', 'om.id=o.main_order_id')
            ->leftJoin('periods_second p', 'p.id=o.period')
            ->leftJoin('periods_second_set s', 's.id=o.package_id')
            ->where(['o.sub_order_no' => $params['sub_order_no']])
            ->find();
        return $data;
    }
}