<?php
/**
 * 收款退款统计接口测试脚本
 */

// 测试配置
$baseUrl = 'http://your-domain.com/orders/v3/payment-statistics/write';

// 测试用例
$testCases = [
    // 新版本收款接口测试
    [
        'name' => '收款接口测试',
        'data' => [
            'main_order_no' => 'VHM202412040001',
            'amount' => 100.50
        ],
        'expected' => '收款数据写入成功'
    ],
    
    // 新版本退款接口测试（工单退款）
    [
        'name' => '工单退款接口测试',
        'data' => [
            'refund_no' => 'GD202412040001',
            'amount' => 50.25
        ],
        'expected' => '退款数据写入成功'
    ],
    
    // 新版本退款接口测试（普通退款）
    [
        'name' => '普通退款接口测试',
        'data' => [
            'refund_no' => 'RF202412040001',
            'amount' => 30.75
        ],
        'expected' => '退款数据写入成功'
    ],
    
    // 旧版本接口测试（向后兼容）
    [
        'name' => '旧版本收款接口测试',
        'data' => [
            'company_code' => '001',
            'operation_type' => 1,
            'amount' => 200.00
        ],
        'expected' => '数据写入成功'
    ],
    
    // 旧版本退款接口测试
    [
        'name' => '旧版本退款接口测试',
        'data' => [
            'company_code' => '002',
            'operation_type' => 2,
            'amount' => 80.00
        ],
        'expected' => '数据写入成功'
    ]
];

/**
 * 发送HTTP POST请求
 */
function sendRequest($url, $data) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'http_code' => $httpCode,
        'response' => json_decode($response, true)
    ];
}

/**
 * 运行测试
 */
function runTests($baseUrl, $testCases) {
    echo "开始运行收款退款统计接口测试...\n\n";
    
    $passCount = 0;
    $totalCount = count($testCases);
    
    foreach ($testCases as $index => $testCase) {
        echo "测试 " . ($index + 1) . ": " . $testCase['name'] . "\n";
        echo "请求数据: " . json_encode($testCase['data'], JSON_UNESCAPED_UNICODE) . "\n";
        
        $result = sendRequest($baseUrl, $testCase['data']);
        
        echo "HTTP状态码: " . $result['http_code'] . "\n";
        echo "响应数据: " . json_encode($result['response'], JSON_UNESCAPED_UNICODE) . "\n";
        
        // 检查测试结果
        $isPass = false;
        if ($result['http_code'] == 200 && 
            isset($result['response']['code']) && 
            $result['response']['code'] == 200 &&
            isset($result['response']['msg']) &&
            strpos($result['response']['msg'], $testCase['expected']) !== false) {
            $isPass = true;
            $passCount++;
        }
        
        echo "测试结果: " . ($isPass ? "通过" : "失败") . "\n";
        echo str_repeat("-", 50) . "\n\n";
    }
    
    echo "测试总结:\n";
    echo "总测试数: $totalCount\n";
    echo "通过数: $passCount\n";
    echo "失败数: " . ($totalCount - $passCount) . "\n";
    echo "通过率: " . round(($passCount / $totalCount) * 100, 2) . "%\n";
}

// 运行测试
if (php_sapi_name() === 'cli') {
    runTests($baseUrl, $testCases);
} else {
    echo "请在命令行环境下运行此测试脚本\n";
}
?>
