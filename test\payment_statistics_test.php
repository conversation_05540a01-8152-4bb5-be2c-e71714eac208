<?php
/**
 * 收款退款统计接口测试脚本
 */

// 测试配置
$baseUrl = 'http://your-domain.com/orders/v3/payment-statistics/write';

// 测试用例
$testCases = [
    // 新版本收款接口测试 - 单频道订单
    [
        'name' => '收款接口测试 - 单频道订单',
        'data' => [
            'main_order_no' => 'VHM202412040001',
            'amount' => 100.50
        ],
        'expected' => '收款数据写入成功'
    ],

    // 新版本收款接口测试 - 多频道订单
    [
        'name' => '收款接口测试 - 多频道混合订单',
        'data' => [
            'main_order_no' => 'VHM202412040002', // 包含闪购+秒发的混合订单
            'amount' => 299.80
        ],
        'expected' => '收款数据写入成功'
    ],
    
    // 新版本退款接口测试（工单退款）
    [
        'name' => '工单退款接口测试',
        'data' => [
            'refund_no' => 'GD202412040001',
            'amount' => 50.25
        ],
        'expected' => '退款数据写入成功'
    ],
    
    // 新版本退款接口测试（普通退款）
    [
        'name' => '普通退款接口测试',
        'data' => [
            'refund_no' => 'RF202412040001',
            'amount' => 30.75
        ],
        'expected' => '退款数据写入成功'
    ],
    
    // 旧版本接口测试（向后兼容）
    [
        'name' => '旧版本收款接口测试',
        'data' => [
            'company_code' => '001',
            'operation_type' => 1,
            'amount' => 200.00
        ],
        'expected' => '数据写入成功'
    ],
    
    // 旧版本退款接口测试
    [
        'name' => '旧版本退款接口测试',
        'data' => [
            'company_code' => '002',
            'operation_type' => 2,
            'amount' => 80.00
        ],
        'expected' => '数据写入成功'
    ]
];

/**
 * 发送HTTP POST请求
 */
function sendRequest($url, $data) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'http_code' => $httpCode,
        'response' => json_decode($response, true)
    ];
}

/**
 * 运行功能测试
 */
function runTests($baseUrl, $testCases) {
    echo "开始运行收款退款统计接口功能测试...\n\n";

    $passCount = 0;
    $totalCount = count($testCases);
    $totalTime = 0;

    foreach ($testCases as $index => $testCase) {
        echo "测试 " . ($index + 1) . ": " . $testCase['name'] . "\n";
        echo "请求数据: " . json_encode($testCase['data'], JSON_UNESCAPED_UNICODE) . "\n";

        $startTime = microtime(true);
        $result = sendRequest($baseUrl, $testCase['data']);
        $endTime = microtime(true);
        $responseTime = round(($endTime - $startTime) * 1000, 2);
        $totalTime += $responseTime;

        echo "HTTP状态码: " . $result['http_code'] . "\n";
        echo "响应时间: {$responseTime}ms\n";
        echo "响应数据: " . json_encode($result['response'], JSON_UNESCAPED_UNICODE) . "\n";

        // 检查测试结果
        $isPass = false;
        if ($result['http_code'] == 200 &&
            isset($result['response']['code']) &&
            $result['response']['code'] == 200 &&
            isset($result['response']['msg']) &&
            strpos($result['response']['msg'], $testCase['expected']) !== false) {
            $isPass = true;
            $passCount++;
        }

        echo "测试结果: " . ($isPass ? "✅ 通过" : "❌ 失败") . "\n";
        echo str_repeat("-", 50) . "\n\n";
    }

    $avgTime = round($totalTime / $totalCount, 2);

    echo "📊 测试总结:\n";
    echo "总测试数: $totalCount\n";
    echo "通过数: $passCount\n";
    echo "失败数: " . ($totalCount - $passCount) . "\n";
    echo "通过率: " . round(($passCount / $totalCount) * 100, 2) . "%\n";
    echo "总响应时间: {$totalTime}ms\n";
    echo "平均响应时间: {$avgTime}ms\n\n";
}

/**
 * 运行性能测试
 */
function runPerformanceTest($baseUrl, $concurrency = 10, $requests = 100) {
    echo "🚀 开始运行性能测试...\n";
    echo "并发数: $concurrency, 总请求数: $requests\n\n";

    $testData = [
        'main_order_no' => 'VHM' . date('YmdHis') . rand(1000, 9999),
        'amount' => rand(100, 1000) / 10
    ];

    $processes = [];
    $results = [];
    $startTime = microtime(true);

    // 启动并发进程
    for ($i = 0; $i < $concurrency; $i++) {
        $requestsPerProcess = intval($requests / $concurrency);
        $cmd = "php " . __FILE__ . " --worker $requestsPerProcess " . base64_encode(json_encode($testData)) . " $baseUrl";
        $processes[$i] = popen($cmd, 'r');
    }

    // 收集结果
    foreach ($processes as $i => $process) {
        $output = stream_get_contents($process);
        pclose($process);
        if ($output) {
            $results[] = json_decode(trim($output), true);
        }
    }

    $endTime = microtime(true);
    $totalTime = ($endTime - $startTime) * 1000;

    // 统计结果
    $totalRequests = 0;
    $successRequests = 0;
    $totalResponseTime = 0;
    $minTime = PHP_FLOAT_MAX;
    $maxTime = 0;

    foreach ($results as $result) {
        $totalRequests += $result['requests'];
        $successRequests += $result['success'];
        $totalResponseTime += $result['total_time'];
        $minTime = min($minTime, $result['min_time']);
        $maxTime = max($maxTime, $result['max_time']);
    }

    $avgResponseTime = $totalRequests > 0 ? round($totalResponseTime / $totalRequests, 2) : 0;
    $qps = $totalTime > 0 ? round($totalRequests / ($totalTime / 1000), 2) : 0;
    $successRate = $totalRequests > 0 ? round(($successRequests / $totalRequests) * 100, 2) : 0;

    echo "📈 性能测试结果:\n";
    echo "总请求数: $totalRequests\n";
    echo "成功请求数: $successRequests\n";
    echo "成功率: {$successRate}%\n";
    echo "总耗时: " . round($totalTime, 2) . "ms\n";
    echo "QPS: $qps\n";
    echo "平均响应时间: {$avgResponseTime}ms\n";
    echo "最小响应时间: " . round($minTime, 2) . "ms\n";
    echo "最大响应时间: " . round($maxTime, 2) . "ms\n\n";
}

/**
 * 工作进程（用于并发测试）
 */
function workerProcess($requests, $testData, $baseUrl) {
    $results = [
        'requests' => $requests,
        'success' => 0,
        'total_time' => 0,
        'min_time' => PHP_FLOAT_MAX,
        'max_time' => 0
    ];

    for ($i = 0; $i < $requests; $i++) {
        $startTime = microtime(true);
        $result = sendRequest($baseUrl, $testData);
        $endTime = microtime(true);

        $responseTime = ($endTime - $startTime) * 1000;
        $results['total_time'] += $responseTime;
        $results['min_time'] = min($results['min_time'], $responseTime);
        $results['max_time'] = max($results['max_time'], $responseTime);

        if ($result['http_code'] == 200) {
            $results['success']++;
        }
    }

    echo json_encode($results);
}

// 主执行逻辑
if (php_sapi_name() === 'cli') {
    // 检查是否为工作进程
    if (isset($argv[1]) && $argv[1] === '--worker') {
        $requests = intval($argv[2]);
        $testData = json_decode(base64_decode($argv[3]), true);
        $baseUrl = $argv[4];
        workerProcess($requests, $testData, $baseUrl);
        exit;
    }

    // 解析命令行参数
    $options = getopt('', ['performance', 'concurrency:', 'requests:', 'help']);

    if (isset($options['help'])) {
        echo "用法:\n";
        echo "  php payment_statistics_test.php                    # 运行功能测试\n";
        echo "  php payment_statistics_test.php --performance      # 运行性能测试\n";
        echo "  php payment_statistics_test.php --performance --concurrency=20 --requests=200\n";
        echo "\n选项:\n";
        echo "  --performance     运行性能测试\n";
        echo "  --concurrency=N   并发数 (默认: 10)\n";
        echo "  --requests=N      总请求数 (默认: 100)\n";
        echo "  --help           显示帮助信息\n";
        exit;
    }

    if (isset($options['performance'])) {
        // 性能测试
        $concurrency = isset($options['concurrency']) ? intval($options['concurrency']) : 10;
        $requests = isset($options['requests']) ? intval($options['requests']) : 100;
        runPerformanceTest($baseUrl, $concurrency, $requests);
    } else {
        // 功能测试
        runTests($baseUrl, $testCases);
    }
} else {
    echo "请在命令行环境下运行此测试脚本\n";
    echo "示例: php payment_statistics_test.php\n";
    echo "性能测试: php payment_statistics_test.php --performance\n";
}
?>
