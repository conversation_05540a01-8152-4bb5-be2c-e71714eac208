<?php
// +----------------------------------------------------------------------
// | 控制台配置
// +----------------------------------------------------------------------
return [
    // 指令定义
    'commands' => [
        'add_cross_cardno_yearmoney'         => 'app\command\CrossCardnoYearMoneyCommand',//累计前一天跨境自然年身份证消费金额(每天1点执行)
        'automatic_goods_receipt'            => 'app\command\AutomaticGoodsReceiptCommand',//订单15天自动确认收货(每天凌晨执行)
        'repurchchase'                       => 'app\command\RepurchChase',//复购短信发送(每半小时执行)
        'tripartite_order_push_WMS'          => 'app\command\TripartiteOrderPushWMSCommand',//三方订单每天10：00到18：00自动推送（30分钟执行一次）
        'cross_auto_push_order_daily_gather' => 'app\command\CrossAutoPushOrderDailyGatherCommand',//跨境自动推单每日统计（每天早上9点执行一次）
        'new_custom_allocation'              => 'app\command\NewCustomAllocationCommand',//新客户每天轮询分配（每天早上9点执行一次）
        'silent_custom_allocation'           => 'app\command\SilentCustomAllocationCommand',//沉默客户每周轮询分配（每周一早上9点执行一次）
        'pre_sales_data_snapshot'            => 'app\command\PreSalesDataSnapshotCommand',//售前数据快照（每月1号凌晨2点执行一次）
        'deposit_grant_coupon_compensate'    => 'app\command\DepositGrantCouponCompensateCommand',//定金订单发放优惠券补偿（每15分钟执行一次）
        'collection_information_query'       => 'app\command\CollectionInformationQueryCommand',//中台制单收款金额查询处理（每天2点执行一次）
        'ldleStock'                          => 'app\command\LdleStock',//跨境闲置存货(每天8点执行一次)
        'lockedOrder'                        => 'app\command\LockedOrder',//跨境锁定订单(每周一9点执行一次)
        'gift'                               => 'app\command\Gift',//每10分钟执行一次
        'not_cancelled'                      => 'app\command\NotCancelled',//
        'delivery_exception'                 => 'app\command\DeliveryException',//每天分钟执行一次
        'cancel_ts'                          => 'app\command\CancelTs',//每天执行一次
        'balanceAutoRefund'                  => 'app\command\BalanceAutoRefund',//常驻,每隔五秒执行一次 传代码到指定分支
        'delivery'                          => 'app\command\Delivery',//每天10点定时发货
        'payment_statistics_sync'            => 'app\command\PaymentStatisticsSyncCommand',//收款统计数据同步(每天凌晨2点执行)
    ],
];
