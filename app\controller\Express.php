<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use app\ErrorCode;
use app\Request;
use app\service\Express as ExpressService;
use app\validate\ListPagination;
use think\Exception;

class Express extends BaseController
{
    /**
     * Description：快递方式列表
     * Author: zjl
     * Date: 2022/5/6
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function index(Request $request)
    {
        $params   = $request->param();
        $validate = new ListPagination();
        if (!$validate->goCheck($params)) {//验证参数
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $result = (new ExpressService())->getExpressList($params);
        return $this->success($result);
    }

    /**
     * 商品添加调用接口
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function webList(Request $request)
    {
        $params   = $request->param();
        $validate = new ListPagination();
        if (!$validate->goCheck($params)) {//验证参数
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $result = (new ExpressService())->webList($params);
        return $this->success($result);
    }

    /**
     * Description：快递方式添加
     * Author: zjl
     * Date: 2022/5/6
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function create(Request $request)
    {
        $params = $request->param();
        $result= (new ExpressService())->createExpress($params);
        return  $this->success($result,"添加成功");

    }

    /**
     * Description：快递方式修改
     * Author: zjl
     * Date: 2022/5/6
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function update(Request $request)
    {
        $params = $request->param();
        $result= (new ExpressService())->createExpress($params);
        return  $this->success($result,"操作成功");
    }

    /**
     * 状态设置
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function setStatus(Request $request)
    {
        $params = $request->param();
        $result= (new ExpressService())->setStatus($params);
        if ($result){
            return  $this->success($result,"操作成功");
        }
        $this->throwError("操作失败");
    }

}
