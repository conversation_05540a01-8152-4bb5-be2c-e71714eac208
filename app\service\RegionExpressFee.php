<?php


namespace app\service;


use app\BaseService;
use think\facade\Db;

class RegionExpressFee extends BaseService
{
    public function query($params)
    {
        $result                       = [];
        $express_fee                  = 0;
        $total_package_materials_fee  = 0;
        $package_materials_fee_config = Db::name('package_materials_fee_config')->where(['id' => 1])->find();
        if (empty($package_materials_fee_config)) $this->throwError('未获取到包材费用配置');
        $package_materials = $package_materials_fee_config['package_materials_weight'];//包材单个重量kg
        $ice_pack          = $package_materials_fee_config['ice_pack_weight'];//冰袋单个重量kg
        $ice_pack_fee      = $package_materials_fee_config['ice_pack_fee'];//冰袋单个费用
        $short_code_arr    = array_column($params['goods_info'], 'short_code');
        $products          = Db::table('vh_wiki.vh_products')->field('id,short_code,weight')->where([['short_code', 'in', $short_code_arr]])->select()->toArray();

        $no_match_arr = [];
        $psc_arr      = array_column($products, 'short_code');
        foreach ($short_code_arr as $sc_item) {
            if (!in_array($sc_item, $psc_arr)) {
                $no_match_arr = [$sc_item];
            }
        }
        if (!empty($no_match_arr)) {
            $this->throwError("未找到简码：" . implode(',', $no_match_arr));
        }

        $total_weight      = 0;
        $total_nums        = 0;
        foreach ($params['goods_info'] as &$val) {
            foreach ($products as &$v) {
                if ($v['short_code'] == $val['short_code']) {
                    $total_weight += $v['weight'] * $val['nums'];
                }
            }
            $total_nums += $val['nums'];
        }
        //冷链加冰袋重量+费用
        if ($params['is_cold_chain'] == 1) {
            $total_weight                = $total_weight + $total_nums * $ice_pack;
            $total_package_materials_fee = $total_nums * $ice_pack_fee;
        }
        //非原箱加包材重量+费用
        if ($params['is_original_package'] == 0) {
            $total_weight          = $total_weight + $total_nums * $package_materials;
            $package_materials_fee = json_decode($package_materials_fee_config['package_materials_fee'], true);//包材费用规则
            $remainder             = $total_nums % 6;
            if ($remainder == 0) {
                $total_package_materials_fee = $total_package_materials_fee + intval($total_nums / 6) * $package_materials_fee[6];
            } else {
                $total_package_materials_fee = $total_package_materials_fee + intval($total_nums / 6) * $package_materials_fee[6] + $package_materials_fee[$remainder];
            }
        }
        //总重量0.5进位处理
        $remainder_weight = $total_weight * 100 % 100 / 100;
        if ($remainder_weight>0 && $remainder_weight < 0.5) {
            $total_weight = intval($total_weight * 100 / 100) + 0.5;
        } else if ($remainder_weight > 0.5) {
            $total_weight = intval($total_weight * 100 / 100) + 1;
        }
        switch ($params['express_type']) {
            case 1://顺丰
                $rule_json = Db::name('region_express_fee')->where(['type' => 3, 'province_id' => $params['province_id']])->value('rule_json');
                if (empty($rule_json)) $this->throwError('未获取到当前收货地区快递收费规则，请联系管理员处理！');
                $rule = json_decode($rule_json, true);
                if ($total_weight <= 1) {
                    $express_fee = $rule['one_kilo'] + 0;
                } else if ($total_weight <= 2) {
                    $express_fee = $rule['two_kilo'] + 0;
                } else if ($total_weight <= 3) {
                    $express_fee = $rule['three_kilo'] + 0;
                } else {
                    $express_fee = $rule['first_weight_three'] + $rule['continuation_weight_three'] * ($total_weight - 3);
                }
                break;
            case 2://京东
                $where = [];
                if ($params['delivery_place'] == 2) {
                    $where[] = ['type', '=', 2];
                } else {
                    $where[] = ['type', '=', 1];
                }
                $where[]   = ['province_id', '=', $params['province_id']];
                $where[]   = ['city_id', '=', $params['city_id']];
                $rule_json = Db::name('region_express_fee')->where($where)->value('rule_json');
                if (empty($rule_json)) $this->throwError('未获取到当前收货地区快递收费规则，请联系管理员处理！');
                $rule = json_decode($rule_json, true);
                if ($total_weight <= 1) {
                    $express_fee = $rule['first_weight'] + 0;
                } else if ($total_weight <= 30) {
                    $express_fee = $rule['first_weight'] + $rule['continuation_weight'] * ($total_weight - 1);
                } else {
                    $express_fee = $rule['first_weight'] + $rule['continuation_weight_thirty'] * ($total_weight - 1);
                }
                //快递折扣处理-京东快递默认4折
                $express_fee = $express_fee * 0.4;
                //保价费用
                if ($params['is_insured'] == 1) {
                    if ($params['order_money'] <= 500) {
                        $express_fee = $express_fee + 1;
                    } else if ($params['order_money'] <= 1000) {
                        $express_fee = $express_fee + 2;
                    } else {
                        $express_fee = round($express_fee + $params['order_money'] * 0.002, 2);
                    }
                }
                break;
        }
        $result = array(
            'package_materials_fee' => round($total_package_materials_fee, 2),
            'express_fee'           => round($express_fee, 2)
        );
        return $result;
    }
}