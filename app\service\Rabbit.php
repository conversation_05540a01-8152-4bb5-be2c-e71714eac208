<?php


namespace app\service;


use app\BaseService;
use app\ErrorCode;
use app\model\Rabbit as RabbitModel;
use app\service\Additional as AdditionalService;
use app\service\elasticsearch\ElasticSearchService;
use app\service\Push as PushService;
use app\service\Wms as WmsService;
use think\facade\Db;
use think\facade\Log;

class Rabbit extends BaseService
{
    /**
     * Description:兔头实物创建订单
     * Author: zrc
     * Date: 2021/8/3
     * Time: 17:15
     * @param $requestparams
     */
    public function createOrder($requestparams)
    {
        $params = $requestparams;
        //主、子订单金额计算
        $total_money = 0;
        $items       = [];
        foreach ($params['items_info'] as &$val) {
            /***限购验证***/
            $userInfo = httpGet(env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo', ['uid' => $params['uid'], 'field' => 'user_level,created_time,is_prohibit_buy']);
            if ($userInfo['error_code'] != 0 || !isset($userInfo['data']['list'][0])) $this->throwError('未获取到用户信息');
            $userInfo = $userInfo['data']['list'][0];
            if ($userInfo['is_prohibit_buy'] == 1) $this->throwError('您已被禁购！');
            $es            = new ElasticSearchService();
            $esPeriodsData = $es->getDocumentList(['index' => ['periods'], 'match' => [['id' => $val['period']]], 'source' => ['title', 'quota_rule'], 'limit' => 1]);
            if (!isset($esPeriodsData['data'][0]['quota_rule'])) $this->throwError('未获取到商品信息');
            $quota_rule = json_decode($esPeriodsData['data'][0]['quota_rule'], true);
            if ($quota_rule) {
                if ($quota_rule['quota_number']) {
                    if ($val['nums'] > $quota_rule['quota_number']) $this->throwError('该商品限购' . $quota_rule['quota_number'] . '份');
                }
                if ($quota_rule['quota_type'] == 0) {
                    $purchased = $es->getDocumentList(['index' => ['orders'], 'match' => [['uid' => $params['uid']], ['period' => $val['period']]], 'range' => [['sub_order_status' => ['gte' => 1]], ['sub_order_status' => ['lte' => 3]]], 'source' => ['id'], 'limit' => 1]);
                    if (count($purchased['data']) + $val['nums'] > $quota_rule['quota_number']) $this->throwError('该商品限购' . $quota_rule['quota_number'] . '份,您已购买' . count($purchased['data']) . '份');
                }
                if (!empty($quota_rule['register_time'])) {
                    if ($quota_rule['register_time'] > $userInfo['created_time']) $this->throwError($esPeriodsData['data'][0]['title'] . ':商品限' . $quota_rule['register_time'] . '之后注册的用户购买');
                }
                if (!empty($quota_rule['rank'])) {
                    $rank      = explode(',', $quota_rule['rank']);
                    $min_level = $rank[0];
                    $max_level = $rank[1];
                    if ($min_level <= $userInfo['user_level'] && $userInfo['user_level'] <= $max_level) $this->throwError($esPeriodsData['data'][0]['title'] . ':商品限等级为' . $min_level . '到' . $max_level . '的用户禁止购买');
                }
                if (!empty($quota_rule['district'])) {
                    $district = explode(',', $quota_rule['district']);
                    if (in_array($params['province_id'], $district)) $this->throwError('抱歉兔友，您的收货地区暂时无法下单');
                }
            }
            /***限购验证***/
            //套餐ID获取商品套餐信息
            $packageInfo = esGetOne($val['package_id'], 'vinehoo.periods_set');
            if (empty($packageInfo)) $this->throwError('未获取到商品套餐信息');
            $val['rabbit_payment_amount'] = $packageInfo['rabbit'] * $val['nums'];
            $total_money                  += $val['rabbit_payment_amount'];
            $items[]                      = array(
                'period'      => $val['period'],
                'set_id'      => $val['package_id'],
                'buy_num'     => $val['nums'],
                'channel'     => 'rabbit',
                'mystery_box' => [],
            );
        }
        $main_order_no = creatOrderNo(env('ORDERS.ORDER_MAIN'), $params['uid']);
        /**库存验证+扣除可用库存 start**/
        $stock_param = json_encode(['orderno' => $main_order_no, 'groupid' => 0, 'items' => $items]);
        $stockVerify = httpPostString(env('ITEM.VINEHOO_INVENTORY_INOUT_URL') . '/inventory_service/v3/inventory/dec', $stock_param);
        if (!isset($stockVerify['error_code']) || $stockVerify['error_code'] != 0) {
            $this->throwError('商品库存不足！',20107);
        }
        /**库存验证+扣除可用库存 end**/
        //兔头扣减
        $pushData  = array(
            'uid'         => $params['uid'],
            'change_type' => 2,
            'rabbit'      => $total_money,
            'source'      => 4,
            'reason'      => $main_order_no . ':兔头商品兑换',
            'orderno'     => $main_order_no,
        );
        $decRabblt = $this->httpPost(env('ITEM.USER_URL') . '/user/v3/user/updateAssets', $pushData);
        if (!isset($decRabblt['error_code']) || $decRabblt['error_code'] != 0) {
            //退还库存
            httpPostString(env('ITEM.VINEHOO_INVENTORY_INOUT_URL') . '/inventory_service/v3/inventory/inc', $stock_param);
            $this->throwError($decRabblt['error_msg']);
        }
        //推送兔头兑换订单处理队列
        $queueData = array(
            'main_order_no'  => $main_order_no,
            'rabbit'         => $total_money,
            'request_params' => $requestparams,
        );
        $pushData  = array(
            'exchange_name' => 'orders',
            'routing_key'   => 'create_rabbit_order',
            'data'          => base64_encode(json_encode($queueData)),
        );
        $result    = httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
        if (!isset($result['error_code']) || $result['error_code'] != 0) {
            //退还库存
            httpPostString(env('ITEM.VINEHOO_INVENTORY_INOUT_URL') . '/inventory_service/v3/inventory/inc', $stock_param);
            $this->throwError($decRabblt['error_msg']);
            //退还兔头
            $pushData = array(
                'uid'         => $params['uid'],
                'change_type' => 1,
                'rabbit'      => $total_money,
                'source'      => 4,
                'reason'      => $main_order_no . ':兔头商品兑换失败，退还兔头。',
                'orderno'     => $main_order_no,
            );
            $this->httpPost(env('ITEM.USER_URL') . '/user/v3/user/updateAssets', $pushData);
            $this->throwError('兑换失败，数据推送队列异常');
        }
        $backParams = array(
            'main_order_no' => $main_order_no,
            'created_time'  => time()
        );
        return $backParams;
    }

    /**
     * Description:兔头兑换实物创建订单队列处理
     * Author: zrc
     * Date: 2022/6/24
     * Time: 11:49
     * @param $requestparams
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function createRabbitOrderDeal($requestparams)
    {
        $queueData     = json_decode($requestparams, true);
        $main_order_no = $queueData['main_order_no'];
        $total_money   = $queueData['rabbit'];
        $params        = $queueData['request_params'];
        Db::startTrans();
        try {
            //用户信息加密处理
            $consignee = trim($params['consignee']);
            $phone     = trim($params['consignee_phone']);
            $encrypt   = cryptionDeal(1, [$consignee, $phone], $params['uid'], '前端用户');
            $consignee = isset($encrypt[$consignee]) ? $encrypt[$consignee] : '';
            $phone     = isset($encrypt[$phone]) ? $encrypt[$phone] : '';
            //主订单数据写入
            $mainData    = array(
                'uid'                   => $params['uid'],
                'main_order_no'         => $main_order_no,
                'main_order_status'     => 1,
                'payment_method'        => 201,
                'payment_amount'        => 0,
                'rabbit_payment_amount' => $total_money,
                'province_id'           => $params['province_id'],
                'city_id'               => $params['city_id'],
                'district_id'           => $params['district_id'],
                'address'               => trim($params['address']),
                'consignee'             => $consignee,
                'consignee_phone'       => $phone,
                'created_time'          => time(),
                'payment_time'          => time(),
            );
            $orderIdMain = Db::name('order_main')->insertGetId($mainData);
            //子订单数据写入
            foreach ($params['items_info'] as &$v) {
                $sub_order_no = creatOrderNo(env('ORDERS.RABBIT'), $params['uid']);
                //仓库编码获取
                $productList    = $this->httpGet(env('ITEM.COMMODITIES_URL') . '/commodities/v3/package/productList', ['period' => $v['period'], 'periods_type' => 4]);
                $warehouse_code = isset($productList['data'][0]['product'][0]['erp_id']) ? $productList['data'][0]['product'][0]['erp_id'] : '';
                $subData        = array(
                    'uid'                   => $params['uid'],
                    'sub_order_no'          => $sub_order_no,
                    'sub_order_status'      => 1,
                    'main_order_id'         => $orderIdMain,
                    'period'                => $v['period'],
                    'package_id'            => $v['package_id'],
                    'order_from'            => $params['order_from'],
                    'order_qty'             => $v['nums'],
                    'rabbit_payment_amount' => $v['rabbit_payment_amount'],
                    'express_type'          => 2,//默认顺丰
                    'express_number'        => '',
                    'created_time'          => time(),
                    'payment_time'          => time(),
                    'order_type'            => 4,
                    'predict_time'          => $v['predict_time'],
                    'warehouse_code'        => $warehouse_code,
                );
                Db::name('rabbit_order')->insert($subData);
            }

            Db::commit();
            //商品已购数量增加
            foreach ($params['items_info'] as &$val) {
                $periodsPlusMinus = array(
                    'period'       => $val['period'],
                    'package_id'   => $val['package_id'],
                    'periods_type' => 4,
                    'type'         => 'order',
                    'count'        => $val['nums']
                );
                $periodsPlusMinus = $this->httpPost(env('ITEM.COMMODITIES_URL') . '/commodities/v3/periods/periodsPlusMinus', $periodsPlusMinus);
                if (!isset($periodsPlusMinus['error_code']) || $periodsPlusMinus['error_code'] != 0) {
                    Log::error($main_order_no . '：兔头商品已购数量增加失败！' . $periodsPlusMinus['error_msg']);
                }
                //期数已购标识添加
                $additionalService = new AdditionalService();
                $additionalService->periodPurchasedAdd(['period' => $val['period'], 'uid' => $params['uid']]);
            }
            //推送萌牙
            $pushWms = httpPostString(env('ITEM.PUSH_ORDERS_URL') . '/pushorders/v3/order/push', json_encode(['sub_order_no' => $sub_order_no, 'order_type' => 4]));
            if (!isset($pushWms['error_code'])) {
                $pushWms = array(
                    'error_code' => 10002,
                    'error_msg'  => '推送发货仓失败',
                );
            }
            if ($pushWms['error_code'] == 0) {
                if (isset($pushWms['data']['wms_type']) && $pushWms['data']['wms_type'] == 1) $pushWms['error_msg'] = '推送萌牙成功';
                if (isset($pushWms['data']['wms_type']) && $pushWms['data']['wms_type'] == 2) $pushWms['error_msg'] = '推送京东成功';
            }
            $wmsService = new WmsService();
            //推送萌牙结果处理
            $resultData = array(
                'push_wms_status' => $pushWms['error_code'] == 0 ? 1 : 2,
                'sub_order_no'    => $sub_order_no,
                'order_type'      => 4,
                'msg'             => isset($pushWms['error_msg']) ? $pushWms['error_msg'] : '推送发货仓失败'
            );
            $wmsService->pushWmsResultDeal($resultData);
        } catch (\Exception $e) {
            Db::rollback();
            $this->throwError($main_order_no . '兔头兑换实物创建订单失败：' . $e->getMessage());
        }
        return true;
    }

    /**
     * Description:兔头实物订单修改
     * Author: zrc
     * Date: 2021/8/4
     * Time: 14:16
     * @param $requestparams
     */
    public function updateOrder($requestparams)
    {
        $params    = $requestparams;
        $orderInfo = Db::name('rabbit_order')->field('id,sub_order_status,main_order_id')->where(['sub_order_no' => $params['sub_order_no']])->find();
        if (empty($orderInfo)) {
            $this->throwError('未获取到订单信息');
        }
        if (isset($params['sub_order_status']) && is_numeric($params['sub_order_status'])) $updateData['sub_order_status'] = $params['sub_order_status'];
        if (isset($params['is_ts']) && is_numeric($params['is_ts'])) $updateData['is_ts'] = $params['is_ts'];
        if (isset($params['express_type']) && is_numeric($params['express_type'])) $updateData['express_type'] = $params['express_type'];
        if (!empty($params['consignee'])) {
            //用户信息加密处理
            $consignee                   = trim($params['consignee']);
            $encrypt                     = cryptionDeal(1, [$consignee], $params['operator'], '前端用户');
            $consignee                   = isset($encrypt[$consignee]) ? $encrypt[$consignee] : '';
            $updateMainData['consignee'] = $consignee;
        }
        if (!empty($params['consignee_phone'])) {
            //用户信息加密处理
            $phone                             = trim($params['consignee_phone']);
            $encrypt                           = cryptionDeal(1, [$phone], $params['operator'], '前端用户');
            $phone                             = isset($encrypt[$phone]) ? $encrypt[$phone] : '';
            $updateMainData['consignee_phone'] = $phone;
        }
        if (!empty($params['province_id'])) $updateMainData['province_id'] = $params['province_id'];
        if (!empty($params['city_id'])) $updateMainData['city_id'] = $params['city_id'];
        if (!empty($params['district_id'])) $updateMainData['district_id'] = $params['district_id'];
        if (!empty($params['address'])) $updateMainData['address'] = trim($params['address']);
        if (!empty($params['return_number'])) $updateData['return_number'] = trim($params['return_number']);
        if (isset($updateData)) {
            $result = Db::name('rabbit_order')->where(['id' => $orderInfo['id']])->update($updateData);
            if (empty($result)) $this->throwError('修改订单信息失败');
        }
        if (isset($updateMainData)) {
            $result = Db::name('order_main')->where(['id' => $orderInfo['main_order_id']])->update($updateMainData);
            if (empty($result)) $this->throwError('修改订单信息失败');
        }
        return true;
    }

    /**
     * Description:兔头实物订单列表
     * Author: zrc
     * Date: 2021/8/4
     * Time: 14:20
     * @param $requestparams
     */
    public function orderList($requestparams)
    {
        $page        = !empty($requestparams['page']) ? $requestparams['page'] : 1;
        $limit       = !empty($requestparams['limit']) ? $requestparams['limit'] : 10;
        $params      = $requestparams;
        $rabbitModel = new RabbitModel();
        $orderLists  = $rabbitModel->getOrderList($params, $page, $limit);
        return $orderLists;
    }
}