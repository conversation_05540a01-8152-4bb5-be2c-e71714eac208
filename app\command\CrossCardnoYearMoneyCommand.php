<?php
declare (strict_types = 1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;

/**
 * 累计前一天跨境自然年身份证消费金额
 * Class CrossCardnoYearMoneyCommand
 * @package app\command
 * zrc
 */
class CrossCardnoYearMoneyCommand extends Command
{
    protected $service;

    protected function configure()
    {
        // 指令配置
        $this->setName('CrossCardnoYearMoneyCommand')
            ->setDescription('the CrossCardnoYearMoneyCommand command');
    }

    protected function execute(Input $input, Output $output)
    {
    	// 指令输出
        $this->init();
        $this->service->exec();
    }

    protected function init(){
      $this->service = new \app\service\command\CrossCardnoYearMoneyCommand();
    }
}
