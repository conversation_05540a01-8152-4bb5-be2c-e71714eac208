<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use app\ErrorCode;
use app\service\Invoice as InvoiceService;
use think\exception\ValidateException;
use think\Request;

class Invoice extends BaseController
{

    /**
     * 列表
     * @param Request $request
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function list(Request $request)
    {
        $params      = $request->param();
        $h_referer   = request()->header('referer', '');
        $referer_arr = parse_url($h_referer);
        $h_host      = $referer_arr['host'] ?? '';
        if ($h_host == 'os.mulando.cn') $params['incoic_form'] = 5;
        $result = InvoiceService::list($params);
        return $this->success($result);
    }

    /**
     * 修改
     * @param Request $request
     * @return \think\response\Json
     */
    public function save(Request $request)
    {

        $params = $request->param();
        $params['operator_id'] = $request->header('vinehoo-uid');
        //验证参数
        validate(\app\validate\InviceValidate::class)->check($params);

        $params['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));

        $result = InvoiceService::save($params);
        return $this->success((object)[]);
    }

    public function detail(Request $request)
    {

        $params = $request->param();
        $result = InvoiceService::detail($params);
        return $this->success($result);
    }

    public function approval(Request $request)
    {
        $params = $request->param();
        $params['approver_id'] = $request->header('vinehoo-uid');
        $params['approver'] = base64_decode($request->header('vinehoo-vos-name'));
        if($params['invoic_status'] == 2){//审核通过

            $result = \app\model\Invoice::where('id',$params['id'])->find();
            if(!$result) new ValidateException("数据不存在");
            $invoic_courier_no = $result['invoic_courier_no'] || $result['invoice_no']??false;//发票快递单号  发票号

            if($invoic_courier_no){
                $params['invoic_status'] = 2;
            }else{
                $incoic_form = $result['incoic_form'];
                switch ($incoic_form){
                    case 1: //1 百酿云酒商务
                        if($params['invoic_type'] == 2){//专票
                            $params['invoic_status'] = 2;//专票手动开票
                        }else{
                            $params['invoic_status'] = 1;
                        }
                        break;
                    case 2:// 2百酿云酒科技
                        $params['invoic_status'] = 1;
                        break;
                    case 3://桃氏物语
                        $params['invoic_status'] = 2;
                        break;
                    default:
                        break;
                        //todo ....  4 => '兔子星球', 5 => '木兰朵'
                }
            }

        }
        $result = InvoiceService::approval($params);

        if(is_string($result)){
            return $this->throwError($result);
        }else if($result === true){
            return $this->success((object)[]);
        }else{
            return $this->throwError("修改失败,未改动");
        }

    }
    /**
     * 筛选需要开票的销售单据
     * @param Request $request
     * @return \think\response\Json
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getSalesDocumentsList(Request $request)
    {
        $params = $request->param();
        if (!isset($params['sales_type']) ||empty($params['sales_type'])) {
            $this->throwError('销售单据类型必传', ErrorCode::PARAM_ERROR);
        }
        $orderService = new InvoiceService();
        $result  = $orderService->searchSalesDocuments($params);
        return $this->success($result);
    }

    public function export(Request $request)
    {
        $params = $request->param();
        [$file,$filename] = InvoiceService::export($params);
        return download($file,$filename);
    }

    public function getSalesDocumentsAllList(Request $request)
    {
        $params = $request->param();
        if (!isset($params['sales_type']) ||empty($params['sales_type'])) {
            $this->throwError('销售单据类型必传', ErrorCode::PARAM_ERROR);
        }
        $orderService = new InvoiceService();
        $result  = $orderService->getSalesDocumentsAllList($params);
        return $this->success($result);
    }

    //开票回调
    public function callback(Request $request)
    {
        $params = $request->param();
        $result = InvoiceService::callback($params);
        return $this->success((object)[]);
    }
}
