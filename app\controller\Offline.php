<?php


namespace app\controller;


use app\BaseController;
use app\ErrorCode;
use app\Request;
use app\service\Offline as OfflineService;
use app\validate\ListPagination;
use think\Exception;
use think\facade\Db;
use think\facade\Log;
use think\facade\Validate;

class Offline extends BaseController
{
    /**
     * Description:生成单据编号
     * Author: zrc
     * Date: 2022/5/11
     * Time: 17:28
     * @return string
     */
    public function createOrderNo()
    {
        $orderSn = 'SO-' . date('Y') . '-' . date('m') . '-' . date('d') . '-' . str_pad(rand(100, 9999), 4, '0', STR_PAD_LEFT);
        return $this->success(['order_no' => $orderSn]);
    }

    /**
     * Description:线下样酒申请
     * Author: zrc
     * Date: 2022/5/11
     * Time: 13:39
     * @param Request $request
     * @throws \Exception
     */
    public function createSaleOrder(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        //参数验证
        $validate = Validate::rule([
            'admin_id|后台用户ID'               => 'require|number',
            'order_no|单据编号'                 => 'require',
            'voucher_date|单据日期'             => 'require',
            'customer|客户'                     => 'require',
            'customer_code|客户编码'            => 'require',
            'settle_customer|结算客户'          => 'require',
            'settle_customer_code|结算客户编码' => 'require',
            'warehouse|仓库'                    => 'require',
            'warehouse_code|仓库编码'           => 'require',
            'address|收货地址'                  => 'require',
            'consignee|联系人'                  => 'require',
            'consignee_phone|联系电话'          => 'require|number',
            'delivery_mode|运输方式'            => 'require',
            'delivery_mode_code|运输方式编码'   => 'require',
            'items_info|明细'                   => 'require',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        if (count($params['items_info']) > 40) $this->throwError('产品明细最多填写40个');
        foreach ($params['items_info'] as &$val) {
            //参数验证
            $validate = Validate::rule([
                'bar_code|条码'         => 'require',
                'short_code|简码'       => 'require',
                'product_name|产品名称' => 'require',
                'nums|数量'             => 'require|number|>:0',
                'price|含税单价'        => 'require|float',
                'unit|规格型号'         => 'require',
                'is_gift|是否赠品'      => 'require|in:0,1',
            ]);
            if (!$validate->check($val)) {
                $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
            }
        }
        $offlineService = new OfflineService();
        $result         = $offlineService->createSaleOrder($params);
        return $this->success($result);
    }

    /**
     * Description:样酒销售单列表
     * Author: zrc
     * Date: 2022/5/11
     * Time: 16:06
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function saleOrderList(Request $request)
    {
        $params   = $request->param();
        $validate = new ListPagination();
        if (!$validate->goCheck($params)) {//验证参数
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $offlineService = new OfflineService();
        $result         = $offlineService->saleOrderList($params);
        return $this->success($result);
    }

    /**
     * Description:编辑推送失败的样酒销售单
     * Author: zrc
     * Date: 2022/5/11
     * Time: 16:49
     * @param Request $request
     * @return mixed
     */
    public function updateSaleOrder(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        //参数验证
        $validate = Validate::rule([
            'id|销售单ID'                       => 'require|number',
            'admin_id|后台用户ID'               => 'require|number',
            'order_no|单据编号'                 => 'require|max:30',
            'voucher_date|单据日期'             => 'require',
            'customer|客户'                     => 'require',
            'customer_code|客户编码'            => 'require',
            'settle_customer|结算客户'          => 'require',
            'settle_customer_code|结算客户编码' => 'require',
            'warehouse|仓库'                    => 'require',
            'warehouse_code|仓库编码'           => 'require',
            'address|收货地址'                  => 'require',
            'consignee|联系人'                  => 'require',
            'consignee_phone|联系电话'          => 'require|number',
            'delivery_mode|运输方式'            => 'require',
            'delivery_mode_code|运输方式编码'   => 'require',
            'items_info|明细'                   => 'require',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        if (count($params['items_info']) > 40) $this->throwError('产品明细最多填写40个');
        foreach ($params['items_info'] as &$val) {
            //参数验证
            $validate = Validate::rule([
                'bar_code|条码'         => 'require',
                'short_code|简码'       => 'require',
                'product_name|产品名称' => 'require',
                'nums|数量'             => 'require|number|>:0',
                'price|含税单价'        => 'require|float',
                'unit|规格型号'         => 'require',
                'is_gift|是否赠品'      => 'require|in:0,1',
            ]);
            if (!$validate->check($val)) {
                $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
            }
        }
        $offlineService = new OfflineService();
        $result         = $offlineService->updateSaleOrder($params);
        return $this->success($result);
    }

    /**
     * Description:新增普通销售单
     * Author: zrc
     * Date: 2022/9/30
     * Time: 14:55
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function newAddSaleOrder(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        //操作类型 type：0-新增（提交审批） 1-保存（不提交审批）
        if (!isset($params['type'])) $params['type'] = 0;
        //是否样酒：0-否 1-是
        if (!isset($params['is_sample_liquor'])) $params['is_sample_liquor'] = 0;
        if ($params['is_sample_liquor'] == 1) {
            $params['is_ts'] = 0;
            $params['settlement_method']      = '现结';
            $params['settlement_method_code'] = '05';
            $params['bank_account_name']      = '科技微信';
            $params['account_no']             = 'kejiweixin';
            if (empty($params['collection_type'])) $this->throwError('请选择领用出库类型');
            if (in_array($params['collection_type'], ['013-4', '013-6', '013-7']) && empty($params['wine_party_id'])) { //酒会领用,市场活动,酒庄活动
                if (empty($params['collection_type'])) $this->throwError('酒会领用,市场活动,酒庄活动 必须选择酒会');
            }

            $collection_type = OfflineService::getCollectionTypes($params);
            if (!in_array($params['collection_type'], array_keys($collection_type))) {
                $this->throwError('领用出库类型不可用');
            }

        }
        //参数验证
        $validate = Validate::rule([
            'admin_id|后台用户ID'               => 'require|number',
            'corp|单据公司编码'                 => 'require',
            'order_no|单据编号'                 => 'require|max:30',
            'voucher_date|单据日期'             => 'require',
            'customer|客户'                     => 'require',
            'customer_code|客户编码'            => 'require',
            'settle_customer|结算客户'          => 'require',
            'settle_customer_code|结算客户编码' => 'require',
            'warehouse|仓库'                    => 'require',
            'warehouse_code|仓库编码'           => 'require',
            'address|收货地址'                  => 'require',
            'consignee|联系人'                  => 'require',
            'consignee_phone|联系电话'          => 'require',
            'is_push_wms|是否推送萌牙'          => 'require|in:0,1',
            'is_ts|是否暂存'                    => 'require|in:0,1',
            'items_info|明细'                   => 'require',
            'is_sample_liquor|是否样酒'         => 'require|in:0,1',
            'settlement_method_code|付款方式'         => 'notIn:03',
            'return_warehouse|退回仓库'         => 'requireIf:is_sample_liquor,1|in:0,1',
            'bank_account_name|收款银行名称'      => 'requireIf:settlement_method_code,05',
            'account_no|收款账户'               => 'requireIf:settlement_method_code,05',
            'settlement_month_type|月结类型'               => 'requireIf:settlement_method_code,00',//月结
            'settlement_day_type|月结日期类型'               => 'requireIf:settlement_method_code,00',//月结
            'settlement_days|月结天数'               => 'requireIf:settlement_method_code,00',//月结
        ]);
        if (!$validate->message(['settlement_method_code.notIn' => '付款方式不能为月结,请刷新页面'])->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        //云酒销售单限制
        $p_price_source = 2;
        if ($params['corp'] == '002') $this->throwError('云酒销售单请前往T+制作');

        $real_unit_price = [];
        foreach ($params['items_info'] as $i_info) {
            $real_unit_price[$i_info['short_code']]['nums']        = bcadd(($real_unit_price[$i_info['short_code']]['nums'] ?? 0), $i_info['nums'], 2);
            $real_unit_price[$i_info['short_code']]['total_price'] = bcadd(($real_unit_price[$i_info['short_code']]['total_price'] ?? 0), $i_info['total_price'], 2);
        }
        foreach ($real_unit_price as &$rup){
            if($rup['nums'] == 0){
                $rup = 0;
            }else{
                $rup = bcdiv($rup['total_price'],$rup['nums'],2);
            }
        }
        $vh_vos_name = base64_decode($request->header('vinehoo-vos-name', '')) ?? '';
        $dept_code   = Db::table('vh_supplychain.vh_staff')->alias('t1')
            ->join('vh_supplychain.vh_department t2', 't1.dept_id = t2.id')
            ->where('t1.realname', $vh_vos_name)
            ->value('t2.dept_code');

        $piiscs              = array_values(array_unique(array_column($params['items_info'], 'short_code')));
        $customer_price_book = Db::table('vh_supplychain.vh_customer_price_book')
            ->where('customer_code', $params['customer_code'])
            ->where('inventory_code', 'in', $piiscs)
            ->where('status', 2)
            ->column('greement_price', 'inventory_code');

        $inventory_price = Db::table('vh_supplychain.vh_inventory_price')
            ->where('inventory_code', 'in', $piiscs)
            ->column('trade_price_json', 'inventory_code');
        foreach ($inventory_price as &$ip_item) {
            $ip_item = json_decode($ip_item, true)[2] ?? 0;
        }
//        $ipffsc = array_diff($piiscs, array_keys($inventory_price));
//        if (!empty($ipffsc)) {
//            $this->throwError('存货' . implode(',', $ipffsc) . '一批价格查询失败');
//        }

        if (($params['is_sample_liquor'] == 0) && in_array($dept_code, ['22', '12', '13', '1402', '29', '2901', '2902', '2903', '2904', '2905', '07'])) {
            //受价格管控的部门，制单时需要存在【客户价格本】，不然无法制单；
            $diffsc = array_diff($piiscs, array_keys($customer_price_book));
            if (!empty($diffsc)) {
                $this->throwError('简码' . implode(',', $diffsc) . '请联系业务助理制作客户价格本');
            }
        }

        foreach ($params['items_info'] as &$val) {
            //参数验证
            $validate = Validate::rule([
                'bar_code|条码'           => 'require',
                'short_code|简码'         => 'require',
                'product_name|产品名称'   => 'require',
                'year|年份'               => 'require',
                'unit|销售单位'           => 'require',
                'wmsStockNumber|萌牙库存' => 'require|number',
                'number|erp库存'          => 'require|number',
                'Specification|规格型号'  => 'require',
                'nums|数量'               => 'require|>:0',
                'is_gift|是否赠品'        => 'require|in:0,1',
                'priceSource|协议价类型'  => 'require|in:0,1,2,3',
            ]);
            if (!$validate->check($val)) {
                $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
            }
            if ($val['is_gift'] != 1 && $params['is_sample_liquor'] == 1) $this->throwError('样酒必需设置为赠品');
            //赠品商品金额为0
            if ($val['price'] == 0 && $val['is_gift'] != 1) {
                $val['is_gift'] = 1;
            }
            if ($val['is_gift'] == 1 && $val['price'] != 0) $this->throwError('赠品单价请设置为0');
            if (!isset($val['agreementPrice']) || empty($val['agreementPrice'])) $val['agreementPrice'] = 0;
            if (!isset($val['price']) || empty($val['price'])) $val['price'] = 0;
            if (!isset($val['total_price']) || empty($val['total_price'])) $val['total_price'] = 0;
            if ($val['is_gift'] == 1 && $val['total_price'] != 0) $this->throwError('赠品总价请设置为0');
            //产品英文名称
            if (!isset($val['en_product_name']) || empty($val['en_product_name'])) $val['en_product_name'] = '';
            $val['en_product_name'] = str_replace(",", "，", $val['en_product_name']);
//            if ($p_price_source != 1) {
//                $p_price_source = $val['priceSource'];
//            }
            if ($val['is_gift'] != 1) {
                if (($params['is_sample_liquor'] == 0) && in_array($dept_code, ['22', '12', '13', '1402', '29', '2901', '2902', '2903', '2904', '2905', '07']) && isset($customer_price_book[$val['short_code']]) && ($val['price'] < $customer_price_book[$val['short_code']])) $this->throwError('录入制单价格时非赠品不能低于【客户价格本】');
                if (($real_unit_price[$val['short_code']] < ($inventory_price[$val['short_code']] ?? 0))) {
                    $p_price_source = 1; //客户价
                }
            }
        }
        $params['priceSource'] = $p_price_source;
        //物料信息验证
        if (!empty($params['material_info'])) {
            foreach ($params['material_info'] as &$v) {
                $validate = Validate::rule([
                    'short_code|简码'       => 'require',
                    'product_name|产品名称' => 'require',
                    'nums|数量'             => 'require|number|>:0',
                ]);
                if (!$validate->check($v)) {
                    $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
                }
            }
        } else {
            $params['material_info'] = [];
        }
        $params['order_no'] = trim($params['order_no']);
        $offlineService     = new OfflineService();
        $offlineService->checkInvPrice($params);
        $result = $offlineService->newAddSaleOrder($params);
        return $this->success($result);
    }

    public function timeOutSaleOrder(Request $request)
    {
        $params = $request->param();
        $offlineService = new OfflineService();
        $result         = $offlineService->timeOutSaleOrder($params);
        return $this->success($result);
    }

    public function collectionTypes(Request $request)
    {
        $param           = $request->param();
        $collection_type = OfflineService::getCollectionTypes($param);
        unset($collection_type['013']);
        $result          = [];
        foreach ($collection_type as $id => $name) {
            $result[] = compact('id', 'name');
        }
        return $this->success($result);
    }

    /**
     * Description:普通销售单列表
     * Author: zrc
     * Date: 2022/10/8
     * Time: 10:34
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function ordinarySaleOrderList(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        $h_referer   = request()->header('referer', '');
        $referer_arr = parse_url($h_referer);
        $h_host      = $referer_arr['host'] ?? '';
        if ($h_host == 'os.mulando.cn') $params['corp'] = '003';
        if (empty($params['admin_id'])) $this->throwError('未获取到后台用户');
        if (empty($params['type']) || !in_array($params['type'], [1, 2])) $params['type'] = 1;//默认获取列表
        if ($params['type'] == 1) {
            $validate = new ListPagination();
            if (!$validate->goCheck($params)) {//验证参数
                $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
            }
        }
        $offlineService = new OfflineService();
        $result         = [];
        switch ($params['type']) {
            case 1:
                $result = $offlineService->ordinarySaleOrderList($params);
                break;
            case 2:
                $result = $offlineService->ordinarySaleOrderExport($params);
                break;
        }
        return $this->success($result);
    }

    /**
     * Description:导出中台销售单队列处理
     * Author: zrc
     * Date: 2023/1/4
     * Time: 14:54
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function exportSalesOrderMqDeal(Request $request)
    {
        $params = file_get_contents('php://input');
        if (empty($params)) {
            $this->throwError('未获取到队列数据', ErrorCode::PARAM_ERROR);
        }
        $offlineService = new OfflineService();
        $result         = $offlineService->exportSalesOrderMqDeal($params);
        return $this->success($result);
    }

    /**
     * Description:编辑普通销售单
     * Author: zrc
     * Date: 2022/10/8
     * Time: 13:48
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function updateOrdinarySaleOrder(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        //操作类型 type：0-新增（提交审批） 1-保存（不提交审批）
        if (!isset($params['type'])) $params['type'] = 0;
        //是否样酒：0-否 1-是
        if (!isset($params['is_sample_liquor'])) $params['is_sample_liquor'] = 0;
        if ($params['is_sample_liquor'] == 1) {
            $params['is_ts'] = 0;
            $params['settlement_method']      = '现结';
            $params['settlement_method_code'] = '05';
            $params['bank_account_name']      = '科技微信';
            $params['account_no']             = 'kejiweixin';
            if (empty($params['collection_type'])) $this->throwError('请选择领用出库类型');
        }
        //参数验证
        $validate = Validate::rule([
            'id|销售单ID'                       => 'require|number',
            'admin_id|后台用户ID'               => 'require|number',
            'corp|单据公司编码'                 => 'require',
            'order_no|单据编号'                 => 'require',
            'voucher_date|单据日期'             => 'require',
            'customer|客户'                     => 'require',
            'customer_code|客户编码'            => 'require',
            'settle_customer|结算客户'          => 'require',
            'settle_customer_code|结算客户编码' => 'require',
            'warehouse|仓库'                    => 'require',
            'warehouse_code|仓库编码'           => 'require',
            'address|收货地址'                  => 'require',
            'consignee|联系人'                  => 'require',
            'consignee_phone|联系电话'          => 'require',
            'is_push_wms|是否推送萌牙'          => 'require|in:0,1',
            'is_ts|是否暂存'                    => 'require|in:0,1',
            'is_sample_liquor|是否样酒'         => 'require|in:0,1',
            'items_info|明细'                   => 'require',
            'return_warehouse|退回仓库'         => 'requireIf:is_sample_liquor,1|in:0,1',
            'bank_account_name|收款银行名称'      => 'requireIf:settlement_method_code,05|requireIf:settlement_method_code,06',
            'account_no|收款账户'               => 'requireIf:settlement_method_code,05|requireIf:settlement_method_code,06',//现结
            'settlement_method_code|付款方式'         => 'notIn:03',
            'settlement_month_type|月结类型'               => 'requireIf:settlement_method_code,00',//月结
            'settlement_day_type|月结日期类型'               => 'requireIf:settlement_method_code,00',//月结
            'settlement_days|月结天数'               => 'requireIf:settlement_method_code,00',//月结
        ]);
        if (!$validate->message(['settlement_method_code.notIn' => '付款方式不能为月结,请刷新页面'])->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        //云酒销售单限制
        if ($params['corp'] == '002') $this->throwError('云酒销售单请前往T+制作');
        $p_price_source = 2;

        $vh_vos_name = base64_decode($request->header('vinehoo-vos-name', '')) ?? '';
        $dept_code   = Db::table('vh_supplychain.vh_staff')->alias('t1')
            ->join('vh_supplychain.vh_department t2', 't1.dept_id = t2.id')
            ->where('t1.realname', $vh_vos_name)
            ->value('t2.dept_code');

        $piiscs              = array_values(array_unique(array_column($params['items_info'], 'short_code')));
        $customer_price_book = Db::table('vh_supplychain.vh_customer_price_book')
            ->where('customer_code', $params['customer_code'])
            ->where('inventory_code', 'in', $piiscs)
            ->where('status', 2)
            ->column('greement_price', 'inventory_code');

        $inventory_price = Db::table('vh_supplychain.vh_inventory_price')
            ->where('inventory_code', 'in', $piiscs)
            ->column('trade_price_json', 'inventory_code');
        foreach ($inventory_price as &$ip_item) {
            $ip_item = json_decode($ip_item, true)[2] ?? 0;
        }
//        $ipffsc = array_diff($piiscs, array_keys($inventory_price));
//        if (!empty($ipffsc)) {
//            $this->throwError('存货' . implode(',', $ipffsc) . '一批价格查询失败');
//        }

        if (($params['is_sample_liquor'] == 0) && in_array($dept_code, ['22', '12', '13', '1402', '29', '2901', '2902', '2903', '2904', '2905', '07'])) {
            //受价格管控的部门，制单时需要存在【客户价格本】，不然无法制单；
            $diffsc = array_diff($piiscs, array_keys($customer_price_book));
            if (!empty($diffsc)) {
                $this->throwError('简码' . implode(',', $diffsc) . '请联系业务助理制作客户价格本');
            }
        }

        foreach ($params['items_info'] as &$val) {
            //参数验证
            $validate = Validate::rule([
                'bar_code|条码'           => 'require',
                'short_code|简码'         => 'require',
                'product_name|产品名称'   => 'require',
                'year|年份'               => 'require',
                'unit|销售单位'           => 'require',
                'wmsStockNumber|萌牙库存' => 'require|number',
                'number|erp库存'          => 'require|number',
                'Specification|规格型号'  => 'require',
                'nums|数量'               => 'require|>:0',
                'is_gift|是否赠品'        => 'require|in:0,1',
                'priceSource|协议价类型'  => 'require|in:0,1,2,3',
            ]);
            if (!$validate->check($val)) {
                $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
            }
            //赠品商品金额为0
            if ($val['price'] == 0 && $val['is_gift'] != 1) {
                $val['is_gift'] = 1;
            }
            if ($val['is_gift'] == 1 && $val['price'] != 0) $this->throwError('赠品单价请设置为0');
            if (!isset($val['agreementPrice']) || empty($val['agreementPrice'])) $val['agreementPrice'] = 0;
            if (!isset($val['price']) || empty($val['price'])) $val['price'] = 0;
            if (!isset($val['total_price']) || empty($val['total_price'])) $val['total_price'] = 0;
            if ($val['is_gift'] == 1 && $val['total_price'] != 0) $this->throwError('赠品总价请设置为0');
            //产品英文名称
            if (!isset($val['en_product_name']) || empty($val['en_product_name'])) $val['en_product_name'] = '';
            $val['en_product_name'] = str_replace(",", "，", $val['en_product_name']);
//            if ($p_price_source != 1) {
//                $p_price_source = $val['priceSource'];
//            }
            if ($val['is_gift'] != 1) {
                if (($params['is_sample_liquor'] == 0) && in_array($dept_code, ['22', '12', '13', '1402', '29', '2901', '2902', '2903', '2904', '2905', '07']) && isset($customer_price_book[$val['short_code']]) && ($val['price'] < $customer_price_book[$val['short_code']])) $this->throwError('录入制单价格时非赠品不能低于【客户价格本】');
                if (($val['price'] < ($inventory_price[$val['short_code']] ?? 0))) {
                    $p_price_source = 1; //客户价
                }
            }
        }
        $params['priceSource'] = $p_price_source;
        //物料信息验证
        if (!empty($params['material_info'])) {
            foreach ($params['material_info'] as &$v) {
                $validate = Validate::rule([
                    'short_code|简码'       => 'require',
                    'product_name|产品名称' => 'require',
                    'nums|数量'             => 'require|number|>:0',
                ]);
                if (!$validate->check($v)) {
                    $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
                }
            }
        } else {
            $params['material_info'] = [];
        }
        $params['order_no'] = trim($params['order_no']);
        $offlineService     = new OfflineService();
        $offlineService->checkInvPrice($params);
        $result = $offlineService->updateOrdinarySaleOrder($params);
        return $this->success($result);
    }

    /**
     * Description:企业微信上传临时素材
     * Author: zrc
     * Date: 2023/1/1
     * Time: 12:42
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function uploadWeiXinTemporary(Request $request)
    {
        $params = $request->param();
        if (empty($params['file'])) $this->throwError('未获取到传入参数', ErrorCode::PARAM_ERROR);
        $offlineService = new OfflineService();
        $result         = $offlineService->uploadWeiXinTemporary($params);
        return $this->success($result);
    }

    /**
     * Description:弃审普通销售单
     * Author: zrc
     * Date: 2023/1/16
     * Time: 10:15
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function rejectSaleOrder(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        //参数验证
        $validate = Validate::rule([
            'admin_id|后台用户ID'   => 'require|number',
            'sub_order_no|单据编号' => 'require',
            'reason|弃审原因'       => 'require|max:900',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $offlineService = new OfflineService();
        $result         = $offlineService->rejectSaleOrder($params);
        return $this->success($result);
    }

    /**
     * Description:添加编辑物料
     * Author: zrc
     * Date: 2023/2/21
     * Time: 11:44
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function addEditMaterial(Request $request)
    {
        $params                 = $request->param();
        $params['admin_id']     = $request->header('vinehoo-uid');
        $params['short_code']   = trim($params['short_code']);
        $params['product_name'] = trim($params['product_name']);
        //参数验证
        $validate = Validate::rule([
            'admin_id|后台用户ID'   => 'require|number',
            'short_code|简码'       => 'require|max:50',
            'product_name|商品名称' => 'require|max:500',
            'warning_value|预警值'  => 'number|>:0',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $offlineService = new OfflineService();
        $result         = $offlineService->addEditMaterial($params);
        return $this->success($result);
    }

    /**
     * Description:物料列表
     * Author: zrc
     * Date: 2023/2/21
     * Time: 13:18
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function materialList(Request $request)
    {
        $params   = $request->param();
        $validate = new ListPagination();
        if (!$validate->goCheck($params)) {//验证参数
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $offlineService = new OfflineService();
        $result         = $offlineService->materialList($params);
        return $this->success($result);
    }

    /**
     * Description:物料删除
     * Author: zrc
     * Date: 2023/2/27
     * Time: 17:03
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function deleteMaterial(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        if (empty($params['ids'])) $this->throwError('未获取到数据ID', ErrorCode::PARAM_ERROR);
        $offlineService = new OfflineService();
        $result         = $offlineService->deleteMaterial($params);
        return $this->success($result);
    }

    /**
     * Description:单据类型转换
     * Author: zrc
     * Date: 2023/11/24
     * Time: 13:48
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function documentTypeChange(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        $validate           = Validate::rule([
            'admin_id|后台用户ID'   => 'require|number',
            'sub_order_no|子订单号' => 'require'
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $offlineService = new OfflineService();
        $result         = $offlineService->documentTypeChange($params);
        return $this->success($result);
    }

    //弃审
    public function auditRejection(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        $validate           = Validate::rule([
            'admin_id|后台用户ID'   => 'require|number',
            'sub_order_no|子订单号' => 'require'
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        Log::write('弃审: param: ', json_encode($params));

        $num = Db::name('offline_order')
            ->where('sub_order_no', $params['sub_order_no'])
            ->where(function ($query) {
                $query->whereOr('push_wms_status', 2);
                $query->whereOr('push_t_status', 'in', [0, 2]);
            })->update(['dingtalk_status' => 3]);
        return $this->success($num);
    }

    public function buildTraceCode(Request $request)
    {
        $params = $request->param();
        Log::write("buildTraceCode 进入: " . json_encode($params));
        $validate = Validate::rule([
            'main_order_ids|主订单ID' => 'require',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }

        $orders = Db::name('offline_order')->alias('oo')
            ->leftJoin('order_main om', 'om.id=oo.main_order_id')
            ->where('oo.main_order_id', 'in', $params['main_order_ids'])
            ->column("oo.main_order_id,oo.items_info,oo.customer_code,om.address,oo.warehouse_code");

        $arr = [];
        foreach ($orders as &$param) {
            try {
                $items_infos = explode(',', $param['items_info']);
                $nums        = 0;
                if (!empty($items_infos)) {
                    foreach ($items_infos as $items_info) {
                        $items_info = explode('*', $items_info);
                        $nums       = bcadd($nums, ($items_info[2] ?? 0));
                    }
                }

                if ($nums >= 6 && in_array($param['warehouse_code'], ['336', '262'])) {
                    $addressAiMatch         = \Curl::addressAiMatch(['address' => $param['address'] ?? '']);
                    $param['province_id']   = $addressAiMatch['province_id'];
                    $param['customer_code'] = str_replace('VH', '', $param['customer_code']);  // 客户代码

                    $arr[$param['main_order_id']] = (new \SealedSource())->encode_with_fixed_length($param['province_id'], $param['customer_code'], $param['main_order_id']);
                }
            } catch (\Exception $e) {
                Log::write("buildTraceCode ERROR: " . $e->getMessage() . " param: " . json_encode($param));
            }
        }
        return $this->success($arr);
    }

    public function productDetail(Request $request)
    {
        $param = $request->param();
        if (empty($param['warehouse'])) return $this->throwError('仓库编码不能为空');
        if (empty($param['short_code']) && empty($param['bar_code'])) return $this->throwError('简码和条码不能都为空');
        $param['vh_uid'] = $request->header('vinehoo-uid', '');
        if (!empty($param['customer'])) {
            $data['customer'] = $param['customer'] ?? '';
            if (!empty($data['bar_code']) && $data['bar_code'] == '12345') $data['customer'] = '';
            if (!empty($data['short_code'][0]['code']) && $data['short_code'][0]['code'] == 'POS-01') $data['customer'] = '';
        }

        $product = Db::table('vh_wiki.vh_products')->alias('t1')
            ->leftJoin('vh_wiki.vh_product_unit_open t2', 't1.product_unit = t2.id')
            ->where(function ($query) use ($param) {
                if (!empty($param['short_code'])) {
                    $query->where('t1.short_code', $param['short_code']);
                } else {
                    $query->where('t1.bar_code', $param['bar_code']);
                }
            })->field('t1.*,t2.name as product_unit_name')->find();

        if (empty($product)) $this->throwError('未找到产品详情,请前往磐石完善');


        #region 组装数据
        $vh_vos_name = base64_decode($request->header('vinehoo-vos-name', '')) ?? '';
        $dept_code   = Db::table('vh_supplychain.vh_staff')->alias('t1')
            ->join('vh_supplychain.vh_department t2', 't1.dept_id = t2.id')
            ->where('t1.realname', $vh_vos_name)
            ->value('t2.dept_code');

        $priceSource = 2;
        $agreementPrice = 0;
        if (in_array($dept_code, ['22', '12', '13',  '14', '1402', '29', '2901', '2902', '2903', '2904', '2905', '07'])) {
            $p_no_query_price = 0;
        }else{
            $p_no_query_price = 1;
        }
        if(!empty($param['no_query_price']) && $param['no_query_price'] == 1){
            $p_no_query_price = 1; //不查询价格
        }
        if ($p_no_query_price != 1) {
            $customer_price_book = Db::table('vh_supplychain.vh_customer_price_book')
                ->where('customer_code', $param['customer'])
                ->where('inventory_code', $product['short_code'])
                ->where('status', 2)
                ->order('greement_price', 'desc')
                ->find();

            if (!$customer_price_book) {
                return $this->throwError('请联系业务助理制作客户价格本');
            }
            $agreementPrice = $customer_price_book['greement_price'];

            $inventory_price = Db::table('vh_supplychain.vh_inventory_price')
                ->where('inventory_code', $product['short_code'])
                ->find();
            if (empty($inventory_price)) {
                $priceSource = 1;
            } else {
                $yp_price = json_decode($inventory_price['trade_price_json'], true);
                if (empty($yp_price) || empty($yp_price[2]) || ($customer_price_book['greement_price'] < $yp_price[2])) {
                    $priceSource = 1;
                }
            }
        }

        $wmsStock = [];
        $short_codes[] = $product['short_code'];
        $inventory_relation = Db::name('inventory_relation')->where('short_code', $product['short_code'])->value('relation_short_code');
        if (!empty($inventory_relation)) {
            $inventory_relation_arr = explode(',', $inventory_relation);
            $short_codes = array_values(array_unique(array_merge($short_codes, $inventory_relation_arr)));
        }

        try {
            $tempWmsStock = \Curl::wmsGoodsGetFictitiousCount(['short_code' => $short_codes]);
            foreach ($tempWmsStock as $k => $v) {
                foreach ($v as $v2) {
                    if (intval($v2['fictitious_id']) === intval($param['warehouse'])) {
                        $wmsStock[$k] = $v2;
                    }
                }
            }
        } catch (\Exception $e) {
        }

        $tmp['bar_code']        = $product['bar_code'] ?? '';
        $tmp['invoice_name']     = $product['invoice_name'] ?? '';
        $tmp['short_code']      = $product['short_code'];
        $tmp['product_name']    = $product['cn_product_name'];
        $tmp['en_product_name'] = $product['en_product_name'];
        $tmp['Specification']   = $product['capacity'];
        $tmp['year']            = $product['grape_picking_years'];
        $tmp['number']          = 9999;
        $tmp['agreementPrice']  = $agreementPrice;
        $tmp['priceSource']     = $priceSource;
        $tmp['wmsStockNumber']  = $wmsStock[$product['short_code']]['goods_count'] ?? 0;
        $tmp['stagePrice']      = [];//阶梯价
        $tmp['unit']            = $product['product_unit_name'] ?? '';//

        if ($product['short_code'] == 'POS-01') {
            $tmp['wmsStockNumber'] = 9999;
        }
        $relation = [];
        if (!empty($inventory_relation_arr)) {
            $relation_products = Db::table('vh_wiki.vh_products')->where('short_code', 'in', $inventory_relation_arr)->column('id,cn_product_name', 'short_code');
            foreach ($inventory_relation_arr as $v) {
                $relation[] = [
                    'short_code' => $v,
                    'product_name' => $relation_products[$v]['cn_product_name'] ?? '',
                    'wmsStockNumber' => $wmsStock[$v]['goods_count'] ?? 0,
                ];
            }
        }
        $tmp['relation']    = $relation;

        return $this->success($tmp);
    }

    //收款 新增单据
    public function arapOrder(Request $request)
    {
        $param = $request->param();
        //参数验证
        $validate = Validate::rule([
            'sub_order_no|订单号'            => 'require',
            'media_url|收款回执'             => 'require',
            'tax_amount|收款金额'            => 'require',
            'bank_account_name|我方银行名称' => 'require',
            'account_no|我方银行账号'        => 'require',
            'bill_date|收款日期'        => 'require|date',
        ]);
        if (!$validate->check($param)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }

        Db::startTrans();
        try {
            $sub_order_nos = explode(',', $param['sub_order_no']);
            $sales_teturns = Db::name('sales_return')
                ->where('sub_order_no','in', $sub_order_nos)
                ->where('dingtalk_status','in', [2,1]) //审批状态：0-待审批 1-审批中 2-已通过 3-拒绝 4-转审
                ->group('sub_order_no')
                ->column('SUM(return_amount)', 'sub_order_no');

            $orders        = Db::name('offline_order')
                ->where('sub_order_no', 'in', $sub_order_nos)
                ->column('sub_order_no,related_order_no,items_info,media_url,voucher_date,department,department_code,clerk,clerk_code,customer,payment_amount,customer_code,settlement_money');

            if (count($orders) != count($sub_order_nos)) {
                throw new Exception("订单号有误!");
            }

            if (count(array_unique(array_column($orders, 'customer_code'))) > 1) {
                throw new Exception("客户不一致!");
            }

            $p_data = [];
            $items  = [];
            $total_tax_amount = $param['tax_amount'];
            foreach ($orders as $order) {
                if(!empty($order['related_order_no'])){
                    throw new Exception("业绩单{$order['sub_order_no']}不可生成应收单");
                }
                if (empty($p_data)) {
                    $p_data = [
                        "bill_type"         => 1, //单据类型：0-应收单 1-收款单
                        "invoice_type"      => 0, //发票类型：0-普票 1-专票
                        "is_advance"        => 0,//是否预收：0-否 1-是
                        "bill_date"         => $param['bill_date'],
                        "department"        => $order['department'],
                        "department_code"   => $order['department_code'],
                        "clerk"             => $order['clerk'],
                        "clerk_code"        => $order['clerk_code'],
                        "account_no"        => $param['account_no'],
                        "bank_account_name" => $param['bank_account_name'],
                        "customer"          => $order['customer'],
                        "customer_code"     => $order['customer_code'],
                        "tax_amount"        => $param['tax_amount'],
                        "media_url"        => $param['media_url'],
                    ];
                }
                $order['payment_amount'] = bcsub($order['payment_amount'],($sales_teturns[$order['sub_order_no']] ?? 0));
                if($order['payment_amount'] == 0) {
                    throw new Exception("{$order['sub_order_no']}有退款,余额为0!");
                }
                if ($total_tax_amount <= 0) {
                    throw new Exception("收款金额不足!");
                }
                $item_tax_amount  = max(0, min($total_tax_amount, $order['payment_amount']));
                $items[]          = [
                    "sub_order_no" => $order['sub_order_no'],
                    "tax_amount"   => $item_tax_amount
                ];
                $total_tax_amount = bcsub($total_tax_amount, $item_tax_amount, 2);
                /*if (empty($order['items_info'])) {
                    $items[] = [
                        "sub_order_no" => $order['sub_order_no'],
                        "tax_amount"   => $order['payment_amount']
                    ];
                } else {
                    $p_items_info = explode(',', $order['items_info']);
                    foreach ($p_items_info as $pii) {
                        $pii_arr = explode('*', $pii);

                        $items[] = [
                            "sub_order_no" => $order['sub_order_no'],
                            "tax_amount" => bcmul(($pii_arr['3'] ?? 0), ($pii_arr['2'] ?? 1), 2),
                            "short_code"   => $pii_arr['1'] ?? '',
                            "item_name"    => $pii_arr['6'] ?? Es::name(Es::PRODUCTS)->where([['short_code', '==', ($pii_arr['1'] ?? '')]])->value('cn_product_name'),
                        ];
                    }
                }*/
                Db::name('offline_order')->where('sub_order_no', $order['sub_order_no'])->update([
                    'media_url' => trim("{$order['media_url']},{$param['media_url']}", ',')
                ]);
            }
            if ($total_tax_amount > 0) {
                $items[] = [
                    "tax_amount" => $total_tax_amount
                ];
            }
            $p_data["items"] = $items;

            $arap = \Curl::arapOrder($p_data);
            Db::name('offline_order')->where('sub_order_no', 'in', $sub_order_nos)->update(['settlement_no' => $arap['bill_no']]);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->throwError("新增收款失败:" . $e->getMessage() . ' ' . $e->getLine(), ErrorCode::PARAM_ERROR);
        }
        return $this->success();
    }

    public function inventoryRelationList(Request $request)
    {
        $params = $request->param();
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 10;

        $where = function ($query) use ($params) {
            if (!empty($params['short_code'])) {
                $query->where('short_code|relation_short_code', 'like', '%' . $params['short_code'] . '%');
            }

            if (!empty($params['cn_product_name'])) {
                $p_short_codes = Db::table('vh_wiki.vh_products')->where('cn_product_name', 'like', '%' . $params['cn_product_name'] . '%')->column('short_code');

                $query->where(function ($query) use ($p_short_codes) {
                    foreach ($p_short_codes as $short_code) {
                        $query->whereOr('short_code|relation_short_code', 'like', '%' . $short_code . '%');
                    }
                });
            }
        };

        $query = Db::name('inventory_relation')->where($where);
        $total = $query->count();
        $list = $query->page($page, $limit)->order('id desc')->column('*');

        $short_codes = [];
        foreach ($list as &$item) {
            $short_codes[] = $item['short_code'];
            $relation_short_code = explode(',', $item['relation_short_code']);
            $short_codes = array_unique(array_merge($short_codes, $relation_short_code));
            $item['relation_short_code'] = $relation_short_code;
        }

        $wiki_products = Db::table('vh_wiki.vh_products')->where('short_code', 'in', $short_codes)->column('bar_code,en_product_name', 'short_code');

        foreach ($list as &$item) {
            $item['cn_product_name'] = $wiki_products[$item['short_code']]['en_product_name'] ?? '';
            $item['bar_code'] = $wiki_products[$item['short_code']]['bar_code'] ?? '';
            $relation_short_code_arr = [];
            foreach ($item['relation_short_code'] as $rsc) {
                if (!empty($wiki_products[$rsc])) {
                    $relation_short_code_arr[] = $wiki_products[$rsc];
                }
            }
            $item['relation_short_code'] = $relation_short_code_arr;
        }

        return $this->success([
            'list' => $list,
            'total' => $total
        ]);
    }

    /**
     * 创建存货关联
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function inventoryRelationCreated(Request $request)
    {
        $params = $request->param();
        $validate = Validate::rule([
            'short_code|存货简码' => 'require|max:32',
            'relation_short_code|关联物料简码' => 'require',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }

        // 检查简码是否已存在关联
        $exists = Db::name('inventory_relation')
            ->where('short_code', $params['short_code'])
            ->find();
        if ($exists) {
            $this->throwError('该简码已存在关联记录，请勿重复添加', ErrorCode::PARAM_ERROR);
        }

        $vh_uid = $request->header('vinehoo-uid', 0);
        $vh_vos_name = base64_decode($request->header('vinehoo-vos-name', '')) ?? '';

        $now = time();
        $data = [
            'short_code' => $params['short_code'],
            'relation_short_code' => $params['relation_short_code'],
            'created_time' => $now,
            'update_time' => $now,
            'vh_uid' => $vh_uid,
            'vh_vos_name' => $vh_vos_name,
        ];

        try {
            $id = Db::name('inventory_relation')->insertGetId($data);
            return $this->success(['id' => $id]);
        } catch (\Exception $e) {
            $this->throwError('创建失败：' . $e->getMessage(), ErrorCode::PARAM_ERROR);
        }
    }

    /**
     * 更新存货关联
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function inventoryRelationUpdate(Request $request)
    {
        $params = $request->param();
        $validate = Validate::rule([
            'id|关联ID' => 'require|number',
            'short_code|存货简码' => 'require|max:32',
            'relation_short_code|关联物料简码' => 'require',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }

        // 检查是否存在
        $exists = Db::name('inventory_relation')
            ->where('id', $params['id'])
            ->find();
        if (!$exists) {
            $this->throwError('关联记录不存在', ErrorCode::PARAM_ERROR);
        }

        // 检查简码是否与其他记录重复
        $duplicate = Db::name('inventory_relation')
            ->where('short_code', $params['short_code'])
            ->where('id', '<>', $params['id'])
            ->find();
        if ($duplicate) {
            $this->throwError('该简码已存在其他关联记录', ErrorCode::PARAM_ERROR);
        }

        $vh_uid = $request->header('vinehoo-uid', 0);
        $vh_vos_name = base64_decode($request->header('vinehoo-vos-name', '')) ?? '';

        $data = [
            'short_code' => $params['short_code'],
            'relation_short_code' => $params['relation_short_code'],
            'update_time' => time(),
            'vh_uid' => $vh_uid,
            'vh_vos_name' => $vh_vos_name,
        ];

        try {
            $result = Db::name('inventory_relation')
                ->where('id', $params['id'])
                ->update($data);
            return $this->success($result > 0);
        } catch (\Exception $e) {
            $this->throwError('更新失败：' . $e->getMessage(), ErrorCode::PARAM_ERROR);
        }
    }

    public function inventoryRelationDelete(Request $request)
    {
        $params = $request->param();
        $validate = Validate::rule([
            'id|关联ID' => 'require|number',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }

        try {
            $result = Db::name('inventory_relation')
                ->where('id', $params['id'])
                ->delete();
            return $this->success($result > 0);
        } catch (\Exception $e) {
            $this->throwError('删除失败：' . $e->getMessage(), ErrorCode::PARAM_ERROR);
        }
    }

}