<?php
declare (strict_types = 1);

namespace app\command;

use app\service\Push;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;

class Gift extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('gift')
            ->setDescription('the gift command');
    }

    protected function execute(Input $input, Output $output)
    {
        (new Push())->gift();
        // 指令输出
        $output->writeln('success');
    }
}
