<?php


namespace app\controller;


use app\BaseController;
use app\ErrorCode;
use app\model\CrossLockOrder;
use app\Request;
use app\service\Additional as AdditionalService;
use app\service\Cross as CrossService;
use app\service\es\Es;
use app\service\Order as OrderService;
use app\validate\ListPagination;
use Curl;
use think\Exception;
use think\facade\Db;
use think\facade\Log;
use think\facade\Validate;

class Cross extends BaseController
{
    private $secret = 'b9e1539cb63741cd';
    private $iv     = '0102030405060708';

    /**
     * Description:跨境订单修改
     * Author: zrc
     * Date: 2021/8/2
     * Time: 14:47
     * @param Request $request
     */
    public function update(Request $request)
    {
        $params             = $request->param();
        $params['operator'] = $request->header('vinehoo-uid');
        //数据验证
        $validate = Validate::rule([
            'sub_order_no|子订单号' => 'require',
            'operator|操作人ID'     => 'require|number',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $crossService = new CrossService();
        $result       = $crossService->updateOrder($params);
        return $this->success($result);
    }

    /**
     * Description:跨境订单列表
     * Author: zrc
     * Date: 2021/8/2
     * Time: 18:25
     * @param Request $request
     */
    public function orderList(Request $request)
    {
        $params   = $request->param();
        $validate = new ListPagination();
        if (!$validate->goCheck($params)) {//验证参数
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $crossService = new CrossService();
        $result       = $crossService->orderList($params);
        return $this->success($result);
    }

    /**
     * Description:跨境订单详情
     * Author: zrc
     * Date: 2021/8/3
     * Time: 9:54
     * @param Request $request
     */
    public function orderDetail(Request $request)
    {
        $params = $request->param();
        if (empty($params['sub_order_no'])) {
            $this->throwError('请传入子订单号', ErrorCode::PARAM_ERROR);
        }
        $crossService = new CrossService();
        $result       = $crossService->orderDetail($params);
        return $this->success($result);
    }

    /**
     * Description:钉钉审批跨境限额黑名单录入
     * Author: zrc
     * Date: 2021/10/25
     * Time: 14:13
     * @param Request $request
     * @return \think\Response
     */
    public function inputBlackList(Request $request)
    {
        $params = $request->param();
        if (empty($params['type']) || !in_array($params['type'], [1, 2])) {
            $this->throwError('缺少黑名单类型', ErrorCode::PARAM_ERROR);
        }
        if (empty($params['id_card_no'])) {
            $this->throwError('缺少身份证号码', ErrorCode::PARAM_ERROR);
        }
        $params['sponsor'] = $params['sponsor'] ?? '';
        $params['note']    = $params['note'] ?? '';
        $crossService      = new CrossService();
        $result            = $crossService->inputBlackList($params);
        return $this->success($result);
    }

    /**
     * Description:钉钉审批跨境退款(原因是限额的录入自然年黑名单，其他原因返还当前自然年消费金额）
     * Author: zrc
     * Date: 2021/10/26
     * Time: 16:41
     * @param Request $request
     * @return mixed
     */
    public function dingTalkDealBlackList(Request $request)
    {
        $params = $request->param();
        if (empty($params['sub_order_no'])) {
            $this->throwError('未获取到子订单号', ErrorCode::PARAM_ERROR);
        }
        if (empty($params['make'])) {
            $this->throwError('未获取到订单退款原因', ErrorCode::PARAM_ERROR);
        }
        $crossService = new CrossService();
        $result       = $crossService->dingTalkDealBlackList($params);
        return $this->success($result);
    }

    /**
     * Description:跨境订单推送池列表
     * Author: zrc
     * Date: 2022/4/24
     * Time: 13:35
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function pushPoolList(Request $request)
    {
        $params   = $request->param();
        $validate = new ListPagination();
        if (!$validate->goCheck($params)) {//验证参数
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $crossService = new CrossService();
        $result       = $crossService->pushPoolList($params);
        return $this->success($result);
    }

    /**
     * Description:跨境商品备案信息列表
     * Author: zrc
     * Date: 2022/4/26
     * Time: 9:06
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function goodsRecordInformationList(Request $request)
    {
        $params   = $request->param();
        $validate = new ListPagination();
        if (!$validate->goCheck($params)) {//验证参数
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $crossService = new CrossService();
        $result       = $crossService->goodsRecordInformationList($params);
        return $this->success($result);
    }

    /**
     * Description:跨境商品备案信息详情
     * Author: zrc
     * Date: 2022/4/27
     * Time: 17:47
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function goodsRecordInformationDetail(Request $request)
    {
        $params = $request->param();
        if (empty('id')) $this->throwError('未获取到信息ID', ErrorCode::PARAM_ERROR);
        $crossService = new CrossService();
        $result       = $crossService->goodsRecordInformationDetail($params);
        return $this->success($result);
    }

    /**
     * Description:跨境商品备案信息excel批量录入
     * Author: zrc
     * Date: 2022/4/26
     * Time: 14:10
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function importInformationList(Request $request)
    {
        $params = $request->param();
        if (empty($params['file'])) $this->throwError('缺少上传内容', ErrorCode::PARAM_ERROR);
        //获取文件路径
        $path   = env('ALIURL') . $params['file'];
        $startI = 1;
        #下载文件
        $local_path = download_image($path, 'xls');
        #解析文件
        $data = getExcelData($local_path, $startI);
        @unlink($local_path);
        if ($data['error_code'] != 0) $this->throwError('excel解析失败');
        if (count($data['data']) == 0) $this->throwError('未获取到excel内容');
        $excelData = $data['data'];
        if ($excelData[$startI][0] != '序号') {
            $this->throwError('格式有误，请下载模板操作');
        }
        unset($excelData[$startI]);
        $crossService = new CrossService();
        $result       = $crossService->importInformationList($excelData);
        return $this->success($result);
    }

    /**
     * Description:跨境商品备案信息修改
     * Author: zrc
     * Date: 2022/4/27
     * Time: 17:00
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function updateInformation(Request $request)
    {
        $params = $request->param();
        if (empty('id')) $this->throwError('未获取到信息ID', ErrorCode::PARAM_ERROR);
        $crossService = new CrossService();
        $result       = $crossService->updateInformation($params);
        return $this->success($result);
    }

    /**
     * Description:跨境订单推送代发仓记录列表
     * Author: zrc
     * Date: 2022/4/28
     * Time: 16:32
     * @param Request $request
     * @return \think\response\Json
     */
    public function pushWarehouseLogList(Request $request)
    {
        $params   = $request->param();
        $validate = new ListPagination();
        if (!$validate->goCheck($params)) {//验证参数
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $crossService = new CrossService();
        $result       = $crossService->pushWarehouseLogList($params);
        return $this->success($result);
    }

    /**
     * Description:海关申报异常记录列表
     * Author: zrc
     * Date: 2022/4/29
     * Time: 15:19
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function declareRecordList(Request $request)
    {
        $params   = $request->param();
        $validate = new ListPagination();
        if (!$validate->goCheck($params)) {//验证参数
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $crossService = new CrossService();
        $result       = $crossService->declareRecordList($params);
        return $this->success($result);
    }

    /**
     * Description:获取海关申报异常明细
     * Author: zrc
     * Date: 2022/4/29
     * Time: 16:21
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function getExceptionDetails(Request $request)
    {
        $params = $request->param();
        if (empty($params['main_order_no'])) $this->throwError('未获取到主订单号');
        $crossService = new CrossService();
        $result       = $crossService->getExceptionDetails($params);
        return $this->success($result);
    }

    /**
     * Description:支付单记录列表
     * Author: zrc
     * Date: 2022/5/9
     * Time: 13:23
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function declareLogList(Request $request)
    {
        $params   = $request->param();
        $validate = new ListPagination();
        if (!$validate->goCheck($params)) {//验证参数
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $crossService = new CrossService();
        $result       = $crossService->declareLogList($params);
        return $this->success($result);
    }

    /**
     * Description:支付单推送查询
     * Author: zrc
     * Date: 2022/5/16
     * Time: 11:52
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function declareQuery(Request $request)
    {
        $params = $request->param();
        //参数验证
        $validate = Validate::rule([
            'main_order_no|主订单号' => 'require',
            'type|类型'              => 'require|in:1,2,3'
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $crossService = new CrossService();
        $result       = $crossService->declareQuery($params);
        return $this->success($result);
    }

    /**
     * Description:嘉创异步回执
     * Author: zrc
     * Date: 2022/6/17
     * Time: 11:55
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function jiaChuangNotify(Request $request)
    {
        $params = $request->param();
        if (empty($params)) $this->throwError('未获取到异步参数', ErrorCode::PARAM_ERROR);
        $crossService = new CrossService();
        $result       = $crossService->jiaChuangNotify($params);
        return $this->success($result);
    }

    /**
     * Description:库存管理列表
     * Author: zrc
     * Date: 2022/9/19
     * Time: 11:32
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function stockManagementList(Request $request)
    {
        $params   = $request->param();
        $validate = new ListPagination();
        if (!$validate->goCheck($params)) {//验证参数
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $crossService = new CrossService();
        $result       = $crossService->stockManagementList($params);
        return $this->success($result);
    }

    /**
     * Description:库存管理编辑
     * Author: zrc
     * Date: 2022/9/19
     * Time: 16:56
     * @param Request $request
     * @return \think\response\Json
     */
    public function stockManagementUpdate(Request $request)
    {
        $params             = $request->param();
        $params['operator'] = $request->header('vinehoo-uid');
        //参数验证
        $validate = Validate::rule([
            'id|自增ID'       => 'require|number',
            'operator|操作人' => 'require|number',
            'entry_time|入库时间' => 'date',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $crossService = new CrossService();
        $result       = $crossService->stockManagementUpdate($params);
        return $this->success($result);
    }

    /**
     * Description:添加库存管理备注
     * Author: zrc
     * Date: 2022/9/20
     * Time: 14:10
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function addRemarks(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        //数据验证
        $validate = Validate::rule([
            'cross_inventory_id|库存记录ID' => 'require|number',
            'admin_id|后台用户ID'           => 'require|number',
            'content|备注内容'              => 'require|max:1000',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $crossService = new CrossService();
        $result       = $crossService->addRemarks($params);
        return $this->success($result);
    }

    /**
     * Description:库存管理备注列表
     * Author: zrc
     * Date: 2022/9/20
     * Time: 14:17
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function remarksList(Request $request)
    {
        $params = $request->param();
        if (empty($params['cross_inventory_id'])) $this->throwError('未获取到库存记录ID', ErrorCode::PARAM_ERROR);
        $crossService = new CrossService();
        $result       = $crossService->remarksList($params);
        return $this->success($result);
    }

    /**
     * Description:库存记录导入
     * Author: zrc
     * Date: 2022/9/20
     * Time: 15:03
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function importInventory(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        if (empty($params['admin_id'])) $this->throwError('未获取到后台用户ID');
        if (empty($params['file'])) $this->throwError('缺少上传内容', ErrorCode::PARAM_ERROR);
        //获取文件路径
        $path   = env('ALIURL') . $params['file'];
        $startI = 1;
        #下载文件
        $local_path = download_image($path, 'xls');
        #解析文件
        $data = getExcelData($local_path, $startI);
        @unlink($local_path);
        if ($data['error_code'] != 0) $this->throwError('excel解析失败');
        if (count($data['data']) == 0) $this->throwError('未获取到excel内容');
        $excelData = $data['data'];
        if ($excelData[$startI][0] != '序号') {
            $this->throwError('格式有误，请下载模板操作');
        }
        unset($excelData[$startI]);
        $crossService = new CrossService();
        $result       = $crossService->importInventory($excelData, $params);
        return $this->success($result);
    }

    /**
     * Description:入库数量导入
     * Author: zrc
     * Date: 2022/9/21
     * Time: 17:35
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function importInventoryNums(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        if (empty($params['admin_id'])) $this->throwError('未获取到后台用户ID');
        if (empty($params['file'])) $this->throwError('缺少上传内容', ErrorCode::PARAM_ERROR);
        //获取文件路径
        $path   = env('ALIURL') . $params['file'];
        $startI = 1;
        #下载文件
        $local_path = download_image($path, 'xls');
        #解析文件
        $data = getExcelData($local_path, $startI);
        @unlink($local_path);
        if ($data['error_code'] != 0) $this->throwError('excel解析失败');
        if (count($data['data']) == 0) $this->throwError('未获取到excel内容');
        $excelData = $data['data'];
        if ($excelData[$startI][0] != '序号') {
            $this->throwError('格式有误，请下载模板操作');
        }
        unset($excelData[$startI]);
        $crossService = new CrossService();
        $result       = $crossService->importInventoryNums($excelData, $params);
        return $this->success($result);
    }

    /**
     * Description:删除库存记录
     * Author: zrc
     * Date: 2022/11/17
     * Time: 9:31
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function delInventory(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        if (empty($params['admin_id'])) $this->throwError('未获取到后台用户ID');
        if (empty($params['cross_inventory_id'])) $this->throwError('未获取到库存记录ID', ErrorCode::PARAM_ERROR);
        $crossService = new CrossService();
        $result       = $crossService->delInventory($params);
        return $this->success($result);
    }

    /**
     * Description:批量修改库存记录信息
     * Author: zrc
     * Date: 2022/9/22
     * Time: 10:19
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function batchUpdate(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        //数据验证
        $validate = Validate::rule([
            'cross_inventory_ids|库存记录ID' => 'require',
            'admin_id|后台用户ID'            => 'require|number',
            'field|修改类型'                 => 'require',
            'content|修改内容'               => 'require',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $crossService = new CrossService();
        $result       = $crossService->batchUpdate($params);
        return $this->success($result);
    }

    /**
     * Description:导出库存信息记录
     * Author: zrc
     * Date: 2022/9/22
     * Time: 11:26
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function exportInventory(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        if (empty($params['admin_id'])) $this->throwError('未获取到后台用户ID');
        $crossService = new CrossService();
        $result       = $crossService->exportInventory($params);
        return $this->success($result);
    }

    /**
     * Description:导出库存信息记录队列处理
     * Author: zrc
     * Date: 2022/9/22
     * Time: 17:24
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function exportInventoryMqDeal(Request $request)
    {
        $params = file_get_contents('php://input');
        if (empty($params)) {
            $this->throwError('未获取到队列数据', ErrorCode::PARAM_ERROR);
        }
        $crossService = new CrossService();
        $result       = $crossService->exportInventoryMqDeal($params);
        return $this->success($result);
    }

    /**
     * Description:跨境订单监听处理
     * Author: zrc
     * Date: 2022/9/23
     * Time: 8:59
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function crossOrderMonitorDeal(Request $request)
    {
        $params = file_get_contents('php://input');
        if (empty($params)) {
            $this->throwError('未获取到监听数据', ErrorCode::PARAM_ERROR);
        }
        $crossService = new CrossService();
        $result       = $crossService->crossOrderMonitorDeal($params);
        return $this->success($result);
    }

    /**
     * Description:跨境商品监听处理
     * Author: zrc
     * Date: 2022/9/26
     * Time: 13:43
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function crossPeriodMonitorDeal(Request $request)
    {
        $params = file_get_contents('php://input');
        if (empty($params)) {
            $this->throwError('未获取到监听数据', ErrorCode::PARAM_ERROR);
        }
        $crossService = new CrossService();
        $result       = $crossService->crossPeriodMonitorDeal($params);
        return $this->success($result);
    }

    /**
     * Description:订单代付详情
     * Author: zrc
     * Date: 2022/9/29
     * Time: 13:31
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function orderOnBehalfDetail(Request $request)
    {
        $params        = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        if (empty($params['uid'])) $this->throwError('未获取到用户ID', ErrorCode::PARAM_ERROR);
        if (empty($params['order_no'])) $this->throwError('未获取到订单号', ErrorCode::PARAM_ERROR);
        $crossService = new CrossService();
        $result       = $crossService->orderOnBehalfDetail($params);
        return $this->success($result);
    }

    /**
     * Description:订单代付提交
     * Author: zrc
     * Date: 2022/9/29
     * Time: 14:15
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function orderOnBehalfSubmit(Request $request)
    {
        $params        = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        //数据验证
        $validate = Validate::rule([
            'uid|用户ID'            => 'require|number',
            'order_no|订单号'       => 'require',
            'realname|真实姓名'     => 'require',
            'id_card_no|身份证号码' => 'require',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $crossService = new CrossService();
        $result       = $crossService->orderOnBehalfSubmit($params);
        return $this->success($result);
    }

    /**
     * Description:跨境库存变动日志
     * Author: zrc
     * Date: 2022/11/2
     * Time: 9:20
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function inventoryChangeLog(Request $request)
    {
        $params = $request->param();
        if (empty($params['cross_inventory_id'])) $this->throwError('未获取到库存记录ID', ErrorCode::PARAM_ERROR);
        $validate = new ListPagination();
        if (!$validate->goCheck($params)) {//验证参数
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $crossService = new CrossService();
        $result       = $crossService->inventoryChangeLog($params);
        return $this->success($result);
    }

    /**
     * Description:跨境黑名单验证
     * Author: zrc
     * Date: 2022/10/28
     * Time: 11:20
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function blackListCheck(Request $request)
    {
        $params = $request->param();
        if (empty($params['id_card_no'])) $this->throwError('未获取到身份证号码', ErrorCode::PARAM_ERROR);
        $crossService = new CrossService();
        $result       = $crossService->blackListCheck($params);
        return $this->success($result);
    }

    /**
     * Description:179跨境订单原始支付信息获取
     * Author: zrc
     * Date: 2022/12/6
     * Time: 15:42
     * @param Request $request
     * @return \think\response\Json
     */
    public function checkCustoms(Request $request)
    {
        $params       = $request->param();
        $params['ip'] = $request->ip();
        if (empty($params['orderno'])) {
            $this->throwError('缺少必传参数：订单编号', 10001, 200);
        }
        if (empty($params['key']) || $params['key'] != 'wineyun') {
            $this->throwError('key值不正确', 10002, 200);
        }
        $crossService = new CrossService();
        $result       = $crossService->checkCustoms($params);
        $logMessage   = '订单编号：' . $params['orderno'] . ' 查询IP：' . $params['ip'] . ' 访问时间：' . date('Y-m-d H:i:s') . '访问数据：' . json_encode($params, true);
        $logDir       = root_path() . "/runtime/log/" . date('Ym');
        if (is_dir($logDir) == false) {
            mkdir($logDir, 755);
        }
        file_put_contents(root_path() . "/runtime/log/" . date('Ym') . "/" . date('Ymd') . 'Customs179' . '.log', $logMessage . PHP_EOL, FILE_APPEND);
        return $this->success($result);
    }

    /**
     * Description:验证跨境库存信息
     * Author: zrc
     * Date: 2023/3/2
     * Time: 13:49
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function verifyCrossStock(Request $request)
    {
        $params   = $request->param();
        $validate = Validate::rule([
            'goods_barcode|商品国际条码' => 'require',
            'warehouse_code|仓库编码'    => 'require|in: 021,028',
            'inventory_nums|库存数'      => 'require|number|>:0'
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $crossService = new CrossService();
        $result       = $crossService->verifyCrossStock($params);
        return $this->success($result);
    }

    /**
     * Description:跨境黑名单录入审批回调处理
     * Author: zrc
     * Date: 2023/3/21
     * Time: 14:15
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function inputBlackListCallBack(Request $request)
    {
        $params = $request->param();
        if (empty($params)) {
            $this->throwError('未获取到参数');
        }
        if ($params['errcode'] != 0) {
            $this->throwError($params['errmsg']);
        }
        $crossService = new CrossService();
        $result       = $crossService->inputBlackListCallBack($params);
        return $this->success($result);
    }

    /**
     * Description:黑名单列表
     * Author: zrc
     * Date: 2023/3/22
     * Time: 9:45
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function blackList(Request $request)
    {
        $params   = $request->param();
        $validate = new ListPagination();
        if (!$validate->goCheck($params)) {//验证参数
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $crossService = new CrossService();
        $result       = $crossService->blackList($params);
        return $this->success($result);
    }


    public function lock(Request $request)
    {
        $params                = $request->param();
        $params['vh_uid']      = $request->header('vinehoo-uid');
        $params['vh_vos_name'] = base64_decode($request->header('vinehoo-vos-name'));

        //数据验证
        $validate = Validate::rule([
            'vh_uid|操作人ID'        => 'require',
            'sub_order_no|子订单号'  => 'require',
            'main_order_no|主订单号' => 'require',
            'order_main_id|主订单ID' => 'require',
            'sub_order_id|子订单ID' => 'require',
            'status|锁定状态'        => 'require|in:0,1',
            'remark|备注'            => 'requireIf:status,1',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $params['lock_time'] = ($params['status'] == 1) ? time() : 0;

        $model = CrossLockOrder::where('sub_order_no', $params['sub_order_no'])->findOrEmpty();
        $res = $model->save($params);

        //添加订单备注
        $remarks = array(
            'sub_order_no' => $params['sub_order_no'],
            'order_type'   => 2,
            'content'      => (($params['status'] == 1) ? ('锁定订单: ' . $params['remark']) : '解锁订单') ,
            'admin_id'     => $params['vh_uid']
        );
        $orderService = new OrderService();
        $orderService->createRemarks($remarks);

        return $this->success();
    }

    public function inspection(Request $request)
    {
        $params = $request->param();
        //数据验证
        $validate = Validate::rule([
            'sub_order_no|子订单号' => 'require',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }

        return $this->success((new \app\service\Cross())->inspectionCheck(['order_no' => $params['sub_order_no']]));
    }

    public function excessPastDue(Request $request)
    {
        $param = $request->param();
        Log::write('进入 excessPastDue param: ' . json_encode($param));
        try {
            $model = Db::name('cross_excess')->find($param['id']);
            if (empty($model)) throw new Exception('未找到锁定记录');
            if ($model['status'] != 1) throw new Exception('不是锁定中状态 ' . $model['status']);
            Db::name('cross_excess')->where('id', $param['id'])->update([
                'unlock_time' => time(),
                'status'      => 3,
                'update_time' => time(),
            ]);
        } catch (\Exception $e) {
            Log::write('excessPastDue ERROR:' . $e->getMessage());
        }
        return $this->success();
    }

    public function excessAutoRefund(Request $request)
    {
        $param = $request->param();
        Log::write('进入 excessAutoRefund param: ' . json_encode($param));
        $p_sub_order_no = $param['sub_order_no'];
        if (!Db::name('cross_excess')->where(['sub_order_no' => $p_sub_order_no])->count()) {
            Db::name('cross_excess')->insert([
                'sub_order_no' => $p_sub_order_no,
                'created_time' => time(),
                'update_time'  => time(),
            ]);
        }
        $sms = [];

        Db::startTrans();
        try {
            $order_field = [
                "om.payment_method", "om.main_order_no", "om.payment_subject", "om.payment_amount as main_payment_amount",
                "o.uid", "o.period", "o.package_id", "o.push_store_status", "o.predict_time", "o.sub_order_status", "o.sub_order_no", "o.refund_status", "o.payment_amount", "o.refund_money",
            ];
            $order       = Db::name('cross_order')->alias('o')
                ->join('order_main om', 'om.id=o.main_order_id')
                ->where('o.sub_order_no', $p_sub_order_no)
                ->where('o.is_delete', 0)
                ->field($order_field)
                ->find();
            if (empty($order)) throw new Exception('未查询到订单');
            if ($order['sub_order_status'] != 1) throw new Exception("子订单状态不是待发货, 状态: " . $order['sub_order_status']);
            if ($order['refund_status'] != 0) throw new Exception("子订单退款状态不是未退款, 退款状态: " . $order['refund_status']);
            if (intval($order['payment_subject']) != 4) throw new Exception("支付主体错误: " . $order['payment_subject']);
//                if (intval($order['push_store_status']) == 1) throw new Exception("已推送发货仓,不可自动退款 " . $order['push_store_status']);

            $order['payment_amount'] = bcsub($order['payment_amount'], $order['refund_money'], 2); // 退款金额去除掉已退款金额

            $order_es   = Es::name(Es::ORDERS)->where([['_id', '==', $order['sub_order_no']]])->find();
            $user_phone = Db::table('vh_user.vh_user')->where('uid', $order['uid'])->value('telephone');
            $phone      = \Curl::cryptionDeal([$user_phone])[$user_phone];

            $year_blacklist = strtotime(date('Y-12-20'));
            if ($order['predict_time'] >= $year_blacklist) {
                //短信文案：
                $sms = [
                    'type'      => 1,
                    'telephone' => strval($phone),
                    'content'   => "亲爱的兔友，您购买的跨境订单{$order['sub_order_no']}（{$order_es['title']}），由于已超过个人年度购买额度，我们将会在新年度，额度刷新后为您发货，请耐心等待，如有疑问可联系在线客服，感谢您的支持与理解~"
                ];
            } else {
                //短信文案：
                $sms = [
                    'type'      => 1, //1通知类,2营销类
                    'telephone' => strval($phone),
                    'content'   => "亲爱的兔友，您购买的跨境订单{$order['sub_order_no']}（{$order_es['title']}），由于已超过个人年度购买额度，支付单已失效，款项将在5个工作日内原路退回，如有疑问可联系在线客服，感谢您的支持与理解~"
                ];


                $orderService = new OrderService();
                $orderService->updateOrder([
                    'order_no'         => $order['sub_order_no'],
                    'order_type'       => 2,
                    'operator'         => 0,
                    'sub_order_status' => 4,
                    'refund_status'    => 2,
                    'refund_money'     => bcadd($order['payment_amount'], $order['refund_money'], 2)
                ]); //更新订单状态

                $remarks = array(
                    'sub_order_no' => $order['sub_order_no'],
                    'order_type'   => 2,
                    'content'      => '海关超额自动退款',
                    'admin_id'     => 0
                );
                $orderService->createRemarks($remarks); //添加订单备注

                $refund_order_no = creatOrderNo(env('ORDERS.REFUND'), $order['uid']);
                //工单记录
                $main_id = Db::table('vh_customer_service.vh_work_order')->insertGetId([
                    'uid'                   => $order['uid'],
                    'work_order_no'         => generateTaskNo(),
                    'order_no'              => $order['sub_order_no'],
                    'period'                => $order['period'],
                    'pay_actual'            => bcadd($order['payment_amount'], $order['refund_money'], 2),
                    'payment_method'        => $order['payment_method'],
                    'order_type'            => 2,
                    'sub_order_status'      => 4,
                    'gd_do_status'          => 8,
                    'gd_status'             => 4,
                    'end_type'              => 0,
                    'work_order_type'       => 4,
                    'after_sales_reason'    => "超额自动退款",
                    'createdor'             => "系统",
                    'created_id'            => 0,
                    'work_order_manage'     => "系统",
                    'work_order_manage_uid' => 0,
                    'refund_no'             => $refund_order_no,
                    'created_time'          => time(),
                    'handle_time'           => time(),
                    'handle_end_time'       => time(),
                    'end_time'              => time(),
                    'is_fast_refund'        => 1,
                ]);
                $sub_id  = Db::table('vh_customer_service.vh_refunds')->insertGetId([
                    'gd_id'           => $main_id,
                    'baosun_num'      => '',
                    'is_return_stock' => 1,
                    'period'          => $order['period'],
                    'return_reason'   => '超额自动退款',
                    'return_money'    => $order['payment_amount'],
                    'customer_name'   => '',
                    'main_order_no'   => $order['main_order_no'],
                ]);

                //退还库存 超额只能做渠道, 不制动退还库存
//                $result = (new AdditionalService())->subOrderReturnInventory([
//                    'sub_order_no'  => $order['sub_order_no'],
//                    'main_order_no' => $order['main_order_no'],
//                    'package_id'    => $order['package_id'],
//                ]);
//                if (empty($result)) $this->throwError('库存退还失败:未获取到退还的套餐数据');
//                if (!isset($result['error_code']) || $result['error_code'] != 0) $this->throwError('库存退还失败:' . $result['error_msg']);

                //退款信息组装
                $ret_main_order_no       = $order['main_order_no'];
                $ret_main_payment_amount = $order['main_payment_amount'];
                $origin_info             = \think\facade\Db::table('vh_orders.vh_cross_split')->where('main_order_no', $order['main_order_no'])->find();
                if ($origin_info) {
                    $ret_main_order_no       = $origin_info['origin_main_order_no'];
                    $ret_main_payment_amount = $origin_info['payment_amount'];
                }

                $url         = '/payment/v3/weAndAli/refund';
                $method      = in_array($order['payment_method'], [0, 1, 2]) ? 'alipay' : 'wechat';
                $data        = [
                    'source'          => 1, //1跨境，2拍卖订单，3拍卖保证金
                    'main_order_no'   => $ret_main_order_no,
                    'refund_order_no' => $refund_order_no,
                    'payment_method'  => intval($order['payment_method']),
                    'method'          => $method,
                    'payment_amount'  => floatval($ret_main_payment_amount),
                    'refund_amount'   => floatval($order['payment_amount']),
                    'refund_desc'     => "跨境商品海关超额,自动退款"
                ];
                $header      = ['Content-Type:application/json'];
                $orderRefund = curlRequest(env('ITEM.PAYMENT_URL') . $url, json_encode($data), $header);
                Log::write("跨境超额支付宝微信发起退款记录: " . env('ITEM.PAYMENT_URL') . '/payment/v3/weAndAli/refund   ' . json_encode($data) . '   ' . json_encode($orderRefund));
                if (!isset($orderRefund['error_code']) || $orderRefund['error_code'] != 0) {
                    $msg = "跨境超额退款申请失败";
                    if (empty($orderRefund['error_msg'])) {
                        if ($method == "alipay" && $orderRefund['data']['code'] != "10000") {
                            $msg = '跨境超额支付宝退款申请失败：' . $orderRefund['data']['sub_msg'];
                        } else {
                            $msg = '跨境超额微信退款申请失败：' . $orderRefund['data']['err_code_des'];
                        }
                    } else {
                        $msg = "跨境超额自动退款失败:" . $orderRefund['error_msg'];
                    }
                    throw new Exception($msg);
                }
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::write('excessAutoRefund ERROR:' . $e->getMessage() . ' ' . $e->getLine());
            \Curl::sendWechatSender([
                'access_token' => "89244e5c-7597-48c1-91c5-2bb5d14924df",
                'at'           => "18696616422",
                'msg'          => "$p_sub_order_no 跨境订单超额自动退款失败, 退款金额: " . ($order['payment_amount'] ?? ''),
            ]);
            $sms = [];
        }
        try {
            if (!empty($sms)) {
                \Curl::sendSms($sms);
            }
        } catch (\Exception $e) {
            Log::write('excessAutoRefund 发送短信失败:' . $e->getMessage() . ' ' . json_encode($sms));
        }
        return $this->success();
    }

    public function monitorMaxNum(Request $request)
    {
        $param         = $request->param();
        $notice_reason = [
            1 => "跨境警告通知：您的账户持续存在使用多个收货地址下单。此行为已涉嫌违反《海关总署194号公告》关于跨境商品个人自用的规定。若继续该风险操作，平台将根据相关要求管理用户行为，对相关手机号及账户采取禁购、封号等措施。",
            2 => "跨境警告通知：您的账户持续存在使用多个身份信息下单。此行为已涉嫌违反《海关总署194号公告》关于跨境商品个人自用的规定。若继续该风险操作，平台将根据相关要求管理用户行为，对相关手机号及账户采取禁购、封号等措施。",
            3 => "跨境警告通知：您登记的收货手机号持续关联多个账户下单。此行为已涉嫌违反《海关总署194号公告》关于跨境商品个人自用的规定。若继续该风险操作，平台将根据相关要求管理用户行为，对相关手机号及账户采取禁购、封号等措施。",
        ];
        try {
            $param['uid'] = $request->header('vinehoo-uid');
            $last_order   = Db::name('cross_order')->alias('o')
                ->join('order_main om', 'om.id=o.main_order_id')
                ->where('o.uid', $param['uid'])
                ->where('o.sub_order_status', 'in', [2, 3, 4])
                ->order('o.id', 'desc')
                ->field('o.sub_order_no,o.id_card_no,om.consignee_phone')
                ->find();

            $enc     = Curl::cryptionDeal([$last_order['consignee_phone'], $last_order['id_card_no']], 'E');
            $monitor = Db::name('cross_monitor')
                ->where('year', date("Y"))
                ->where(function ($query) use ($param, $enc, $last_order) {
                    $query->where(function ($query) use ($param, $enc, $last_order) {
                        $query->where('type', 'in', [1, 2, 3]);
                        $query->where(function ($query) use ($param, $enc, $last_order) {
                            $query->whereOr('uid', $param['uid']);
                            $query->whereOr('consignee_phone', $enc[$last_order['consignee_phone']]);
                            $query->whereOr('id_card_no', $enc[$last_order['id_card_no']]);
                        });
                    });
//                    $query->whereOr(function ($query) use ($param, $enc, $last_order) {
//                        $query->where('type', 'in', [4, 5]);
//                        $query->where('uid', $param['uid']);
//                        $query->where('period', $param['period']);
//                    });
                })->order('num', 'desc')->find() ?? [];

            $notice  = $notice_reason[($monitor['type'] ?? '')] ?? '';
            $max_num = $monitor['num'] ?? 0;
            $is_pop  = $max_num >= 8;
        } catch (Exception $e) {
            $max_num = 0;
            $is_pop  = false;
        }
        return $this->success(compact('max_num', 'is_pop', 'notice'));
    }

    public function monitorAnomaly(Request $request)
    {
        $param = $request->param();
        Log::write('进入 monitorAnomaly param: ' . json_encode($param));
        $p_monitor_id         = $param['monitor_id'];
        $monitor              = Db::name('cross_monitor')->where('id', $p_monitor_id)->find();
        $p_sub_order_no       = $monitor['sub_order_no'];
        $monitor_update       = [
            'update_time'        => time(),
            'operate_time'       => time(),
            'remark'             => "{$monitor['remark']} ",
            'status'             => 1,
            'operate_id'         => 0,
            'operate_name'       => '系统',
            'notice_status'      => $monitor['notice_status'] ?? 3,
            'sms_status'         => $monitor['sms_status'] ?? 3,
            'refund_status'      => $monitor['refund_status'] ?? 3,
            'restrictive_status' => $monitor['restrictive_status'] ?? 3,
        ];
        $sms                  = $notice = "";
        $refund_status        = false;
        $restrictive_longtime = 0;

        try {

            $order_field = [
                "om.payment_method", "om.main_order_no", "om.payment_subject", "om.payment_amount as main_payment_amount",
                "o.uid", "o.period", "o.id_card_no", "o.package_id", "o.push_store_status", "o.predict_time", "o.sub_order_status", "o.sub_order_no", "o.refund_status", "o.payment_amount", "o.refund_money",
            ];
            $order       = Db::name('cross_order')->alias('o')
                ->join('order_main om', 'om.id=o.main_order_id')
                ->where('o.sub_order_no', $p_sub_order_no)
                ->where('o.is_delete', 0)
                ->field($order_field)
                ->find();
            if (empty($order)) throw new Exception('未查询到订单');
            if ($order['sub_order_status'] != 1) throw new Exception("子订单状态不是待发货, 状态: " . $order['sub_order_status']);
            if ($order['refund_status'] != 0) throw new Exception("子订单退款状态不是未退款, 退款状态: " . $order['refund_status']);
            if (intval($order['payment_subject']) != 4) throw new Exception("支付主体错误: " . $order['payment_subject']);

            $dec   = Curl::cryptionDeal([$monitor['phone'], $monitor['consignee'], $monitor['consignee_phone'], $order['id_card_no']]);
            $phone = array_values(array_unique([
                $dec[$monitor['phone']],
                $dec[$monitor['consignee_phone']],
            ]));

//                    1 => "的账户存在使用多个收货地址下单",
//                    2 => "账户使用多个身份证下单",
//                    3 => "登记的收货手机号关联多个账户下单",
//                    4 => "期数复购次数达{$monitor['num']}",//(金额>=1000)
//                    5 => "期数复购次数达{$monitor['num']}",//(金额<1000)
//                    6 => "物流异常转投次数达{$monitor['num']}"

            if (in_array($monitor['num'], [5])) {
                $notice_reason = [
                    1 => "跨境温馨提醒：系统检测到您的账户存在使用多个收货地址下单。根据《海关总署194号公告》，跨境交易商品仅限个人自用，禁止用于二次销售。",
                    2 => "跨境温馨提醒：系统检测到您的账户存在使用多个身份信息下单。根据《海关总署194号公告》，跨境交易商品仅限个人自用，禁止用于二次销售。",
                    3 => "跨境温馨提醒：系统检测到您登记的收货手机号关联多个账户下单。根据《海关总署194号公告》，跨境交易商品仅限个人自用，禁止用于二次销售。",
                ];
            } elseif (in_array($monitor['num'], [6, 7, 8])) {
                $notice_reason = [
                    1 => "跨境警告通知：您的账户持续存在使用多个收货地址下单。此行为已涉嫌违反《海关总署194号公告》关于跨境商品个人自用的规定。若继续该风险操作，平台将根据相关要求管理用户行为，对相关手机号及账户采取禁购、封号等措施。",
                    2 => "跨境警告通知：您的账户持续存在使用多个身份信息下单。此行为已涉嫌违反《海关总署194号公告》关于跨境商品个人自用的规定。若继续该风险操作，平台将根据相关要求管理用户行为，对相关手机号及账户采取禁购、封号等措施。",
                    3 => "跨境警告通知：您登记的收货手机号持续关联多个账户下单。此行为已涉嫌违反《海关总署194号公告》关于跨境商品个人自用的规定。若继续该风险操作，平台将根据相关要求管理用户行为，对相关手机号及账户采取禁购、封号等措施。",
                ];
                $sms_reason    = [
                    1 => "【跨境重要警告】您的账户持续使用多收货地址下单，涉嫌违反海关194号公告（个人自用规定）。若继续该风险操作，平台将对相关账户及手机号采取限购、封号等措施。",
                    2 => "【跨境重要警告】您的账户持续使用多个身份信息下单，涉嫌违反海关194号公告（个人自用规定）。若继续该风险操作，平台将对相关账户及手机号采取限购、封号等措施。",
                    3 => "【跨境重要警告】您登记的收货手机号持续关联多账户下单，涉嫌违反海关194号公告（个人自用规定）。若继续该风险操作，平台将对相关手机号及账户采取限购、封号等措施。",
                ];
            } elseif ($monitor['num'] >= 9) {
                $notice_reason = [
                    1 => "【跨境重要通知】尊敬的兔友，经核查确认，您的账户存在使用多个收货地址下单的行为。依据《海关总署194号公告》，此行为在跨境交易范围存在极大违规风险。平台跨境板块将：1. 对您的账户及关联手机号禁购365个自然日；2. 取消涉及的订单并安排退款。即时生效。",
                    2 => "【跨境重要通知】尊敬的兔友，经核查确认，您的账户存在使用多个身份信息下单的行为。依据《海关总署194号公告》，此行为在跨境交易范围存在极大违规风险。平台跨境板块将：1. 对您的账户及关联手机号禁购365个自然日；2. 取消涉及的订单并安排退款。即时生效。",
                    3 => "【跨境重要通知】尊敬的兔友，经核查确认，您登记的收货手机号存在关联多个账户下单的行为。依据《海关总署194号公告》，此行为在跨境交易范围存在极大违规风险。平台跨境板块将：1. 对您的账户及关联手机号禁购365个自然日；2. 取消涉及的订单并安排退款。即时生效。",
                ];
                $sms_reason    = [
                    1 => "【跨境重要通知】尊敬的兔友，因您的账户使用多个收货地址下单，违反海关194号公告，平台已对您的账户及关联手机号执行365个自然日封禁，并取消涉及的订单退款。即时生效。",
                    2 => "【跨境重要通知】尊敬的兔友，因您的账户使用多个身份信息下单，违反海关194号公告，平台已对您的账户及关联手机号执行365个自然日封禁，并取消涉及的订单退款。即时生效。",
                    3 => "【跨境重要通知】尊敬的兔友，因您登记的收货手机号关联多账户下单，违反海关194号公告，平台已对相关手机号及所有关联账户执行365个自然日封禁，并取消涉及的订单退款。即时生效。",
                ];

                $refund_status = true;
                if (Db::name("cross_restrictive")
                        ->whereOr("id_card_no", $dec[$order['id_card_no']])
                        ->whereOr('phone', $dec[$monitor['consignee_phone']])
                        ->whereOr('uid', $order['uid'])
                        ->count() > 0) {
                    $restrictive_longtime = strtotime('+1 year');
                } else {
                    $restrictive_longtime = strtotime('+1 year');
                }
            }
            $notice = $notice_reason[$monitor['type']] ?? "";
            $sms    = $sms_reason[$monitor['type']] ?? "";


            //发送APP信息
            if (!empty($notice) && in_array($monitor['notice_status'], [0, 2])) {
                try {
                    $single     = [
                        'is_push'      => 1,
                        'uid'          => $order['uid'],
                        'title'        => "订单通知",
                        'content'      => $notice,
                        'data_type'    => 18,
                        'data'         => [
                            'title'   => "订单通知",
                            'content' => $notice,
                        ],
                        'label'        => "MyOrder",
                        'custom_param' => []
                    ];
                    $single_res = httpPostString(env('ITEM.APPPUSH_URL') . '/apppush/v3/push/single', json_encode($single, JSON_UNESCAPED_UNICODE));
                    if ($single_res['error_code'] != 0) {
                        throw new Exception($single_res['error_msg'] ?? '推送失败');
                    }
                    $monitor_update['notice_status'] = 1;//成功
                    $monitor_update['remark']        .= "\n站内信提醒成功";//成功
                } catch (\Exception $e) {
                    $monitor_update['notice_status'] = 2;//失败
                    $monitor_update['remark']        .= "\n站内信提醒失败: " . $e->getMessage();//失败
                }
            }

            //发送短信
            if (!empty($sms) && in_array($monitor['sms_status'], [0, 2])) {
                try {
                    $smsParam = [
                        'type'      => 1, //1通知类,2营销类
                        'telephone' => strval(implode(',', $phone)),
                        'content'   => $sms
                    ];

                    \Curl::sendSms($smsParam);
                    $monitor_update['sms_status'] = 1;//成功
                    $monitor_update['remark']     .= "\n短信提醒成功";//成功
                } catch (\Exception $e) {
                    $monitor_update['sms_status'] = 2;//失败
                    $monitor_update['remark']     .= "\n短信提醒失败: " . $e->getMessage();//失败
                }
            }

            //限购
            if (($restrictive_longtime > 0) && in_array($monitor['restrictive_status'], [0, 2])) {
                try {
                    Db::name("cross_restrictive")->insert([
                        "id_card_no"   => $dec[$order['id_card_no']],
                        "phone"        => $dec[$monitor['consignee_phone']],
                        "uid"          => $order['uid'],
                        "sponsor"      => "系统",
                        "note"         => "限购添加",
                        "sub_order_no" => $order['sub_order_no'],
                        "created_time" => time(),
                        "end_time"     => $restrictive_longtime,
                    ]);

                    $monitor_update['restrictive_status'] = 1;//成功
                    $monitor_update['remark']             .= "\n限购成功";//成功
                } catch (\Exception $e) {
                    $monitor_update['restrictive_status'] = 2;//失败
                    $monitor_update['remark']             .= "\n限购失败: " . $e->getMessage();//失败
                }
            }

            //取消订单并且退款
            if ($refund_status && in_array($monitor['refund_status'], [0, 2])) {
                Db::startTrans();
                try {
                    $order['payment_amount'] = bcsub($order['payment_amount'], $order['refund_money'], 2); // 退款金额去除掉已退款金额

                    $orderService = new OrderService();
                    $orderService->updateOrder([
                        'order_no'         => $order['sub_order_no'],
                        'order_type'       => 2,
                        'operator'         => 0,
                        'sub_order_status' => 4,
                        'refund_status'    => 2,
                        'refund_money'     => bcadd($order['payment_amount'], $order['refund_money'], 2)
                    ]); //更新订单状态

                    $remarks = array(
                        'sub_order_no' => $order['sub_order_no'],
                        'order_type'   => 2,
                        'content'      => '异常购买自动退款',
                        'admin_id'     => 0
                    );
                    $orderService->createRemarks($remarks); //添加订单备注

                    $refund_order_no = creatOrderNo(env('ORDERS.REFUND'), $order['uid']);
                    //工单记录
                    $main_id = Db::table('vh_customer_service.vh_work_order')->insertGetId([
                        'uid'                   => $order['uid'],
                        'work_order_no'         => generateTaskNo(),
                        'order_no'              => $order['sub_order_no'],
                        'period'                => $order['period'],
                        'pay_actual'            => bcadd($order['payment_amount'], $order['refund_money'], 2),
                        'payment_method'        => $order['payment_method'],
                        'order_type'            => 2,
                        'sub_order_status'      => 4,
                        'gd_do_status'          => 8,
                        'gd_status'             => 4,
                        'end_type'              => 0,
                        'work_order_type'       => 4,
                        'after_sales_reason'    => "异常购买自动退款",
                        'createdor'             => "系统",
                        'created_id'            => 0,
                        'work_order_manage'     => "系统",
                        'work_order_manage_uid' => 0,
                        'refund_no'             => $refund_order_no,
                        'created_time'          => time(),
                        'handle_time'           => time(),
                        'handle_end_time'       => time(),
                        'end_time'              => time(),
                        'is_fast_refund'        => 1,
                    ]);
                    $sub_id  = Db::table('vh_customer_service.vh_refunds')->insertGetId([
                        'gd_id'           => $main_id,
                        'baosun_num'      => '',
                        'is_return_stock' => 1,
                        'period'          => $order['period'],
                        'return_reason'   => '异常购买自动退款',
                        'return_money'    => $order['payment_amount'],
                        'customer_name'   => '',
                        'main_order_no'   => $order['main_order_no'],
                    ]);

                    //退还库存
                    $result = (new AdditionalService())->subOrderReturnInventory([
                        'sub_order_no'  => $order['sub_order_no'],
                        'main_order_no' => $order['main_order_no'],
                        'package_id'    => $order['package_id'],
                    ]);
                    if (empty($result)) $this->throwError('库存退还失败:未获取到退还的套餐数据');
                    if (!isset($result['error_code']) || $result['error_code'] != 0) $this->throwError('库存退还失败:' . $result['error_msg']);

                    //退款信息组装
                    $ret_main_order_no       = $order['main_order_no'];
                    $ret_main_payment_amount = $order['main_payment_amount'];
                    $origin_info             = \think\facade\Db::table('vh_orders.vh_cross_split')->where('main_order_no', $order['main_order_no'])->find();
                    if ($origin_info) {
                        $ret_main_order_no       = $origin_info['origin_main_order_no'];
                        $ret_main_payment_amount = $origin_info['payment_amount'];
                    }

                    $url         = '/payment/v3/weAndAli/refund';
                    $method      = in_array($order['payment_method'], [0, 1, 2]) ? 'alipay' : 'wechat';
                    $data        = [
                        'source'          => 1, //1跨境，2拍卖订单，3拍卖保证金
                        'main_order_no'   => $ret_main_order_no,
                        'refund_order_no' => $refund_order_no,
                        'payment_method'  => intval($order['payment_method']),
                        'method'          => $method,
                        'payment_amount'  => floatval($ret_main_payment_amount),
                        'refund_amount'   => floatval($order['payment_amount']),
                        'refund_desc'     => "跨境商品海关超额,自动退款"
                    ];
                    $header      = ['Content-Type:application/json'];
                    $orderRefund = curlRequest(env('ITEM.PAYMENT_URL') . $url, json_encode($data), $header);
                    Log::write("跨境超额支付宝微信发起退款记录: " . env('ITEM.PAYMENT_URL') . '/payment/v3/weAndAli/refund   ' . json_encode($data) . '   ' . json_encode($orderRefund));
                    if (!isset($orderRefund['error_code']) || $orderRefund['error_code'] != 0) {
                        $msg = "跨境超额退款申请失败";
                        if (empty($orderRefund['error_msg'])) {
                            if ($method == "alipay" && $orderRefund['data']['code'] != "10000") {
                                $msg = '跨境超额支付宝退款申请失败：' . $orderRefund['data']['sub_msg'];
                            } else {
                                $msg = '跨境超额微信退款申请失败：' . $orderRefund['data']['err_code_des'];
                            }
                        } else {
                            $msg = "跨境异常购买自动退款失败:" . $orderRefund['error_msg'];
                        }
                        throw new Exception($msg);
                    }

                    Db::commit();
                    $monitor_update['refund_status'] = 1;//成功
                    $monitor_update['remark']        .= "\n自动退款成功";//成功
                } catch (\Exception $e) {
                    Db::rollback();
                    $monitor_update['refund_status'] = 2;//失败
                    $monitor_update['remark']        .= "\n自动退款失败: " . $e->getMessage();//失败
                }
            }

            Db::name('cross_monitor')->where('id', $p_monitor_id)->update($monitor_update);

        } catch (\Exception $e) {
            Log::write('monitorAnomaly ERROR:' . $e->getMessage() . ' ' . $e->getLine());
            \Curl::sendWechatSender([
                'access_token' => "89244e5c-7597-48c1-91c5-2bb5d14924df",
                'at'           => "17772336502",
                'msg'          => "$p_sub_order_no 跨境订单异常监控回调处理失败 {$p_monitor_id} : " . $e->getMessage(),
            ]);
        }
        return $this->success();
    }

    public function stockSalesCallback(Request $request)
    {
        $param = $request->param();
        $msg   = __FUNCTION__ . " param:  " . json_encode($param) . PHP_EOL;
        Log::write($msg);

        $wechat_status = $param['process_instance']['status'] ?? null;
        if (!in_array($wechat_status, ['COMPLETED', 'TERMINATED'])) {
            throw new Exception("审批未完成");
        }
        if ($wechat_status == 'COMPLETED') {
            $wechat_result = $param['process_instance']['result'] ?? null;
            if (!$wechat_result) {
                throw new Exception('审批状态错误');
            }
        } else {
            $wechat_result = $wechat_status;
        }
        $user_id = $param['process_instance']['originator_userid'];

        if ($wechat_result == 'agree') {
            //region 插入数据
            try {
                #region 导出数据
                //region 列表查询
                $tmp_list = Db::name('cross_inventory')
                    ->where('is_delete', 0)
                    ->where('available_nums', '>', 0)
                    ->column('*');

                $goods_barcodes           = array_values(array_unique(array_column($tmp_list, 'goods_barcode')));
                $has_short_periods        = Es::name(Es::PERIODS)->where([
                    ['short_code', 'in', $goods_barcodes]
                ])->field('id,short_code')->order(['id' => 'desc'])->select()->toArray();
                $goods_barcode_period_ids = [];
                foreach ($has_short_periods as $has_short_period) {
                    foreach ($has_short_period['short_code'] as $goods_barcode) {
                        $goods_barcode_period_ids[$goods_barcode][] = $has_short_period['id'];
                    }
                }
                $pids                  = Db::table('vh_wiki.vh_products')->where('short_code', 'in', $goods_barcodes)->column('short_code', 'id');
                $has_short_pkgs        = Db::table('vh_commodities.vh_periods_cross_set')
                    ->where('period_id', 'in', array_column($has_short_periods, 'id'))
                    ->where(function ($query) use ($pids) {
                        foreach ($pids as $pid => $sc) {
                            $query->whereOr('associated_products', 'LIKE', "%:{$pid},%");
                        }
                    })->column('associated_products', 'id');
                $goods_barcode_pkg_ids = [];
                foreach ($has_short_pkgs as $pkg_id => $associated_products) {
                    $associated_products = json_decode($associated_products, true);
                    foreach ($associated_products as $ap_info) {
                        if (!is_array($ap_info['product_id']) && !empty($pids[$ap_info['product_id']])) {
                            if (!empty($ap_info['sub_package_id']))
                                $goods_barcode_pkg_ids[$pids[$ap_info['product_id']]][] = $ap_info['sub_package_id'];
                            $goods_barcode_pkg_ids[$pids[$ap_info['product_id']]][] = $pkg_id;
                        }
                    }
                }


                $inv_nums = Db::name('cross_excess')->where('log_id', 'in', array_column($tmp_list, 'id'))->where('status', 1)->group('log_id')->column('SUM(num)', 'log_id');

                $temp_quantitys = [];
                $wh_list        = range(1, 2);
                foreach ($goods_barcode_period_ids as $i_goods_barcode => $i_goods_barcode_period_id) {
                    if (in_array($i_goods_barcode, $goods_barcodes)) {

                        $ts_where = $wd_where = [
                            ['period', 'in', $i_goods_barcode_period_id], //简码包含的全部期数ID
                            ['package_id', 'in', $goods_barcode_pkg_ids[$i_goods_barcode] ?? []], //简码包含的全部套餐ID
                            ['is_delete', '=', 0], //是否删除 0-正常 1-已删除
                            ['order_type', '=', 2], //是否删除 2-跨境
                            ['store_type', 'in', $wh_list],
                            ['sub_order_status', '=', 1], //订单状态=1 订单状态：0-待支付 1-已支付 2-已发货 3-已完成 4-已取消
                            ['refund_status', '=', 0], //退款状态=0  0-未退款 1-退款中 2-退款成功 3-退款失败
                        ];

                        $ts_where[] = ['is_ts', '=', 1]; //是否暂存：0否 1是
                        $ts_where[] = ['push_store_status', '!=', 1]; //代发仓推送状态：0-未推送 1-推送成功 2-推送失败 3-不推送
                        $wd_where[] = ['push_store_status', '=', 1]; //代发仓推送状态：0-未推送 1-推送成功 2-推送失败 3-不推送

                        $temp_orders  = Es::name(Es::ORDERS)->where($ts_where)->field('id,store_type,order_qty')->select()->toArray();
                        $wait_deliver = Es::name(Es::ORDERS)->where($wd_where)->field('id,store_type,order_qty')->select()->toArray();

                        $temp_order_group   = array_group($temp_orders, 'store_type');
                        $wait_deliver_group = array_group($wait_deliver, 'store_type');

                        foreach ($wh_list as $wh_id) {
                            //代发仓：1-古斯缇 2-南沙仓
                            $temp_quantitys[$i_goods_barcode][$wh_id][0] = array_sum(array_column($wait_deliver_group[$wh_id] ?? [], 'order_qty')); //已支付未发货
                            $temp_quantitys[$i_goods_barcode][$wh_id][1] = array_sum(array_column($temp_order_group[$wh_id] ?? [], 'order_qty')); //暂存
                        }
                    }
                }

                #推单时需要【实物库存-次品数量-暂存数量-已推送未发货数量】
                $entry_nums             = [];
                $temp_cross_inventorys  = Db::name('cross_inventory')->where([
                    ['goods_barcode', 'in', $goods_barcodes],
                    ['store_type', 'in', $wh_list],
                    ['is_delete', '=', 0]
                ])->column('id,goods_barcode,store_type,real_nums,defective_nums');
                $group_cross_inventorys = array_group($temp_cross_inventorys, 'goods_barcode');
                foreach ($group_cross_inventorys as &$group_cross_inventory_format) {
                    $group_cross_inventory_format = array_group($group_cross_inventory_format, 'store_type');
                }

                foreach ($group_cross_inventorys as $b_code => $group_cross_inventory) {
                    foreach ($wh_list as $wh_id) {
                        $wh_gci                      = $group_cross_inventory[$wh_id] ?? [];
                        $entry_nums[$b_code][$wh_id] = bcsub(array_sum(array_column($wh_gci, 'real_nums')), array_sum(array_column($wh_gci, 'defective_nums'))); //实物库存 - 次品库存之和
                    }
                }

                $list      = [];
                $bar_codes = [];
                foreach ($tmp_list as $tmp_item) {
                    $tmp_item['excess_nums']    = $excess_nums[$tmp_item['goods_barcode']][$tmp_item['store_type']] ?? 0;
                    $tmp_item['available_nums'] = bcsub($tmp_item['available_nums'], ($inv_nums[$tmp_item['id']] ?? 0));

                    //是否暂存：0否 1是
                    $i_temp_quantity        = $temp_quantitys[$tmp_item['goods_barcode']][$tmp_item['store_type']][1] ?? 0; //暂存数
                    $i_normal_temp_quantity = $temp_quantitys[$tmp_item['goods_barcode']][$tmp_item['store_type']][0] ?? 0; //已推送未发货数量
                    $i_quantity             = bcadd(strval($i_temp_quantity), strval($i_normal_temp_quantity));

                    $tmp_item['temp_quantity']        = $i_temp_quantity;
                    $tmp_item['normal_temp_quantity'] = $i_normal_temp_quantity;
                    $tmp_item['pushable']             = bcsub(strval($entry_nums[$tmp_item['goods_barcode']][$tmp_item['store_type']] ?? 0), strval($i_quantity)); //可推送数量

                    $tmp_item['entry_days']    = $tmp_item['entry_time'] == 0 ? 0 : bcdiv(bcsub(time(), $tmp_item['entry_time']), 86400);
                    $tmp_item['entry_time']    = $tmp_item['entry_time'] == 0 ? '-' : date('Y-m-d', $tmp_item['entry_time']);
                    $tmp_item['last_out_time'] = $tmp_item['last_out_time'] == 0 ? '-' : date('Y-m-d', $tmp_item['last_out_time']);

                    if ($tmp_item['available_nums'] > 0) {
                        $list[$tmp_item['store_type']][$tmp_item['goods_barcode']][] = $tmp_item;
                        $bar_codes[]                                                 = $tmp_item['goods_barcode'];
                    }
                }
                //endregion 列表查询

                $products = Db::table('vh_wiki.vh_products')->alias('t1')
                    ->leftJoin('vh_wiki.vh_product_unit t2', 't1.product_unit = t2.id')
                    ->leftJoin('vh_wiki.vh_country_base t3', 't1.country_id = t3.id')
                    ->where('t1.bar_code', 'in', $bar_codes)
                    ->column('t1.id,t1.short_code,t1.bar_code,t1.capacity,t2.name as product_type_name,t3.country_name_en,t3.country_name_cn', 't1.bar_code');


                $store_type_arr = ['1' => ['code' => '021', 'name' => '古斯缇'], '2' => ['erp_id' => '028', 'name' => '南沙仓'],];

                $tmp_invs = Db::table('vh_commodities.vh_periods_cross')->alias('t1')
                    ->join('vh_commodities.vh_periods_cross_set t2', 't1.id = t2.period_id')
                    ->join('vh_commodities.vh_periods_product_inventory t3', 't1.id = t3.period')
                    ->where('t3.bar_code', 'in', array_keys($products))
                    ->where('t1.onsale_status', 'in', [2, 3, 4]) //上架状态（0：待上架，1：待售中，2：在售中，3：已下架，4：已售馨）
                    ->where(function ($query) use ($products) {
                        $pids = array_column($products, 'id');
                        foreach ($pids as $pid) {
                            $query->whereOr("t2.associated_products", 'LIKE', "%:{$pid},%");
                        }
                    })
                    ->order('t1.onsale_time ASC')
                    ->column('t3.id,t1.onsale_status,t1.price,t1.purchased,t1.title,t1.onsale_time,t3.period,t3.bar_code,t3.erp_id,t3.costprice');

                $invs = [];
                foreach ($tmp_invs as $tmp_inv) {
                    $tmp_inv['onsale_time'] = date('Y-m-d H:i:s',$tmp_inv['onsale_time']);
                    if ($tmp_inv['onsale_status'] == 2) {
                        $invs[$tmp_inv['erp_id']][$tmp_inv['bar_code']][2] = $tmp_inv;
                    } else {
                        $invs[$tmp_inv['erp_id']][$tmp_inv['bar_code']][3] = $tmp_inv;
                    }
                }

                $data[] = [
                    '简码',
                    '国家',
                    '类型',
                    '容量',
                    '仓库',
                    '在售期数',
                    '预售/现货',
                    '采购成本',
                    '上架成本',
                    '售价',
                    '上架时间',
                    '已售瓶数',
                    '最近下单时间',
                    '可售库存',
                    '可推送库存',
                    '残次数量',
                    '期数标题'
                ];

                foreach ($list as $store_type => $bar_code_group) {
                    $st_invs = $invs[$store_type_arr[$store_type]['code'] ?? ''] ?? [];

                    foreach ($bar_code_group as $bar_code => $bc_list) {
                        $product        = $products[$bar_code] ?? [];
                        $bc_st_inv_info = [
                            'period'          => '-',
                            'costprice'       => '-',
                            'price'           => '-',
                            'onsale_time'     => '-',
                            'purchased'       => '-',
                            'title'           => '-',
                            'order_last_time' => '-',
                        ];

                        $bc_st_inv = $st_invs[$bar_code] ?? [];
                        if (!empty($bc_st_inv[2])) {
                            $bc_st_inv_info['period']          = $bc_st_inv[2]['period'];
                            $bc_st_inv_info['costprice']       = $bc_st_inv[2]['costprice'];
                            $bc_st_inv_info['price']           = $bc_st_inv[2]['price'];
                            $bc_st_inv_info['onsale_time']     = $bc_st_inv[2]['onsale_time'];
                            $bc_st_inv_info['purchased']       = $bc_st_inv[2]['purchased'];
                            $bc_st_inv_info['title']           = $bc_st_inv[2]['title'];
                            $bc_st_inv_info['order_last_time'] = Es::name(Es::ORDERS)->where([['period', '==', $bc_st_inv[2]['period']]])->order(['created_time' => 'desc'])->value('created_time') ?? '-';
                        } elseif (!empty($bc_st_inv[3])) {
                            $bc_st_inv_info['costprice']       = $bc_st_inv[3]['costprice'];
                            $bc_st_inv_info['price']           = $bc_st_inv[3]['price'];
                            $bc_st_inv_info['title']           = $bc_st_inv[3]['title'];
                            $bc_st_inv_info['order_last_time'] = Es::name(Es::ORDERS)->where([['period', '==', $bc_st_inv[3]['period']]])->order(['created_time' => 'desc'])->value('created_time') ?? '-';
                        }

                        foreach ($bc_list as $item) {
                            $purchase_data  = \Curl::erpGetCostPrice([
                                'short_code' => [$product['short_code']],
                            ]);
                            $purchase_items = array_column($purchase_data['list'], 'cost', 'short_code');

                            $data[] = [
                                $item['goods_barcode'],
                                implode('/', [($product['country_name_cn'] ?? ''), ($product['country_name_en'] ?? '')]),
                                $product['product_type_name'] ?? '',
                                $product['capacity'] ?? '',
                                $store_type_arr[$store_type]['name'] ?? '',
                                $bc_st_inv_info['period'],
                                ($item['pushable'] > 0 || $item['defective_nums'] > 0) ? '现货' : '预售',
                                $purchase_items[$product['short_code']] ?? '-',
                                $bc_st_inv_info['costprice'],
                                $bc_st_inv_info['price'],
                                $bc_st_inv_info['onsale_time'],
                                $bc_st_inv_info['purchased'],
                                empty($bc_st_inv_info['order_last_time']) ? '-' : date('Y-m-d', strtotime($bc_st_inv_info['order_last_time'])),
                                $item['available_nums'],
                                $item['pushable'],
                                $item['defective_nums'],
                                $bc_st_inv_info['title'],
                            ];
                        }
                    }
                }
                //endregion

                $path      = app()->getRootPath() . "public/storage/";
                $excel     = new \Vtiful\Kernel\Excel(['path' => $path]);
                $file_name = time() . '跨境库存&销售数据.xlsx';
                $filePath  = $excel->fileName($file_name, 'sheet1')->data($data)->output();


                //上传文件到微信服务器
                $wechat_up_data = \Curl::upTempFileToWechat($filePath);

                //发送文件到微信聊天框
                $res = \Curl::wecomSend($wechat_up_data['media_id'], $user_id, 'file');
                unlink($filePath);
            } catch (\Exception $e) {
                if (!empty($filePath)) unset($filePath);
                Log::error('库存销售数据导出审批回调错误:  ' . $e->getMessage() . ' SQL: ' . Db::getLastSql()); // SQL报错信息不返回给用户
                \Curl::wecomSend('库存销售数据导出失败' . $e->getMessage() . ' ' . $e->getLine(), $user_id, 'text');
                return $this->throwError('库存销售数据导出审批回调错误: ' . $e->getMessage(). $e->getLine(), ErrorCode::EXEC_ERROR);
            }
            //endregion
        }

        return $this->success();
    }


    /**
     * Description: 跨境异常行为监控列表
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function monitorList(Request $request)
    {
        $params = $request->param();

        // 验证分页参数
        $validate = new ListPagination();
        if (!$validate->goCheck($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }

        // 构建查询条件
        $where = [];

        // 订单号搜索
        if (!empty($params['sub_order_no'])) {
            $where[] = ['sub_order_no', 'like', '%' . $params['sub_order_no'] . '%'];
        }

        // 用户昵称搜索
        if (!empty($params['nickname'])) {
            $where[] = ['nickname', 'like', '%' . $params['nickname'] . '%'];
        }

        // 用户手机号搜索
        if (!empty($params['phone']) || !empty($params['consignee']) || !empty($params['consignee_phone'])) {
            $dec = array_values(array_filter([$params['phone'] ?? '', $params['consignee'] ?? '', $params['consignee_phone'] ?? '']));
            $enc = \Curl::cryptionDeal($dec, 'E');

            if (!empty($params['phone']) && !empty($enc[$params['phone']])) {
                $where[] = ['phone', '=', $enc[$params['phone']]];
            }

            if (!empty($params['consignee']) && !empty($enc[$params['consignee']])) {
                $where[] = ['consignee', '=', $enc[$params['consignee']]];
            }

            if (!empty($params['consignee_phone']) && !empty($enc[$params['consignee_phone']])) {
                $where[] = ['consignee_phone', '=', $enc[$params['consignee_phone']]];
            }
        }

        // 异常类型搜索
        if (isset($params['type']) && $params['type'] > 0) {
            $where[] = ['type', '=', $params['type']];
        }

        // 处理状态搜索
        if (isset($params['status'])) {
            $where[] = ['status', '=', $params['status']];
        }

        // 时间范围搜索
        if (!empty($params['start_time'])) {
            $where[] = ['created_time', '>=', strtotime($params['start_time'])];
        }
        if (!empty($params['end_time'])) {
            $where[] = ['created_time', '<=', strtotime(date('Y-m-d 23:59:59', strtotime($params['end_time'])))];
        }

        // 分页参数
        $page  = !empty($params['page']) ? intval($params['page']) : 1;
        $limit = !empty($params['limit']) ? intval($params['limit']) : 10;

        try {
            // 查询总数
            $total      = Db::name('cross_monitor')->where($where)->count();
            $type_count = Db::name('cross_monitor')->where($where)->group('type')->count();

            // 如果有数据再查询列表
            $list = [];
            if ($total > 0) {
                $list = Db::name('cross_monitor')
                    ->where($where)
                    ->order('id desc')
                    ->page($page, $limit)
                    ->select()
                    ->toArray();

                $enc_data = array_values(array_filter(array_unique(array_merge(array_column($list, 'consignee_phone'), array_column($list, 'consignee'), array_column($list, 'phone')))));
                $dec_data = \Curl::cryptionDeal($enc_data);
                if (!empty($list)) {
                    foreach ($list as &$item) {
                        // 格式化时间戳
                        $item['created_time'] = !empty($item['created_time']) ? date('Y-m-d H:i:s', $item['created_time']) : '';
                        $item['update_time']  = !empty($item['update_time']) ? date('Y-m-d H:i:s', $item['update_time']) : '';
                        $item['operate_time'] = !empty($item['operate_time']) ? date('Y-m-d H:i:s', $item['operate_time']) : '';

                        // 异常类型文字说明
                        $type_text         = [
                            1 => '同一用户多个收货地址下单',
                            2 => '同一账号使用多个身份证下单',
                            3 => '同一收货手机号被多个账号使用',
                            4 => '用户高频复购(金额>=1000)',
                            5 => '用户高频复购(金额<1000)',
                            6 => '物流异常'
                        ];
                        $item['type_text'] = $type_text[$item['type']] ?? '';

                        // 状态文字说明
                        $item['status_text'] = $item['status'] == 0 ? '待处理' : '已处理';

                        $item['phone']           = $dec_data[$item['phone']] ?? '';
                        $item['consignee']       = $dec_data[$item['consignee']] ?? '';
                        $item['consignee_phone'] = $dec_data[$item['consignee_phone']] ?? '';
                    }
                }
            }

            return $this->success([
                'list'       => $list,
                'total'      => $total,
                'type_count' => $type_count,
                'page'       => $page,
                'limit'      => $limit
            ]);

        } catch (\Exception $e) {
            Log::error('跨境异常行为监控列表查询失败:' . $e->getMessage());
            return $this->throwError($e->getMessage(), ErrorCode::EXEC_ERROR);
        }
    }

}