<?php


namespace app\validate;


class ListPagination extends BaseValidate
{
    /**
     * 定义验证规则
     * 格式：'字段名'    =>    ['规则1','规则2'...]
     *
     * @var array
     */
    protected $rule = [
        'page'  => ['require', '/^[1-9][0-9]*$/'],
        'limit' => ['require', '/^[1-9][0-9]*$/']
    ];

    /**
     * 定义错误信息
     * 格式：'字段名.规则名'    =>    '错误信息'
     *
     * @var array
     */
    protected $message = [
        'page.require'  => '页码必传',
        'limit.require' => '分页条数必传'
    ];
}