<?php
declare (strict_types = 1);

namespace app\service\command;

use app\service\Cross as CrossService;
use GuzzleHttp\Exception\RequestException;
use think\facade\Db;
use think\facade\Log;

/**
 * 跨境自动推单每日统计
 * CrossCardnoYearMoneyCommand
 * Class CrossCardnoYearMoneyCommand
 * @package app\service\command
 */
class CrossAutoPushOrderDailyGatherCommand
{
    public function exec()
    {
        $todaytime     = strtotime(date('Y-m-d'));
        $yesterdaytime = strtotime(date('Y-m-d', strtotime('-1 day')));
        $pushLog       = Db::name('cross_auto_push_log')->where([['created_time', '>=', $yesterdaytime], ['created_time', '<', $todaytime]])->select()->toArray();
        if (!empty($pushLog)) {
            $data = [];
            foreach ($pushLog as &$val) {
                switch ($val['store_type']) {
                    case 1:
                        $val['store_type'] = '古斯缇';
                        break;
                    case 2:
                        $val['store_type'] = '南沙仓';
                        break;
                }
                switch ($val['status']) {
                    case 0:
                        $val['status'] = '暂不推送';
                        break;
                    case 1:
                        $val['status'] = '推送成功';
                        break;
                    case 2:
                        $val['status'] = '推送失败';
                        break;
                }
                $data[] = array(
                    'main_order_no' => $val['main_order_no'],
                    'period'        => $val['period'],
                    'title'         => $val['title'],
                    'short_code'    => $val['short_code'],
                    'store_type'    => $val['store_type'],
                    'status'        => $val['status'],
                    'error_msg'     => $val['error_msg'],
                    'created_time'  => date('Y-m-d H:i:s', $val['created_time']),
                );
            }
            $filename = "跨境自动推单" . date('Y-m-d', strtotime('-1 day')) . "日统计";
            $header   = array(
                array('column' => 'main_order_no', 'name' => '主订单号', 'width' => 30),
                array('column' => 'period', 'name' => '期数', 'width' => 15),
                array('column' => 'title', 'name' => '商品名称', 'width' => 30),
                array('column' => 'short_code', 'name' => '简码', 'width' => 15),
                array('column' => 'store_type', 'name' => '仓库', 'width' => 15),
                array('column' => 'status', 'name' => '推送状态', 'width' => 15),
                array('column' => 'created_time', 'name' => '推送时间', 'width' => 15),
                array('column' => 'error_msg', 'name' => '错误信息', 'width' => 40),
            );
            $uploadUrl = exportSheelExcel($data, $header, $filename);
            $file     = app()->getRootPath() . "public/storage/" . $uploadUrl;
            $media_id = weixinUpload($file, $filename.'.xlsx');
            unlink($file);
            $msgData = array(
                'content' => $media_id,
                'userid'  => 'f1715a108e9d32b92c416aa5bf0c281a',
                'msgtype' => 'file',
                'agentid' => 0,
            );
            httpPostString(env('ITEM.WECHART_URL') . '/wechat/v3/wecom/app/send', json_encode($msgData));
            $msgData = array(
                'content' => $media_id,
                'userid'  => 'CenQiSheng',
                'msgtype' => 'file',
                'agentid' => 0,
            );
            httpPostString(env('ITEM.WECHART_URL') . '/wechat/v3/wecom/app/send', json_encode($msgData));
            $msgData = array(
                'content' => $media_id,
                'userid'  => 'WangYunFei',
                'msgtype' => 'file',
                'agentid' => 0,
            );
            httpPostString(env('ITEM.WECHART_URL') . '/wechat/v3/wecom/app/send', json_encode($msgData));
            $msgData = array(
                'content' => $media_id,
                'userid'  => 'XiaJing',
                'msgtype' => 'file',
                'agentid' => 0,
            );
            httpPostString(env('ITEM.WECHART_URL') . '/wechat/v3/wecom/app/send', json_encode($msgData));
            $msgData = array(
                'content' => $media_id,
                'userid'  => 'WangDan',
                'msgtype' => 'file',
                'agentid' => 0,
            );
            httpPostString(env('ITEM.WECHART_URL') . '/wechat/v3/wecom/app/send', json_encode($msgData));
            $msgData = array(
                'content' => $media_id,
                'userid'  => 'b807e8256667436f24d47f948c741514',
                'msgtype' => 'file',
                'agentid' => 0,
            );
            httpPostString(env('ITEM.WECHART_URL') . '/wechat/v3/wecom/app/send', json_encode($msgData));
        }
        return true;
    }
}