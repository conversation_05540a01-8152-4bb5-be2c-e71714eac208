<?php


namespace app\controller;

use app\ErrorCode;
use app\service\RegionExpressFee as RegionExpressFeeService;
use app\BaseController;
use app\Request;
use think\facade\Validate;

class RegionExpressFee extends BaseController
{
    /**
     * Description:快递费用查询
     * Author: zrc
     * Date: 2023/8/9
     * Time: 13:27
     * @param Request $request
     * @return \think\response\Json
     */
    public function query(Request $request)
    {
        $params = $request->param();
        //数据验证
        $validate = Validate::rule([
            'delivery_place|发货地'         => 'require|number|in:1,2',
            'province_id|收货地省ID'         => 'require|number',
            'city_id|收货地市ID'             => 'require|number',
            'is_cold_chain|是否冰袋发货'       => 'require|in:0,1',
            'is_original_package|是否原箱发货' => 'require|in:0,1',
            'express_type|快递方式'          => 'require|in:1,2',
            'goods_info|商品信息'            => 'require',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        foreach ($params['goods_info'] as &$val) {
            if (!isset($val['short_code']) || empty($val['short_code'])) $this->throwError('请输入产品简码', ErrorCode::PARAM_ERROR);
            if (!isset($val['nums']) || empty($val['nums']) || !is_numeric($val['nums'])) $this->throwError('请填入正确的产品数量', ErrorCode::PARAM_ERROR);
        }
        if ($params['express_type'] == 2) {
            if (!isset($params['is_insured'])) $this->throwError('是否保价必传');
            if (!in_array($params['is_insured'], [0, 1])) $this->throwError('是否保价参数错误');
            if ($params['is_insured'] == 1 && empty($params['order_money'])) $this->throwError('订单金额必传');
        }
        if ($params['is_cold_chain'] == 1 && $params['is_original_package'] == 1) $this->throwError('原箱不支持冰袋发货');
        if ($params['express_type'] == 1 && $params['is_original_package'] == 1) $this->throwError('顺丰不支持原箱发货');
        $RegionExpressFeeService = new RegionExpressFeeService();
        $result                  = $RegionExpressFeeService->query($params);
        return $this->success($result);
    }
}