<?php

namespace app\service\es;

use Elasticsearch\ClientBuilder;

class Es
{

    const PERIODS         = 'periods'; //期数
    const PERIODS_PACKAGE = 'periods_set'; //期数套餐
    const PRODUCTS        = 'panshi.products'; //磐石产品
    const ORDERS          = 'orders'; //订单

    public static function getInstance()
    {
        $hosts = [
            [
                'host' => env('ES.HOST', '127.0.0.1'),
                'port' => env('ES.PORT', 9200),
                'user' => env('ES.USER', 'root'),
                'pass' => env('ES.PASS', 'vinehoo666')
            ]
        ];
        return ClientBuilder::create()->setHosts($hosts)->build();
    }

    /**
     * 设置ES索引
     */
    public static function name($index)
    {
        return (new Facade(self::getInstance()))->setIndex(env('ES.PREFIX') . $index);
    }

    /**
     * 兼容旧文档查询
     * @param array $param 是一个多维关联数组
     */
    public static function getDocumentList($param)
    {
        return (new Facade(self::getInstance()))->getDocumentList($param);
    }

}