<?php

namespace app\validate;

class SalesReturn extends BaseValidate
{
    protected $rule = [
//            "id|主键"=> "",
            "operator_id|中台用户"=> "require",
            "sub_order_no|单据编号"=> "require",
            "bill_date|单据日期"=> "require|date",
            "warehouse|仓库"=> "require",
            "warehouse_code|仓库编码"=> "require",
            "sale_bill_type|销售单据类型"=> "in:1,2,3",
            "corp|单据公司编码"=> "in:002,515,003,001,031,032",
            "detail_json|详细"=> "require|array",
            "order_amount|订单金额"=> "require",
        //    "return_courier_no|退货快递单号"=> "require",

    ];

    protected $message = [
        "operator_id.require"=>"请先登录"
    ];

}