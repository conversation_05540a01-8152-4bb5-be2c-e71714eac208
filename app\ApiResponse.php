<?php

namespace app;

use app\exception\BaseException;
use app\exception\BusinessException;

trait ApiResponse
{
    protected $errorCode;
    protected $msg;

    /**
     * Author: zrc
     * Date: 2021/11/10
     * Time: 14:02
     * @param $errorCode
     * @return $this
     */
    public function setErrorCode($errorCode)
    {
        $this->errorCode = $errorCode;
        return $this;
    }

    /**
     * Author: zrc
     * Date: 2021/11/10
     * Time: 14:02
     * @param $message
     * @return $this
     */
    public function setMessage($message)
    {
        $this->msg = $message;
        return $this;
    }

    /**
     * Author: zrc
     * Date: 2021/11/10
     * Time: 14:02
     * @param array $data
     * @param string $errorCode
     * @return \think\response\Json
     */
    public function status($data = [], $errorCode = 0)
    {
        $this->setErrorCode($errorCode);
        $result = [
            'error_code' => $this->errorCode,
            'error_msg' => $this->msg ?? '',
            'data' => empty($data) && is_array($data) ? (object)[] : $data
        ];
        return json($result);
    }

    /**
     * Author: zrc
     * Date: 2021/11/10
     * Time: 14:03
     * @param array $data
     * @param string $msg
     * @return \think\response\Json
     */
    public function success($data = [], $msg = '')
    {
        return $this->setMessage($msg)->status($data);
    }

    /**
     * Author: zrc
     * Date: 2021/11/10
     * Time: 14:03
     * @param string $msg
     * @param string $errorCode
     * @throws \Exception
     */
    public function throwError($msg = "", $errorCode = 10002,$data=[])
    {
        $return_data = [
            'error_code'=>$errorCode,
            'error_msg'=>$msg,
            'data'=>$data,
        ];
        throw new BaseException($return_data);
    }

}