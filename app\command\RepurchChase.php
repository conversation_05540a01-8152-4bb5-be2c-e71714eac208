<?php
declare (strict_types=1);

namespace app\command;

use app\service\RepurchaseStatistics;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;

class RepurchChase extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('repurchchase')
            ->addOption('period',null,Argument::OPTIONAL,'default')
            ->setDescription('复购短信执行');
    }

    protected function execute(Input $input, Output $output)
    {
        $option = $input->getOption('period')??"";
        switch ($option){
            case 'count'://统计
                RepurchaseStatistics::statisticsperiods();
                break;
            default://发送消息
                $res = RepurchaseStatistics::sendRepurchChasemsg();
                if ($res === true) {
                    $output->writeln('success');
                } else {
                    $output->writeln('ERROR: ' . $res);
                }
                break;
        }
    }
}
