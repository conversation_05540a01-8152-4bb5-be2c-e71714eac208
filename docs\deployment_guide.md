# 收款退款统计接口重构部署指南

## 重构概述

根据新的业务需求，对 `/orders/v3/payment-statistics/write` 接口进行了重构，主要变化如下：

### 主要变化

1. **收款逻辑变化**：
   - 从接收公司编码改为接收主订单号
   - 自动根据子订单期数查询收款商户ID
   - 支持一个主订单多个收款主体的情况
   - 按子订单金额比例分配收款金额

2. **退款逻辑变化**：
   - 接收退款单号和退款金额
   - 自动识别工单退款（GD开头）和普通退款
   - 自动查询对应的收款商户

3. **防重复机制**：
   - 新增日志表防止重复调用
   - 主订单号+收款商户ID+操作类型唯一约束

4. **向后兼容**：
   - 保持旧版本接口完全兼容
   - 根据参数自动识别调用方式

## 部署步骤

### 1. 数据库变更

执行以下SQL创建日志表：

```sql
-- 创建收款退款统计日志表
CREATE TABLE `vh_payment_statistics_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_no` varchar(36) NOT NULL COMMENT '订单号（主订单号或退款单号）',
  `merchant_id` int(10) NOT NULL COMMENT '收款商户ID',
  `operation_type` tinyint(1) NOT NULL COMMENT '操作类型：1-收款，2-退款',
  `amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '金额',
  `created_time` int(11) NOT NULL COMMENT '创建时间',
  `updated_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_merchant_type` (`order_no`,`merchant_id`,`operation_type`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收款退款统计日志表';
```

### 2. 代码部署

部署以下文件：
- `app/model/DailyPaymentStatistics.php` - 更新收款商户映射
- `app/model/PaymentStatisticsLog.php` - 新增日志模型
- `app/service/PaymentStatistics.php` - 重构服务类
- `app/controller/PaymentStatistics.php` - 更新控制器

### 3. 权限检查

确保应用有权限访问以下数据库和表：
- `vh_customer_service.vh_work_order` - 工单表
- `vh_orders.vh_refund_order` - 退款订单表
- ElasticSearch 的 `periods` 和 `orders` 索引

### 4. 配置验证

检查以下配置是否正确：
- `config/config.php` 中的 `order_type` 配置
- 数据库连接配置
- ElasticSearch 连接配置

## 测试验证

### 1. 功能测试

使用提供的测试脚本进行功能验证：
```bash
php test/payment_statistics_test.php
```

### 2. 接口测试

#### 新版本收款接口
```bash
curl -X POST http://your-domain.com/orders/v3/payment-statistics/write \
  -H "Content-Type: application/json" \
  -d '{"main_order_no":"VHM202412040001","amount":100.50}'
```

#### 新版本退款接口
```bash
curl -X POST http://your-domain.com/orders/v3/payment-statistics/write \
  -H "Content-Type: application/json" \
  -d '{"refund_no":"GD202412040001","amount":50.25}'
```

#### 旧版本接口（兼容性测试）
```bash
curl -X POST http://your-domain.com/orders/v3/payment-statistics/write \
  -H "Content-Type: application/json" \
  -d '{"company_code":"001","operation_type":1,"amount":200.00}'
```

### 3. 数据验证

检查以下数据是否正确：
- Redis缓存数据更新
- 日志表记录创建
- 防重复机制生效

## 监控和日志

### 1. 错误监控

关注以下错误日志：
- 主订单不存在
- 子订单查询失败
- ES查询失败
- 工单表查询失败

### 2. 业务监控

监控以下指标：
- 接口调用量
- 成功率
- 重复调用次数
- 各收款商户的数据分布

## 回滚方案

如果出现问题，可以通过以下方式回滚：

1. **代码回滚**：恢复原有的服务类和控制器文件
2. **数据回滚**：删除日志表（如果需要）
3. **配置回滚**：恢复原有的公司编码映射

## 注意事项

1. **数据一致性**：确保新旧接口的数据统计结果一致
2. **性能影响**：新接口涉及更多数据库查询，注意性能监控
3. **错误处理**：完善的错误处理和日志记录
4. **文档更新**：及时更新相关的API文档和使用说明

## 联系方式

如有问题，请联系开发团队。
