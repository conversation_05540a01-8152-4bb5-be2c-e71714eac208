<?php


namespace app\controller;

use app\service\Additional as AdditionalService;
use app\BaseController;
use app\ErrorCode;
use app\Request;
use think\facade\Log;
use think\facade\Validate;

class Additional extends BaseController
{
    /**
     * Description:获取订单相关文本
     * Author: zrc
     * Date: 2021/12/16
     * Time: 10:50
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function getOrderText(Request $request)
    {
        $params = $request->param();
        if (empty($params['type'])) $this->throwError('未获取到文本类型', ErrorCode::PARAM_ERROR);
        $additionalService = new AdditionalService();
        $result            = $additionalService->getOrderText($params);
        return $this->success($result);
    }

    /**
     * Description:普通商品冷链快递费计算
     * Author: zrc
     * Date: 2022/5/20
     * Time: 15:56
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function calcFreight(Request $request)
    {
        $params = $request->param();
        //数据验证
        $validate = Validate::rule([
            'city_id|市ID'  => 'require|number',
            'capacity|总容量' => 'require|number',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $result = calcFreight($params['city_id'], $params['capacity']);
        return $this->success($result);
    }

    /**
     * Description:物流同步推送队列
     * Author: zrc
     * Date: 2022/6/14
     * Time: 9:21
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function logisticSync(Request $request)
    {
        Log::write('logisticSync 进入');
        $params = $request->param();
        $file   = request()->file('file');
        //代发机器人回写物流excel
        if (!empty($file)) {
            Log::write('logisticSync 传入文件');
            $savename = \think\facade\Filesystem::putFile('exportExcel', $file);
            $path = app()->getRuntimePath() . 'storage/' . $savename;
            $data     = getExcelData($path, 1);
            $extension  = pathinfo($path, PATHINFO_EXTENSION);
            @unlink($path);
            if ($data['error_code'] != 0) $this->throwError('excel解析失败');
            if (count($data['data']) == 0) $this->throwError('未获取到excel内容');
            $excelData = $data['data'];
            $admin_id = 0;
        } else {
            Log::write('logisticSync 传入OSS文件');
            $admin_id = $request->header('vinehoo-uid');
            if (empty($admin_id)) $this->throwError('未获取到用户ID', ErrorCode::PARAM_ERROR);
            if (empty($params['file'])) $this->throwError('缺少上传内容', ErrorCode::PARAM_ERROR);
            //获取文件路径
            $path   = env('ALIURL') . $params['file'];
            $startI = 1;
            #下载文件
            $extension  = pathinfo($params['file'], PATHINFO_EXTENSION);
            $local_path = download_image($path, $extension);
            #解析文件
            $data = getExcelData($local_path, $startI);
            @unlink($local_path);
            if ($data['error_code'] != 0) $this->throwError('excel解析失败');
            if (count($data['data']) == 0) $this->throwError('未获取到excel内容');
            $excelData = $data['data'];
        }
        Log::write('logisticSync 解析出来的参数: ' . json_encode($excelData));
        $additionalService = new AdditionalService();
        $result            = $additionalService->logisticSync($excelData, $admin_id);
        return $this->success($result);
    }

    /**
     * Description:物流同步队列处理
     * Author: zrc
     * Date: 2022/6/14
     * Time: 12:02
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function logisticSyncDeal(Request $request)
    {
        $params = file_get_contents('php://input');
        if (empty($params)) {
            $this->throwError('未获取到队列数据', ErrorCode::PARAM_ERROR);
        }
        $additionalService = new AdditionalService();
        $result            = $additionalService->logisticSyncDeal($params);
        return $this->success($result);
    }

    /**
     * Description:获取后台物流同步回执信息
     * Author: zrc
     * Date: 2022/8/16
     * Time: 17:33
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function getLogisticSyncReceipt(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        //数据验证
        $validate = Validate::rule([
            'admin_id|后台用户ID' => 'require|number'
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $additionalService = new AdditionalService();
        $result            = $additionalService->getLogisticSyncReceipt($params);
        return $this->success($result);
    }

    /**
     * Description:子订单退还库存
     * Author: zrc
     * Date: 2022/8/1
     * Time: 10:34
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function subOrderReturnInventory(Request $request)
    {
        $params = $request->param();
        //数据验证
        $validate = Validate::rule([
            'sub_order_no|子订单号'  => 'require',
            'main_order_no|主订单号' => 'require',
            'package_id|套餐ID'    => 'require|number|>:0',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $additionalService = new AdditionalService();
        $result            = $additionalService->subOrderReturnInventory($params);
        if (empty($result)) $this->throwError('库存退还失败:未获取到退还的套餐数据');
        if (!isset($result['error_code']) || $result['error_code'] != 0) $this->throwError('库存退还失败:' . $result['error_msg']);
        return $this->success([]);
    }

    /**
     * Description:批量修改订单发票状态
     * Author: zrc
     * Date: 2022/7/15
     * Time: 17:12
     * @param Request $request
     */
    public function batchUpdateInvoice(Request $request)
    {
        $params = $request->param();
        //数据验证
        $validate = Validate::rule([
            'invoice_progress|开票进度' => 'require|in:0,1,2,3',
            'order_info|订单信息'       => 'require'
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $additionalService = new AdditionalService();
        $result            = $additionalService->batchUpdateInvoice($params);
        return $this->success($result);
    }

    /**
     * Description:期数已购推送队列数据
     * Author: zrc
     * Date: 2022/7/22
     * Time: 16:32
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function periodPurchasedPushQueueData(Request $request)
    {
        $params = $request->param();
        //数据验证
        $validate = Validate::rule([
            'periods|期数' => 'require'
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $additionalService = new AdditionalService();
        $result            = $additionalService->periodPurchasedPushQueueData($params);
        return $this->success($result);
    }

    /**
     * Description:期数已购统计
     * Author: zrc
     * Date: 2022/7/22
     * Time: 15:02
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function periodPurchasedStatistics(Request $request)
    {
        $params = $request->param();
        //数据验证
        $validate = Validate::rule([
            'periods|期数' => 'require'
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $additionalService = new AdditionalService();
        $result            = $additionalService->periodPurchasedStatistics($params);
        return $this->success($result);
    }

    /**
     * Description:查询订单银联支付情况
     * Author: zrc
     * Date: 2022/7/27
     * Time: 13:28
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function inquireOrderPay(Request $request)
    {
        $params = $request->param();
        //数据验证
        $validate = Validate::rule([
            'main_order_no|主订单号' => 'require'
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $additionalService = new AdditionalService();
        $result            = $additionalService->inquireOrderPay($params);
        return $this->success($result);
    }

    /**
     * Description:获取换绑仓库订单列表
     * Author: zrc
     * Date: 2022/8/10
     * Time: 14:08
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function getExchangeOrderList(Request $request)
    {
        $params   = $request->param();
        $validate = Validate::rule([
            'period|期数'  => 'require|number|>:0',
            'page|页码'    => 'require|number|>:0',
            'limit|每页条数' => 'require|number|>:0'
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $additionalService = new AdditionalService();
        $result            = $additionalService->getExchangeOrderList($params);
        return $this->success($result);
    }

    /**
     * Description:换绑订单仓库
     * Author: zrc
     * Date: 2022/8/10
     * Time: 16:29
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function changeOrderWarehouse(Request $request)
    {
        $params             = $request->param();
        $params['operator'] = $request->header('vinehoo-uid');
        $validate           = Validate::rule([
            'operator|操作人'        => 'require',
            'sub_order_no|子订单号'   => 'require',
            'warehouse_code|仓库编码' => 'require',
            'order_type|订单类型'     => 'require|in:0,1,2,3,4',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $additionalService = new AdditionalService();
        $result            = $additionalService->changeOrderWarehouse($params);
        return $this->success($result);
    }

    /**
     * Description:工单系统获取订单信息
     * Author: zrc
     * Date: 2022/8/15
     * Time: 9:14
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function workGetOrderInfo(Request $request)
    {
        $params   = $request->param();
        $validate = Validate::rule([
            'sub_order_no|子订单号' => 'require',
            'order_type|订单类型'   => 'require|in:0,1,2,3,4,9,11',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $additionalService = new AdditionalService();
        if ($params['order_type'] == 11) {
            $result = $additionalService->workGetOrderInfoAuction($params);
        } else {
            $result = $additionalService->workGetOrderInfo($params);
        }
        return $this->success($result);
    }

    /**
     * Description:期数下架未付尾款的订金订单批量退款
     * Author: zrc
     * Date: 2023/6/25
     * Time: 16:18
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function depositRefund(Request $request)
    {
        $params   = $request->param();
        $validate = Validate::rule([
            'period|期数'        => 'require|number|>:0',
            'period_type|期数类型' => 'require|in:in:0',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $additionalService = new AdditionalService();
        $result            = $additionalService->depositRefund($params);
        return $this->success($result);
    }

    /**
     * Description:订金订单批量退款队列回调处理
     * Author: zrc
     * Date: 2023/6/25
     * Time: 17:14
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function depositRefundDeal(Request $request)
    {
        $params = file_get_contents('php://input');
        if (empty($params)) {
            $this->throwError('未获取到队列数据', ErrorCode::PARAM_ERROR);
        }
        $additionalService = new AdditionalService();
        $result            = $additionalService->depositRefundDeal($params);
        return $this->success($result);
    }

    /**
     * Description:获取订单开票信息
     * Author: zrc
     * Date: 2022/12/27
     * Time: 11:03
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function getInvoiceInfo(Request $request)
    {
        $params = $request->param();
        if (empty($params['items_info'])) $this->throwError('未获取到商品信息', ErrorCode::PARAM_ERROR);
        foreach ($params['items_info'] as &$val) {
            $validate = Validate::rule([
                'period|期数'        => 'require|number|>:0',
                'package_id|套餐ID'  => 'require|number|>:0',
                'goods_money|商品总价' => 'require',
            ]);
            if (!$validate->check($val)) {
                $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
            }
        }
        $additionalService = new AdditionalService();
        $result            = $additionalService->getInvoiceInfo($params);
        return $this->success($result);
    }

    /**
     * Description:导出前天11:00点到今天11:00点代发订单
     * Author: zrc
     * Date: 2023/10/10
     * Time: 14:10
     * @param Request $request
     * @return \think\response\Json
     */
    public function supplierDeliveryOrderExport(Request $request)
    {
        $params            = $request->param();
        $additionalService = new AdditionalService();
        $result            = $additionalService->supplierDeliveryOrderExport($params);
        return $this->success($result);
    }
    public function supplierDeliveryOrderExportTsh(Request $request)
    {
        $params            = $request->param();
        $additionalService = new AdditionalService();
        $result            = $additionalService->supplierDeliveryOrderExportTsh($params);
        return $this->success($result);
    }
}