<?php


namespace app\controller;


use app\BaseController;
use app\ErrorCode;
use app\Request;
use app\service\Tail as TailService;
use app\validate\ListPagination;
use app\validate\TailCreateOrder;

class Tail extends BaseController
{

    /**
     * Description:尾货订单修改
     * Author: zrc
     * Date: 2021/8/2
     * Time: 14:47
     * @param Request $request
     */
    public function update(Request $request)
    {
        $params             = $request->param();
        $params['operator'] = $request->header('vinehoo-uid');
        if (empty($params['sub_order_no'])) {
            $this->throwError('请传入子订单号', ErrorCode::PARAM_ERROR);
        }
        if (!isset($params['operator'])) {
            $this->throwError('请传入操作人ID', ErrorCode::PARAM_ERROR);
        }
        $tailService = new TailService();
        $result      = $tailService->updateOrder($params);
        return $this->success($result);
    }

    /**
     * Description:尾货订单列表
     * Author: zrc
     * Date: 2021/8/2
     * Time: 18:25
     * @param Request $request
     */
    public function orderList(Request $request)
    {
        $params   = $request->param();
        $validate = new ListPagination();
        if (!$validate->goCheck($params)) {//验证参数
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $tailService = new TailService();
        $result      = $tailService->orderList($params);
        return $this->success($result);
    }

    /**
     * Description:尾货订单详情
     * Author: zrc
     * Date: 2021/8/3
     * Time: 9:54
     * @param Request $request
     */
    public function orderDetail(Request $request)
    {
        $params = $request->param();
        if (empty($params['sub_order_no'])) {
            $this->throwError('请传入子订单号', ErrorCode::PARAM_ERROR);
        }
        $tailService = new TailService();
        $result      = $tailService->orderDetail($params);
        return $this->success($result);
    }
}