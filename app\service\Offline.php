<?php


namespace app\service;


use app\BaseService;
use app\ErrorCode;
use app\service\DingTalk as DingTalkService;
use app\service\Order as OrderService;
use app\service\Push as PushService;
use app\service\WeChat as WeChatService;
use think\facade\Db;
use think\facade\Log;

class Offline extends BaseService
{
    /**
     * Description:线下样酒申请
     * Author: zrc
     * Date: 2022/5/11
     * Time: 14:52
     * @param $requestparams
     * @return bool
     * @throws \think\db\exception\DbException
     */
    public function createSaleOrder($requestparams)
    {
        $params = $requestparams;
        //订单号重复查询
        $isset  = Db::name('offline_order')->where(['sub_order_no' => $params['order_no']])->count();
        $issets = Db::name('tripartite_order')->where(['sub_order_no' => $params['order_no']])->count();
        if ($isset > 0 || $issets > 0) $this->throwError('订单号重复');
        //用户信息加密处理
        $consignee = '';
        $phone     = '';
        if (!empty($params['consignee'])) {
            $consignee = trim($params['consignee']);
            $encrypt   = cryptionDeal(1, [$consignee], $params['admin_id'], '后端用户');
            $consignee = isset($encrypt[$consignee]) ? $encrypt[$consignee] : '';
        }
        if (!empty($params['consignee_phone'])) {
            $phone   = trim($params['consignee_phone']);
            $encrypt = cryptionDeal(1, [$phone], $params['admin_id'], '后端用户');
            $phone   = isset($encrypt[$phone]) ? $encrypt[$phone] : '';
        }
        //订单金额处理+子订单订单商品信息处理
        $payment_amount = 0;
        $items_info     = '';
        foreach ($params['items_info'] as $val) {
            $payment_amount += $val['nums'] * $val['price'];
            if (empty($items_info)) {
                $items_info = $val['bar_code'] . '*' . $val['short_code'] . '*' . $val['nums'] . '*' . $val['price'] . '*' . $val['is_gift'];
            } else {
                $items_info = $items_info . ',' . $val['bar_code'] . '*' . $val['short_code'] . '*' . $val['nums'] . '*' . $val['price'] . '*' . $val['is_gift'];
            }
        }
        //收货地址换行处理
        $params['address'] = str_replace(array("\r\n", "\r", "\n"), "", $params['address']);
        //运费方式对应快递方式处理
        $express_type     = 4;//默认京东不保价
        $delivery_express = config('config')['delivery_express'];
        if (isset($params['delivery_mode'])) {
            foreach ($delivery_express as &$val) {
                if ($val['delivery_mode'] == $params['delivery_mode']) $express_type = $val['express_type'];
            }
        }
        Db::startTrans();
        try {
            //主订单写入
            $mainData      = array(
                'uid'               => 0,
                'main_order_no'     => $params['order_no'],
                'main_order_status' => 3,
                'payment_amount'    => $payment_amount,
                'address'           => $params['address'],
                'consignee'         => $consignee,
                'consignee_phone'   => $phone,
                'created_time'      => time(),
                'order_type'        => 8,
                'operator'          => $params['admin_id'],
            );
            $main_order_id = Db::name('order_main')->insertGetId($mainData);
            if (empty($main_order_id)) $this->throwError('写入主订单失败');
            //子订单写入
            $subData     = array(
                'sub_order_no'         => $params['order_no'],
                'sub_order_status'     => 1,//默认已支付
                'main_order_id'        => $main_order_id,
                'items_info'           => $items_info,
                'order_qty'            => 1,
                'payment_amount'       => $payment_amount,
                'express_type'         => $express_type,
                'express_number'       => isset($params['express_number']) ? $params['express_number'] : '',
                'order_type'           => 8,
                'operator'             => $params['admin_id'],
                'voucher_date'         => strtotime($params['voucher_date']),
                'customer'             => $params['customer'],
                'customer_code'        => $params['customer_code'],
                'settle_customer'      => $params['settle_customer'],
                'settle_customer_code' => $params['settle_customer_code'],
                'department'           => isset($params['department']) ? $params['department'] : '',
                'department_code'      => isset($params['department_code']) ? $params['department_code'] : '',
                'clerk'                => isset($params['clerk']) ? $params['clerk'] : '',
                'clerk_code'           => isset($params['clerk_code']) ? $params['clerk_code'] : '',
                'warehouse'            => $params['warehouse'],
                'warehouse_code'       => $params['warehouse_code'],
                'delivery_mode'        => isset($params['delivery_mode']) ? $params['delivery_mode'] : '',
                'delivery_mode_code'   => isset($params['delivery_mode_code']) ? $params['delivery_mode_code'] : '',
                'business_type'        => '普通销售单',
                'business_type_code'   => '15',
                'memo'                 => isset($params['memo']) ? $params['memo'] : '',
                'dingtalk_status'      => 1,
                'created_time'         => time(),
                'document_type'        => 0,
            );
            $addSubOrder = Db::name('offline_order')->insert($subData);
            if (empty($addSubOrder)) $this->throwError('写入子订单失败');
            //发起钉钉审批
            $dingTalkService = new DingTalkService();
            $dingTalkService->offlineCreateDingtalkVerify($params);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->throwError($e->getMessage());
        }
        return true;
    }

    /**
     * Description:样酒销售单列表
     * Author: zrc
     * Date: 2022/5/11
     * Time: 16:46
     * @param $requestparams
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function saleOrderList($requestparams)
    {
        $page    = !empty($requestparams['page']) ? $requestparams['page'] : 1;
        $limit   = !empty($requestparams['limit']) ? $requestparams['limit'] : 10;
        $params  = $requestparams;
        $offset  = ($page - 1) * $limit;
        $where   = [];
        $where[] = ["oo.is_delete", "=", 0];
        $where[] = ["oo.document_type", "=", 0];
        $where[] = ['oo.corp', '=', '002'];
        if (!empty($params['sub_order_no'])) {
            $where[] = ["oo.sub_order_no", "=", $params['sub_order_no']];
        }
        if (!empty($params['voucher_date_start'])) {
            $where[] = ['oo.voucher_date', '>', strtotime($params['voucher_date_start'])];
        }
        if (!empty($params['voucher_date_end'])) {
            $where[] = ['oo.voucher_date', '<', strtotime($params['voucher_date_end'])];
        }
        if (!empty($params['settle_customer'])) {
            $where[] = ['oo.settle_customer', 'like', "%{$params['settle_customer']}%"];
        }
        if (!empty($params['operator'])) {
            $adminInfo = $this->httpGet(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/conditionQueryList', ['limit' => 1, 'realname' => $params['operator'], 'field' => 'id']);
            if (isset($adminInfo['data']['list'][0]['id'])) {
                $where[] = ['oo.operator', '=', $adminInfo['data']['list'][0]['id']];
            }
        }
        if (!empty($params['warehouse'])) {
            $where[] = ['oo.warehouse', '=', $params['warehouse']];
        }
        if (!empty($params['consignee'])) {
            $encrypt             = cryptionDeal(1, [$params['consignee']], '15736175219', '宗仁川');
            $params['consignee'] = isset($encrypt[$params['consignee']]) ? $encrypt[$params['consignee']] : '';
            $where[]             = ['om.consignee', '=', $params['consignee']];
        }
        if (!empty($params['consignee_phone'])) {
            $encrypt                   = cryptionDeal(1, [$params['consignee_phone']], '15736175219', '宗仁川');
            $params['consignee_phone'] = isset($encrypt[$params['consignee_phone']]) ? $encrypt[$params['consignee_phone']] : '';
            $where[]                   = ['om.consignee_phone', '=', $params['consignee_phone']];
        }
        if (isset($params['push_t_status']) && is_numeric($params['push_t_status'])) {
            $where[] = ['oo.push_t_status', '=', $params['push_t_status']];
        }
        $totalNum     = Db::name('offline_order')->alias('oo')->leftJoin('order_main om', 'om.id=oo.main_order_id')->where($where)->count();
        $lists        = Db::name('offline_order')
            ->alias('oo')
            ->field('oo.*,om.consignee,om.consignee_phone,om.address')
            ->leftJoin('order_main om', 'om.id=oo.main_order_id')
            ->where($where)
            ->limit($offset, $limit)
            ->order('oo.id desc')
            ->select()->toArray();
        $admin_id_arr = array_unique(array_column($lists, 'operator'));
        $admin_id_str = implode(',', $admin_id_arr);
        $adminInfo    = $this->httpGet(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info', ['admin_id' => $admin_id_str, 'field' => 'realname']);
        if (count($lists) > 0) {
            foreach ($lists as $key => $val) {
                $lists[$key]['voucher_date'] = date('Y-m-d H:i:s', $val['voucher_date']);
                if (!empty($val['consignee'])) {
                    //用户信息加密处理
                    $consignee = $val['consignee'];
                    $encrypt   = cryptionDeal(2, [$consignee], '15736175219', '宗仁川');
                    $consignee = isset($encrypt[$consignee]) ? $encrypt[$consignee] : '';
                    //$val['consignee_ecrypt'] = hidestr($consignee, 1);
                    $lists[$key]['consignee_ecrypt'] = $consignee;
                }
                if (!empty($val['consignee_phone'])) {
                    //用户信息加密处理
                    $phone   = $val['consignee_phone'];
                    $encrypt = cryptionDeal(2, [$phone], '15736175219', '宗仁川');
                    $phone   = isset($encrypt[$phone]) ? $encrypt[$phone] : '';
                    //$val['consignee_phone_ecrypt'] = hidestr($phone, 3, 4);
                    $lists[$key]['consignee_phone_ecrypt'] = $phone;
                }
                $lists[$key]['operator'] = isset($adminInfo['data'][$val['operator']]) ? $adminInfo['data'][$val['operator']] : '';
                //明细处理
                $items_info = [];
                $goodsInfo  = explode(',', $val['items_info']);
                foreach ($goodsInfo as $k => $v) {
                    $goods                        = explode('*', $v);
                    $items_info[$k]['bar_code']   = $goods[0];
                    $items_info[$k]['short_code'] = $goods[1];
                    $items_info[$k]['nums']       = intval($goods[2]);
                    $items_info[$k]['price']      = $goods[3];
                    $items_info[$k]['is_gift']    = intval($goods[4]);
                }
                $lists[$key]['items_info'] = $items_info;
            }
        }
        $result['list']  = $lists;
        $result['total'] = $totalNum;
        return $result;
    }

    /**
     * Description:编辑推送失败的样酒销售单
     * Author: zrc
     * Date: 2022/5/12
     * Time: 9:57
     * @param $requestparams
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updateSaleOrder($requestparams)
    {
        $params       = $requestparams;
        $offlineOrder = Db::name('offline_order')->field('id,main_order_id,sub_order_no,push_t_status,dingtalk_status')->where(['id' => $params['id']])->find();
        if (empty($offlineOrder)) $this->throwError('未获取到订单信息');
        if ($offlineOrder['dingtalk_status'] == 1) $this->throwError('样酒审批审核中,不允许编辑');
        if ($offlineOrder['dingtalk_status'] == 2) $this->throwError('样酒审批已通过,不允许编辑');
        //用户信息加密处理
        if (!empty($params['consignee'])) {
            $consignee = trim($params['consignee']);
            $encrypt   = cryptionDeal(1, [$consignee], $params['operator'], '后端用户');
            $consignee = isset($encrypt[$consignee]) ? $encrypt[$consignee] : '';
        }
        if (!empty($params['consignee_phone'])) {
            $phone   = trim($params['consignee_phone']);
            $encrypt = cryptionDeal(1, [$phone], $params['operator'], '后端用户');
            $phone   = isset($encrypt[$phone]) ? $encrypt[$phone] : '';
        }
        //订单金额处理+子订单订单商品信息处理
        $payment_amount = 0;
        $items_info     = '';
        foreach ($params['items_info'] as $val) {
            $payment_amount += $val['nums'] * $val['price'];
            if (empty($items_info)) {
                $items_info = $val['bar_code'] . '*' . $val['short_code'] . '*' . $val['nums'] . '*' . $val['price'] . '*' . $val['is_gift'];
            } else {
                $items_info = $items_info . ',' . $val['bar_code'] . '*' . $val['short_code'] . '*' . $val['nums'] . '*' . $val['price'] . '*' . $val['is_gift'];
            }
        }
        //收货地址换行处理
        $params['address'] = str_replace(array("\r\n", "\r", "\n"), "", $params['address']);
        //运费方式对应快递方式处理
        $express_type     = 4;//默认京东不保价
        $delivery_express = config('config')['delivery_express'];
        if (isset($params['delivery_mode'])) {
            foreach ($delivery_express as &$val) {
                if ($val['delivery_mode'] == $params['delivery_mode']) $express_type = $val['express_type'];
            }
        }
        Db::startTrans();
        try {
            if ($params['order_no'] != $offlineOrder['sub_order_no']) {//单据编号变更处理(删除原订单，创建新订单)
                //订单号重复查询
                $isset  = Db::name('offline_order')->where(['sub_order_no' => $params['order_no']])->count();
                $issets = Db::name('tripartite_order')->where(['sub_order_no' => $params['order_no']])->count();
                if ($isset > 0 || $issets > 0) $this->throwError('订单号重复');
                $deleteOldOrder = Db::name('offline_order')->where(['id' => $params['id']])->update(['sub_order_status' => 4, 'is_delete' => 1, 'update_time' => time()]);
                if (empty($deleteOldOrder)) $this->throwError('变更单据编号失败');
                //主订单写入
                $mainData      = array(
                    'uid'               => 0,
                    'main_order_no'     => $params['order_no'],
                    'main_order_status' => 3,
                    'payment_amount'    => $payment_amount,
                    'address'           => $params['address'],
                    'consignee'         => $consignee,
                    'consignee_phone'   => $phone,
                    'created_time'      => time(),
                    'order_type'        => 8,
                    'operator'          => $params['admin_id'],
                );
                $main_order_id = Db::name('order_main')->insertGetId($mainData);
                if (empty($main_order_id)) $this->throwError('写入主订单失败');
                //子订单写入
                $subData     = array(
                    'sub_order_no'         => $params['order_no'],
                    'sub_order_status'     => 1,
                    'main_order_id'        => $main_order_id,
                    'items_info'           => $items_info,
                    'order_qty'            => 1,
                    'payment_amount'       => $payment_amount,
                    'express_type'         => $express_type,
                    'express_number'       => isset($params['express_number']) ? $params['express_number'] : '',
                    'order_type'           => 8,
                    'operator'             => $params['admin_id'],
                    'voucher_date'         => strtotime($params['voucher_date']),
                    'customer'             => $params['customer'],
                    'customer_code'        => $params['customer_code'],
                    'settle_customer'      => $params['settle_customer'],
                    'settle_customer_code' => $params['settle_customer_code'],
                    'department'           => isset($params['department']) ? $params['department'] : '',
                    'department_code'      => isset($params['department_code']) ? $params['department_code'] : '',
                    'clerk'                => isset($params['clerk']) ? $params['clerk'] : '',
                    'clerk_code'           => isset($params['clerk_code']) ? $params['clerk_code'] : '',
                    'warehouse'            => $params['warehouse'],
                    'warehouse_code'       => $params['warehouse_code'],
                    'delivery_mode'        => isset($params['delivery_mode']) ? $params['delivery_mode'] : '',
                    'delivery_mode_code'   => isset($params['delivery_mode_code']) ? $params['delivery_mode_code'] : '',
                    'business_type'        => '普通销售单',
                    'business_type_code'   => '15',
                    'memo'                 => isset($params['memo']) ? $params['memo'] : '',
                    'created_time'         => time(),
                );
                $addSubOrder = Db::name('offline_order')->insert($subData);
                if (empty($addSubOrder)) $this->throwError('写入子订单失败');
                //查询有无订单备注，有迁移到新订单
                $remarks = Db::name('order_remarks')->where(['sub_order_no' => $offlineOrder['sub_order_no']])->count();
                if ($remarks > 0) {
                    Db::name('order_remarks')->where(['sub_order_no' => $offlineOrder['sub_order_no']])->update(['sub_order_no' => $params['order_no']]);
                }
            } else {//修改订单数据
                //主订单修改
                $mainData        = array(
                    'payment_amount'  => $payment_amount,
                    'address'         => $params['address'],
                    'consignee'       => $consignee,
                    'consignee_phone' => $phone,
                    'operator'        => $params['admin_id'],
                    'update_time'     => time()
                );
                $updateMainOrder = Db::name('order_main')->where(['id' => $offlineOrder['main_order_id']])->update($mainData);
                if (empty($updateMainOrder)) $this->throwError('修改主订单失败');
                //子订单写入
                $subData        = array(
                    'items_info'           => $items_info,
                    'payment_amount'       => $payment_amount,
                    'express_number'       => $params['express_number'],
                    'operator'             => $params['admin_id'],
                    'voucher_date'         => strtotime($params['voucher_date']),
                    'customer'             => $params['customer'],
                    'customer_code'        => $params['customer_code'],
                    'settle_customer'      => $params['settle_customer'],
                    'settle_customer_code' => $params['settle_customer_code'],
                    'department'           => $params['department'],
                    'department_code'      => $params['department_code'],
                    'clerk'                => $params['clerk'],
                    'clerk_code'           => $params['clerk_code'],
                    'warehouse'            => $params['warehouse'],
                    'warehouse_code'       => $params['warehouse_code'],
                    'delivery_mode'        => $params['delivery_mode'],
                    'delivery_mode_code'   => $params['delivery_mode_code'],
                    'memo'                 => $params['memo'],
                    'update_time'          => time(),
                    'dingtalk_status'      => 1
                );
                $updateSubOrder = Db::name('offline_order')->where(['id' => $params['id']])->update($subData);
                if (empty($updateSubOrder)) $this->throwError('修改子订单失败');
            }
            //发起钉钉审批
            $dingTalkService = new DingTalkService();
            $dingTalkService->offlineCreateDingtalkVerify($params);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->throwError($e->getMessage());
        }
        return true;
    }

    /**
     * Description:新增普通销售单
     * Author: zrc
     * Date: 2022/10/8
     * Time: 10:09
     * @param $params
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function newAddSaleOrder($params)
    {
        //订单号重复查询
        $isset  = Db::name('offline_order')->where(['sub_order_no' => $params['order_no']])->count();
        $issets = Db::name('tripartite_order')->where(['sub_order_no' => $params['order_no']])->count();
        if ($isset > 0 || $issets > 0) $this->throwError('订单号重复');
        if(!empty($params['related_order_no'])){
            if(!empty(Db::name('offline_order')->where(['sub_order_no' => $params['related_order_no']])->value('related_order_no'))){
                $this->throwError('形式单不能关联形式单');
            }
        }
        //用户信息加密处理
        $consignee = '';
        $phone     = '';
        if (!empty($params['consignee'])) {
            $consignee = trim($params['consignee']);
            $encrypt   = cryptionDeal(1, [$consignee], $params['admin_id'], '后端用户');
            $consignee = isset($encrypt[$consignee]) ? $encrypt[$consignee] : '';
        }
        if (!empty($params['consignee_phone'])) {
            $phone   = trim($params['consignee_phone']);
            $encrypt = cryptionDeal(1, [$phone], $params['admin_id'], '后端用户');
            $phone   = isset($encrypt[$phone]) ? $encrypt[$phone] : '';
        }
        //订单金额处理+子订单订单商品信息处理
        $payment_amount = 0;
        $items_info     = '';
        $is_have_gift   = '无赠品';//默认无赠品
        foreach ($params['items_info'] as $val) {
            if ($val['is_gift'] == 1) $is_have_gift = '有赠品';
            $payment_amount += $val['total_price'];
            if (empty($items_info)) {
                $items_info = $val['bar_code'] . '*' . $val['short_code'] . '*' . $val['nums'] . '*' . $val['price'] . '*' . $val['is_gift'] . '*' . $val['agreementPrice'] . '*' . $val['product_name'] . '*' . $val['year'] . '*' . $val['unit'] . '*' . $val['wmsStockNumber'] . '*' . $val['number'] . '*' . $val['Specification'] . '*' . $val['total_price'] . '*' . $val['priceSource'] . '*' . $val['en_product_name'];
            } else {
                $items_info = $items_info . ',' . $val['bar_code'] . '*' . $val['short_code'] . '*' . $val['nums'] . '*' . $val['price'] . '*' . $val['is_gift'] . '*' . $val['agreementPrice'] . '*' . $val['product_name'] . '*' . $val['year'] . '*' . $val['unit'] . '*' . $val['wmsStockNumber'] . '*' . $val['number'] . '*' . $val['Specification'] . '*' . $val['total_price'] . '*' . $val['priceSource'] . '*' . $val['en_product_name'];
            }
        }
        //收货地址换行处理
        $params['address'] = str_replace(array("\r\n", "\r", "\n"), "", $params['address']);
        //运费方式对应快递方式处理
        $express_type     = 4;//默认京东不保价
        $delivery_express = config('config')['delivery_express'];
        if (isset($params['delivery_mode'])) {
            foreach ($delivery_express as &$val) {
                if ($val['delivery_mode'] == $params['delivery_mode']) $express_type = $val['express_type'];
            }
        }
        Db::startTrans();
        try {
            //主订单写入
            $count = Db::name('order_main')->where(['main_order_no' => $params['order_no']])->count();
            if ($count > 0) $params['order_no'] = $params['order_no'] . '-' . rand(1, 9999);
            $mainData      = array(
                'uid'               => 0,
                'main_order_no'     => $params['order_no'],
                'main_order_status' => 3,
                'payment_amount'    => $payment_amount,
                'address'           => $params['address'],
                'consignee'         => $consignee,
                'consignee_phone'   => $phone,
                'created_time'      => time(),
                'order_type'        => 8,
                'operator'          => $params['admin_id'],
            );
            $regions = [];
            if (isset($params['province_id'])) {
                $mainData['province_id'] = $params['province_id'];
                $regions['province_id'] = $params['province_id'];
            }
            if (isset($params['city_id'])) {
                $mainData['city_id'] = $params['city_id'];
                $regions['city_id'] = $params['city_id'];
            }
            if (isset($params['district_id'])) {
                $mainData['district_id'] = $params['district_id'];
                $regions['district_id'] = $params['district_id'];
            }
            $regions_name = [];
            if(!empty($regions)){
                $regions_name = Db::table('vh_user.vh_regional')->where('id','in',array_values($regions))->column('name', 'id' );
            }
            $params['province_name'] = $regions_name[$params['province_id']] ?? '';
            $params['city_name'] = $regions_name[$params['city_id']] ?? '';
            $params['district_name'] = $regions_name[$params['district_id']] ?? '';

            $main_order_id = Db::name('order_main')->insertGetId($mainData);
            if (empty($main_order_id)) $this->throwError('写入主订单失败');
            //溯源码更新
            $product_total_nums = array_sum(array_column($params['items_info'], 'nums'));
            if ($product_total_nums >= 6 && in_array($params['warehouse_code'], ['336', '262'])) {
                //新增
                $addressAiMatch = \Curl::addressAiMatch(['address' => $params['address']]);
                $province_id    = $addressAiMatch['province_id'];
                $customer_code  = str_replace('VH', '', $params['customer_code']);  // 客户代码
                $encoded_str    = (new \SealedSource())->encode_with_fixed_length($province_id, $customer_code, $main_order_id);
            } else {
                //新增无需删除
                (new \SealedSource())->decode_with_delete(['main_order_id' => $main_order_id]);
            }
            //子订单写入
            $subData = array(
                'return_warehouse'        => $params['return_warehouse'] ?? '',
                'sub_order_no'            => $params['order_no'],
                'sub_order_status'        => 1,//默认已支付
                'main_order_id'           => $main_order_id,
                'items_info'              => $items_info,
                'material_info'           => json_encode($params['material_info'], JSON_UNESCAPED_UNICODE),
                'order_qty'               => 1,
                'payment_amount'          => $payment_amount,
                'express_type'            => $express_type,
                'express_number'          => isset($params['express_number']) ? $params['express_number'] : '',
                'order_type'              => 8,
                'operator'                => $params['admin_id'],
                'voucher_date'            => strtotime($params['voucher_date']),
                'customer'                => $params['customer'],
                'customer_code'           => $params['customer_code'],
                'customer_abbreviation'   => isset($params['customer_abbreviation']) ? $params['customer_abbreviation'] : '',
                'settle_customer'         => $params['settle_customer'],
                'settle_customer_code'    => $params['settle_customer_code'],
                'department'              => isset($params['department']) ? $params['department'] : '',
                'department_code'         => isset($params['department_code']) ? $params['department_code'] : '',
                'clerk'                   => isset($params['clerk']) ? $params['clerk'] : '',
                'clerk_code'              => isset($params['clerk_code']) ? $params['clerk_code'] : '',
                'warehouse'               => $params['warehouse'],
                'warehouse_code'          => $params['warehouse_code'],
                'delivery_mode'           => isset($params['delivery_mode']) ? $params['delivery_mode'] : '',
                'delivery_mode_code'      => isset($params['delivery_mode_code']) ? $params['delivery_mode_code'] : '',
                'business_type'           => '普通销售单',
                'business_type_code'      => '15',
                'memo'                    => isset($params['memo']) ? $params['memo'] : '',
                'dingtalk_status'         => $params['type'] == 1 ? 0 : 1,
                'created_time'            => time(),
                'document_type'           => 1,
                'express_pay_method'      => isset($params['express_pay_method']) ? $params['express_pay_method'] : '',
                'express_pay_method_code' => isset($params['express_pay_method_code']) ? $params['express_pay_method_code'] : '',
                'settlement_method'       => isset($params['settlement_method']) ? $params['settlement_method'] : '',
                'settlement_method_code'  => isset($params['settlement_method_code']) ? $params['settlement_method_code'] : '',
                'corp'                    => $params['corp'],
                'media_url'               => isset($params['media_url']) ? $params['media_url'] : '',
                'priceSource'             => $params['priceSource'],
                'is_ts'                   => $params['is_ts'],
                'bank_account_name'       => $params['bank_account_name'] ?? null,
                'account_no'              => $params['account_no'] ?? null,
                'settlement_month_type'              => $params['settlement_month_type'] ?? null,
                'settlement_day_type'              => $params['settlement_day_type'] ?? null,
                'settlement_days'              => $params['settlement_days'] ?? 0,
                'related_order_no'              => $params['related_order_no'] ?? '',
            );
            //科技样酒销售单处理
            if ($params['is_sample_liquor'] == 1) {
                $subData['document_type']   = 0;
                $subData['collection_type'] = $params['collection_type'];
            }
            //003木兰朵不推erp
            if ($params['corp'] == '003') {
                $subData['push_t_status'] = 3;
            }
            //不推送萌牙处理
            if ($params['is_push_wms'] == 0) {
                $subData['push_wms_status'] = 3;
                //添加订单备注
                $remarks      = array(
                    'sub_order_no' => $params['order_no'],
                    'order_type'   => 8,
                    'content'      => '新增销售单设置不推送萌牙',
                    'admin_id'     => $params['admin_id']
                );
                $orderService = new OrderService();
                $orderService->createRemarks($remarks);
            }
            $addSubOrder            = Db::name('offline_order')->insert($subData);
            $params['is_have_gift'] = $is_have_gift;
            //附件处理
            if (isset($params['media_url']) && !empty($params['media_url'])) {
                \Curl::timingAdd([
                    'namespace' => 'orders', //命名空间。可根据项目自定义名称。
                    'key'       => $params['order_no'], //唯一键名，在同一个命名空间下，如果有相同键名存在会覆盖已有数据。
                    'data'      => base64_encode(json_encode($params)), //超时触发时，提交的数据。建议使用Base64编码（注：回调接口传递的数据会自动解码）。
                    'callback'  => env('ITEM.ORDERS_URL') . '/orders/v3/offline/timeOutSaleOrder', //超时触发时，调用的接口回调地址。
                    'timeout'   => '1s', //超时时间。支持s,m,h单位进行描述。比如：1s=1秒，1分钟=1m，1小时=1h
                ]);
            } else {
                if (empty($addSubOrder)) $this->throwError('写入子订单失败');
                //发起企业微信审批
                if ($params['type'] != 1) {
                    $weChatService = new WeChatService();
                    $weChatService->ordinarySaleOrderCreateVerify($params);
                }
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->throwError($e->getMessage());
        }
        return true;
    }

    public function timeOutSaleOrder($params)
    {
        \think\facade\Log::write("timeOutSaleOrder : " . json_encode($params));
        try {
            //获取发起人信息
            $userInfo = $this->httpGet(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info', ['admin_id' => $params['admin_id'], 'field' => 'userid,dept_id']);
            if ($userInfo['error_code'] != 0 || !isset($userInfo['data'][$params['admin_id']])) $this->throwError('未获取到企业微信用户信息');
            $userid = $userInfo['data'][$params['admin_id']]['userid'];//企业微信userid

            $params['media_ids'] = $this->uploadWeiXinTemporary(['file' => $params['media_url']]);
            Db::name('offline_order')->where('sub_order_no', $params['order_no'])->update(['media_id' => $params['media_ids']]);
            if ($params['type'] != 1) {
                $weChatService = new WeChatService();
                $weChatService->ordinarySaleOrderCreateVerify($params);
            }
            \Curl::wecomSend('异步上传销售单图片成功', $userid, 'text');
        } catch (\Exception $e) {
            \think\facade\Log::write("timeOutSaleOrder 错误: " . $e->getMessage());
            \Curl::wecomSend('异步上传销售单图片错误: ' . $e->getMessage(), $userid, 'text');
        }
        return $this->success();
    }

    public static function getCollectionTypes($param)
    {
        $p_department_code = $param['department_code'] ?? '';
        $collection_type   = [
            '013'    => '领用出库',
            '013-2'  => '客情领用',
            '013-3'  => '物料领用',
            '013-4'  => '酒会领用',
            '013-10'  => '调拨出库',
        ];
        if (in_array($p_department_code, ['15'])) { //市场部 15
            $collection_type['013-6'] = '市场活动';
            $collection_type['013-7'] = '酒庄活动';
            $collection_type['013-8'] = '培训用酒';
            $collection_type['013-9'] = '品鉴用酒';
        }
        if (in_array($p_department_code, ['02', '03', '04', '05'])) { //02 产品运营组 05	内容运营组 04	文案组 03	客服部
            $collection_type['013-1'] = '样酒申请';
            $collection_type['013-9'] = '品鉴用酒';
        }
        if (in_array($p_department_code, ['01'])) { //产品中心 01
            $collection_type['013-9'] = '品鉴用酒';
        }
        if (in_array($p_department_code, ['01', '02', '03', '04', '05', '10', '11', '14', '15', '19', '1402', '29', '2901', '2902', '2903', '2904', '2905'])) { //销售部门 01	产品中心 02	产品运营组 03	客服部 04	文案组 05	内容运营组 10	新零售事业部 11	公域电商事业部 14	木兰朵酒庄 15	市场部 19	总经办 1402	木兰朵销售部 29	B2B事业部 2901	华西区 2902	华北区 2903	华东区 2904	华南区 2905	SO&Marketing
            $collection_type['013-1'] = '样酒申请';
        }
        if (in_array($p_department_code, ['11'])) { //公域 11
            $collection_type['013-1'] = '样酒申请';
        }

        return $collection_type;
    }

    /**
     * Description:普通销售单列表
     * Author: zrc
     * Date: 2022/10/8
     * Time: 13:45
     * @param $params
     * @return mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function ordinarySaleOrderList($params)
    {
        $result['list']  = [];
        $result['total'] = 0;
        $result['total_amount'] = 0;
        $page            = !empty($params['page']) ? $params['page'] : 1;
        $limit           = !empty($params['limit']) ? $params['limit'] : 10;
        $offset          = ($page - 1) * $limit;
        $where           = [];
        if (isset($params['is_sample_liquor']) && $params['is_sample_liquor'] == 1) {//科技样酒
            $where[] = ["oo.document_type", "=", 0];
            $where[] = ["oo.corp", "<>", '002'];
            $where[] = ["oo.is_delete", "=", 0];
            $whereOr = "oo.document_type = 0 and oo.is_delete = 0 && oo.corp !='002'";
        } else {//普通销售单
            $where[] = ["oo.document_type", "=", 1];
            $where[] = ["oo.is_delete", "=", 0];
            $whereOr = "oo.document_type = 1 and oo.is_delete = 0";
        }
        //获取当前用户权限
        $preparedsInfo = $this->httpGet(env('ITEM.ERP_PREPARED_URL') . '/prepared/v3/prepareds/detail', ['prepared_uid' => $params['admin_id']]);
        if (!isset($preparedsInfo['error_code']) || $preparedsInfo['error_code'] != 0 || empty($preparedsInfo['data'])) return $result;
        if ($preparedsInfo['data']['status'] == 0) return $result;
        if ($preparedsInfo['data']['is_all_inquire_prepared'] == 0) {
            if (empty($preparedsInfo['data']['prepared_inquire_items'])) return $result;
            $prepared_inquire_uid = array_column($preparedsInfo['data']['prepared_inquire_items'], 'uid');
            $whereOr              .= " and oo.operator in (" . implode(',', $prepared_inquire_uid) . ")";
        }
        if ($preparedsInfo['data']['is_all_inquire_salesman'] == 0) {
            if (empty($preparedsInfo['data']['salesman_inquire_items'])) return $result;
            $salesman_inquire_name = array_column($preparedsInfo['data']['salesman_inquire_items'], 'Name');
//            $staff_ids = Db::table('vh_supplychain.vh_staff')->where('realname',"in",$salesman_inquire_name)->column('id');
//            $staff_entity_codes = Db::table('vh_supplychain.vh_partner_entity')->where('maintainer_id',"in", $staff_ids)->column('code');
//            $where[] = ['oo.customer_code', 'in', $staff_entity_codes];
//            $whereOr .= " and oo.customer_code in ('" . implode("','", $staff_entity_codes) . "')";
            $whereOr               .= " and FIND_IN_SET(oo.clerk,('" . implode(',', $salesman_inquire_name) . "'))";
        }
//        if ($preparedsInfo['data']['is_all_use_clientele'] == 0 && !empty($preparedsInfo['data']['client_use_items'])) {
//            $salesman_inquire_name = array_column($preparedsInfo['data']['client_use_items'], 'Name');
//            $whereOr               .= " and FIND_IN_SET(oo.customer,('" . implode(',', $salesman_inquire_name) . "'))";
//        }
        if (!empty($params['sub_order_no'])) {
            $where[] = ["oo.sub_order_no", "=", $params['sub_order_no']];
            $whereOr .= " and oo.sub_order_no = '" . $params['sub_order_no'] . "'";
        }
        if (!empty($params['voucher_date_start'])) {
            $where[] = ['oo.voucher_date', '>=', strtotime($params['voucher_date_start'])];
            $whereOr .= " and oo.voucher_date >= " . strtotime($params['voucher_date_start']);
        }
        if (!empty($params['voucher_date_end'])) {
            $where[] = ['oo.voucher_date', '<', strtotime($params['voucher_date_end']) + 86400];
            $whereOr .= " and oo.voucher_date < " . (strtotime($params['voucher_date_end']) + 86400);
        }
        if (!empty($params['customer_code'])) {
            $where[] = ['oo.customer_code', '=', "{$params['customer_code']}"];
            $whereOr .= " and oo.customer_code = '{$params['customer_code']}'";
        } elseif (!empty($params['customer'])) {
            $where[] = ['oo.customer', 'like', "%{$params['customer']}%"];
            $whereOr .= " and oo.customer like '%{$params['customer']}%'";
        }
        if (!empty($params['department'])) {
            $where[] = ['oo.department', 'like', "%{$params['department']}%"];
            $whereOr .= " and oo.department like '%{$params['department']}%'";
        }
        if (!empty($params['department_code'])) {
            $where[] = ['oo.department_code', 'in', "{$params['department_code']}"];
            $whereOr .= " and oo.department_code in ('" . implode("','", explode(',', $params['department_code'])) . "')";
        }
        if (!empty($params['items_info'])) {
            $where[] = ['oo.items_info', 'like', "%{$params['items_info']}%"];
            $whereOr .= " and oo.items_info like '%{$params['items_info']}%'";
        }
        if (!empty($params['settle_customer'])) {
            $where[] = ['oo.settle_customer', 'like', "%{$params['settle_customer']}%"];
            $whereOr .= " and oo.settle_customer like '%{$params['settle_customer']}%'";
        }
        if (!empty($params['operator'])) {
            $where[] = ['oo.operator', '=', $params['operator']];
            $whereOr .= " and oo.operator = {$params['operator']}";
        }
        if (!empty($params['customer_abbreviation'])) {
            $where[] = ['oo.customer_abbreviation', 'like', "%{$params['customer_abbreviation']}%"];
            $whereOr .= " and oo.customer_abbreviation like '%{$params['customer_abbreviation']}%'";
        }
        if (!empty($params['clerk'])) {
            $where[] = ['oo.clerk', '=', $params['clerk']];
            $whereOr .= " and oo.clerk = '" . $params['clerk'] . "'";
//            $staff_ids = Db::table('vh_supplychain.vh_staff')->where('realname',"LIKE","%{$params['clerk']}%")->column('id');
//            $staff_entity_codes = Db::table('vh_supplychain.vh_partner_entity')->where('maintainer_id',"in", $staff_ids)->column('code');
//            $where[] = ['oo.customer_code', 'in', $staff_entity_codes];
//            $whereOr .= " and oo.customer_code in ('" . implode("','", $staff_entity_codes) . "')";
        }
        if (!empty($params['warehouse'])) {
            $where[] = ['oo.warehouse', 'in', $params['warehouse']];
            $whereOr .= " and oo.warehouse in ('" . implode("','", explode(',', $params['warehouse'])) . "')";
        }
        if (!empty($params['settlement_method_code'])) {
            $where[] = ['oo.settlement_method_code', 'in', $params['settlement_method_code']];
            $whereOr .= " and oo.settlement_method_code in ('" . implode("','", explode(',', $params['settlement_method_code'])) . "')";
        }
        if (!empty($params['delivery_mode_code'])) {
            $where[] = ['oo.delivery_mode_code', '=', $params['delivery_mode_code']];
            $whereOr .= " and oo.delivery_mode_code = '" . $params['delivery_mode_code'] . "'";
        }
        if (!empty($params['consignee'])) {
            $encrypt             = cryptionDeal(1, [$params['consignee']], '15736175219', '宗仁川');
            $params['consignee'] = isset($encrypt[$params['consignee']]) ? $encrypt[$params['consignee']] : '';
            $where[]             = ['om.consignee', '=', $params['consignee']];
            $whereOr             .= " and om.consignee = '" . $params['consignee'] . "'";
        }
        if (!empty($params['consignee_phone'])) {
            $encrypt                   = cryptionDeal(1, [$params['consignee_phone']], '15736175219', '宗仁川');
            $params['consignee_phone'] = isset($encrypt[$params['consignee_phone']]) ? $encrypt[$params['consignee_phone']] : '';
            $where[]                   = ['om.consignee_phone', '=', $params['consignee_phone']];
            $whereOr                   .= " and om.consignee_phone = '" . $params['consignee_phone'] . "'";
        }
        if (isset($params['push_t_status']) && is_numeric($params['push_t_status'])) {
            $where[] = ['oo.push_t_status', '=', $params['push_t_status']];
            $whereOr .= " and oo.push_t_status = {$params['push_t_status']}";
        }
        if (isset($params['push_wms_status']) && is_numeric($params['push_wms_status'])) {
            $where[] = ['oo.push_wms_status', '=', $params['push_wms_status']];
            $whereOr .= " and oo.push_wms_status = {$params['push_wms_status']}";
        }
        if (isset($params['dingtalk_status']) && is_numeric($params['dingtalk_status'])) {
            $where[] = ['oo.dingtalk_status', '=', $params['dingtalk_status']];
            $whereOr .= " and oo.dingtalk_status = {$params['dingtalk_status']}";
        }
        if (isset($params['is_reject']) && is_numeric($params['is_reject'])) {
            $where[] = ['oo.is_reject', '=', $params['is_reject']];
            $whereOr .= " and oo.is_reject = {$params['is_reject']}";
        }
        if (isset($params['return_warehouse']) && is_numeric($params['return_warehouse'])) {
            $where[] = ['oo.return_warehouse', '=', $params['return_warehouse']];
            $whereOr .= " and oo.return_warehouse = {$params['return_warehouse']}";
        }
        if (!empty($params['corp'])) {
            $where[] = ['oo.corp', '=', $params['corp']];
            $whereOr .= " and oo.corp = {$params['corp']}";
        }
        if (!empty($params['filter_corp'])) {
            $where[] = ['oo.corp', 'not in', $params['filter_corp']];
            $whereOr .= " and oo.corp not in ('" . implode("','", explode(',', $params['filter_corp'])) . "')";
        }
        if (!empty($params['short_code'])) {
            $where[] = ['oo.items_info', 'like', "%{$params['short_code']}%"];
            $whereOr .= " and oo.items_info like '%{$params['short_code']}%'";
        }
        if (!empty($params['approval_time_start'])) {
            $where[] = ['oo.approval_time', '>=', strtotime($params['approval_time_start'])];
            $whereOr .= " and oo.approval_time >= " . strtotime($params['approval_time_start']);
        }
        if (!empty($params['approval_time_end'])) {
            $where[] = ['oo.approval_time', '<', strtotime($params['approval_time_end']) + 86400];
            $whereOr .= " and oo.approval_time < " . (strtotime($params['approval_time_end']) + 86400);
        }
        if (isset($params['is_ts']) && is_numeric($params['is_ts'])) {
            $where[] = ['oo.is_ts', '=', $params['is_ts']];
            $whereOr .= " and oo.is_ts = {$params['is_ts']}";
        }
        if (isset($params['has_related_order_no']) && is_numeric($params['has_related_order_no'])) {
            if ($params['has_related_order_no'] == 1) {
                $where[] = ['oo.related_order_no', '<>', ''];
                $whereOr .= " and oo.related_order_no <> ''";
            } else {
                $where[] = ['oo.related_order_no', '=', ''];
                $whereOr .= " and oo.related_order_no = ''";
            }
        }
        if (isset($params['settlement_status']) && is_numeric($params['settlement_status'])) {
            $where[] = ['oo.settlement_status', '=', $params['settlement_status']];
            $whereOr .= " and oo.settlement_status = {$params['settlement_status']}";
        }
        if (isset($params['invoice_progress']) && is_numeric($params['invoice_progress'])) {
            $where[] = ['oo.invoice_progress', '=', $params['invoice_progress']];
            $whereOr .= " and oo.invoice_progress = {$params['invoice_progress']}";
        }
        if (!empty($params['settlement_time_start'])) {
            $where[] = ['oo.settlement_time', '>=', strtotime($params['settlement_time_start'])];
            $whereOr .= " and oo.settlement_time >= " . strtotime($params['settlement_time_start']);
        }
        if (!empty($params['settlement_time_end'])) {
            $where[] = ['oo.settlement_time', '<', strtotime($params['settlement_time_end'])];
            $whereOr .= " and oo.settlement_time < " . (strtotime($params['settlement_time_end']));
        }
        if (!empty($params['address'])) {
            // 1. 先查询匹配地址关键词的城市ID
            $reg_ids = Db::table('vh_user.vh_regional')
                ->where('name', 'like', "%{$params['address']}%")
                ->column('id');
            
            if (!empty($reg_ids)) {
                // 2. 使用原生 SQL 的正确写法
                $where[] = ['', 'exp', Db::raw("(om.address LIKE '%{$params['address']}%' OR om.city_id IN (" . implode(',', $reg_ids) . "))")];
                $whereOr .= " and (om.address like '%{$params['address']}%' OR om.city_id in (" . implode(',', $reg_ids) . "))";
            } else {
                // 如果没有匹配的城市ID,则只匹配详细地址
                $where[] = ['om.address', 'like', "%{$params['address']}%"];
                $whereOr .= " and om.address like '%{$params['address']}%'";
            }
        }

        $totalNum     = Db::name('offline_order')
            ->alias('oo')
            ->leftJoin('order_main om', 'om.id=oo.main_order_id')
            ->where($where)
            ->where([["oo.operator", "=", $params['admin_id']]])
            ->whereRaw($whereOr, [], 'OR')
            ->count();
        $total_amount     = Db::name('offline_order')
            ->alias('oo')
            ->leftJoin('order_main om', 'om.id=oo.main_order_id')
            ->where($where)
            ->where([["oo.operator", "=", $params['admin_id']]])
            ->whereRaw($whereOr, [], 'OR')
            ->sum('oo.payment_amount');
        $lists        = Db::name('offline_order')
            ->alias('oo')
            ->field('oo.*,om.consignee,om.consignee_phone,om.address,om.province_id,om.city_id,om.district_id')
            ->leftJoin('order_main om', 'om.id=oo.main_order_id')
            ->where($where)
            ->where([["oo.operator", "=", $params['admin_id']]])
            ->whereRaw($whereOr, [], 'OR')
            ->limit($offset, $limit)
            ->order('oo.id desc')
            ->select()->toArray();
        $admin_id_arr = array_unique(array_column($lists, 'operator'));
        $admin_id_str = implode(',', $admin_id_arr);
        $adminInfo    = $this->httpGet(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info', ['admin_id' => $admin_id_str, 'field' => 'realname']);
        if (count($lists) > 0) {
            foreach ($lists as $key => $val) {
                $lists[$key]['voucher_date']    = date('Y-m-d H:i:s', $val['voucher_date']);
                $lists[$key]['approval_time']   = !empty($val['approval_time']) ? date('Y-m-d H:i:s', $val['approval_time']) : '';
                $lists[$key]['settlement_time'] = !empty($val['settlement_time']) ? date('Y-m-d', $val['settlement_time']) : '';
                $lists[$key]['is_push_wms']     = $val['push_wms_status'] == 3 ? 0 : 1;
                if (!empty($val['consignee'])) {
                    //用户信息加密处理
                    $consignee = $val['consignee'];
                    $encrypt   = cryptionDeal(2, [$consignee], '15736175219', '宗仁川');
                    $consignee = isset($encrypt[$consignee]) ? $encrypt[$consignee] : '';
                    //$val['consignee_ecrypt'] = hidestr($consignee, 1);
                    $lists[$key]['consignee_ecrypt'] = $consignee;
                }
                if (!empty($val['consignee_phone'])) {
                    //用户信息加密处理
                    $phone   = $val['consignee_phone'];
                    $encrypt = cryptionDeal(2, [$phone], '15736175219', '宗仁川');
                    $phone   = isset($encrypt[$phone]) ? $encrypt[$phone] : '';
                    //$val['consignee_phone_ecrypt'] = hidestr($phone, 3, 4);
                    $lists[$key]['consignee_phone_ecrypt'] = $phone;
                }
                $lists[$key]['operator'] = isset($adminInfo['data'][$val['operator']]) ? $adminInfo['data'][$val['operator']] : '';
                //明细处理
                $items_info = [];
                $goodsInfo  = explode(',', $val['items_info']);
                //$val['bar_code'] . '*' . $val['short_code'] . '*' . $val['nums'] . '*' . $val['price'] . '*' . $val['is_gift'] . '*' . $val['agreementPrice'] . '*' . $val['product_name'] . '*' . $val['year'] . '*' . $val['unit'] . '*' . $val['wmsStockNumber'] . '*' . $val['number'] . '*' . $val['Specification'] . '*' . $val['total_price'] . '*' . $val['priceSource'] . '*' . $val['en_product_name']
                foreach ($goodsInfo as $k => $v) {
                    $goods                             = explode('*', $v);
                    $items_info[$k]['bar_code']        = isset($goods[0]) ? $goods[0] : '';
                    $items_info[$k]['short_code']      = isset($goods[1]) ? $goods[1] : '';
                    $items_info[$k]['nums']            = isset($goods[2]) ? $goods[2] : 0;
                    $items_info[$k]['price']           = isset($goods[3]) ? $goods[3] : 0;
                    $items_info[$k]['is_gift']         = isset($goods[4]) ? intval($goods[4]) : 0;
                    $items_info[$k]['agreementPrice']  = isset($goods[5]) ? $goods[5] : '';
                    $items_info[$k]['product_name']    = isset($goods[6]) ? $goods[6] : '';
                    $items_info[$k]['year']            = isset($goods[7]) ? $goods[7] : '';
                    $items_info[$k]['unit']            = isset($goods[8]) ? $goods[8] : '';
                    $items_info[$k]['wmsStockNumber']  = isset($goods[9]) ? $goods[9] : '';
                    $items_info[$k]['number']          = isset($goods[10]) ? intval($goods[10]) : 0;
                    $items_info[$k]['Specification']   = isset($goods[11]) ? $goods[11] : '';
                    $items_info[$k]['total_price']     = isset($goods[12]) ? $goods[12] : '';
                    $items_info[$k]['priceSource']     = isset($goods[13]) ? $goods[13] : '';
                    $items_info[$k]['en_product_name'] = isset($goods[14]) ? $goods[14] : '';
                }
                $lists[$key]['items_info']    = $items_info;
                $lists[$key]['material_info'] = json_decode($val['material_info'], true);
                //附件处理
                if (!empty($val['media_url'])) {
                    $media_url                = explode(',', $val['media_url']);
                    $lists[$key]['media_url'] = [];
                    foreach ($media_url as $kk => $vv) {
                        $lists[$key]['media_url'][] = imagePrefix($vv);
                    }
                }
            }
        }
        $result['list']  = $lists;
        $result['total'] = $totalNum;
        $result['total_amount'] = round($total_amount,2);
        return $result;
    }

    /**
     * Description:中台销售单导出
     * Author: zrc
     * Date: 2023/1/4
     * Time: 14:52
     * @param $params
     * @return bool
     * @throws \Exception
     */
    public function ordinarySaleOrderExport($params)
    {
        //创建导出队列
        $pushData = array(
            'exchange_name' => 'orders',
            'routing_key'   => 'sales_order_export',
            'data'          => base64_encode(json_encode($params))
        );
        $result   = httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
        if (!isset($result['error_code']) || $result['error_code'] != 0) $this->throwError('推送导出队列失败');
        return true;
    }

    /**
     * Description:导出中台销售单队列处理
     * Author: zrc
     * Date: 2023/1/6
     * Time: 16:13
     * @param $params
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */

    public function exportSalesOrderMqDeal($params)
    {
        $params        = json_decode($params, true);
        $weChatService = new WeChatService();
        //获取发起人企业微信信息
        $userInfo = $this->httpGet(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info', ['admin_id' => $params['admin_id'], 'field' => 'userid,dept_id']);
        if ($userInfo['error_code'] != 0 || !isset($userInfo['data'][$params['admin_id']])) $this->throwError('未获取到企业微信用户信息');
        $userid = $userInfo['data'][$params['admin_id']]['userid'];
        //获取当前用户权限
        $preparedsInfo = $this->httpGet(env('ITEM.ERP_PREPARED_URL') . '/prepared/v3/prepareds/detail', ['prepared_uid' => $params['admin_id']]);
        if (!isset($preparedsInfo['error_code']) || $preparedsInfo['error_code'] != 0 || empty($preparedsInfo['data']) || $preparedsInfo['data']['status'] == 0) {
            $weChatService->weChatSendText($userid, '销售单导出失败，销售单制单权限获取异常');
        }
        $where = [];
        $data  = [];
        if (isset($params['is_sample_liquor']) && $params['is_sample_liquor'] == 1) {//科技样酒
            $where[] = ["oo.document_type", "=", 0];
            $where[] = ["oo.corp", "<>", '002'];
            $where[] = ["oo.is_delete", "=", 0];
            $whereOr = "oo.document_type = 0 and oo.is_delete = 0 && oo.corp !='002'";
        } else {//普通销售单
            $where[] = ["oo.document_type", "=", 1];
            $where[] = ["oo.is_delete", "=", 0];
            $whereOr = "oo.document_type = 1 and oo.is_delete = 0";
        }
        if ($preparedsInfo['data']['is_all_inquire_prepared'] == 0) {
            if (empty($preparedsInfo['data']['prepared_inquire_items'])) $no_permissions = 1;//部分权限未配置数据视为无权限
            $prepared_inquire_uid = array_column($preparedsInfo['data']['prepared_inquire_items'], 'uid');
            $whereOr              .= " and oo.operator in (" . implode(',', $prepared_inquire_uid) . ")";
        }
        if ($preparedsInfo['data']['is_all_inquire_salesman'] == 0) {
            if (empty($preparedsInfo['data']['salesman_inquire_items'])) $no_permissions = 1;//部分权限未配置数据视为无权限
            $salesman_inquire_name = array_column($preparedsInfo['data']['salesman_inquire_items'], 'Name');
//            $staff_ids = Db::table('vh_supplychain.vh_staff')->where('realname',"in",$salesman_inquire_name)->column('id');
//            $staff_entity_codes = Db::table('vh_supplychain.vh_partner_entity')->where('maintainer_id',"in", $staff_ids)->column('code');
//            $where[] = ['oo.customer_code', 'in', $staff_entity_codes];
//            $whereOr .= " and oo.customer_code in ('" . implode("','", $staff_entity_codes) . "')";
            $whereOr               .= " and FIND_IN_SET(oo.clerk,('" . implode(',', $salesman_inquire_name) . "'))";
        }
        if (!empty($params['sub_order_no'])) {
            $where[] = ["oo.sub_order_no", "=", $params['sub_order_no']];
            $whereOr .= " and oo.sub_order_no = '" . $params['sub_order_no'] . "'";
        }
        if (!empty($params['voucher_date_start'])) {
            $where[] = ['oo.voucher_date', '>=', strtotime($params['voucher_date_start'])];
            $whereOr .= " and oo.voucher_date >= " . strtotime($params['voucher_date_start']);
        }
        if (!empty($params['voucher_date_end'])) {
            $where[] = ['oo.voucher_date', '<', strtotime($params['voucher_date_end']) + 86400];
            $whereOr .= " and oo.voucher_date < " . (strtotime($params['voucher_date_end']) + 86400);
        }
        if (!empty($params['customer_code'])) {
            $where[] = ['oo.customer_code', '=', "{$params['customer_code']}"];
            $whereOr .= " and oo.customer_code = '{$params['customer_code']}'";
        } else if (!empty($params['customer'])) {
            $where[] = ['oo.customer', 'like', "%{$params['customer']}%"];
            $whereOr .= " and oo.customer like '%{$params['customer']}%'";
        }
        if (!empty($params['department'])) {
            $where[] = ['oo.department', 'like', "%{$params['department']}%"];
            $whereOr .= " and oo.department like '%{$params['department']}%'";
        }
        if (!empty($params['department_code'])) {
            $where[] = ['oo.department_code', 'in', "{$params['department_code']}"];
            $whereOr .= " and oo.department_code in ('" . implode("','", explode(',', $params['department_code'])) . "')";
        }
        if (!empty($params['items_info'])) {
            $where[] = ['oo.items_info', 'like', "%{$params['items_info']}%"];
            $whereOr .= " and oo.items_info like '%{$params['items_info']}%'";
        }
        if (!empty($params['settle_customer'])) {
            $where[] = ['oo.settle_customer', 'like', "%{$params['settle_customer']}%"];
            $whereOr .= " and oo.settle_customer like '%{$params['settle_customer']}%'";
        }
        if (!empty($params['operator'])) {
            $where[] = ['oo.operator', '=', $params['operator']];
            $whereOr .= " and oo.operator = {$params['operator']}";
        }
        if (!empty($params['customer_abbreviation'])) {
            $where[] = ['oo.customer_abbreviation', 'like', "%{$params['customer_abbreviation']}%"];
            $whereOr .= " and oo.customer_abbreviation like '%{$params['customer_abbreviation']}%'";
        }
        if (!empty($params['clerk'])) {
            $where[] = ['oo.clerk', '=', $params['clerk']];
            $whereOr .= " and oo.clerk = '" . $params['clerk'] . "'";
//            $staff_ids = Db::table('vh_supplychain.vh_staff')->where('realname',"LIKE","%{$params['clerk']}%")->column('id');
//            $staff_entity_codes = Db::table('vh_supplychain.vh_partner_entity')->where('maintainer_id',"in", $staff_ids)->column('code');
//            $where[] = ['oo.customer_code', 'in', $staff_entity_codes];
//            $whereOr .= " and oo.customer_code in ('" . implode("','", $staff_entity_codes) . "')";
        }
        if (!empty($params['warehouse'])) {
            $where[] = ['oo.warehouse', 'in', $params['warehouse']];
            $whereOr .= " and oo.warehouse in ('" . implode("','", explode(',', $params['warehouse'])) . "')";
        }
        if (!empty($params['settlement_method_code'])) {
            $where[] = ['oo.settlement_method_code', 'in', $params['settlement_method_code']];
            $whereOr .= " and oo.settlement_method_code in ('" . implode("','", explode(',', $params['settlement_method_code'])) . "')";
        }
        if (!empty($params['delivery_mode_code'])) {
            $where[] = ['oo.delivery_mode_code', '=', $params['delivery_mode_code']];
            $whereOr .= " and oo.delivery_mode_code = '" . $params['delivery_mode_code'] . "'";
        }
        if (!empty($params['consignee'])) {
            $encrypt             = cryptionDeal(1, [$params['consignee']], '15736175219', '宗仁川');
            $params['consignee'] = isset($encrypt[$params['consignee']]) ? $encrypt[$params['consignee']] : '';
            $where[]             = ['om.consignee', '=', $params['consignee']];
            $whereOr             .= " and om.consignee = '" . $params['consignee'] . "'";
        }
        if (!empty($params['consignee_phone'])) {
            $encrypt                   = cryptionDeal(1, [$params['consignee_phone']], '15736175219', '宗仁川');
            $params['consignee_phone'] = isset($encrypt[$params['consignee_phone']]) ? $encrypt[$params['consignee_phone']] : '';
            $where[]                   = ['om.consignee_phone', '=', $params['consignee_phone']];
            $whereOr                   .= " and om.consignee_phone = '" . $params['consignee_phone'] . "'";
        }
        if (isset($params['push_t_status']) && is_numeric($params['push_t_status'])) {
            $where[] = ['oo.push_t_status', '=', $params['push_t_status']];
            $whereOr .= " and oo.push_t_status = {$params['push_t_status']}";
        }
        if (isset($params['dingtalk_status']) && is_numeric($params['dingtalk_status'])) {
            $where[] = ['oo.dingtalk_status', '=', $params['dingtalk_status']];
            $whereOr .= " and oo.dingtalk_status = {$params['dingtalk_status']}";
        }
        if (isset($params['is_reject']) && is_numeric($params['is_reject'])) {
            $where[] = ['oo.is_reject', '=', $params['is_reject']];
            $whereOr .= " and oo.is_reject = {$params['is_reject']}";
        } else {
            $where[] = ['oo.is_reject', '=', 0];
            $whereOr .= " and oo.is_reject = 0";
        }
        if (!empty($params['corp'])) {
            $where[] = ['oo.corp', '=', $params['corp']];
            $whereOr .= " and oo.corp = {$params['corp']}";
        }
        if (!empty($params['filter_corp'])) {
            $where[] = ['oo.corp', 'not in', $params['filter_corp']];
            $whereOr .= " and oo.corp not in ('" . implode("','", explode(',', $params['filter_corp'])) . "')";
        }
        if (!empty($params['short_code'])) {
            $where[] = ['oo.items_info', 'like', "%{$params['short_code']}%"];
            $whereOr .= " and oo.items_info like '%{$params['short_code']}%'";
        }
        if (!empty($params['approval_time_start'])) {
            $where[] = ['oo.approval_time', '>=', strtotime($params['approval_time_start'])];
            $whereOr .= " and oo.approval_time >= " . strtotime($params['approval_time_start']);
        }
        if (!empty($params['approval_time_end'])) {
            $where[] = ['oo.approval_time', '<', strtotime($params['approval_time_end']) + 86400];
            $whereOr .= " and oo.approval_time < " . (strtotime($params['approval_time_end']) + 86400);
        }
        if (isset($params['is_ts']) && is_numeric($params['is_ts'])) {
            $where[] = ['oo.is_ts', '=', $params['is_ts']];
            $whereOr .= " and oo.is_ts = {$params['is_ts']}";
        }
        if (isset($params['has_related_order_no']) && is_numeric($params['has_related_order_no'])) {
            if ($params['has_related_order_no'] == 1) {
                $where[] = ['oo.related_order_no', '<>', ''];
                $whereOr .= " and oo.related_order_no <> ''";
            } else {
                $where[] = ['oo.related_order_no', '=', ''];
                $whereOr .= " and oo.related_order_no = ''";
            }
        }
        if (isset($params['settlement_status']) && is_numeric($params['settlement_status'])) {
            $where[] = ['oo.settlement_status', '=', $params['settlement_status']];
            $whereOr .= " and oo.settlement_status = {$params['settlement_status']}";
        }
        if (isset($params['return_warehouse']) && is_numeric($params['return_warehouse'])) {
            $where[] = ['oo.return_warehouse', '=', $params['return_warehouse']];
            $whereOr .= " and oo.return_warehouse = {$params['return_warehouse']}";
        }
        if (isset($params['invoice_progress']) && is_numeric($params['invoice_progress'])) {
            $where[] = ['oo.invoice_progress', '=', $params['invoice_progress']];
            $whereOr .= " and oo.invoice_progress = {$params['invoice_progress']}";
        }
        if (!empty($params['settlement_time_start'])) {
            $where[] = ['oo.settlement_time', '>=', strtotime($params['settlement_time_start'])];
            $whereOr .= " and oo.settlement_time >= " . strtotime($params['settlement_time_start']);
        }
        if (!empty($params['settlement_time_end'])) {
            $where[] = ['oo.settlement_time', '<', strtotime($params['settlement_time_end'])];
            $whereOr .= " and oo.settlement_time < " . (strtotime($params['settlement_time_end']));
        }
        if (!empty($params['address'])) {
            // 1. 先查询匹配地址关键词的城市ID
            $reg_ids = Db::table('vh_user.vh_regional')
                ->where('name', 'like', "%{$params['address']}%")
                ->column('id');
            
            if (!empty($reg_ids)) {
                // 2. 使用原生 SQL 的正确写法
                $where[] = ['', 'exp', Db::raw("(om.address LIKE '%{$params['address']}%' OR om.city_id IN (" . implode(',', $reg_ids) . "))")];
                $whereOr .= " and (om.address like '%{$params['address']}%' OR om.city_id in (" . implode(',', $reg_ids) . "))";
            } else {
                // 如果没有匹配的城市ID,则只匹配详细地址
                $where[] = ['om.address', 'like', "%{$params['address']}%"];
                $whereOr .= " and om.address like '%{$params['address']}%'";
            }
        }

        $lists = [];
        $return_warehouse = ['0'=>'否','1'=>'是'];
        $invoice_progress = [0 => '不开票', 1 => '开票中', 2 => '开票成功', 3 => '开票失败'];
        if (!isset($no_permissions)) {
            $lists = Db::name('offline_order')
                ->alias('oo')
                ->field('oo.corp,oo.items_info,oo.collection_type,oo.created_time,oo.document_type,
                        oo.clerk,oo.voucher_date,oo.sub_order_no,oo.customer,oo.customer_code,oo.department,
                        oo.settlement_method,oo.dingtalk_status,oo.push_wms_status,oo.warehouse,
                        om.consignee,om.consignee_phone,oo.push_t_status,oo.is_reject,oo.memo,
                        oo.material_info,oo.approval_time,oo.is_ts,oo.settlement_money,
                        oo.settlement_time,oo.settlement_status,oo.return_warehouse,
                        oo.invoice_progress,om.address,oo.approver') // 添加 approver 字段
                ->leftJoin('order_main om', 'om.id=oo.main_order_id')
                ->where($where)
                ->where([["oo.operator", "=", $params['admin_id']]])
                ->whereRaw($whereOr, [], 'OR')
                ->order('oo.id desc')
                ->select()->toArray();
        }
        $collection_type = [
            '013'    => '领用出库',
            '013-1'  => '样酒申请',
            '013-2'  => '客情领用',
            '013-3'  => '物料领用',
            '013-4'  => '酒会领用',
            '013-6'  => '市场活动',
            '013-7'  => '酒庄活动',
            '013-8'  => '培训用酒',
            '013-9'  => '品鉴用酒',
            '013-10'  => '调拨出库',
        ];
        if (count($lists) > 0) {
            $corps = Db::name('collecting_company')->column('name', 'corp');
            foreach ($lists as $key => $val) {
                $val['corp'] = str_pad($val['corp'],3,"0",STR_PAD_LEFT);
                if ($val['document_type'] == 0) {
                    $val['collection_type'] = $collection_type[$val['collection_type']] ?? '';
                } else {
                    $val['collection_type'] = '';
                }
                $val['voucher_date']    = date('Y-m-d', $val['voucher_date']);
                $val['approval_time']   = !empty($val['approval_time']) ? date('Y-m-d H:i:s', $val['approval_time']) : '';
                $val['settlement_time'] = !empty($val['settlement_time']) ? date('Y-m-d', $val['settlement_time']) : '';
                if (!empty($val['consignee'])) {
                    //用户信息加密处理
                    $consignee = $val['consignee'];
                    $encrypt   = cryptionDeal(2, [$consignee], '15736175219', '宗仁川');
                    $consignee = isset($encrypt[$consignee]) ? $encrypt[$consignee] : '';
                }
                if (!empty($val['consignee_phone'])) {
                    //用户信息加密处理
                    $phone   = $val['consignee_phone'];
                    $encrypt = cryptionDeal(2, [$phone], '15736175219', '宗仁川');
                    $phone   = isset($encrypt[$phone]) ? $encrypt[$phone] : '';
                }
                switch ($val['dingtalk_status']) {
                    case 0:
                        $val['dingtalk_status'] = '待审批';
                        break;
                    case 1:
                        $val['dingtalk_status'] = '审批中';
                        break;
                    case 2:
                        $val['dingtalk_status'] = '审批通过';
                        break;
                    case 3:
                        $val['dingtalk_status'] = '审批驳回';
                        break;
                }
                switch ($val['push_wms_status']) {
                    case 0:
                        $val['push_wms_status'] = '未推送';
                        break;
                    case 1:
                        $val['push_wms_status'] = '推送成功';
                        break;
                    case 2:
                        $val['push_wms_status'] = '推送失败';
                        break;
                    case 3:
                        $val['push_wms_status'] = '不推送';
                        break;
                }
                switch ($val['push_t_status']) {
                    case 0:
                        $val['push_t_status'] = '未推送';
                        break;
                    case 1:
                        $val['push_t_status'] = '推送成功';
                        break;
                    case 2:
                        $val['push_t_status'] = '推送失败';
                        break;
                    case 3:
                        $val['push_t_status'] = '不推送';
                        break;
                }
                switch ($val['is_reject']) {
                    case 0:
                        $val['is_reject'] = '否';
                        break;
                    case 1:
                        $val['is_reject'] = '是';
                        break;
                }
                switch ($val['is_ts']) {
                    case 0:
                        $val['is_ts'] = '否';
                        break;
                    case 1:
                        $val['is_ts'] = '是';
                        break;
                }
                $val['return_warehouse'] = $return_warehouse[$val['return_warehouse']] ?? $val['return_warehouse'] ;
                $val['invoice_progress'] = $invoice_progress[$val['invoice_progress']] ?? $val['invoice_progress'] ;
                switch ($val['settlement_status']) {
                    case 0:
                        $val['settlement_status'] = '未知';
                        break;
                    case 1:
                        $val['settlement_status'] = '未收款';
                        break;
                    case 2:
                        $val['settlement_status'] = '部分收款';
                        break;
                    case 3:
                        $val['settlement_status'] = '全额收款';
                        break;
                    case 4:
                        $val['settlement_status'] = '不收款';
                        break;
                }
                //明细处理
                $goodsInfo = explode(',', $val['items_info']);
                foreach ($goodsInfo as $k => $v) {
                    $goods = explode('*', $v);
                    if (isset($goods[4])) $goods[4] = $goods[4] == 1 ? '是' : '否';
                    $oneData               = array(
                        'clerk'             => $val['clerk'],
                        'voucher_date'      => $val['voucher_date'],
                        'voucher_date_y' => date('Y', strtotime($val['voucher_date'])),
                        'voucher_date_m' => date('m', strtotime($val['voucher_date'])),
                        'corp' =>  $corps[$val['corp']] ?? $val['corp'],
                        'sub_order_no'      => $val['sub_order_no'],
                        'customer'          => $val['customer'],
                        'customer_code'          => $val['customer_code'],
                        'department'        => $val['department'],
                        'settlement_method' => $val['settlement_method'],
                        'consignee'         => $consignee,
                        'consignee_phone'   => $phone,
                        'warehouse'         => $val['warehouse'],
                        'dingtalk_status'   => $val['dingtalk_status'],
                        'approver'          => $val['approver'] ?? '', // 添加审核人字段
                        'approval_time'     => $val['approval_time'],
                        'push_t_status'     => $val['push_t_status'],
                        'push_wms_status'   => $val['push_wms_status'],
                        'product_name'      => isset($goods[6]) ? $goods[6] : '',
                        'short_code'        => isset($goods[1]) ? $goods[1] : '',
                        'nums'              => isset($goods[2]) ? $goods[2] : 0,
                        'price'             => isset($goods[3]) ? $goods[3] : 0,
                        'is_gift'           => isset($goods[4]) ? $goods[4] : '否',
                        'is_reject'         => $val['is_reject'],
                        'is_ts'             => $val['is_ts'],
                        'settlement_money'  => $val['settlement_money'],
                        'settlement_time'   => $val['settlement_time'],
                        'settlement_status' => $val['settlement_status'],
                        'return_warehouse' => $val['return_warehouse'],
                        'invoice_progress' => $val['invoice_progress'],
                        'memo'              => $val['memo'],
                        'address'           => $val['address'],
                        'collection_type'        => $val['collection_type'],
                        'created_time' => empty($val['created_time']) ? '' : date('Y-m', $val['created_time'])
                    );
                    $oneData['totalMoney'] = isset($goods[12]) ? $goods[12] : $oneData['nums'] * $oneData['price'];
                    $data[]                = $oneData;
                }
                //物料明细处理
                $materialInfo = json_decode($val['material_info'], true);
                if (!empty($materialInfo)) {
                    foreach ($materialInfo as $kk => $vv) {
                        $data[] = array(
                            'clerk'             => $val['clerk'],
                            'voucher_date'      => $val['voucher_date'],
                            'voucher_date_y' => date('Y', strtotime($val['voucher_date'])),
                            'voucher_date_m' => date('m', strtotime($val['voucher_date'])),
                            'corp' =>  $corps[$val['corp']] ?? $val['corp'],
                            'sub_order_no'      => $val['sub_order_no'],
                            'customer'          => $val['customer'],
                            'department'        => $val['department'],
                            'settlement_method' => $val['settlement_method'],
                            'consignee'         => $consignee,
                            'consignee_phone'   => $phone,
                            'warehouse'         => $val['warehouse'],
                            'dingtalk_status'   => $val['dingtalk_status'],
                            'approver'          => $val['approver'] ?? '', // 添加审核人字段
                            'approval_time'     => $val['approval_time'],
                            'push_t_status'     => $val['push_t_status'],
                            'push_wms_status'   => $val['push_wms_status'],
                            'product_name'      => $vv['product_name'],
                            'short_code'        => $vv['short_code'],
                            'nums'              => $vv['nums'],
                            'price'             => 0,
                            'is_gift'           => '否',
                            'is_reject'         => $val['is_reject'],
                            'is_ts'             => $val['is_ts'],
                            'customer_code'             => $val['customer_code'],
                            'settlement_money'  => $val['settlement_money'],
                            'settlement_time'   => $val['settlement_time'],
                            'settlement_status' => $val['settlement_status'],
                            'return_warehouse' => $val['return_warehouse'],
                            'invoice_progress' => $val['invoice_progress'],
                            'memo'              => $val['memo'],
                            'address'           => $val['address'],
                            'totalMoney'        => 0,
                            'collection_type'        => $val['collection_type'],
                            'created_time' => empty($val['created_time']) ? '' : date('Y-m', $val['created_time'])
                        );
                    }
                }
            }
        }
        $filename = "中台销售单导出数据";
        $header   = array(
            array('column' => 'clerk', 'name' => '业务员', 'width' => 15),
            array('column' => 'voucher_date', 'name' => '单据日期', 'width' => 15),
            array('column' => 'voucher_date_y', 'name' => '单据日期年', 'width' => 15),
            array('column' => 'voucher_date_m', 'name' => '单据日期月', 'width' => 15),
            array('column' => 'corp', 'name' => '主体', 'width' => 15),
            array('column' => 'sub_order_no', 'name' => '单据编号', 'width' => 30),
            array('column' => 'customer', 'name' => '客户', 'width' => 15),
            array('column' => 'customer_code', 'name' => '客户编码', 'width' => 15),
            array('column' => 'department', 'name' => '部门', 'width' => 15),
            array('column' => 'settlement_method', 'name' => '收款方式', 'width' => 15),
            array('column' => 'consignee', 'name' => '联系人', 'width' => 15),
            array('column' => 'consignee_phone', 'name' => '联系电话', 'width' => 15),
            array('column' => 'consignee_phone', 'name' => '客户手机号', 'width' => 15),
            array('column' => 'warehouse', 'name' => '仓库', 'width' => 15),
            array('column' => 'dingtalk_status', 'name' => '企微审核状态', 'width' => 15),
            array('column' => 'approver', 'name' => '审核人', 'width' => 15), // 添加审核人列
            array('column' => 'approval_time', 'name' => '审批完成时间', 'width' => 15),
            array('column' => 'push_wms_status', 'name' => '萌牙推送状态', 'width' => 15),
            array('column' => 'push_t_status', 'name' => 'erp推送状态', 'width' => 15),
            array('column' => 'product_name', 'name' => '存货', 'width' => 15),
            array('column' => 'short_code', 'name' => '简码', 'width' => 15),
            array('column' => 'regions_name', 'name' => '酒庄', 'width' => 30),
            array('column' => 'nums', 'name' => '数量', 'width' => 15),
            array('column' => 'price', 'name' => '单价', 'width' => 15),
            array('column' => 'totalMoney', 'name' => '金额', 'width' => 15),
            array('column' => 'is_gift', 'name' => '赠品', 'width' => 15),
            array('column' => 'is_reject', 'name' => '是否弃审', 'width' => 15),
            array('column' => 'is_ts', 'name' => '是否暂存', 'width' => 15),
            array('column' => 'settlement_money', 'name' => '收款金额', 'width' => 15),
            array('column' => 'settlement_time', 'name' => '收款时间', 'width' => 15),
            array('column' => 'settlement_status', 'name' => '收款状态', 'width' => 15),
            array('column' => 'return_warehouse', 'name' => '退回仓库', 'width' => 15),
            array('column' => 'invoice_progress', 'name' => '开票状态', 'width' => 15),
            array('column' => 'memo', 'name' => '备注', 'width' => 30),
            array('column' => 'address', 'name' => '地址', 'width' => 30),
            array('column' => 'collection_type', 'name' => '领用出库类型', 'width' => 30),
            array('column' => 'created_time', 'name' => '制单月份', 'width' => 30),
        );
        try {
            $wiki_products = Db::table('vh_wiki.vh_products')->alias('p')
                ->leftJoin('vh_wiki.vh_winery_base r', 'p.chateau_id = r.id')
                ->where('p.short_code', 'in', array_column($data, 'short_code'))
                ->column('p.id,p.short_code,r.winery_name_cn,r.winery_name_en', 'p.short_code');

            foreach ($data as &$datum) {
                $datum['regions_name'] = $wiki_products[$datum['short_code']]['winery_name_cn'] ?? ($wiki_products[$datum['short_code']]['winery_name_en'] ?? '');
            }

            $uploadUrl = exportSheelExcel($data, $header, $filename);
            if (empty($uploadUrl)) $this->throwError('生成excel文件异常');
            $file     = app()->getRootPath() . "public/storage/" . $uploadUrl;
            $media_id = weixinUpload($file, '中台销售单导出数据.xlsx');
            if (empty($media_id)) $this->throwError('上传企业微信临时文件失败');
            unlink($file);
            $msgData = array(
                'content' => $media_id,
                'userid'  => $userid,
                'msgtype' => 'file',
                'agentid' => 0,
            );
            $result  = httpPostString(env('ITEM.WECHART_URL') . '/wechat/v3/wecom/app/send', json_encode($msgData));
            if ($result['error_code'] != 0) $this->throwError('发送文件到企业微信失败');
        } catch (\Exception $e) {
            $weChatService->weChatSendText($userid, '销售单导出失败：' . $e->getMessage());
            $this->throwError("导出失败：" . $e->getMessage());
        }
        return true;
    }

    /**
     * Description:编辑普通销售单
     * Author: zrc
     * Date: 2022/10/8
     * Time: 13:52
     * @param $params
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updateOrdinarySaleOrder($params)
    {
        $offlineOrder = Db::name('offline_order')->field('id,main_order_id,sub_order_no,push_t_status,dingtalk_status,push_wms_status,media_id,update_time')->where(['id' => $params['id']])->find();
        if (empty($offlineOrder)) $this->throwError('未获取到订单信息');
        if ($offlineOrder['dingtalk_status'] == 1) $this->throwError('销售单审批审核中,不允许编辑');
        if ($offlineOrder['dingtalk_status'] == 2) $this->throwError('销售单审批已通过,不允许编辑');
        if(!empty($params['related_order_no'])){
            if(!empty(Db::name('offline_order')->where(['sub_order_no' => $params['related_order_no']])->value('related_order_no'))){
                $this->throwError('形式单不能关联形式单');
            }
        }
        $params['old_dingtalk_status'] = $offlineOrder['dingtalk_status'];
        //用户信息加密处理
        if (!empty($params['consignee'])) {
            $consignee = trim($params['consignee']);
            $encrypt   = cryptionDeal(1, [$consignee], $params['admin_id'], '后端用户');
            $consignee = isset($encrypt[$consignee]) ? $encrypt[$consignee] : '';
        }
        if (!empty($params['consignee_phone'])) {
            $phone   = trim($params['consignee_phone']);
            $encrypt = cryptionDeal(1, [$phone], $params['admin_id'], '后端用户');
            $phone   = isset($encrypt[$phone]) ? $encrypt[$phone] : '';
        }
        //订单金额处理+子订单订单商品信息处理
        $payment_amount = 0;
        $items_info     = '';
        $is_have_gift   = '无赠品';//默认无赠品
        foreach ($params['items_info'] as $val) {
            if ($val['is_gift'] == 1) $is_have_gift = '有赠品';
            $payment_amount += $val['total_price'];
            if (empty($items_info)) {
                $items_info = $val['bar_code'] . '*' . $val['short_code'] . '*' . $val['nums'] . '*' . $val['price'] . '*' . $val['is_gift'] . '*' . $val['agreementPrice'] . '*' . $val['product_name'] . '*' . $val['year'] . '*' . $val['unit'] . '*' . $val['wmsStockNumber'] . '*' . $val['number'] . '*' . $val['Specification'] . '*' . $val['total_price'] . '*' . $val['priceSource'] . '*' . $val['en_product_name'];
            } else {
                $items_info = $items_info . ',' . $val['bar_code'] . '*' . $val['short_code'] . '*' . $val['nums'] . '*' . $val['price'] . '*' . $val['is_gift'] . '*' . $val['agreementPrice'] . '*' . $val['product_name'] . '*' . $val['year'] . '*' . $val['unit'] . '*' . $val['wmsStockNumber'] . '*' . $val['number'] . '*' . $val['Specification'] . '*' . $val['total_price'] . '*' . $val['priceSource'] . '*' . $val['en_product_name'];
            }
        }
        $params['is_have_gift'] = $is_have_gift;
        //收货地址换行处理
        $params['address'] = str_replace(array("\r\n", "\r", "\n"), "", $params['address']);
        //运费方式对应快递方式处理
        $express_type     = 4;//默认京东不保价
        $delivery_express = config('config')['delivery_express'];
        if (isset($params['delivery_mode'])) {
            foreach ($delivery_express as &$val) {
                if ($val['delivery_mode'] == $params['delivery_mode']) $express_type = $val['express_type'];
            }
        }
        Db::startTrans();
        try {
            if ($params['order_no'] != $offlineOrder['sub_order_no']) {//单据编号变更处理(删除原订单，创建新订单)
                //订单号重复查询
                $isset  = Db::name('offline_order')->where(['sub_order_no' => $params['order_no']])->count();
                $issets = Db::name('tripartite_order')->where(['sub_order_no' => $params['order_no']])->count();
                if ($isset > 0 || $issets > 0) $this->throwError('订单号重复');
                $deleteOldOrder = Db::name('offline_order')->where(['id' => $params['id']])->update(['sub_order_status' => 4, 'is_delete' => 1, 'update_time' => time()]);
                if (empty($deleteOldOrder)) $this->throwError('变更单据编号失败');
                //主订单写入
                $count = Db::name('order_main')->where(['main_order_no' => $params['order_no']])->count();
                if ($count > 0) $params['order_no'] = $params['order_no'] . '-' . rand(1, 9999);
                $mainData      = array(
                    'uid'               => 0,
                    'main_order_no'     => $params['order_no'],
                    'main_order_status' => 3,
                    'payment_amount'    => $payment_amount,
                    'address'           => $params['address'],
                    'consignee'         => $consignee,
                    'consignee_phone'   => $phone,
                    'created_time'      => time(),
                    'order_type'        => 8,
                    'operator'          => $params['admin_id'],
                );
                $regions = [];
                if (isset($params['province_id'])) {
                    $mainData['province_id'] = $params['province_id'];
                    $regions['province_id'] = $params['province_id'];
                }
                if (isset($params['city_id'])) {
                    $mainData['city_id'] = $params['city_id'];
                    $regions['city_id'] = $params['city_id'];
                }
                if (isset($params['district_id'])) {
                    $mainData['district_id'] = $params['district_id'];
                    $regions['district_id'] = $params['district_id'];
                }
                $regions_name = [];
                if(!empty($regions)){
                    $regions_name = Db::table('vh_user.vh_regional')->where('id','in',array_values($regions))->column('name', 'id' );
                }
                $params['province_name'] = $regions_name[$params['province_id']] ?? '';
                $params['city_name'] = $regions_name[$params['city_id']] ?? '';
                $params['district_name'] = $regions_name[$params['district_id']] ?? '';
                $main_order_id = Db::name('order_main')->insertGetId($mainData);
                if (empty($main_order_id)) $this->throwError('写入主订单失败');
                //子订单写入
                $subData = array(
                    'return_warehouse'        => $params['return_warehouse'],
                    'sub_order_no'            => $params['order_no'],
                    'sub_order_status'        => 1,
                    'main_order_id'           => $main_order_id,
                    'items_info'              => $items_info,
                    'material_info'           => json_encode($params['material_info'], JSON_UNESCAPED_UNICODE),
                    'order_qty'               => 1,
                    'payment_amount'          => $payment_amount,
                    'express_type'            => $express_type,
                    'express_number'          => isset($params['express_number']) ? $params['express_number'] : '',
                    'order_type'              => 8,
                    'operator'                => $params['admin_id'],
                    'voucher_date'            => strtotime($params['voucher_date']),
                    'customer'                => $params['customer'],
                    'customer_code'           => $params['customer_code'],
                    'customer_abbreviation'   => isset($params['customer_abbreviation']) ? $params['customer_abbreviation'] : '',
                    'settle_customer'         => $params['settle_customer'],
                    'settle_customer_code'    => $params['settle_customer_code'],
                    'department'              => isset($params['department']) ? $params['department'] : '',
                    'department_code'         => isset($params['department_code']) ? $params['department_code'] : '',
                    'clerk'                   => isset($params['clerk']) ? $params['clerk'] : '',
                    'clerk_code'              => isset($params['clerk_code']) ? $params['clerk_code'] : '',
                    'warehouse'               => $params['warehouse'],
                    'warehouse_code'          => $params['warehouse_code'],
                    'delivery_mode'           => isset($params['delivery_mode']) ? $params['delivery_mode'] : '',
                    'delivery_mode_code'      => isset($params['delivery_mode_code']) ? $params['delivery_mode_code'] : '',
                    'business_type'           => '普通销售单',
                    'business_type_code'      => '15',
                    'memo'                    => isset($params['memo']) ? $params['memo'] : '',
                    'created_time'            => time(),
                    'document_type'           => 1,
                    'express_pay_method'      => isset($params['express_pay_method']) ? $params['express_pay_method'] : '',
                    'express_pay_method_code' => isset($params['express_pay_method_code']) ? $params['express_pay_method_code'] : '',
                    'settlement_method'       => isset($params['settlement_method']) ? $params['settlement_method'] : '',
                    'settlement_method_code'  => isset($params['settlement_method_code']) ? $params['settlement_method_code'] : '',
                    'corp'                    => $params['corp'],
                    'media_url'               => isset($params['media_url']) ? $params['media_url'] : '',
                    'dingtalk_status'         => $params['type'] == 1 ? 0 : 1,
                    'approver'                => '',
                    'approval_process'        => [],
                    'priceSource'             => $params['priceSource'],
                    'is_ts'                   => $params['is_ts'],
                    'bank_account_name'       => $params['bank_account_name'] ?? null,
                    'account_no'              => $params['account_no'] ?? null,
                    'settlement_month_type'              => $params['settlement_month_type'] ?? null,
                    'settlement_day_type'              => $params['settlement_day_type'] ?? null,
                    'settlement_days'              => $params['settlement_days'] ?? 0,
                    'related_order_no'              => $params['related_order_no'] ?? '',
                );
                //科技样酒销售单处理
                if ($params['is_sample_liquor'] == 1) {
                    $subData['document_type']   = 0;
                    $subData['collection_type'] = $params['collection_type'];
                }
                //003木兰朵不推erp
                if ($params['corp'] == '003') {
                    $subData['push_t_status'] = 3;
                }
                //不推送萌牙处理
                if ($params['is_push_wms'] == 0) {
                    $subData['push_wms_status'] = 3;
                    if ($offlineOrder['push_wms_status'] != 3) {
                        //添加订单备注
                        $remarks      = array(
                            'sub_order_no' => $params['order_no'],
                            'order_type'   => 8,
                            'content'      => '编辑销售单设置不推送萌牙',
                            'admin_id'     => $params['admin_id']
                        );
                        $orderService = new OrderService();
                        $orderService->createRemarks($remarks);
                    }
                } else if ($params['is_push_wms'] == 1) {
                    $subData['push_wms_status'] = 0;
                    if ($offlineOrder['push_wms_status'] == 3) {
                        //添加订单备注
                        $remarks      = array(
                            'sub_order_no' => $params['order_no'],
                            'order_type'   => 8,
                            'content'      => '编辑销售单设置推送萌牙',
                            'admin_id'     => $params['admin_id']
                        );
                        $orderService = new OrderService();
                        $orderService->createRemarks($remarks);
                    }
                }
                //附件处理
                $subData['media_id'] = '';
                if (!empty($params['media_url'])) {
                    \Curl::timingAdd([
                        'namespace' => 'orders', //命名空间。可根据项目自定义名称。
                        'key'       => $params['order_no'], //唯一键名，在同一个命名空间下，如果有相同键名存在会覆盖已有数据。
                        'data'      => base64_encode(json_encode($params)), //超时触发时，提交的数据。建议使用Base64编码（注：回调接口传递的数据会自动解码）。
                        'callback'  => env('ITEM.ORDERS_URL') . '/orders/v3/offline/timeOutSaleOrder', //超时触发时，调用的接口回调地址。
                        'timeout'   => '2s', //超时时间。支持s,m,h单位进行描述。比如：1s=1秒，1分钟=1m，1小时=1h
                    ]);
                } else {
                    $subData['media_url'] = '';
                }
                $addSubOrder = Db::name('offline_order')->insert($subData);
                if (empty($addSubOrder)) $this->throwError('写入子订单失败');
                //查询有无订单备注，有迁移到新订单
                $remarks = Db::name('order_remarks')->where(['sub_order_no' => $offlineOrder['sub_order_no']])->count();
                if ($remarks > 0) {
                    Db::name('order_remarks')->where(['sub_order_no' => $offlineOrder['sub_order_no']])->update(['sub_order_no' => $params['order_no']]);
                }
            } else {//修改订单数据
                //主订单修改
                $mainData        = array(
                    'payment_amount'  => $payment_amount,
                    'address'         => $params['address'],
                    'consignee'       => $consignee,
                    'consignee_phone' => $phone,
                    'operator'        => $params['admin_id'],
                    'update_time'     => time()
                );
                $regions = [];
                if (isset($params['province_id'])) {
                    $mainData['province_id'] = $params['province_id'];
                    $regions['province_id'] = $params['province_id'];
                }
                if (isset($params['city_id'])) {
                    $mainData['city_id'] = $params['city_id'];
                    $regions['city_id'] = $params['city_id'];
                }
                if (isset($params['district_id'])) {
                    $mainData['district_id'] = $params['district_id'];
                    $regions['district_id'] = $params['district_id'];
                }
                $regions_name = [];
                if(!empty($regions)){
                    $regions_name = Db::table('vh_user.vh_regional')->where('id','in',array_values($regions))->column('name', 'id' );
                }
                $params['province_name'] = $regions_name[$params['province_id']] ?? '';
                $params['city_name'] = $regions_name[$params['city_id']] ?? '';
                $params['district_name'] = $regions_name[$params['district_id']] ?? '';
                $updateMainOrder = Db::name('order_main')->where(['id' => $offlineOrder['main_order_id']])->update($mainData);
                if (empty($updateMainOrder)) $this->throwError('修改主订单失败');
                $main_order_id = $offlineOrder['main_order_id'];
                //子订单写入
                $subData = array(
                    'return_warehouse'        => $params['return_warehouse'],
                    'sub_order_status'        => 1,
                    'items_info'              => $items_info,
                    'material_info'           => json_encode($params['material_info'], JSON_UNESCAPED_UNICODE),
                    'payment_amount'          => $payment_amount,
                    'express_type'            => $express_type,
                    'express_number'          => $params['express_number'],
                    'voucher_date'            => strtotime($params['voucher_date']),
                    'customer'                => $params['customer'],
                    'customer_code'           => $params['customer_code'],
                    'customer_abbreviation'   => isset($params['customer_abbreviation']) ? $params['customer_abbreviation'] : '',
                    'settle_customer'         => $params['settle_customer'],
                    'settle_customer_code'    => $params['settle_customer_code'],
                    'department'              => $params['department'],
                    'department_code'         => $params['department_code'],
                    'clerk'                   => $params['clerk'],
                    'clerk_code'              => $params['clerk_code'],
                    'warehouse'               => $params['warehouse'],
                    'warehouse_code'          => $params['warehouse_code'],
                    'delivery_mode'           => $params['delivery_mode'],
                    'delivery_mode_code'      => $params['delivery_mode_code'],
                    'memo'                    => isset($params['memo']) ? $params['memo'] : '',
                    'update_time'             => time(),
                    'express_pay_method'      => isset($params['express_pay_method']) ? $params['express_pay_method'] : '',
                    'express_pay_method_code' => isset($params['express_pay_method_code']) ? $params['express_pay_method_code'] : '',
                    'settlement_method'       => isset($params['settlement_method']) ? $params['settlement_method'] : '',
                    'settlement_method_code'  => isset($params['settlement_method_code']) ? $params['settlement_method_code'] : '',
                    'corp'                    => $params['corp'],
                    'media_url'               => isset($params['media_url']) ? $params['media_url'] : '',
                    'dingtalk_status'         => $params['type'] == 1 ? 0 : 1,
                    'is_reject'               => 0,
                    'approver'                => '',
                    'approval_process'        => [],
                    'approval_time'           => 0,
                    'priceSource'             => $params['priceSource'],
                    'is_ts'                   => $params['is_ts'],
                    'bank_account_name'       => $params['bank_account_name'] ?? null,
                    'account_no'              => $params['account_no'] ?? null,
                    'settlement_month_type'              => $params['settlement_month_type'] ?? null,
                    'settlement_day_type'              => $params['settlement_day_type'] ?? null,
                    'settlement_days'              => $params['settlement_days'] ?? 0,
                );
                //科技样酒销售单处理
                if ($params['is_sample_liquor'] == 1) {
                    $subData['document_type']   = 0;
                    $subData['collection_type'] = $params['collection_type'];
                }
                //003木兰朵不推erp
                if ($params['corp'] == '003') {
                    $subData['push_t_status'] = 3;
                }
                //不推送萌牙处理
                if ($params['is_push_wms'] == 0 && $offlineOrder['push_wms_status'] != 3) {
                    $subData['push_wms_status'] = 3;
                    //添加订单备注
                    $remarks      = array(
                        'sub_order_no' => $params['order_no'],
                        'order_type'   => 8,
                        'content'      => '编辑销售单设置不推送萌牙',
                        'admin_id'     => $params['admin_id']
                    );
                    $orderService = new OrderService();
                    $orderService->createRemarks($remarks);
                } else if ($params['is_push_wms'] == 1 && $offlineOrder['push_wms_status'] == 3) {
                    $subData['push_wms_status'] = 0;
                    //添加订单备注
                    $remarks      = array(
                        'sub_order_no' => $params['order_no'],
                        'order_type'   => 8,
                        'content'      => '编辑销售单设置推送萌牙',
                        'admin_id'     => $params['admin_id']
                    );
                    $orderService = new OrderService();
                    $orderService->createRemarks($remarks);
                }
                //附件处理
                $subData['media_id']  = '';
                if (!empty($params['media_url'])) {
                    \Curl::timingAdd([
                        'namespace' => 'orders', //命名空间。可根据项目自定义名称。
                        'key'       => $params['order_no'], //唯一键名，在同一个命名空间下，如果有相同键名存在会覆盖已有数据。
                        'data'      => base64_encode(json_encode($params)), //超时触发时，提交的数据。建议使用Base64编码（注：回调接口传递的数据会自动解码）。
                        'callback'  => env('ITEM.ORDERS_URL') . '/orders/v3/offline/timeOutSaleOrder', //超时触发时，调用的接口回调地址。
                        'timeout'   => '2s', //超时时间。支持s,m,h单位进行描述。比如：1s=1秒，1分钟=1m，1小时=1h
                    ]);
                } else {
                    $subData['media_url'] = '';
                }
                $updateSubOrder = Db::name('offline_order')->where(['id' => $params['id']])->update($subData);
                if (empty($updateSubOrder)) $this->throwError('修改子订单失败');
            }
            //发起企业微信审批
            if ($params['type'] != 1 && empty($params['media_url'])) {
                $weChatService          = new WeChatService();
                $weChatService->ordinarySaleOrderCreateVerify($params);
            }
            //溯源码更新
            $product_total_nums = array_sum(array_column($params['items_info'], 'nums'));
            if ($product_total_nums >= 6 && in_array($params['warehouse_code'], ['336', '262'])) {
                //新增
                $addressAiMatch = \Curl::addressAiMatch(['address' => $params['address']]);
                $province_id    = $addressAiMatch['province_id'];
                $customer_code  = str_replace('VH', '', $params['customer_code']);  // 客户代码
                $encoded_str    = (new \SealedSource())->encode_with_fixed_length($province_id, $customer_code, $main_order_id);
            } else {
                //新增无需删除
                (new \SealedSource())->decode_with_delete(['main_order_id' => $main_order_id]);
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->throwError($e->getMessage());
        }
        return true;
    }

    /**
     * Description:企业微信上传临时素材
     * Author: zrc
     * Date: 2023/1/1
     * Time: 13:50
     * @param $params
     * @return string
     * @throws \Exception
     */
    public function uploadWeiXinTemporary($params)
    {
        $fileArr      = explode(',', $params['file']);
        $media_id_arr = [];
        foreach ($fileArr as &$val) {
            $url  = env('ALIURL') . $val;
            $purl = parse_url($url);;
            if (!empty($purl['path'])) {
                $ext = pathinfo($purl['path'], PATHINFO_EXTENSION);
                if (in_array(strtoupper($ext), ['JPG', 'JPEG', 'PNG', 'BMP', 'GIF', 'WEBP', 'TIFF', 'HEIC'])) {
                    if (empty($purl['query'])) {
                        $url .= '?x-oss-process=image/resize,w_1500,limit_0';
                    } else {
                        $url .= '&x-oss-process=image/resize,w_1500,limit_0';
                    }
                }
            }
            $file = @file_get_contents($url);
            if (!$file) $this->throwError('未获取到上传文件');
            $pathArr  = explode('/', ($purl['path'] ?? $url));
            $fileName = array_pop($pathArr);
            $dir      = app()->getRootPath() . 'public/storage/';
            file_put_contents($dir . $fileName, $file);
            $media_id = weixinUpload($dir . $fileName, $fileName);
            if (empty($media_id)) $this->throwError('上传临时附件失败');
            $media_id_arr[] = $media_id;
            @unlink($dir . $fileName);
        }
        if (empty($media_id_arr)) $this->throwError('上传临时附件失败');
        return implode(',', $media_id_arr);
    }

    /**
     * Description:弃审普通销售单
     * Author: zrc
     * Date: 2023/1/16
     * Time: 11:24
     * @param $params
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function rejectSaleOrder($params)
    {
        Db::startTrans();
        try {
            $orderInfo = Db::name('offline_order')->field('push_t_status,push_wms_status,dingtalk_status,is_reject')->where(['sub_order_no' => $params['sub_order_no']])->find();
            if (empty($orderInfo)) throw new \Exception('未获取到单据信息');
            if ($orderInfo['dingtalk_status'] != 2) throw new \Exception('单据审批未通过，弃审失败');
            if ($orderInfo['is_reject'] == 1) throw new \Exception('单据已弃审，请勿重复操作');

            $arap_djzb = Db::name('arap_djzb')->where([
                ['bill_source', '=', 1],
                ['sub_order_no', '=', $params['sub_order_no']]
            ])->column('id,bill_no,bill_type,bill_status,write_off_status');
            $arap_ids = [];
            foreach ($arap_djzb as $v) {
                if ($v['bill_type'] == 1 && $v['bill_status'] == 2) {
                    throw new \Exception("收款单【{$v['bill_no']}】已审核，弃审失败");
                }
                if (in_array($v['write_off_status'], [1, 2])) {
                    throw new \Exception("应收收款单据已核销，弃审失败");
                }
                $arap_ids[] = $v['id'];
            }

            $updateData = array(
                'dingtalk_status' => 0,
                'is_reject'       => 1,
                'update_time'     => time()
            );
            //已推送erp的删除erp单据
            if ($orderInfo['push_t_status'] == 1) {
                $delErp = curlRequest(env('ITEM.ERP_URL') . '/erp/v3/saleOrder/deleteHandle', ['sub_order_no' => $params['sub_order_no']]);
                if (isset($delErp['error_code']) && $delErp['error_code'] != 0) throw new \Exception($delErp['error_msg']);
            }
            //推送失败的，订单状态取消来控制推送的重试机制
            if ($orderInfo['push_wms_status'] == 2) $updateData['sub_order_status'] = 4;
            $result = Db::name('offline_order')->where(['sub_order_no' => $params['sub_order_no']])->update($updateData);
            if (empty($result)) throw new \Exception('弃审失败，请重试');

            // 删除相关应收收款单据
            Db::name('arap_djzb')->whereIn('id', $arap_ids)->update(['is_delete' => 1, 'update_time' => time()]);
            
            //添加订单备注
            $remarks      = array(
                'sub_order_no' => $params['sub_order_no'],
                'order_type'   => 8,
                'content'      => '销售单弃审:' . $params['reason'],
                'admin_id'     => $params['admin_id']
            );
            $orderService = new OrderService();
            $orderService->createRemarks($remarks);
            // 提交事务
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->throwError($e->getMessage());
        }
        return true;
    }

    /**
     * Description:添加编辑物料
     * Author: zrc
     * Date: 2023/2/21
     * Time: 13:16
     * @param $params
     * @return int|string
     * @throws \Exception
     */
    public function addEditMaterial($params)
    {
        $result = [];
        //磐石调用处理
        if (!empty($params['is_wiki'])) {
            $isset = Db::name('material')->where(['short_code' => $params['short_code'], 'is_delete' => 0])->count();
            if ($isset == 0) {
                $data   = array(
                    'short_code'   => $params['short_code'],
                    'product_name' => $params['product_name'],
                    'operator'     => $params['admin_id'],
                    'created_time' => time(),
                );
                $result = Db::name('material')->insert($data);
            } else {
                $updateData = array(
                    'short_code'   => $params['short_code'],
                    'product_name' => $params['product_name'],
                    'operator'     => $params['admin_id'],
                    'update_time'  => time()
                );
                $result     = Db::name('material')->where(['short_code' => $params['short_code']])->update($updateData);
            }
            return $result;
        }
        if (empty($params['id'])) {
            $isset = Db::name('material')->where(['short_code' => $params['short_code'], 'is_delete' => 0])->count();
            if ($isset > 0) $this->throwError('数据已存在，请勿重复添加');
            $data   = array(
                'short_code'   => $params['short_code'],
                'product_name' => $params['product_name'],
                'operator'     => $params['admin_id'],
                'warning_value' => $params['warning_value'] ?? 0,
                'created_time' => time(),
            );
            $result = Db::name('material')->insert($data);
        } else {
            if (empty($params['id'])) $this->throwError('未获取到数据ID', ErrorCode::PARAM_ERROR);
            $updateData = ['operator' => $params['admin_id'], 'update_time' => time()];
            if (!empty($params['short_code'])) {
                $isset = Db::name('material')->where([['short_code', '=', $params['short_code']], ['is_delete', '=', 0], ['id', '<>', $params['id']]])->count();
                if ($isset > 0) $this->throwError('改简码已存在，请重新修改');
                $updateData['short_code'] = $params['short_code'];
            }
            if (!empty($params['product_name'])) $updateData['product_name'] = $params['product_name'];
            if (isset($params['is_delete']) && in_array($params['is_delete'], [0, 1])) $updateData['is_delete'] = $params['is_delete'];
            if (!empty($params['warning_value'])) $updateData['warning_value'] = $params['warning_value'];
            $result = Db::name('material')->where(['id' => $params['id']])->update($updateData);
        }
        return $result;
    }

    /**
     * Description:物料列表
     * Author: zrc
     * Date: 2023/2/21
     * Time: 13:25
     * @param $params
     * @return mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function materialList($params)
    {
        $offset  = ($params['page'] - 1) * $params['limit'];
        $where   = [];
        $where[] = ['is_delete', '=', 0];
        if (!empty($params['short_code'])) {
            $where[] = ['short_code', '=', $params['short_code']];
        }
        if (!empty($params['product_name'])) {
            $where[] = ['product_name', 'like', "%{$params['product_name']}%"];
        }
        $totalNum     = Db::name('material')->where($where)->count();
        $lists        = Db::name('material')->where($where)->limit($offset, $params['limit'])->order('id desc')->select()->toArray();
        $admin_id_arr = array_unique(array_column($lists, 'operator'));
        $admin_id_str = implode(',', $admin_id_arr);
        $adminInfo    = httpGet(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info', ['admin_id' => $admin_id_str, 'field' => 'realname']);
        foreach ($lists as $key => $val) {
            $lists[$key]['update_time']  = $val['update_time'] == 0 ? '-' : date('Y-m-d H:i:s', $val['update_time']);
            $lists[$key]['created_time'] = date('Y-m-d H:i:s', $val['created_time']);
            $lists[$key]['operator']     = isset($adminInfo['data'][$val['operator']]) ? $adminInfo['data'][$val['operator']] : '系统';
        }
        $result['list']  = $lists;
        $result['total'] = $totalNum;
        return $result;
    }

    /**
     * Description:物料删除
     * Author: zrc
     * Date: 2023/2/27
     * Time: 17:08
     * @param $params
     * @return bool
     * @throws \think\db\exception\DbException
     */
    public function deleteMaterial($params)
    {
        $idArr = explode(',', $params['ids']);
        foreach ($idArr as &$val) {
            $updateData = ['operator' => $params['admin_id'], 'update_time' => time(), 'is_delete' => 1];
            $result     = Db::name('material')->where(['id' => $val])->update($updateData);
            if (empty($result)) $this->throwError('删除失败!' . '数据ID为' . $val);
        }
        return true;
    }

    /**
     * Description:单据类型转换
     * Author: zrc
     * Date: 2023/11/24
     * Time: 14:17
     * @param $params
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function documentTypeChange($params)
    {
        $push_erp  = false;
        $push_data = [];
        Db::startTrans();
        try {
        $role_id = Db::table('vh_authority.vh_admins_roles')->where(['admin_id' => $params['admin_id'], 'role_id' => 18])->value('role_id');
        if ($role_id != 18) $this->throwError('仅财务可转换单据');
        $orderInfo = Db::name('offline_order')->field('document_type,corp,dingtalk_status,push_t_status')->where(['sub_order_no' => $params['sub_order_no']])->find();
        if (empty($orderInfo)) $this->throwError('未获取到订单信息');
        if ($orderInfo['dingtalk_status'] != 2) $this->throwError('单据未通过审核，不能转换');
//        if ($orderInfo['corp'] != '001') $this->throwError('请选择科技样酒或科技销售单进行操作');
        $updateData['update_time'] = time();
        $content                   = '';
        if ($orderInfo['push_t_status'] == 1) {
            $updateData['push_t_status'] = 0; //未推送
            $push_erp                    = true;
            $push_data                   = ['sub_order_no' => $params['sub_order_no'], 'order_type' => 8];
            if ($orderInfo['document_type'] == 1) {
                //删除销售单
                \Curl::saleOrderDelete([
                    'sub_order_no' => $params['sub_order_no'],
                    "force"        => 1
                ]);
                $push_data['corp'] = '001';
            } else if ($orderInfo['document_type'] == 0) {
                //删除科技样酒单
                \Curl::icOtherOutDelete([
                    'sub_order_no' => $params['sub_order_no'],
                    "corp"         => '001'
                ]);
            }
        }
        if ($orderInfo['document_type'] == 1) {
            if (!isset($params['collection_type'])) $this->throwError('请选择领用出库类型');
            if (!in_array($params['collection_type'], ['013-1', '013-2', '013-4', '013-6', '013-7', '013-8', '013-9', '013-10'])) $this->throwError('领用出库类型错误');
            $updateData['document_type']   = 0;
            $updateData['collection_type'] = $params['collection_type'];
            $collection_type = [
                '013'    => '领用出库',
                '013-1'  => '样酒申请',
                '013-2'  => '客情领用',
                '013-3'  => '物料领用',
                '013-4'  => '酒会领用',
                '013-6'  => '市场活动',
                '013-7'  => '酒庄活动',
                '013-8'  => '培训用酒',
                '013-9'  => '品鉴用酒',
                '013-10'  => '调拨出库',
            ];
            $typeName = $collection_type[$params['collection_type']] ?? '领用出库';
            $updateData['return_warehouse'] = $params['return_warehouse'] ?? '';
            $content = '销售单转科技样酒-' . $typeName;
        } else if ($orderInfo['document_type'] == 0) {
            $updateData['document_type']   = 1;
            $updateData['collection_type'] = '013';
            $updateData['return_warehouse'] = '';
            $content                       = '科技样酒转销售单';
        }
        $updateOrder = Db::name('offline_order')->where(['sub_order_no' => $params['sub_order_no']])->update($updateData);
        if (empty($updateOrder)) $this->throwError('修改订单单据类型失败');
        if (!empty($content)) {
            $remarks      = array(
                'sub_order_no' => $params['sub_order_no'],
                'order_type'   => 8,
                'content'      => '单据类型转换:' . $content,
                'admin_id'     => $params['admin_id']
            );
            $orderService = new OrderService();
            $orderService->createRemarks($remarks);
        }

            if (!Db::name('offline_order')->where([
                'sub_order_no'  => $params['sub_order_no'],
                'document_type' => $updateData['document_type'],
            ])->count()) {
                throw new \Exception('转单更新数据失败');
            }

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::write('修改订单单据类型失败: ' . $e->getMessage() . ' param:' . json_encode($params));
            $this->throwError($e->getMessage());
        }

        if ($push_erp) {
            try {
                $result = (new PushService())->pushTplus($push_data);
            } catch (\Exception $e) {
                Log::write('修改订单单据类型成功后自动推单失败: ' . $e->getMessage() . ' param:' . json_encode($params));
            }
        }
        return true;
    }

    /**
     * @方法描述: 批量获取存货价格本信息
     * @Date 2024/3/4 17:51
     */
    public function checkInvPrice(array $params)
    {
        $itemsInfo = $params['items_info'];
        $shortCodeArr = array_column($itemsInfo, 'short_code');
        $url = env('ITEM.SUPPLYCHAIN_URL') . '/supplychain/v3/inventoryprice/batch';
        $res = $this->httpGet($url, ['inventory_code' => implode(',', $shortCodeArr)]);
        if (!isset($res['error_code']) || $res['error_code'] != 0)
            $this->throwError('查询存货价格本失败'.($res['error_msg'] ?? ''));
        $invPriceList = $res['data']['list'] ?? [];
        if (!$invPriceList) return true;
        $stagePriceList = array_column($invPriceList, 'stage_price_json', 'short_code');
        $onePriceList = array_column($invPriceList, 'one_price', 'short_code');
        $total = $isIncludeDeliveryFee = $checkOnePrice = $isIncludeInvPrice =0;
        foreach ($itemsInfo as $val){
            $total += $val['nums'];
            if (empty($val['is_gift'])){
                // 运费
                if ($val['short_code'] == 'POS-01' || preg_match("/运费/", $val['product_name'])){
                    $isIncludeDeliveryFee = 1;
                }
                $priceSource = $val['priceSource'] ?? 0;
                if ($priceSource == 2){
                    $isIncludeInvPrice = 1;
                    $onePrice = $onePriceList[$val['short_code']] ?? 0;
                    if ($val['price'] > $onePrice)  $checkOnePrice = 1;
                    // 阶梯价
                    $stagePrice = $stagePriceList[$val['short_code']] ?? [];
                    $max = 0;
                    foreach ($stagePrice as $s){
                        if ($val['nums'] >= $s['start']
                            && $val['nums'] <= $s['end']
                            && $val['price'] < $s['price']
                        ){
//                            $this->throwError($val['short_code'].'当前价格低于存货价格本配置的阶梯价格');
                        }
                        if ($s['end'] > $max){
                            $max = $s['end'];
                            $temp = $s;
                        }
                    }
                    // 数量大于阶梯最大设置数量
                    if (!empty($temp) && $val['nums'] > $temp['end'] &&  $val['price'] < $temp['price']){
//                        $this->throwError($val['short_code'].'阶梯价格异常,不能低于阶梯最大瓶数对应价格');
                    }
                }
            }
        }
        // 运费与瓶数验证, 提交审批验证
        $documentType = $params['document_type'] ?? 0;
        $deliveryMode = $params['delivery_mode_code'] ?? '';
        if ($deliveryMode != '0009'
            && $total < 600
            && $isIncludeDeliveryFee < 1
            && $params['type'] != 1
            && $documentType == 1
            && $isIncludeInvPrice
            && $checkOnePrice == 0
            && !in_array($params['department_code'], [19, 14, 1402])
        ){
            // 19-总经办
//            $this->throwError('该销售单总销售瓶数低于600瓶，不满足包邮条件，请添加运费或者提高销售瓶数！');
        }
        return true;
    }
}