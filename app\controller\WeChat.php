<?php


namespace app\controller;


use app\BaseController;
use app\Request;
use app\service\WeChat as WeChatService;

class WeChat extends BaseController
{
    /**
     * Description:新增销售单审批回调处理
     * Author: zrc
     * Date: 2022/10/8
     * Time: 11:15
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function ordinarySaleOrderVerifyCallBack(Request $request)
    {
        $params = $request->param();
        file_put_contents(root_path() . "/runtime/log/" . date('Ym') . "/" . date('Ymd') . 'weChatCallBackLog' . '.log', json_encode($params,JSON_UNESCAPED_UNICODE) . PHP_EOL, FILE_APPEND);
        if (empty($params)) {
            $this->throwError('未获取到参数');
        }
        if ($params['errcode'] != 0) {
            $this->throwError($params['errmsg']);
        }
        $weChatService = new WeChatService();
        $result          = $weChatService->ordinarySaleOrderVerifyCallBack($params);
        return $this->success($result);
    }

    /**
     * Description:预计发货时间修改审批回调处理
     * Author: zrc
     * Date: 2022/10/28
     * Time: 13:30
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function predictTimeVerifyCallBack(Request $request)
    {
        $params = $request->param();
        file_put_contents(root_path() . "/runtime/log/" . date('Ym') . "/" . date('Ymd') . 'weChatCallBackLog' . '.log', json_encode($params,JSON_UNESCAPED_UNICODE) . PHP_EOL, FILE_APPEND);
        if (empty($params)) {
            $this->throwError('未获取到参数');
        }
        if ($params['errcode'] != 0) {
            $this->throwError($params['errmsg']);
        }
        $weChatService = new WeChatService();
        $result          = $weChatService->predictTimeVerifyCallBack($params);
        return $this->success($result);
    }

    /**
     * Description:对公转账-线下转账审批回调处理
     * Author: zrc
     * Date: 2023/8/16
     * Time: 14:05
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function offlineTransferVerifyCallBack(Request $request)
    {
        $params = $request->param();
        file_put_contents(root_path() . "/runtime/log/" . date('Ym') . "/" . date('Ymd') . 'weChatCallBackLog' . '.log', json_encode($params,JSON_UNESCAPED_UNICODE) . PHP_EOL, FILE_APPEND);
        if (empty($params)) {
            $this->throwError('未获取到参数');
        }
        if ($params['errcode'] != 0) {
            $this->throwError($params['errmsg']);
        }
        $weChatService = new WeChatService();
        $result          = $weChatService->offlineTransferVerifyCallBack($params);
        return $this->success($result);
    }


    public function changeStaffCallback(Request $request)
    {
        $params = $request->param();
        file_put_contents(root_path() . "/runtime/log/" . date('Ym') . "/" . date('Ymd') . 'weChatCallBackLog' . '.log', json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL, FILE_APPEND);
        if (empty($params)) {
            $this->throwError('未获取到参数');
        }
        if ($params['errcode'] != 0) {
            $this->throwError($params['errmsg']);
        }
        $weChatService = new WeChatService();
        $result        = $weChatService->changeStaffCallback($params);
        return $this->success($result);
    }
}