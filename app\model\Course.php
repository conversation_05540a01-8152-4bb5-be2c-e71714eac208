<?php


namespace app\model;


use think\facade\Db;
use think\Model;

class Course extends Model
{
    protected $name = 'wine_academy_order';

    protected function getTableName()
    {
        return Db::name($this->name);
    }

    /**
     * Description:修改订单信息
     * Author: zrc
     * Date: 2021/8/25
     * Time: 10:09
     */
    public function updateOrder($data, $where)
    {
        $result = $this->getTableName()->where($where)->update($data);
        return $result;
    }
}