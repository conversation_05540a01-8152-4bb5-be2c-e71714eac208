<?php


namespace app\service\command;


use app\service\PreSales as PreSalesService;
use think\facade\Db;

class SilentCustomAllocationCommand
{
    public function exec()
    {
//        $path   = 'D:\最后一次购买时间在2022年，订单数大于15且总金额大于5000的用户.xls';
//        $startI = 1;
//        #下载文件
//        $local_path = download_image($path, 'xls');
//        #解析文件
//        $data = getExcelData($local_path, $startI);
//        @unlink($local_path);
//        if ($data['error_code'] != 0) {
//            echo 'excel解析失败' . PHP_EOL;
//            return false;
//        }
//        if (count($data['data']) == 0) {
//            echo '未获取到excel内容' . PHP_EOL;
//            return false;
//        }
//        $excelData = $data['data'];
//        unset($excelData[$startI]);
//        shuffle($excelData);
//        foreach ($excelData as &$val) {
//            $data = array(
//                'user_id'       => $val[0],
//                'total_money'   => $val[1],
//                'order_nums'    => $val[2],
//                'work_nums'     => $val[3],
//                'last_buy_time' => strtotime($val[4]),
//                'flash_moeny'   => $val[5],
//                'second_money'  => $val[6],
//                'cross_money'   => $val[7],
//                'tail_money'    => $val[8],
//                'nickname'      => $val[9],
//                'register_time' => strtotime($val[10]),
//                'telephone'     => $val[11],
//                'reg_from'      => $val[12],
//                'consignee'     => $val[13],
//                'region'        => $val[14],
//                'export_time'   => '1690882200',
//            );
//            $id   = Db::name('presales_silent_user')->insertGetId($data);
//            echo $id . PHP_EOL;
//        }
//        echo '执行完成';exit;
        $week = date('w');
        if ($week != 1) {
            echo '今天不是周一，不分配沉默用户' . PHP_EOL;
            return false;
        }
        $presalesAdmin = Db::name('presales_admin')->field('id,admin_id,allocation_nums')->where(['status' => 1])->order('allocation_nums asc')->select()->toArray();
        if (empty($presalesAdmin)) {
            echo '未获取到售前客服' . PHP_EOL;
            return false;
        }
        $time = time();
        try {
            Db::startTrans();
            foreach ($presalesAdmin as &$val) {
                $silentUser = Db::name('presales_silent_user')->where(['status' => 1])->limit(200)->select()->toArray();
                if (!empty($silentUser)) {
                    $insertData = [];
                    foreach ($silentUser as &$v) {
                        $insertData[] = array(
                            'admin_id'       => $val['admin_id'],
                            'type'           => 2,
                            'user_id'        => $v['user_id'],
                            'binding_status' => 1,
                            'created_time'   => $time,
                            'register_time'  => $v['register_time'],
                            'nickname'       => $v['nickname'],
                            'telephone'      => $v['telephone'],
                            'reg_from'       => $v['reg_from'],
                            'region'         => $v['region'],
                            'export_time'    => $v['export_time'],
                            'consignee'      => $v['consignee'],
                            'order_nums'     => $v['order_nums'],
                            'last_buy_time'  => $v['last_buy_time'],
                            'flash_moeny'    => $v['flash_moeny'],
                            'second_money'   => $v['second_money'],
                            'cross_money'    => $v['cross_money'],
                            'tail_money'     => $v['tail_money'],
                            'total_money'    => $v['total_money'],
                            'work_nums'      => $v['work_nums'],
                        );
                        Db::name('presales_silent_user')->where(['id' => $v['id']])->update(['status' => 2]);
                    }
                    $result = Db::name('presales_allocation_user')->insertAll($insertData);
                }
            }
            //发送分配excel到企微
            if (isset($result)) {
                $PreSalesService = new PreSalesService();
                $PreSalesService->sendExcel(['created_time' => $time, 'type' => 2]);
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            echo '分配失败，失败原因：' . $e->getMessage() . PHP_EOL;
            return false;
        }
        echo '执行完成' . PHP_EOL;
        exit;
    }
}