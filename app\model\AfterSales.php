<?php


namespace app\model;


use think\facade\Db;
use think\Model;

class AfterSales extends Model
{
    protected $name = 'order_main';

    protected function getTableName()
    {
        return Db::name($this->name);
    }

    /**
     * Description:售后手动创建订单
     * Author: zrc
     * Date: 2022/4/20
     * Time: 18:21
     * @param $requestparams
     * @return array|bool
     */
    public function createOrder($requestparams)
    {
        $params = $requestparams;
        if (!isset($params['main_order_no'])) {
            $main_order_no = creatOrderNo(env('ORDERS.ORDER_MAIN'), $params['uid']);
        } else {
            $main_order_no = $params['main_order_no'];
        }
        $mainData = array(
            'uid'               => $params['uid'],
            'main_order_no'     => $main_order_no,
            'main_order_status' => isset($params['order_status']) ? $params['order_status'] : 0,
            'payment_method'    => isset($params['payment_method']) ? $params['payment_method'] : -1,
            'payment_amount'    => $params['payment_amount'],
            'cash_amount'    => $params['payment_amount'],
            'province_id'       => $params['province_id'],
            'city_id'           => $params['city_id'],
            'district_id'       => $params['district_id'],
            'address'           => $params['address'],
            'consignee'         => $params['consignee'],
            'consignee_phone'   => $params['consignee_phone'],
            'created_time'      => time(),
            'payment_time'      => isset($params['payment_time']) ? $params['payment_time'] : 0,
            'order_type'        => $params['order_type'],
            'operator'          => $params['operator']
        );
        //换货、补发订单，子订单号统一为VHG+原单号，后续-1-2-3....-lf
        if (isset($params['old_sub_order_no'])) {
            $sub_order_no = $this->getSubOrderNo($params['old_sub_order_no'], $params['order_type']);
        } else {
            $sub_order_no = creatOrderNo(env('ORDERS.ORDER_GD'), $params['uid']);
        }
        $subData = array(
            'uid'               => $params['uid'],
            'sub_order_no'      => $sub_order_no,
            'sub_order_status'  => isset($params['order_status']) ? $params['order_status'] : 0,
            'period'            => $params['period'],
            'package_id'        => $params['package_id'],
            'order_from'        => $params['order_from'],
            'order_qty'         => $params['order_qty'],
            'payment_amount'    => $params['payment_amount'],
            'cash_amount'    => $params['payment_amount'],
            'express_fee'       => isset($params['express_fee']) ? $params['express_fee'] : 0,
            'express_type'      => $params['express_type'],
            'is_ts'             => isset($params['is_ts']) ? $params['is_ts'] : 0,
            'is_virtual'        => isset($params['is_virtual']) ? $params['is_virtual'] : 0,
            'created_time'      => time(),
            'payment_time'      => isset($params['payment_time']) ? $params['payment_time'] : 0,
            'push_t_status'     => isset($params['push_t_status']) ? $params['push_t_status'] : 0,
            'order_type'        => $params['order_type'],
            'express_coupon_id' => '',
            'predict_time'      => isset($params['predict_time']) ? $params['predict_time'] : 0,
            'related_order_no'  => isset($params['related_order_no']) ? $params['related_order_no'] : '',
            'operator'          => $params['operator'],
            'work_order_id'     => isset($params['work_order_id']) ? $params['work_order_id'] : 0,
            'warehouse_code'    => isset($params['warehouse_code']) ? $params['warehouse_code'] : 0,
        );
        if ($params['order_type'] == 2) {
            $subData['realname']          = $params['realname'];
            $subData['id_card_no']        = $params['id_card_no'];
            $subData['payment_doc']       = isset($params['payment_doc']) ? $params['payment_doc'] : 0;
            $subData['push_store_status'] = isset($params['push_store_status']) ? $params['push_store_status'] : 0;
        } else if ($params['order_type'] == 9) {
            $subData['product_channel']     = $params['product_channel'];
            $subData['delivery_method']     = $params['delivery_method'];
            $subData['delivery_store_id']   = $params['delivery_store_id'];
            $subData['delivery_store_name'] = $params['delivery_store_name'];
            $subData['merchant_id']         = $params['merchant_id'];
            $subData['push_wms_status']     = 3;
        } else {
            $subData['push_wms_status']        = isset($params['push_wms_status']) ? $params['push_wms_status'] : 0;
            $subData['preferential_reduction'] = isset($params['preferential_reduction']) ? $params['preferential_reduction'] : 0;
            $subData['is_original_package']    = isset($params['is_original_package']) ? $params['is_original_package'] : 0;
        }
        Db::startTrans();
        try {
            $main_order_id = Db::name('order_main')->insertGetId($mainData);
            if (empty($main_order_id)) {
                Db::rollback();
                return false;
            }
            $subData['main_order_id'] = $main_order_id;
            $order_type               = config('config')['order_type'];//订单频道获取
            $addSubOrder              = Db::name($order_type[intval($params['order_type'])]['table'])->insert($subData);
            if (empty($addSubOrder)) {
                Db::rollback();
                return false;
            }

            // 用户属性
            $user_info = getUserInfoByUids($params['uid'], 'uid,user_attribute');
            $user_info = $user_info[$params['uid']] ?? [];

            // if (isset($params['erp_push_amount'])) {
            $extendData = array(
                'sub_order_no'    => $sub_order_no,
                'order_type'      => $params['order_type'],
                //ERP推单金额处理
                'erp_push_amount' => !empty($params['erp_push_amount']) ? $params['erp_push_amount'] : 0,
                //优化属性
                'user_attribute'  => !empty($user_info['user_attribute']) ? intval($user_info['user_attribute']) : 0,
                'created_time'    => time(),
            );
            Db::name('sub_order_extend')->insert($extendData);
            // }
            Db::commit();
            return ['main_order_no' => $main_order_no, 'sub_order_no' => $sub_order_no];
        } catch (\Exception $e) {
            Db::rollback();
            return false;
        }
    }

    /**
     * Description:换货补发生成子订单号
     * Author: zrc
     * Date: 2022/7/25
     * Time: 14:56
     * @param $old_sub_order_no
     * @param $order_type
     * @param int $str
     * @return string
     */
    public function getSubOrderNo($old_sub_order_no, $order_type, $str = 0)
    {
        $sub_order_no_str = substr($old_sub_order_no, 3);
        if ($str < 0) {
            $sub_order_no = env('ORDERS.ORDER_GD') . $sub_order_no_str . $str;
        } else {
            $sub_order_no = env('ORDERS.ORDER_GD') . $sub_order_no_str;
        }
        $cofig_order_type = config('config')['order_type'];//订单频道获取
        $num              = Db::name($cofig_order_type[intval($order_type)]['table'])->where(['sub_order_no' => $sub_order_no])->count();
        if ($num >= 1) {
            $sub_order_no = $this->getSubOrderNo($old_sub_order_no, $order_type, $str - 1);
        }
        return $sub_order_no;
    }
}