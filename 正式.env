APP_DEBUG = true
#图片地址
ALIURL = "https://images.wineyun.com"
[ITEM]
#队列地址
QUEUE_URL = "https://callback.vinehoo.com/queueservice/services/v3/queue/push"
#正式队列地址
QUEUE_URL_BAK = "https://callback.vinehoo.com/queueservice/services/v3/queue/push"
#正式解密地址
CRYPTION_ADDRESS = "https://callback.vinehoo.com/des-server"
#优惠券模块
COUPON_URL = "https://callback.vinehoo.com/coupon"
#营销模块
MARKET_URL = "https://callback.vinehoo.com/marketing"
#营销配置模块
MARKET_CONF_URL = "https://callback.vinehoo.com/marketing-conf"
#商品模块
COMMODITIES_URL = "https://callback.vinehoo.com/commodities"
#磐石系统
WINE_WIKI_URL= "https://callback.vinehoo.com/wine-wiki"
#酒会模块
WINEPARTY_URL = "https://callback.vinehoo.com/wineparty"
#用户模块
USER_URL = "https://callback.vinehoo.com/user"
#支付模块
PAYMENT_URL = "https://callback.vinehoo.com/payment"
#vinehoo库存扣减退换服务
VINEHOO_INVENTORY_INOUT_URL="https://callback.vinehoo.com/vinehoo-inventory-inout"
#vinehoo订单金额计算
CALC-ORDERS_PRICE="https://callback.vinehoo.com/calc-orders-price"
#萌牙分发
DISTRIBUTE_URL = "https://wms-test.wineyun.com/distribute"
#发票模块
INVOICE_URL = "http://test-wine.wineyun.com/invoice"
#GO发票模块
GO_INVOICE_URL = "http://test-wine.wineyun.com/go-invoice"
#工单模块
WORK_URL = "http://test-wine.wineyun.com/work"
#满赠模块
FULLGIFT_URL = "http://test-wine.wineyun.com/fullgift"
#推送萌芽
PUSH_ORDERS_URL = "https://callback.vinehoo.com/pushorders"
#推送T+
PUSH_T_PLUS_URL = "https://callback.vinehoo.com/push-t-plus"
#权限模块
AUTHORITY_URL = "https://callback.vinehoo.com/authority"
#订单模块
ORDERS_URL = "http://test-wine.wineyun.com/orders"
#超时任务服务
TIMEING_SERVICE_URL= "https://callback.vinehoo.com/go-timing-service"
#微信授权
WECHART_URL_BAK="https://callback.vinehoo.com/go-wechat"
WECHART_URL="https://callback.vinehoo.com/wechat"
#钉钉审批
DINGTALK_APPROVAL_URL = "https://callback.vinehoo.com/dingtalk-system-notice"
#app推送
APPPUSH_URL = https://callback.vinehoo.com/app-push
#专题活动模块
ACTIVITIES_MANAGEMENT_URL= "http://test-wine.wineyun.com/activities-management"
#分布数据查询模块
MYSQL_BATCH_SEARCH = "https://callback.vinehoo.com/mysql-batch-search"
#商家秒发模块
VMALL_URL="https://callback.vinehoo.com/vmall"
#商家秒发库存模块
VMALL_STOCK_URL="https://callback.vinehoo.com/vmall-stockchange"
#短信模块
SMS_URL = "https://callback.vinehoo.com/sms"
#制单人管理模块
ERP_PREPARED_URL = "https://callback.vinehoo.com/erp-prepared"
#erp模块
ERP_URL = "https://callback.vinehoo.com/erp"
#拍卖订单模块
AUCTION_ORDERS_URL = "http://test-wine.wineyun.com/auction-order"
#订单微服务模块
ORDERS_MICRO_SERVICE_URL = "https://callback.vinehoo.com/orders-micro-service"


[ORDERS]
#支付倒计时-300秒
pay_time_out = "60"
#拍卖订单超时时间
AUCTION_ORDER_TIMEOUT = "600"
AUCTION_ORDER_TIMEOUT_BAK = "172800"
#代付倒计时-1800秒
replace_pay_time_out = "1800"
#代付超时时间-30分
replace_pay_time_out_m = "30m"
#银联验签key
key = "rRfKYjr7AFiykDnfBmeitWnTa6t2iQb74MBKpcH4eMGnnce3"
#基准快递费收取标准：小于99元收取9元快递费
MIN_MONEY=99
COURIER_FEE=9
#订单号前缀
ORDER_MAIN=VHM
ORDER_SON=VHS
ORDER_GD=VHG
WINEPARTY=VHP
RABBIT=VHR
COURCE=VHC
REFUND=REF
EARNEST=VHE
#萌芽实体仓编号
STORE_CODE = "xGpS0mPbNqo5FMQcdTk4nR1DJr9LUEfa"
#自动确认收货时间-15天
receipt_time = "15"
#暂存费用计算
free_ts_day = "30"
ts_rate = "0.01"
#拼团分享链接
group_share_url = "http://test-wine.wineyun.com"
#跨境南沙仓测试环境地址
nan_sha_url = "http://************/ocp/rest"
#跨境南沙仓正式环境地址
nan_sha_url_BAK = "https://service.etopideal.com/ocp2/rest"
#保证金金额
EARNEST_MONEY = "0.01"

#跨境银联申报支付单测试服配置项
#支付单申报请求地址
declare_url = "https://cb-test.chinapay.com/overseasPay/B2cCustoms"
Version = ********
MerId = ***************
TranType = 0008
ProductType = ********
BusiType = 0001
CurryNo = CNY
BankInstNo = ***************
#支付单申报异步回调地址
MerBgUrl = "http://test-wine.wineyun.com/orders/orders/v3/push/declareNotify"
BillMode = 1
CertType = 01
#支付单申报查询地址
QueryUrl = "https://cb-test.chinapay.com/overseasQuery/CustomsQuery"
#交易证书
pfx = 13.pfx
#商户号密码
password = VineHoo_001
#公钥
cer = cp_test.cer

#售后需支付订单超时时间
AFTER_SALES_ORDER_TIMEOUT = "1m"
#订单发货微信模板配置
WEIXINTEMPLATE_CURLOPT_URL = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send"
WEIXINTEMPLATE_TEMPLATE_ID = "0tiG9uQEuyT4OqvLl5Z6_tJ2P58dyxin_zyOzQBLhuY"
WEIXINTEMPLATE_REMARK = "您的快递已发货，点击进入订单详情查看"
#小程序订阅消息配置
MINIPROGRAM_STATE = "developer"
MINIPROGRAM_STATE_BAk = "formal"

#超时订单支付异步回调审批ID
time_out_order_create_verify_code_bak = "3WKi3vzovEDk8jDPGJCNNoi8k8iFhHovHQ3fT2WJ"
time_out_order_create_verify_code = "C4RaAzRksy9s3tkpkQ7TuNZU13F1uQ4bQVVFtvkzB"
#保证金订单超时时间
EARNEST_MONEY_TIMEOUT = "300"
#订单价格异常机器人token
order_price_unusual_token = "8dd3dcd8-3820-4af9-9ce6-597a314c3698"
order_price_unusual_token_bck = "5c0f6e6a-8cbe-44c9-8909-fb4b5e530b79"
#跨境小助手机器人token
cross_token = "e2c9c71c-5710-4b8f-8126-111d8daa8e52"
cross_token_bck = "25377ece-f484-4f4e-9e2d-8fbe4318166b"
#科技新增销售单审批ID
ordinary_sale_order_verify_id_001_bak = "C4RambRN6SxGxNLCdQdRw6TA3e7WaA6Fm3cajFqnM"
ordinary_sale_order_verify_id_001 = "C4RcZo7kMy79Z8zhtfrMCwwxBaptTeZ2f4eHhgwFQ"
#电子新增销售单审批ID
ordinary_sale_order_verify_id_002_bak = "3WKibAXdjasfvYBiXpLhmwKqYBsix3J8aCR37PbU"
ordinary_sale_order_verify_id_002 = "C4RcZo7kMy79Z8zhtfrMCwwxBaptTeZ2f4eHhgwFQ"
#科技样酒销售单审批ID
sample_liquor_verify_id_001 = "C4UCJJKGVPqEAUL7v72CSdJ1m9YUjaZGAdbYJ6rtE"
#中台制单客户名称
CUSTOMER = "酒云研酒所-重庆点零售,佰酿美誉,佰酿美誉贸易（重庆）有限公司,MKT-酒云教育,深圳蜜思园酒业有限公司,猿小姐的甜水铺,吉顺（重庆）,佰酿美酒APP,佰酿云酒（重庆）科技有限公司,酒云研酒所,酒云研酒所-美团订单,酒云研酒所-饿了么,快团团-酒云网VINEHOO,其他"
#未发货提醒审批
un_ship_verify_id = "C4UAW6Nc8PaH8K6EkT2aXtoh3R1LREezRbv45eG58"
un_ship_verify_id_bak = "3WLJ7Cae1snwPm6sP9KJUHP5MLGc86hyJ2gYK3gv"
#测试 销售退货审批id
ordinary_sales_return_order_verify_id = "C4RdAGD8qzkGzt9tytRZLmTkKwN6K93bUHEgUYEPy"
#正式 销售退货审批id
#ordinary_sales_return_order_verify_id = "3WKibHeUnMX5TjiY6h96f3x6gUVLTTE3JmxeuHfD"
CUSTOMER = "测试,test"
#销售退货导出审批id
ordinary_sales_return_order_export_verify_id = 3WLJP4avjLPi7U66tH7pgrTDxtC39jXhebnGm1w5

[APP]
DEFAULT_TIMEZONE = Asia/Shanghai

[LANG]
default_lang = zh-cn

[NACOS]
URL = "http://*************:8848/nacos/v1/cs/configs"
TENANT = "d9fd09cf-6569-4a2d-a623-5df9999dd91a"
USERNAME = "nacos"
PASSWORD = "vinehoo666"

[DATABASE_ORDERS_BAK]
TYPE = mysql
HOSTNAME = *************
DATABASE = vh_orders
USERNAME = vinehoodev
PASSWORD = vinehoo123
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = true

[DATABASE_ORDERS]
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhduwo.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_orders
USERNAME = wy_zrc
PASSWORD = KnwpClg8PkrpBnp1
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = true

[ES_BAK]
HOST = *************
PORT = 9200
USER = elastic
PASS = vinehoo666
PREFIX = "vinehoo."

[ES]
HOST = es-cn-7mz2retry0008zrzt.public.elasticsearch.aliyuncs.com
PORT = 9200
USER = elastic
PASS = BYI8uuJGQuo45smj
PREFIX = "vinehoo."

[CACHE]
DRIVER = redis
HOST = *************
PASSWORD = vh@123
PORT = 6379
prefix = vinehoo.
db = 1


