<?php

namespace app\controller;

use app\BaseController;
use app\ErrorCode;
use app\Request;
use app\service\PaymentStatistics as PaymentStatisticsService;
use app\model\DailyPaymentStatistics;
use think\facade\Validate;

class PaymentStatistics extends BaseController
{
    /**
     * 写入收款/退款数据接口
     * @param Request $request
     * @return \think\response\Json
     */
    public function write(Request $request)
    {
        $params = $request->param();
        
        // 参数验证
        $validate = Validate::rule([
            'company_code|公司编码' => 'require|max:10',
            'operation_type|操作类型' => 'require|in:1,2', // 1:收款 2:退款
            'amount|金额' => 'require|float|gt:0'
        ]);
        
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        
        // 验证公司编码是否有效
        if (!DailyPaymentStatistics::isValidCompanyCode($params['company_code'])) {
            $this->throwError('无效的公司编码', ErrorCode::PARAM_ERROR);
        }
        
        $service = new PaymentStatisticsService();
        $result = $service->writePaymentData(
            $params['company_code'],
            (int)$params['operation_type'],
            (float)$params['amount']
        );
        
        return $this->success($result, '数据写入成功');
    }
    
    /**
     * 获取当日数据接口
     * @param Request $request
     * @return \think\response\Json
     */
    public function today(Request $request)
    {
        $params = $request->param();
        $companyCode = $params['company_code'] ?? null;
        
        // 如果指定了公司编码，验证其有效性
        if ($companyCode && !DailyPaymentStatistics::isValidCompanyCode($companyCode)) {
            $this->throwError('无效的公司编码', ErrorCode::PARAM_ERROR);
        }
        
        $service = new PaymentStatisticsService();
        $data = $service->getTodayData($companyCode);
        
        return $this->success($data);
    }
    
    /**
     * 获取历史数据列表接口
     * @param Request $request
     * @return \think\response\Json
     */
    public function list(Request $request)
    {
        $params = $request->param();
        
        // 参数验证
        $validate = Validate::rule([
            'page|页码' => 'integer|egt:1',
            'limit|每页数量' => 'integer|between:1,100',
            'company_code|公司编码' => 'max:10',
            'start_date|开始日期' => 'date',
            'end_date|结束日期' => 'date'
        ]);
        
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        
        // 如果指定了公司编码，验证其有效性
        if (!empty($params['company_code']) && !DailyPaymentStatistics::isValidCompanyCode($params['company_code'])) {
            $this->throwError('无效的公司编码', ErrorCode::PARAM_ERROR);
        }
        
        $service = new PaymentStatisticsService();
        $data = $service->getHistoryList($params);
        
        return $this->success($data);
    }
    
    /**
     * 手动同步数据接口（管理员使用）
     * @param Request $request
     * @return \think\response\Json
     */
    public function syncData(Request $request)
    {
        $params = $request->param();
        $date = $params['date'] ?? null;
        
        // 如果指定了日期，验证格式
        if ($date && !preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
            $this->throwError('日期格式错误，请使用 Y-m-d 格式', ErrorCode::PARAM_ERROR);
        }
        
        $service = new PaymentStatisticsService();
        $result = $service->syncRedisToDatabase($date);
        
        return $this->success($result, '数据同步成功');
    }
    
    /**
     * 获取公司列表接口
     * @return \think\response\Json
     */
    public function companies()
    {
        $companies = [];
        foreach (DailyPaymentStatistics::COMPANY_MAP as $code => $name) {
            $companies[] = [
                'code' => $code,
                'name' => $name
            ];
        }
        
        return $this->success($companies);
    }
}
