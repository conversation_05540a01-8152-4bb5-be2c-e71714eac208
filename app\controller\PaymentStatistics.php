<?php

namespace app\controller;

use app\BaseController;
use app\ErrorCode;
use app\Request;
use app\service\PaymentStatistics as PaymentStatisticsService;
use app\model\DailyPaymentStatistics;
use think\facade\Validate;

class PaymentStatistics extends BaseController
{
    /**
     * 写入收款/退款数据接口（优化版本）
     * @param Request $request
     * @return \think\response\Json
     */
    public function write(Request $request)
    {
        $params = $request->param();

        // 统一参数预处理
        $params = $this->preprocessParams($params);

        // 根据参数类型路由到不同的处理方法
        $handler = $this->getRequestHandler($params);

        try {
            $service = new PaymentStatisticsService();
            $result = $handler($service, $params);

            return $this->success($result['data'], $result['message']);

        } catch (\Exception $e) {
            // 统一错误处理和日志记录
            $this->logError($e, $params);
            $this->throwError($e->getMessage(), ErrorCode::BUSINESS_ERROR);
        }
    }

    /**
     * 参数预处理
     * @param array $params
     * @return array
     */
    private function preprocessParams($params)
    {
        // 去除空白字符
        array_walk_recursive($params, function(&$value) {
            if (is_string($value)) {
                $value = trim($value);
            }
        });

        // 金额格式化
        if (isset($params['amount'])) {
            $params['amount'] = round((float)$params['amount'], 2);
        }

        return $params;
    }

    /**
     * 获取请求处理器
     * @param array $params
     * @return callable
     */
    private function getRequestHandler($params)
    {
        // 新版本收款接口
        if (isset($params['main_order_no']) && isset($params['amount']) && !isset($params['refund_no'])) {
            return function($service, $params) {
                $this->validatePaymentParams($params);
                $result = $service->writePaymentDataV2($params['main_order_no'], $params['amount']);
                return ['data' => $result, 'message' => '收款数据写入成功'];
            };
        }

        // 新版本退款接口
        if (isset($params['refund_no']) && isset($params['amount'])) {
            return function($service, $params) {
                $this->validateRefundParams($params);
                $result = $service->writeRefundDataV2($params['refund_no'], $params['amount']);
                return ['data' => $result, 'message' => '退款数据写入成功'];
            };
        }

        // 旧版本接口
        return function($service, $params) {
            $this->validateLegacyParams($params);
            $result = $service->writePaymentData(
                $params['company_code'],
                (int)$params['operation_type'],
                $params['amount']
            );
            return ['data' => $result, 'message' => '数据写入成功'];
        };
    }

    /**
     * 验证收款参数
     * @param array $params
     */
    private function validatePaymentParams($params)
    {
        $validate = Validate::rule([
            'main_order_no|主订单号' => 'require|max:32|regex:/^VHM\d+$/',
            'amount|金额' => 'require|float|gt:0|lt:1000000'
        ]);

        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
    }

    /**
     * 验证退款参数
     * @param array $params
     */
    private function validateRefundParams($params)
    {
        $validate = Validate::rule([
            'refund_no|退款单号' => 'require|max:36|regex:/^(GD|RF)\w+$/',
            'amount|金额' => 'require|float|gt:0|lt:1000000'
        ]);

        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
    }

    /**
     * 验证旧版本参数
     * @param array $params
     */
    private function validateLegacyParams($params)
    {
        $validate = Validate::rule([
            'company_code|公司编码' => 'require|max:10|in:001,002,008,032',
            'operation_type|操作类型' => 'require|in:1,2',
            'amount|金额' => 'require|float|gt:0|lt:1000000'
        ]);

        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }

        if (!DailyPaymentStatistics::isValidCompanyCode($params['company_code'])) {
            $this->throwError('无效的公司编码', ErrorCode::PARAM_ERROR);
        }
    }

    /**
     * 记录错误日志
     * @param \Exception $e
     * @param array $params
     */
    private function logError($e, $params)
    {
        $logData = [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'params' => $params,
            'trace' => $e->getTraceAsString()
        ];

        \think\facade\Log::error('PaymentStatistics Error: ' . json_encode($logData, JSON_UNESCAPED_UNICODE));
    }
    
    /**
     * 获取当日数据接口
     * @param Request $request
     * @return \think\response\Json
     */
    public function today(Request $request)
    {
        $params = $request->param();
        $companyCode = $params['company_code'] ?? null;
        
        // 如果指定了公司编码，验证其有效性
        if ($companyCode && !DailyPaymentStatistics::isValidCompanyCode($companyCode)) {
            $this->throwError('无效的公司编码', ErrorCode::PARAM_ERROR);
        }
        
        $service = new PaymentStatisticsService();
        $data = $service->getTodayData($companyCode);
        
        return $this->success($data);
    }
    
    /**
     * 获取历史数据列表接口
     * @param Request $request
     * @return \think\response\Json
     */
    public function list(Request $request)
    {
        $params = $request->param();
        
        // 参数验证
        $validate = Validate::rule([
            'page|页码' => 'integer|egt:1',
            'limit|每页数量' => 'integer|between:1,100',
            'company_code|公司编码' => 'max:10',
            'start_date|开始日期' => 'date',
            'end_date|结束日期' => 'date'
        ]);
        
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        
        // 如果指定了公司编码，验证其有效性
        if (!empty($params['company_code']) && !DailyPaymentStatistics::isValidCompanyCode($params['company_code'])) {
            $this->throwError('无效的公司编码', ErrorCode::PARAM_ERROR);
        }
        
        $service = new PaymentStatisticsService();
        $data = $service->getHistoryList($params);
        
        return $this->success($data);
    }
    
    /**
     * 手动同步数据接口（管理员使用）
     * @param Request $request
     * @return \think\response\Json
     */
    public function syncData(Request $request)
    {
        $params = $request->param();
        $date = $params['date'] ?? null;
        
        // 如果指定了日期，验证格式
        if ($date && !preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
            $this->throwError('日期格式错误，请使用 Y-m-d 格式', ErrorCode::PARAM_ERROR);
        }
        
        $service = new PaymentStatisticsService();
        $result = $service->syncRedisToDatabase($date);
        
        return $this->success($result, '数据同步成功');
    }
    
    /**
     * 获取公司列表接口
     * @return \think\response\Json
     */
    public function companies()
    {
        $companies = [];
        foreach (DailyPaymentStatistics::COMPANY_MAP as $code => $name) {
            $companies[] = [
                'code' => $code,
                'name' => $name
            ];
        }
        
        return $this->success($companies);
    }
}
