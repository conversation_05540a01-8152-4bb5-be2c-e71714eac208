<?php

namespace app\model;

use think\Model;

class PaymentStatisticsLog extends Model
{
    protected $table = 'vh_payment_statistics_log';
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_time';
    protected $updateTime = 'updated_time';
    
    /**
     * 检查是否已经处理过该记录
     * @param string $orderNo 主订单号或退款单号
     * @param int $merchantId 收款商户ID
     * @param int $operationType 操作类型 1:收款 2:退款
     * @return bool
     */
    public static function isProcessed($orderNo, $merchantId, $operationType)
    {
        return self::where([
            'order_no' => $orderNo,
            'merchant_id' => $merchantId,
            'operation_type' => $operationType
        ])->count() > 0;
    }
    
    /**
     * 记录处理日志
     * @param string $orderNo 主订单号或退款单号
     * @param int $merchantId 收款商户ID
     * @param int $operationType 操作类型 1:收款 2:退款
     * @param float $amount 金额
     * @return bool
     */
    public static function recordProcess($orderNo, $merchantId, $operationType, $amount)
    {
        return self::create([
            'order_no' => $orderNo,
            'merchant_id' => $merchantId,
            'operation_type' => $operationType,
            'amount' => $amount
        ]);
    }
}
