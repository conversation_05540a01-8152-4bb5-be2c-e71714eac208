<?php


namespace app\controller;


use app\BaseController;
use app\Request;
use app\service\DingTalk as DingTalkService;

class DingTalk extends BaseController
{
    /**
     * Description:退款审批回调处理(已弃用)
     * Author: zrc
     * Date: 2022/4/6
     * Time: 17:13
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function refundCallBack(Request $request)
    {
        $params = $request->param();
        if (empty($params)) {
            $this->throwError('未获取到参数');
        }
        if ($params['errcode'] != 0) {
            $this->throwError($params['errmsg']);
        }
        $dingTalkService = new DingTalkService();
        $result          = $dingTalkService->refundCallBack($params);
        return $this->success($result);
    }

    /**
     * Description:换货审批回调处理(已弃用)
     * Author: zrc
     * Date: 2022/4/6
     * Time: 17:13
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function changeGoodsCallBack(Request $request)
    {
        $params = $request->param();
        if (empty($params)) {
            $this->throwError('未获取到参数');
        }
        if ($params['errcode'] != 0) {
            $this->throwError($params['errmsg']);
        }
        $dingTalkService = new DingTalkService();
        $result          = $dingTalkService->changeGoodsCallBack($params);
        return $this->success($result);
    }

    /**
     * Description:样酒审批回调处理
     * Author: zrc
     * Date: 2022/5/26
     * Time: 16:35
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function offlineVerifyCallBack(Request $request)
    {
        $params = $request->param();
        file_put_contents(root_path() . "/runtime/log/" . date('Ym') . "/" . date('Ymd') . 'dingTalkCallBackLog' . '.log', json_encode($params,JSON_UNESCAPED_UNICODE) . PHP_EOL, FILE_APPEND);
        if (empty($params)) {
            $this->throwError('未获取到参数');
        }
        if ($params['errcode'] != 0) {
            $this->throwError($params['errmsg']);
        }
        $dingTalkService = new DingTalkService();
        $result          = $dingTalkService->offlineVerifyCallBack($params);
        return $this->success($result);
    }

    /**
     * Description:超时订单退款审批回调处理
     * Author: zrc
     * Date: 2022/9/26
     * Time: 9:28
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function timeOutOrderRefundCallBack(Request $request)
    {
        $params = $request->param();
        if (empty($params)) {
            $this->throwError('未获取到参数');
        }
        if ($params['errcode'] != 0) {
            $this->throwError($params['errmsg']);
        }
        $dingTalkService = new DingTalkService();
        $result          = $dingTalkService->timeOutOrderRefundCallBack($params);
        return $this->success($result);
    }
}