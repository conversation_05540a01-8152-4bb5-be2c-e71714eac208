-- 创建收款退款统计日志表
CREATE TABLE `vh_payment_statistics_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_no` varchar(36) NOT NULL COMMENT '订单号（主订单号或退款单号）',
  `merchant_id` int(10) NOT NULL COMMENT '收款商户ID',
  `operation_type` tinyint(1) NOT NULL COMMENT '操作类型：1-收款，2-退款',
  `amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '金额',
  `created_time` int(11) NOT NULL COMMENT '创建时间',
  `updated_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_merchant_type` (`order_no`,`merchant_id`,`operation_type`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收款退款统计日志表';
