<?php
declare (strict_types = 1);

namespace app\command;

use app\service\Order;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;

class CancelTs extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('cancelTs')
            ->setDescription('the cancelTs command');
    }

    protected function execute(Input $input, Output $output)
    {
        (new Order())->cancelTs([]);
        $output->writeln('success');
    }
}
