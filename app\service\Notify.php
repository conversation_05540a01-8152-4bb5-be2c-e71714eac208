<?php


namespace app\service;

use app\BaseService;
use app\model\OrderMain;
use app\service\Additional as AdditionalService;
use app\service\DingTalk as DingTalkService;
use app\service\elasticsearch\ElasticSearchService;
use app\service\es\Es;
use app\service\Order as OrderService;
use app\service\OrderInvoice as OrderInvoiceService;
use app\service\Push as PushService;
use think\Exception;
use think\facade\Db;
use think\facade\Log;

class Notify extends BaseService
{
    /**
     * Description:支付异步回调
     * Author: zrc
     * Date: 2022/11/23
     * Time: 13:34
     * @param $requestparams
     * @return bool|string
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function notify($requestparams)
    {
        //日志记录
        $this->notifyLog('回调日志：' . $requestparams);
        $notify_type = 1;//默认银联
        if(is_string($requestparams) && strpos( $requestparams,'callbackType') !== false){
            $params = json_decode($requestparams,true);
        }elseif(is_string($requestparams) && strpos( $requestparams,'p_notify_type') !== false){
            $params = json_decode($requestparams, true);
            $notify_type = $params['p_notify_type'];
            $main_order_no = $params['main_order_no'];
        }else{
            parse_str($requestparams, $params);
        }
        if (!isset($params['billNo']) && !isset($params['merOrderId']) && !isset($params['trade_status']) && !isset($params['callbackType']) && !isset($params['p_notify_type'])) {
            $request_xml = simplexml_load_string($requestparams, 'SimpleXmlElement', LIBXML_NOCDATA);//从字符串获取simpleXML对象
            $params      = json_decode(json_encode($request_xml), true);
        }
        if (!empty($params) && isset($params['trade_status'])) $notify_type = 2;//支付宝
        if (!empty($params) && isset($params['result_code'])) $notify_type = 3;//微信
        if (!empty($params) && isset($params['is_offline_transfer'])) $notify_type = 4;//对公转账-线下转账
        if (!empty($params) && isset($params['callbackType']) && $params['callbackType'] =='COMBINED_TRADE_SUCCESS') $notify_type = 5;//华为支付
        try {
            switch ($notify_type) {
                case 1:
                    //获取主订单号
                    if (isset($params['billNo'])) {//pc
                        $main_order_no = substr($params['billNo'], strripos($params['billNo'], "VH"));
                        $billPayment   = json_decode($params['billPayment'], true);
                        if ($billPayment['status'] != 'TRADE_SUCCESS') $this->throwError('回调状态异常');
                    } else if (isset($params['merOrderId'])) {
                        $main_order_no = substr($params['merOrderId'], strripos($params['merOrderId'], "VH"));
                        if ($params['status'] != 'TRADE_SUCCESS') $this->throwError('回调状态异常');
                    }
                    //判断订单类型
                    if (strpos($main_order_no, 'VHP') !== false) {//酒会
                        $result = httpPostString(env('ITEM.WINEPARTY_URL') . '/wineparty/v3/order/notifyDeal', json_encode(['main_order_no' => $main_order_no, 'params' => $params]));
                        if ($result['error_code'] != 0) {
                            //日志记录
                            $this->notifyLog($main_order_no . '异步回调失败：' . $result['error_msg']);
                            return 'FAILED';
                        }
                        return 'SUCCESS';
                    } elseif (strpos($main_order_no, 'VHL') !== false) {//订单
                        $result = httpPostString(env('ITEM.USER_URL') . '/user/v3/giftCards/orderNotify', json_encode([
                            'order_no'    => $main_order_no,
                            'notify_type' => $notify_type,
                            'params'      => $params
                        ]));
                        if ($result['error_code'] != 0) {
                            //日志记录
                            $this->notifyLog($main_order_no . '异步回调失败：' . $result['error_msg']);
                            return 'FAILED';
                        }
                        return 'SUCCESS';
                    }
                    $sign = $params['sign'];
                    unset($params['sign']);
                    if (isset($params['merOrderId']) && strpos($params['merOrderId'], '35V9') !== false) {//对公转账验签
                        if (sign($params, env('ORDERS.upg_key')) != $sign) $this->throwError('验签失败');
                    } else {
                        if (sign($params, env('ORDERS.key')) != $sign) $this->throwError('验签失败');
                    }
                    if(isset($params['billNo']) && empty($params['merOrderId'])){
                        $params['merOrderId'] = $params['billNo'];
                    }

                    $up_payment_method = substr($params['merOrderId'],4,1); //$orderMain['payment_method']
                    break;
                case 2:
                    $main_order_no         = $params['out_trade_no'];
                    $params['totalAmount'] = bcmul($params['total_amount'], 100, 2);
                    $params['seqId']       = $params['trade_no'];
                    $signData              = array(
                        'method'    => 'alipay',
                        'data'      => $requestparams,
                        'is_refund' => false,
                    );
                    $checkSign             = $this->httpPost(env('ITEM.PAYMENT_URL') . '/payment/v3/weAndAli/notify', $signData);
                    if (!isset($checkSign['error_code']) || $checkSign['error_code'] != 0) $this->throwError('验签失败');
                    if ($params['trade_status'] != 'TRADE_SUCCESS') $this->throwError('回调状态异常');
                    $up_payment_method = 0;//支付宝
                    break;
                case 3:
                    $main_order_no         = $params['out_trade_no'];
                    $params['totalAmount'] = $params['total_fee'];
                    $params['seqId']       = $params['transaction_id'];
                    $signData              = array(
                        'method'    => 'wechat',
                        'data'      => $requestparams,
                        'is_refund' => false,
                    );
                    $checkSign             = httpPostString(env('ITEM.PAYMENT_URL') . '/payment/v3/weAndAli/notify', json_encode($signData));
                    if (!isset($checkSign['error_code']) || $checkSign['error_code'] != 0) $this->throwError('验签失败');
                    if ($params['result_code'] != 'SUCCESS') $this->throwError('回调状态异常');
                    if (strpos($main_order_no, 'VHP') !== false) {//酒会
                        $result = httpPostString(env('ITEM.WINEPARTY_URL') . '/wineparty/v3/order/notifyDeal', json_encode(['main_order_no' => $main_order_no, 'params' => $params]));
                        if ($result['error_code'] != 0) {
                            //日志记录
                            $this->notifyLog($main_order_no . '异步回调失败：' . $result['error_msg']);
                            return 'FAILED';
                        }
                        return 'SUCCESS';
                    }
                    if (!empty($params['trade_type']) && $params['trade_type'] == 'JSAPI') {
                        $up_payment_method = 4;//微信小程序
                    } else {
                        $up_payment_method = 3;//微信APP
                    }
                    break;
                case 4:
                    $main_order_no = $params['merOrderId'];
                    break;
                case 5:
                    $main_order_no         = $params['combinedMercOrderNo'];
                    $checkSign             = httpPostString(env('ITEM.PAYMENT_URL') . '/payment/v3/kit/verify', json_encode($params));
                    $params['totalAmount'] = round(array_sum(array_column($params['subOrders'], 'totalAmount')));
                    $params['seqId']       = $params['combinedSysTransOrderNo'];
                    if (!isset($checkSign['error_code']) || $checkSign['error_code'] != 0) $this->throwError('验签失败');
                    if (!isset($checkSign['data']) || $checkSign['data'] != true) $this->throwError('验签失败');
                    if ($params['orderStatus'] != 'TRX_SUCCESS') $this->throwError('回调状态异常');
                    if (strpos($main_order_no, 'VHP') !== false) {//酒会
                        $result = httpPostString(env('ITEM.WINEPARTY_URL') . '/wineparty/v3/order/notifyDeal', json_encode(['main_order_no' => $main_order_no, 'params' => $params]));
                        if ($result['error_code'] != 0) {
                            //日志记录
                            $this->notifyLog($main_order_no . '异步回调失败：' . $result['error_msg']);
                            return json_encode(['resultCode' => 'Fail', 'resultDesc' => 'Fail'], JSON_UNESCAPED_UNICODE);
                        }
                        return json_encode(['resultCode' => '000000', 'resultDesc' => 'Success'], JSON_UNESCAPED_UNICODE);
                    }
                    break;
            }
            //记录回调数据
            $orderMain      = Db::name('order_main')
                ->field('id,uid,main_order_no,main_order_status,payment_method,payment_subject,cash_amount,payment_amount,order_type,special_type,bonus_balance,recharge_balance,tradeno')
                ->where(['main_order_no' => $main_order_no])
                ->find();
            $double_payment = (!empty($orderMain['tradeno']) && !empty($params['seqId']) && ($orderMain['tradeno'] != $params['seqId']) && ($orderMain['main_order_status'] != 0) && in_array($notify_type, [1, 2, 3]));
            if ($double_payment) {
                try {
                    $refund_order_no    = creatOrderNo(env('ORDERS.REFUND'), $orderMain['uid']);
                    $p_total_amount     = bcdiv($params['totalAmount'], 100, 2);
                    $refund_result_code = 0;
                    $refund_result_msg  = '';
                    $p_payment_method = substr($params['merOrderId'],4,1); //$orderMain['payment_method']
                    if (in_array($orderMain['payment_subject'], [1, 2])) {
                        //银联退款
                        $pushData    = array(
                            'main_order_no'   => $p_payment_method . $main_order_no,//支付方式+主订单号为银联交易订单号
                            'payment_method'  => $p_payment_method,
                            'refund_amount'   => $p_total_amount,
                            'refund_order_no' => $refund_order_no,
                            'subject'         => $orderMain['payment_subject'],
                            'is_cross'        => $orderMain['order_type'] == 2 ? 1 : 0,
                            'order_type'      => -1,//子订单全退给-1
                        );
                        $orderRefund = $this->httpPost(env('ITEM.PAYMENT_URL') . '/payment/v3/ums/refund', $pushData);
                        if (!isset($orderRefund['error_code']) || $orderRefund['error_code'] != 0) {
                            $refund_result_code = $orderRefund['error_code'];
                            $refund_result_msg  = '发起退款失败：' . $orderRefund['error_msg'];
                        }
                    } elseif (in_array($orderMain['payment_subject'], [3, 4])) {
                        //支付宝微信退款
                        $method = 'alipay';
                        if (in_array($p_payment_method, [3, 4, 5, 7, 8, 9])) $method = 'wechat';
                        $pushData    = array(
                            'main_order_no'   => $main_order_no,
                            'refund_order_no' => $refund_order_no,
                            'payment_method'  => $p_payment_method,
                            'method'          => $method,
                            'payment_amount'  => $p_total_amount,
                            'refund_amount'   => $p_total_amount,
                            'refund_desc'     => '重复支付自动退款'
                        );
                        $orderRefund = $this->httpPost(env('ITEM.PAYMENT_URL') . '/payment/v3/weAndAli/refund', $pushData);
                        if ((!isset($orderRefund['error_code']) || $orderRefund['error_code'] != 0)) {
                            $refund_result_code = $orderRefund['error_code'];
                            $refund_result_msg  = '支付宝微信发起退款失败：' . $orderRefund['error_msg'];
                        }
                    } else {
                        $refund_result_code = 1000;
                        $refund_result_msg  = '不支持的支付主体';
                    }
                    Db::name('double_payment_log')->insert([
                        'main_order_no'      => $main_order_no,
                        'payment_method'     => $p_payment_method,
                        'refund_amount'      => $p_total_amount,
                        'refund_order_no'    => $refund_order_no,
                        'subject'            => $orderMain['payment_subject'],
                        'refund_result_code' => $refund_result_code,
                        'refund_result_msg'  => $refund_result_msg,
                        'remark'             => '重复付款',
                        'created_time'       => time(),
                        'update_time'        => time(),
                    ]);

                    $getTelephone = httpGet(env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo', ['uid' => $orderMain['uid'], 'field' => 'telephone_encrypt']);
                    if (isset($getTelephone['data']['list'][0]['telephone_encrypt'])) {
                        $encrypt   = cryptionDeal(2, [$getTelephone['data']['list'][0]['telephone_encrypt']], $orderMain['uid'], '前端用户');
                        $telephone = isset($encrypt[$getTelephone['data']['list'][0]['telephone_encrypt']]) ? $encrypt[$getTelephone['data']['list'][0]['telephone_encrypt']] : '';
                    }
                    if (isset($telephone)) {
                        $this->httpPost(env('ITEM.SMS_URL') . '/sms/v3/group/sendSms', ['telephone' => $telephone, 'content' => "尊敬的酒云网用户，您好！我们发现您的订单【{$main_order_no}】出现了重复支付的情况，现已将重复的付款按原路退回。如有疑问，请和我们的客服联系，祝您生活愉快"]);
                    }

                    //提醒后台
                    \Curl::sendWechatSender([
                        'msg'          => "订单[$main_order_no]重复支付,自动退款{$p_total_amount} 操作" . ($refund_result_code == 0 ? '成功' : '失败') . "! {$refund_result_msg}",
                        'at'           => '15922995135',
                        'access_token' => '80e700e4-bb46-496c-a5e5-1a41714c77df',
                    ]);
                } catch (\Exception $e) {
                    Log::write('重复支付退款执行错误: ' . $e->getMessage() . ' ' . $e->getLine() . ' ' . $main_order_no);
                }
                switch ($notify_type) {
                    case 1:
                        return 'SUCCESS';
                        break;
                    case 2:
                        return 'success';
                        break;
                    case 3:
                        return json_encode(['code' => 'SUCCESS', 'message' => '成功'], JSON_UNESCAPED_UNICODE);
                        break;
                    case 5:
                        return json_encode(['resultCode' => '000000', 'resultDesc' => 'Success'], JSON_UNESCAPED_UNICODE);
                        break;
                }
            }

            if ($notify_type == 6) {
                if (($orderMain['payment_amount'] != bcadd($orderMain['cash_amount'], bcadd($orderMain['recharge_balance'], $orderMain['bonus_balance'], 2), 2)) || ($orderMain['cash_amount'] != 0)) {
                    return $this->throwError('余额支付金额错误!'. json_encode($orderMain));
                }
            }
            Db::name('order_deal_log')->where(['main_order_no' => $main_order_no])->update(['notify_param' => $requestparams]);
            $order_type     = config('config')['order_type'];//订单频道获取
            $field          = 'so.*,om.consignee,om.consignee_phone,om.province_id,om.city_id,om.district_id,om.address';
            $order_type_arr = explode(',', $orderMain['order_type']);
            $orderSub       = [];
            foreach ($order_type_arr as &$val) {
                $order = Db::name($order_type[intval($val)]['table'])
                    ->alias('so')
                    ->field($field)
                    ->leftJoin('order_main om', 'om.id=so.main_order_id')
                    ->where(['main_order_id' => $orderMain['id']])
                    ->select()->toArray();
                if ($val == 9) {
                    $merchant_id = $order[0]['merchant_id'];
                }
                $orderSub = array_merge($orderSub, $order);
            }
            if (empty($orderMain) || empty($orderSub)) $this->throwError('获取订单信息失败');
            //订单金额、状态验证
            if ((bcmul($orderMain['cash_amount'], 100, 2) == $params['totalAmount'] && $orderMain['main_order_status'] == 0) || (($notify_type == 4) && in_array($orderMain['main_order_status'],[0,4]) && empty($orderMain['tradeno']))) {
                $vmProductReport = [];
                Db::startTrans();
                try {
                    //主订单修改
                    $mainUpdateData = array(
                        'main_order_status' => 1,
                        'payment_time'      => time(),
                        'tradeno'           => $params['seqId'],
                        'update_time'       => time()
                    );
                    if ($notify_type == 4) $mainUpdateData['payment_method'] = 11;
                    if (isset($up_payment_method)) {
                        $mainUpdateData['payment_method'] = $up_payment_method;
                    }
                    $updateMain = Db::name('order_main')->where(['id' => $orderMain['id']])->update($mainUpdateData);
                    if (empty($updateMain)) $this->throwError($main_order_no . ':主订单状态修改失败');
                    //商家秒发补差价处理
                    $period     = 0;
                    $package_id = 0;
                    if (isset($merchant_id)) {
                        //获取商家对应的补差价商品期数套餐ID
                        $merchantInfo = $this->httpGet(env('ITEM.VMALL_URL') . '/vmall/v3/merchant/detail', ['id' => $merchant_id]);
                        if (isset($merchantInfo['error_code']) || $merchantInfo['error_code'] == 0) {
                            $period     = $merchantInfo['data']['period'];
                            $package_id = $merchantInfo['data']['package_id'];
                        }
                    }
                    //子订单处理
                    foreach ($orderSub as $key => $val) {
                        $vmProductReport[] = [
                            "uid"          => strval($val['uid']),//用户id，不能为空，未登录的也需要用临时的唯一uid
                            "created_time" => time()*1000,//触发事件，毫秒时间戳
                            "metric_name"  => "period_order_ct",//上报名称：（period_exposure：商品曝光）
                            "period"       => intval($val['period']),//期数
                            "period_type"  => intval($val['order_type']),//频道
                            "device"       => ""//设备来源（）
                        ];
                        if ($val['period'] == 71502) {//补差价订单提前改成已完成
                            Db::name('order_main')->where(['id' => $orderMain['id']])->update(['main_order_status' => 3]);
                            Db::name('flash_order')->where(['id' => $val['id']])->update(['sub_order_status' => 3, 'payment_time' => time(), 'update_time' => time()]);
                        } else if ($val['period'] == $period) {//商家秒发补差价订单提前改成已完成
                            Db::name('order_main')->where(['id' => $orderMain['id']])->update(['main_order_status' => 3]);
                            Db::name('merchant_second_order')->where(['id' => $val['id']])->update(['sub_order_status' => 3, 'payment_time' => time(), 'update_time' => time(), 'goods_receipt_time' => time()]);
                        } else {
                            $subUpdateData = array(
                                'sub_order_status' => 1,
                                'payment_time'     => time(),
                                'update_time'      => time()
                            );
                            if ($val['order_type'] == 2) $subUpdateData['payment_response_raw_data'] = $requestparams;
                            $updateSub = Db::name($order_type[$val['order_type']]['table'])->where(['id' => $val['id']])->update($subUpdateData);
                            if (empty($updateSub)) $this->throwError($main_order_no . ':子订单状态修改失败');
                        }
                        $orderSub[$key]['bcjPeriod']     = $period;
                        $orderSub[$key]['bcjPackage_id'] = $package_id;
                    }
                    $orderSub[$key]['bcjPeriod']     = $period;
                    $orderSub[$key]['bcjPackage_id'] = $package_id;

                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    $this->throwError($e->getMessage());
                }
                if(!empty($vmProductReport)){
                    try {
                        \Curl::vmProductReport(['data'=> $vmProductReport]);
                    } catch (\Exception $e) {
                        Log::write("vmProductReport ERROR:" . json_encode($vmProductReport) . ' ' . $e->getMessage());
                    }
                }
                $queueData = json_encode(['orderMain' => $orderMain, 'orderSub' => $orderSub, 'notify_param' => $params], JSON_UNESCAPED_UNICODE);
                //记录队列分发数据
                Db::name('order_deal_log')->where(['main_order_no' => $main_order_no])->update(['queue_distribute_param' => $queueData]);
                //创建银联支付异步处理队列
                $data      = base64_encode($queueData);
                $pushData  = array(
                    'exchange_name' => 'orders',
                    'routing_key'   => 'orders_await_validate',
                    'data'          => $data,
                );
                $pushQueue = httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
                if (!isset($pushQueue['error_code']) || $pushQueue['error_code'] != 0) $this->throwError('异步回调推送队列失败');
                //app推送消息
                $num            = array_sum(array_column($orderSub, 'order_qty'));
                $es             = new ElasticSearchService();
                $esSubOrderData = $es->getDocumentList(['index' => ['orders'], 'match' => [['main_order_no' => $main_order_no]]]);
                $title          = isset($esSubOrderData['data'][0]['title']) ? $esSubOrderData['data'][0]['title'] : '';
                $single         = array(
                    'is_push'      => 1,
                    'uid'          => $orderMain['uid'],
                    'title'        => "订单通知",
                    'content'      => "订单通知 【酒云网】尊敬的客户，您已成功支付下单【" . $title . "】，合计数量【" . $num . "】，如有疑问请联系客服。",
                    'data_type'    => 18,
                    'data'         => [
                        'title'   => "订单通知",
                        'content' => "订单通知 【酒云网】尊敬的客户，您已成功支付下单【" . $title . "】，合计数量【" . $num . "】，如有疑问请联系客服。",
                    ],
                    'label'        => "MyOrder",
                    'custom_param' => []
                );
                httpPostString(env('ITEM.APPPUSH_URL') . '/apppush/v3/push/single', json_encode($single, JSON_UNESCAPED_UNICODE));
                //支付金额<=10元的订单推送通知到机器人
                $sub_order_no_str = implode(',', array_column($orderSub, 'sub_order_no'));
                if ($orderMain['payment_amount'] <= 10 && (strpos($sub_order_no_str,'VHD') === false)) {
                    $content   = '#订单支付推送：';
                    $content   .= "主订单号：{$main_order_no}，";
                    $content   .= "子订单号：{$sub_order_no_str}，";
                    $content   .= "支付金额：{$orderMain['payment_amount']}。";
                    $queueData = array(
                        'access_token' => env('ORDERS.order_price_unusual_token'),
                        'type'         => 'text',
                        'at'           => '',
                        'content'      => base64_encode($content),
                    );
                    $data      = base64_encode(json_encode($queueData));
                    $pushData  = array(
                        'exchange_name' => 'dingtalk',
                        'routing_key'   => 'dingtalk_sender',
                        'data'          => $data,
                    );
                    httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
                }
                //跨境自动推单处理
                if ($orderMain['order_type'] == 2) {
                    try {(new \app\service\Cross())->inspectionCheck(['order_no' => $main_order_no]);} catch (\Exception $e) {}
                    //组合套餐拆分为多个订单
                    $main_order_no_arr = $this->split($main_order_no);
                    $config = Db::name('cross_auto_push_config')->where(['id' => 1])->find();
                    //是否开启自动推送
                    if (isset($config['is_auto_push']) && $config['is_auto_push'] == 1) {
                        //添加超时任务

                        $periodsEsData         = Es::name(Es::PERIODS)->where([
                            ['id', 'in', array_values(array_unique(array_column($orderSub, 'period')))]
                        ])->field('id,is_timing_pushorder,predict_shipment_time,is_presell')->find();
                        $is_timing_pushorder   = $periodsEsData['is_timing_pushorder'] ?? 0;//是否定时发货：0-否，1-是
                        $predict_shipment_time = $periodsEsData['predict_shipment_time'] ?? null;//预计发货时间
                        $is_presell = $periodsEsData['is_presell'] ?? 0;//预计发货时间
                        $push_wms              = true;
                        if ($is_timing_pushorder == 1 && !empty($predict_shipment_time)) {
                            try {
                                if (strtotime($predict_shipment_time) > time()) {
                                    $push_wms = false;
                                    Log::write('支付回调进入定时发货执行: ' . implode(',', $main_order_no_arr));
                                }
                            } catch (\Exception $e) {
                                Log::write('支付回调推送萌芽判断是否定时发货执行错误: ' . $e->getMessage() . ' ' . $e->getLine());
                            }
                        } elseif ($is_presell == 1) {
                            $push_wms = false;
                            Log::write('支付回调进入 预售订单不自动推单: ' . implode(',', $main_order_no_arr));
                        }

                        if ($push_wms) {
                            foreach ($main_order_no_arr as $main_order_no_item) {
                                $pushData = array(
                                    'namespace' => "orders",
                                    'key'       => "cross_push_" . $main_order_no_item,
                                    'data'      => base64_encode(json_encode(['main_order_no' => $main_order_no_item])),
                                    'callback'  => env('ITEM.ORDERS_URL') . '/orders/v3/crossAutoPush/crossAutoPushDeal',
                                    'timeout'   => $config['pay_order_after_time'] . 'm',
                                );
                                $this->httpPost(env('ITEM.TIMEING_SERVICE_URL') . '/services/v3/timing/add', $pushData);
                            }
                        }
                    }
                }
                //商家秒发订单群消息提醒处理
                if (strpos($orderMain['order_type'], '9') !== false) {
                    $remindGoodsContent  = '';
                    $delivery_store_name = '';
                    $merchant_id         = 0;
                    $delivery_store_id   = 0;
                    foreach ($esSubOrderData['data'] as &$value) {
                        if ($value['order_type'] == 9 && empty($value['group_id'])) {
                            $products = '';
                            try {
                                //获取商品套餐信息
                                $periodsSetArr    = array(
                                    'index'  => ['periods_set'],
                                    'match'  => [['id' => $value['package_id']]],
                                    'source' => ['associated_products', 'is_mystery_box']
                                );
                                $periodsSetEsData = $es->getDocumentList($periodsSetArr);
                                if (!isset($periodsSetEsData['data'][0])) $this->throwError('未获取到商品套餐信息');
                                $periodsSetInfo = $periodsSetEsData['data'][0];
                                if ($periodsSetInfo['is_mystery_box'] == 1) {//盲盒产品信息处理
                                    $mystery_box_log = Db::name('order_mystery_box_log')->where(['main_order_no' => $value['main_order_no'], 'period' => $value['period'], 'package_id' => $value['package_id']])->find();
                                    if (empty($mystery_box_log)) $this->throwError('未获取到订单盲盒套餐信息');
                                    $associated_products = json_decode($mystery_box_log['product_info'], true);
                                } else {
                                    $associated_products = json_decode($periodsSetInfo['associated_products'], true);
                                }
                                //获取产品信息
                                $product_ids = array_column($associated_products, 'product_id');
                                $wikiData    = array(
                                    'ids'    => $product_ids,
                                    'fields' => 'cn_product_name,short_code',
                                );
                                $productInfo = $this->httpPost(env('ITEM.WINE_WIKI_URL') . '/wiki/v3/product/fieldsdatarr', $wikiData);
                                if ($productInfo['error_code'] != 0 || count($productInfo['data']['list']) == 0) $this->throwError('未获取到产品信息');
                                foreach ($productInfo['data']['list'] as &$va) {
                                    $products .= $va['cn_product_name'] . '(' . $va['short_code'] . ') ';
                                }
                            } catch (\Exception $e) {
                                $products = '';
                            }
                            $remindGoodsContent  .= "-商品名称：" . $value['title'] . "\n";
                            $remindGoodsContent  .= "-商品图片：[商品图片](" . imagePrefix($value['banner_img']) . ")\n";
                            $remindGoodsContent  .= "-子订单号：" . $value['sub_order_no'] . "\n";
                            $remindGoodsContent  .= "-子订单金额：" . $value['payment_amount'] . "\n";
                            $remindGoodsContent  .= "-套餐/数量：" . $value['package_name'] . '/' . $value['order_qty'] . "\n";
                            $remindGoodsContent  .= "-产品：" . $products . "\n";
                            $delivery_store_name = $value['delivery_store_name'];
                            $merchant_id         = $value['merchant_id'];
                            $delivery_store_id   = $value['delivery_store_id'];
                        }
                    }
                    $encrypt         = cryptionDeal(2, [$esSubOrderData['data'][0]['consignee'], $esSubOrderData['data'][0]['consignee_phone']], $esSubOrderData['data'][0]['uid'], '前端用户');
                    $consignee       = isset($encrypt[$esSubOrderData['data'][0]['consignee']]) ? $encrypt[$esSubOrderData['data'][0]['consignee']] : '';
                    $consignee_phone = isset($encrypt[$esSubOrderData['data'][0]['consignee_phone']]) ? $encrypt[$esSubOrderData['data'][0]['consignee_phone']] : '';
                    $delivery_method = [1 => "物流配送", 2 => "同城配送", 3 => "自提订单"];
                    $remindContent   = "#### 实时订单\n";
                    $remindContent   .= "-用户昵称: " . $esSubOrderData['data'][0]['nickname'] . "\n";
                    $remindContent   .= "-收货人信息: " . $consignee . ' ' . $consignee_phone . "\n";
                    $remindContent   .= $remindGoodsContent;
                    $remindContent   .= "-发货点: " . $delivery_store_name . "\n";
                    $remindContent   .= "-配送地址: " . $esSubOrderData['data'][0]['province_name'] . $esSubOrderData['data'][0]['city_name'] . $esSubOrderData['data'][0]['district_name'] . $esSubOrderData['data'][0]['address'] . "\n";
                    $remindContent   .= "-快递方式: " . ($delivery_method[($esSubOrderData['data'][0]['delivery_method'] ?? '')] ?? '') . "\n";
                    $remindData      = array(
                        'mid'     => $merchant_id,
                        'shop_id' => $delivery_store_id,
                        'msgtype' => 'markdown',
                        'content' => $remindContent,
                    );
                    curlRequest(env('ITEM.VMALL_URL') . '/vmall/v3/order/remind', json_encode($remindData, JSON_UNESCAPED_UNICODE), []);
                }
            } else {
                //超时任务已取消订单发起退款审批
                if ($orderMain['main_order_status'] == 4) {
                    $dingTalkService = new DingTalkService();
                    $dingTalkService->TimeOutOrderCreateDingtalkVerify(['orderMain' => $orderMain, 'orderSub' => $orderSub, 'notify_param' => $params]);
                    if ($notify_type == 6) {
                        $this->success();
                    }
                    return 'SUCCESS';
                }
                $this->throwError('订单金额、状态验证失败');
            }
        } catch (\Exception $e) {
            $this->notifyLog($main_order_no . '异步回调失败：' . $e->getMessage() . '，回调参数：' . json_encode($params, JSON_UNESCAPED_UNICODE));
            switch ($notify_type) {
                case 1:
                    return 'FAILED';
                    break;
                case 2:
                    return 'fail';
                    break;
                case 3:
                    return json_encode(['code' => 'FAIL', 'message' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
                    break;
                case 5:
                    return json_encode(['resultCode' => 'Fail', 'resultDesc' => 'Fail'], JSON_UNESCAPED_UNICODE);
                    break;
                case 6:
                    $this->throwError('回调失败: ' . $e->getMessage());
                    break;
            }
        }
        switch ($notify_type) {
            case 1:
                return 'SUCCESS';
                break;
            case 2:
                return 'success';
                break;
            case 3:
                return json_encode(['code' => 'SUCCESS', 'message' => '成功'], JSON_UNESCAPED_UNICODE);
                break;
            case 5:
                return json_encode(['resultCode' => '000000', 'resultDesc' => 'Success'], JSON_UNESCAPED_UNICODE);
                break;
            case 6:
                return $this->success();
                break;
        }
    }

    public function split($main_order_no)
    {
        Db::startTrans();
        try {
            $main = Db::name('order_main')->where(['main_order_no' => $main_order_no])->find();
            if ($main['order_type'] != 2) throw new  Exception("不是跨境订单!");
            $log = Db::name('order_deal_log')->where('main_order_no', $main['main_order_no'])->find();
            if ($log['stock_param']) {
                $log_stock_param = json_decode($log['stock_param'], true);
            }
            $sub = Db::name('cross_order')->where(['main_order_id' => $main['id']])->find();
            if ($main['payment_amount'] != $sub['payment_amount']) throw new Exception("主子订单金额不一致");
            $package             = Es::name(Es::PERIODS_PACKAGE)->where([['id', '=', $sub['package_id']]])->find();
            $associated_products = json_decode($package['associated_products'], true);
            if (count($associated_products) <= 1) throw new Exception("无需拆分");

            $split_packages = \Curl::packageSplit(['package_id' => $sub['package_id']]);

            $max_price     = $total_price = $main['payment_amount'];
            $i             = 1;
            $package_count = count($split_packages);
            $main_list     = [];
            $sub_list      = [];
            $log_list      = [];
            $cross_split   = [];
            foreach ($split_packages as $spkg) {
                $temp_main = $main;
                $temp_sub  = $sub;

                unset($temp_main['created_time']);
                unset($temp_main['update_time']);

                if ($package_count == $i) {
                    $temp_payment_amount = $max_price;
                } else {
                    $temp_payment_amount = $spkg['price'];
//                    $temp_payment_amount = bcmul($spkg['price_ratio'], $total_price, 2);
//                    $temp_payment_amount = min(bcdiv($temp_payment_amount, 100, 2), $max_price);
                }
                $max_price = bcsub($max_price, $temp_payment_amount, 2);


                $temp_main_obj = new OrderMain();

                if (isset($log_stock_param['items'][0]['set_id'])) {
                    $log_stock_param['items'][0]['set_id'] = $spkg['id'];
                }

                if ($i == 1) {
                    if (!empty($log_stock_param)) {
                        Db::name('order_deal_log')->where('main_order_no', $main['main_order_no'])->update(['stock_param' => json_encode($log_stock_param)]);
                    }
                    $temp_main_obj = $temp_main_obj->where('id', $main['id'])->find();
                } else {
                    unset($temp_main['id']);
                    $temp_main['main_order_no'] = creatOrderNo(env('ORDERS.ORDER_MAIN'), $temp_main['uid']);

                    $temp_log                  = $log;
                    if (!empty($log_stock_param)) {
                        $temp_log['stock_param'] = json_encode($log_stock_param);
                    }
                    $temp_log['main_order_no'] = $temp_main['main_order_no'];
                    unset($temp_log['id']);
                    $log_list[] = $temp_log;


                    unset($temp_sub['id']);
                    $temp_sub['sub_order_no'] = creatOrderNo(env('ORDERS.ORDER_SON'), $temp_sub['uid']);
                    $temp_sub['guid']         = buildGuid();

                }
                $temp_main['payment_amount'] = $temp_payment_amount;
                $temp_main_obj->save($temp_main);
                $temp_main['id'] = $temp_main_obj['id'];
                $cross_split[]   = ['origin_main_order_no' => $main['main_order_no'], 'main_order_no' => $temp_main_obj['main_order_no'], 'payment_amount' => $main['payment_amount'], 'origin_pkg_id' => $sub['package_id']];

                $temp_sub['payment_amount'] = $temp_payment_amount;
                $temp_sub['package_id']     = $spkg['id'];
                $temp_sub['main_order_id']  = $temp_main_obj['id'];

                $main_list[] = $temp_main;
                $sub_list[]  = $temp_sub;
                $i++;
            }

            (new \app\model\Cross())->saveAll($sub_list);
            Db::name('order_deal_log')->insertAll($log_list);
            Db::name('cross_split')->insertAll($cross_split);
            //order_remarks,sub_order_extend
            Db::commit();

            return array_column($cross_split, 'main_order_no');
        } catch (\Exception $e) {
            Db::rollback();
            Log::write("split 跨境订单拆分错误: $main_order_no " . $e->getMessage() . ' LINE ' . $e->getLine());
            return [$main_order_no];
        }
    }

    /**
     * Description:队列处理异步回调处理抽奖记录+补差价订单+已购数量+首三单福利处理
     * Author: zrc
     * Date: 2021/8/16
     * Time: 12:10
     * @param $requestparams
     */
    public function notifyDealOrderStatus($requestparams)
    {
        $params             = json_decode($requestparams, true);
        $orderMain          = $params['orderMain'];
        $orderSub           = $params['orderSub'];
        $main_order_no      = $orderMain['main_order_no'];
        $notify_deal_status = Db::name('order_deal_log')->where(['main_order_no' => $main_order_no])->json(['notify_deal_status', true])->value('notify_deal_status');
        $is_after_sales     = 0;
        $redis              = new \Redis();
        $redis->connect(env('CACHE.HOST'), env('CACHE.PORT'));
        $redis->auth(env('CACHE.PASSWORD'));
        $redis->select(8);
        Db::startTrans();
        try {
            //主订单修改
            if ($notify_deal_status['order'] == 0) {
                //抽奖记录添加(订金订单除外)
                if ($orderMain['special_type'] != 4) {
                    $addRecord = $this->httpPost(env('ITEM.MARKET_CONF_URL') . '/marketing-conf/v3/game/salesOrderRecord', ['uid' => $orderMain['uid'], 'orderno' => $main_order_no]);
                    if ($addRecord['error_code'] != 0) $this->throwError('添加抽奖记录失败:' . $addRecord['error_msg']);
                }
                //子订单处理
                $sub_orders = [];
                foreach ($orderSub as &$val) {
                    //补差价订单处理
                    if (($val['period'] == 71502 && $val['package_id'] == 205401) || ($val['period'] == $val['bcjPeriod'] && $val['package_id'] == $val['bcjPackage_id'])) {
                        $is_after_sales    = 1;
                        $after_order_no    = $val['sub_order_no'];
                        $work_order_id     = $val['work_order_id'];
                        $makeUpData        = array(
                            'uid'              => $val['uid'],
                            'sub_order_no'     => $val['sub_order_no'],
                            'express_fee'      => $val['payment_amount'],
                            'express_type'     => $val['express_type'],
                            'related_order_no' => $val['related_order_no'],
                            'consignee'        => $val['consignee'],
                            'consignee_phone'  => $val['consignee_phone'],
                            'province_id'      => $val['province_id'],
                            'city_id'          => $val['city_id'],
                            'district_id'      => $val['district_id'],
                            'address'          => $val['address'],
                        );
                        $additionalService = new AdditionalService();
                        $additionalService->expressAdditionalDeal($makeUpData);
                    } else {
                        //商品已购数量增加
                        $periodsPlusMinus = array(
                            'period'       => $val['period'],
                            'package_id'   => $val['package_id'],
                            'periods_type' => $val['order_type'],
                            'type'         => 'order',
                            'count'        => $val['order_qty']
                        );
                        $plusMinus        = $this->httpPost(env('ITEM.COMMODITIES_URL') . '/commodities/v3/periods/periodsPlusMinus', $periodsPlusMinus);
                        if ($plusMinus['error_code'] != 0) $this->throwError('增加商品已购数量失败:' . $plusMinus['error_msg']);
                    }
                    //期数已购标识添加
                    $additionalService = new AdditionalService();
                    $additionalService->periodPurchasedAdd($val);
                    //跨境代付处理
                    if ($val['order_type'] == 2 && isset($val['is_replace_pay']) && $val['is_replace_pay'] == 1) {
                        $this->payOnBehalf($params);
                    }
                    //售前分享下单记录处理
                    $source_user = $redis->get('pre_sales_' . $val['uid'] . '_' . $val['period']);
                    if (!empty($source_user)) {
                        $shar_data['share_name'] = '';
                        // 获取中台用户名称
                        $adminInfo = $this->httpGet(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info', ['admin_id' => $source_user]);
                        if (isset($adminInfo['data'][$source_user]['realname'])) {
                            $shar_data['share_name'] = $adminInfo['data'][$source_user]['realname'];
                        }
                        $shareLog = array(
                            'source_event' => 'share',
                            'source_user'  => $source_user,
                            'sub_order_no' => $val['sub_order_no'],
                            'uid'          => $val['uid'],
                            'share_name'   => $shar_data['share_name'],
                            'period'       => $val['period'],
                            'order_type'   => $val['order_type'],
                            'created_time' => time(),
                        );
                        Db::name('share_order_log')->insert($shareLog);
                        //清除标识记录
                        $redis->del('pre_sales_' . $val['uid'] . '_' . $val['period']);
                    }
                    if ($val['order_type'] == 9 && empty($val['group_id'])) {
                        //商家秒发订单语音提示redis数据添加
                        $channel = 'merchant_second_channel_' . $val['merchant_id'];
                        $message = '您有新的待发货订单，请及时处理';
                        $redis->publish($channel, $message);
                        $redis->close();
                        //闪送下单数据组装
                        if ($val['delivery_method'] == 2) {
                            $sub_orders[] = array(
                                'sub_order_no'      => $val['sub_order_no'],
                                'period'            => $val['period'],
                                'package_id'        => $val['package_id'],
                                'order_qty'         => $val['order_qty'],
                                'delivery_method'   => $val['delivery_method'],
                                'delivery_store_id' => $val['delivery_store_id'],
                                'merchant_id'       => $val['merchant_id'],
                            );
                        }
                    }
                    //沉默用户回归统计需要参数处理
                    $coupon_id = 0;
                    if (isset($val['coupon_id']) && $val['coupon_id'] > 0) {
                        $coupon_id = $val['coupon_id'];
                    }
                }
                //沉默用户回归统计处理
                $submitData = array(
                    'main_order_no' => $main_order_no,
                    'uid'           => $orderMain['uid'],
                    'use_coupon_id' => intval($coupon_id),
                    'pay_money'     => floatval($orderMain['payment_amount']),
                    'pay_time'      => time(),
                );
                curlRequest(env('ITEM.MAIDIAN_URL') . '/maidian/v3/silentStatistics/orderReport', json_encode($submitData));
                //来源记录写入
                if (isset($shareLog)) {
                    $sourceData = array(
                        'source_platform' => 'vinehoo',
                        'source_event'    => $shareLog['source_event'],
                        'source_user'     => $shareLog['source_user'],
                        'main_order_id'   => $orderMain['id'],
                        'order_type'      => $orderMain['order_type'],
                        'created_time'    => time()
                    );
                    Db::name('order_source_log')->insert($sourceData);
                }
                //闪送下单
                if (!empty($sub_orders)) {
                    //省市区处理
                    $province_name = !empty($orderSub[0]['province_id']) ? getRegionalInfo($orderSub[0]['province_id']) : '';
                    $city_name     = !empty($orderSub[0]['city_id']) ? getRegionalInfo($orderSub[0]['city_id']) : '';
                    $district_name = !empty($orderSub[0]['district_id']) ? getRegionalInfo($orderSub[0]['district_id']) : '';
                    //用户信息处理
                    $consignee       = $orderSub[0]['consignee'];
                    $consignee_phone = $orderSub[0]['consignee_phone'];
                    $encrypt         = cryptionDeal(2, [$consignee, $consignee_phone], $orderSub[0]['uid'], '前端用户');
                    $consignee       = isset($encrypt[$consignee]) ? $encrypt[$consignee] : '';
                    $consignee_phone = isset($encrypt[$consignee_phone]) ? $encrypt[$consignee_phone] : '';
                    $shipmentsData   = array(
                        'main_order_no' => $main_order_no,
                        'sub_orders'    => $sub_orders,
                        'receiver'      => array(
                            'address'        => $province_name . $city_name . $district_name,
                            'address_detail' => $orderSub[0]['address'],
                            'name'           => $consignee,
                            'mobile'         => $consignee_phone,
                        )
                    );
                    $shipments       = curlRequest(env('ITEM.VMALL_URL') . '/vmall/v3/shansong/shipments', json_encode($shipmentsData, JSON_UNESCAPED_UNICODE));
                    if (!isset($shipments['error_code'])) $this->throwError('商家模块访问异常');
                    if ($shipments['error_code'] != 0) $this->throwError($shipments['error_msg']);
                    foreach ($shipments['data'] as &$v) {
                        if ($v['status'] == 1) {
                            $secondOrderData = array(
                                'sub_order_status' => 2,
                                'express_type'     => 100,
                                'express_name'     => '闪送',
                                'express_number'   => $v['waybill_order_no'],
                                'delivery_time'    => time(),
                                'update_time'      => time(),
                            );
                            Db::name('merchant_second_order')->where(['sub_order_no' => $v['sub_order_sn']])->update($secondOrderData);
                        }
                    }
                }
                $this->updateOrderLog('order', $main_order_no);
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            //补差价售后异常处理
            if ($is_after_sales == 1) {
                //支付订单处理失败通知工单系统回滚工单状态
                if (isset($work_order_id)) {
                    $this->httpPost(env('ITEM.WORK_URL') . '/work/v3/CallBack/orderFailNotify', ['work_order_id' => $work_order_id, 'description' => $e->getMessage()]);
                }
                //记录订单日志
                $orderService = new OrderService();
                $remarks      = array(
                    'sub_order_no' => $after_order_no,
                    'order_type'   => 0,
                    'content'      => '补差价订单支付回调处理异常：' . $e->getMessage(),
                    'admin_id'     => 0
                );
                $orderService->createRemarks($remarks);
            }
            //日志记录
            $this->notifyLog($requestparams . '------' . $e->getMessage());
            $this->throwError($main_order_no . $e->getMessage());
        }
        //首三单福利处理
        $redis = new \Redis();
        $redis->connect(env('CACHE.HOST'), env('CACHE.PORT'));
        $redis->auth(env('CACHE.PASSWORD'));
        $redis->select(8);
        $redisCache = $redis->hGet('userIsHaveTopThree', $orderMain['uid']);
        if ($redisCache != 1) {
            $where         = array(
                ['uid', '=', $orderMain['uid']],
                ['payment_time', '>', 0],
            );
            $topThreeOrder = Db::name('order_main')->where($where)->order('id asc')->count();
            if ($topThreeOrder > 3) {
                $redis->hSetNx('userIsHaveTopThree', $orderMain['uid'], 1);
            } else {
                $stage = 0;
                switch ($topThreeOrder) {
                    case 0:
                        $stage = 1;
                        break;
                    case 1:
                        $stage = 1;
                        break;
                    case 2:
                        $stage = 2;
                        break;
                    case 3:
                        $stage = 3;
                        break;
                }
                //获取规则
                $discountInfo = Db::table('vh_marketing.vh_top_three_order_discount')->where(['stage' => $stage, 'status' => 1, 'type' => 1])->find();
                if (!empty($discountInfo)) {
                    $pushCouponData = array(
                        'uid'       => $orderMain['uid'],
                        'remark'    => $orderMain['main_order_no'] . '首三单发放优惠券，第' . $stage . '单',
                        'coupon_id' => $discountInfo['data_id'],
                    );
                    $dealCoupon     = httpPostString(env('ITEM.COUPON_URL') . '/coupon/v3/coupon/depositGrantCoupon', json_encode($pushCouponData));
                    if (!isset($dealCoupon['error_code'])) $this->throwError('优惠券模块访问异常');
                    if ($dealCoupon['error_code'] != 0) $this->throwError('优惠券发放异常：' . $dealCoupon['error_msg']);
                    //记录订单日志
                    $orderService = new OrderService();
                    $remarks      = array(
                        'sub_order_no' => $orderSub[0]['sub_order_no'],
                        'order_type'   => $orderSub[0]['order_type'],
                        'content'      => '首三单发放优惠券，第' . $stage . '单,优惠券ID:' . $discountInfo['data_id'] . ',优惠券发放记录ID:' . $dealCoupon['data']['coupon_issue_id'],
                        'admin_id'     => 0
                    );
                    $orderService->createRemarks($remarks);
                }
            }
        }
        return true;
    }

    /**
     * Description:支付回调开票处理
     * Author: zrc
     * Date: 2021/8/18
     * Time: 14:15
     */
    public function notifyDealInvoice($requestparams)
    {
        $params             = json_decode($requestparams, true);
        $orderMain          = $params['orderMain'];
        $orderSub           = $params['orderSub'];
        $main_order_no      = $orderMain['main_order_no'];
        $order_type         = config('config')['order_type'];//订单频道获取
        $notify_deal_status = Db::name('order_deal_log')->where(['main_order_no' => $main_order_no])->json(['notify_deal_status', true])->value('notify_deal_status');
        if ($notify_deal_status['invoice'] == 0 && isset($orderSub[0]['invoice_id'])) {
            Db::startTrans();
            try {
                //获取发票抬头信息
                $receipt = curlRequest(env('ITEM.USER_URL') . '/user/v3/receipt/queryInfo', ['invoice_id' => $orderSub[0]['invoice_id']], [], 'GET');
                if (!isset($receipt['data']['list'][0]['invoice_name'])) $this->throwError('获取发票抬头信息失败');
                $invoiceInfo = $receipt['data']['list'][0];
                foreach ($orderSub as $key => $val) {
                    $invoiceData         = array(
                        'sub_order_no'    => $val['sub_order_no'],
                        'order_type'      => $val['order_type'],
                        'invoice_type'    => $invoiceInfo['invoice_type'],
                        'invoice_name'    => $invoiceInfo['invoice_name'] ?? '',
                        'type_id'         => $invoiceInfo['type_id'],
                        'email'           => $invoiceInfo['email'] ?? '',
                        'taxpayer'        => $invoiceInfo['taxpayer'] ?? '',
                        'telephone'       => $invoiceInfo['telephone'] ?? '',
                        'company_address' => $invoiceInfo['company_address'] ?? '',
                        'company_tel'     => $invoiceInfo['company_tel'] ?? '',
                        'opening_bank'    => $invoiceInfo['opening_bank'] ?? '',
                        'bank_account'    => $invoiceInfo['bank_account'] ?? '',
                    );
                    $orderInvoiceService = new OrderInvoiceService();
                    $invoice_id          = $orderInvoiceService->addInvoiceRecord($invoiceData);
                    Db::name($order_type[$val['order_type']]['table'])->where(['id' => $val['id']])->update(['invoice_id' => $invoice_id, 'update_time' => time()]);
                }
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                $this->notifyLog($requestparams . '------' . '开票记录写入失败，' . '错误信息：' . $e->getMessage());
                $this->throwError($main_order_no . '开票记录写入失败:' . $e->getMessage());
            }
            $this->updateOrderLog('invoice', $main_order_no);
        }
        return true;
    }

    /**
     * Description:队列处理异步回调订单拼团
     * Author: zrc
     * Date: 2022/4/12
     * Time: 12:10
     * @param $requestparams
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function notifyDealGroup($requestparams)
    {
        $params             = json_decode($requestparams, true);
        $orderMain          = $params['orderMain'];
        $main_order_no      = $orderMain['main_order_no'];
        $notify_deal_status = Db::name('order_deal_log')->where(['main_order_no' => $main_order_no])->json(['notify_deal_status', true])->value('notify_deal_status');
        if ($notify_deal_status['group'] == 0 || $notify_deal_status['group'] == 3) { //3.拼单失败
            try {
                $order_type = config('config')['order_type'];//订单频道获取
                $group_id   = Db::name($order_type[$orderMain['order_type']]['table'])->where(['main_order_id' => $orderMain['id']])->value('group_id');
                if (empty($group_id)) $this->throwError('未获取到拼团ID');
                $groupInfo = Db::name('order_group')->where(['id' => $group_id])->find();
                if (empty($groupInfo)) $this->throwError('未获取到拼团信息');
                //拼团成员处理
                if ($orderMain['main_order_no'] != $groupInfo['head_order_no'] && strpos($groupInfo['member_order_no'], $orderMain['main_order_no']) === false) {
                    $groupData                    = [];
                    $groupData['group_join_nums'] = $groupInfo['group_join_nums'] + 1;
                    if ($groupData['group_join_nums'] >= $groupInfo['group_limit_nums']) {
                        $groupData['group_status'] = 2;
                    }
                    if (empty($groupInfo['member_order_no'])) {
                        $groupData['member_order_no'] = $orderMain['main_order_no'];
                        $groupData['member_uids']     = $orderMain['uid'];
                    } else {
                        $groupData['member_order_no'] = $groupInfo['member_order_no'] . ',' . $orderMain['main_order_no'];
                        $groupData['member_uids']     = $groupInfo['member_uids'] . ',' . $orderMain['uid'];
                    }
                    $groupData['update_time'] = time();
                    $updateGroup              = Db::name('order_group')->where(['id' => $group_id])->update($groupData);
                    if (empty($updateGroup)) $this->throwError('修改拼团信息失败');
                    //拼团成功子订单处理
                    if (isset($groupData['group_status'])) {
                        $main_order_no_arr = explode(',', $groupInfo['head_order_no'] . ',' . $groupData['member_order_no']);
                        $main_order_no_arr = Db::name('order_main')->alias('t1')
                            ->join('flash_order t2', 't1.id=t2.main_order_id')
                            ->where([['t1.main_order_no', 'in', $main_order_no_arr]])
                            ->where([['t2.sub_order_status', '=', 1]])
                            ->column('t1.main_order_no');
                        $mainInfo          = Db::name('order_main')->field('id,uid,main_order_no,order_type')->where([['main_order_no', 'in', $main_order_no_arr]])->select()->toArray();
                        //所有拼团订单扣取商品库存
                        $dealLog = Db::name('order_deal_log')->field('id,stock_param')->where([['main_order_no', 'in', $main_order_no_arr]])->select()->toArray();
                        $items   = [];
                        foreach ($dealLog as &$vv) {
                            $stock_param = json_decode($vv['stock_param'], true);
                            foreach ($stock_param['items'] as &$v) {
                                $items[] = $v;
                            }
                        }
                        $stock_param = array(
                            'orderno'      => $main_order_no . '团长',
                            'groupid'      => $group_id,
                            'group_status' => 2,
                            'items'        => $items
                        );
                        $stockVerify = curlRequest(env('ITEM.VINEHOO_INVENTORY_INOUT_URL') . '/inventory_service/v3/inventory/dec', json_encode($stock_param, true));
                        //商品库存扣减失败当拼团超时处理
                        if ($stockVerify['error_code'] != 0) {
                            Db::name('order_group')->where(['id' => $group_id])->update(['group_status' => 1, 'update_time' => time()]);
                            $orderService = new OrderService();
                            $orderService->groupOrderTimeOutDeal(json_encode(['head_order_no' => $groupInfo['head_order_no'], 'group_id' => $group_id], true));
                            $this->throwError($stockVerify['error_msg']);
                        }
                        //修改所有拼团子订单拼团状态为拼团成功，推送萌牙
                        foreach ($mainInfo as &$val) {
                            $updateSubOrder = Db::name($order_type[intval($val['order_type'])]['table'])->where(['main_order_id' => $val['id']])->update(['group_status' => 2, 'update_time' => time()]);
                            if (empty($updateSubOrder)) $this->throwError('修改子订单拼团信息失败');
                            if ($val['order_type'] == 9) {//商家秒发处理推送语音、闪送下单、群消息推送
                                $es                = new ElasticSearchService();
                                $esSubOrderData    = $es->getDocumentList(['index' => ['orders'], 'match' => [['main_order_id' => $val['id']]], 'limit' => 1]);
                                $merchantOrderInfo = $esSubOrderData['data'][0];
                                if (!empty($merchantOrderInfo)) {
                                    //推送订单语音消息
                                    $redis = new \Redis();
                                    $redis->connect(env('CACHE.HOST'), env('CACHE.PORT'));
                                    $redis->auth(env('CACHE.PASSWORD'));
                                    $redis->select(8);
                                    $redis->lPush('merchantSecondEventNotice', json_encode(['event' => 'order', 'merchant_id' => $merchantOrderInfo['merchant_id'], 'data' => '您有新的待发货订单，请及时处理'], JSON_UNESCAPED_UNICODE));
                                    //用户信息处理
                                    $userInfo        = httpGet(env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo', ['uid' => $val['uid'], 'field' => 'nickname']);
                                    $nickname        = isset($userInfo['data']['list'][0]['nickname']) ? $userInfo['data']['list'][0]['nickname'] : '';
                                    $consignee       = $merchantOrderInfo['consignee'];
                                    $consignee_phone = $merchantOrderInfo['consignee_phone'];
                                    $encrypt         = cryptionDeal(2, [$consignee, $consignee_phone], $merchantOrderInfo['uid'], '前端用户');
                                    $consignee       = isset($encrypt[$consignee]) ? $encrypt[$consignee] : '';
                                    $consignee_phone = isset($encrypt[$consignee_phone]) ? $encrypt[$consignee_phone] : '';
                                    $shipmentsData   = array(
                                        'main_order_no' => $merchantOrderInfo['main_order_no'],
                                        'sub_orders'    => array(
                                            array(
                                                'sub_order_no'      => $merchantOrderInfo['sub_order_no'],
                                                'period'            => $merchantOrderInfo['period'],
                                                'package_id'        => $merchantOrderInfo['package_id'],
                                                'order_qty'         => $merchantOrderInfo['order_qty'],
                                                'delivery_method'   => $merchantOrderInfo['delivery_method'],
                                                'delivery_store_id' => $merchantOrderInfo['delivery_store_id'],
                                                'merchant_id'       => $merchantOrderInfo['merchant_id'],
                                            )
                                        ),
                                        'receiver'      => array(
                                            'address'        => $merchantOrderInfo['province_name'] . $merchantOrderInfo['city_name'] . $merchantOrderInfo['district_name'],
                                            'address_detail' => $merchantOrderInfo['address'],
                                            'name'           => $consignee,
                                            'mobile'         => $consignee_phone,
                                        )
                                    );
                                    $shipments       = curlRequest(env('ITEM.VMALL_URL') . '/vmall/v3/shansong/shipments', json_encode($shipmentsData, JSON_UNESCAPED_UNICODE));
                                    if (!isset($shipments['error_code'])) $this->throwError('商家模块访问异常');
                                    if ($shipments['error_code'] != 0) $this->throwError($shipments['error_msg']);
                                    foreach ($shipments['data'] as &$v) {
                                        if ($v['status'] == 1) {
                                            $secondOrderData = array(
                                                'sub_order_status' => 2,
                                                'express_type'     => 100,
                                                'express_name'     => '闪送',
                                                'express_number'   => $v['waybill_order_no'],
                                                'delivery_time'    => time(),
                                                'update_time'      => time(),
                                            );
                                            Db::name('merchant_second_order')->where(['sub_order_no' => $v['sub_order_sn']])->update($secondOrderData);
                                        }
                                    }
                                    $delivery_method = [1 => "物流配送", 2 => "同城配送", 3 => "自提订单"];
                                    //群消息提醒
                                    $remindContent = "#### 实时订单\n";
                                    $remindContent .= "-发货点: " . $merchantOrderInfo['delivery_store_name'] . "\n";
                                    $remindContent .= "-用户昵称: " . $nickname . "\n";
                                    $remindContent .= "-手机号码: " . $consignee_phone . "\n";
                                    $remindContent .= "-商品名称：" . $merchantOrderInfo['title'] . "\n";
                                    $remindContent .= "-商品图片：[商品图片](" . imagePrefix($merchantOrderInfo['banner_img']) . ")\n";
                                    $remindContent .= "-子订单号：" . $merchantOrderInfo['sub_order_no'] . "\n";
                                    $remindContent .= "-子订单金额：" . $merchantOrderInfo['payment_amount'] . "\n";
                                    $remindContent .= "-配送地址: " . $merchantOrderInfo['province_name'] . $merchantOrderInfo['city_name'] . $merchantOrderInfo['district_name'] . $merchantOrderInfo['address'] . "\n";
                                    $remindContent   .= "-快递方式: " . ($delivery_method[$merchantOrderInfo['delivery_method']] ?? '') . "\n";
                                    $remindData    = array(
                                        'mid'     => $merchantOrderInfo['merchant_id'],
                                        'shop_id' => $merchantOrderInfo['delivery_store_id'],
                                        'msgtype' => 'markdown',
                                        'content' => $remindContent,
                                    );
                                    curlRequest(env('ITEM.VMALL_URL') . '/vmall/v3/order/remind', json_encode($remindData, JSON_UNESCAPED_UNICODE), []);
                                }
                            } else {//非商家秒发推送萌牙
                                $sub_order_no = Db::name($order_type[intval($val['order_type'])]['table'])->where(['main_order_id' => $val['id']])->value('sub_order_no');
                                $pushService  = new PushService();
                                $pushService->pushWms(['sub_order_no' => $sub_order_no, 'order_type' => $val['order_type']]);
                            }
                        }
                    }
                }
            } catch (\Exception $e) {
                //日志记录
                $this->notifyLog($requestparams . '------' . $e->getMessage());
                $this->throwError($main_order_no . $e->getMessage());
            }
            $this->updateOrderLog('group', $main_order_no);
        }
        return true;
    }

    /**
     * Description:队列处理异步回调订单满赠
     * Author: zrc
     * Date: 2022/4/12
     * Time: 13:45
     * @param $requestparams
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function notifyDealFullGift($requestparams)
    {
        $params             = json_decode($requestparams, true);
        $orderMain          = $params['orderMain'];
        $main_order_no      = $orderMain['main_order_no'];
        $notify_deal_status = Db::name('order_deal_log')->where(['main_order_no' => $main_order_no])->json(['notify_deal_status', true])->value('notify_deal_status');
        if ($notify_deal_status['fullgift'] == 0) {
            $sendGift = $this->httpPost(env('ITEM.FULLGIFT_URL') . '/fullgift/v3/activity/sendGift', ['main_order_no' => $main_order_no, 'order_type' => 0]);
            if ($sendGift['error_code'] != 0) {
                $this->notifyLog($requestparams . '------' . '满赠处理失败，' . '错误信息：' . $sendGift['error_msg']);
                $this->throwError($main_order_no . '满赠处理失败:' . $sendGift['error_msg']);
            }
            $this->updateOrderLog('fullgift', $main_order_no);
        }
        return true;
    }

    /**
     * Description:队列处理异步回调订单推送萌牙+订金膨胀订单处理
     * Author: zrc
     * Date: 2022/4/12
     * Time: 14:06
     * @param $requestparams
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function notifyDealPushWMS($requestparams)
    {
        sleep(2);
        $params             = json_decode($requestparams, true);
        $orderMain          = $params['orderMain'];
        $orderSub           = $params['orderSub'];
        $main_order_no      = $orderMain['main_order_no'];
        $notify_deal_status = Db::name('order_deal_log')->where(['main_order_no' => $main_order_no])->json(['notify_deal_status', true])->value('notify_deal_status');
        $pushService        = new PushService();
        if ($notify_deal_status['wms'] == 0) {
            $es         = new ElasticSearchService();
            $order_type = config('config')['order_type'];//订单频道获取
            foreach ($orderSub as &$val) {
                if (in_array($val['order_type'], [0, 1, 3, 4])) {
                    //订金膨胀订单处理
                    if (isset($val['special_type']) && $val['special_type'] == 4) {
                        try {
                            //查询订金套餐信息
                            $periodsSetData = Db::table('vh_commodities.vh_periods_flash_set')->field('deposit_coupon_id,is_deposit')->where(['id' => $val['package_id']])->find();
                            if (empty($periodsSetData)) $this->throwError('未获取到订金套餐优惠券信息');
                            $inflationRecordId = Db::name('deposit_inflation_record')->where(['uid' => $val['uid'], 'sub_order_no' => $val['sub_order_no']])->value('id');
                            //订金膨胀记录写入，后续发券使用
                            if (empty($inflationRecordId)) {
                                $inflationRecord   = array(
                                    'uid'          => $val['uid'],
                                    'sub_order_no' => $val['sub_order_no'],
                                    'period'       => $val['period'],
                                    'package_id'   => $val['package_id'],
                                    'deposit'      => $val['payment_amount'],
                                    'status'       => 1,
                                    'coupon_id'    => $periodsSetData['deposit_coupon_id'],
                                    'order_type'   => $val['order_type'],
                                    'created_time' => time(),
                                );
                                $inflationRecordId = Db::name('deposit_inflation_record')->insertGetId($inflationRecord);
                                if (empty($inflationRecordId)) $this->throwError('添加订金膨胀记录失败');
                            }
                            $pushCouponData = array(
                                'uid'       => $val['uid'],
                                'remark'    => $val['sub_order_no'] . '订金膨胀订单支付发放优惠券',
                                'coupon_id' => $periodsSetData['deposit_coupon_id'],
                            );
                            $dealCoupon     = httpPostString(env('ITEM.COUPON_URL') . '/coupon/v3/coupon/depositGrantCoupon', json_encode($pushCouponData));
                            if (!isset($dealCoupon['error_code'])) $this->throwError('优惠券模块访问异常');
                            if ($dealCoupon['error_code'] != 0) $this->throwError('优惠券发放异常：' . $dealCoupon['error_msg']);
                            $updateRecordData = array(
                                'status'          => 2,
                                'coupon_issue_id' => $dealCoupon['data']['coupon_issue_id'],
                                'update_time'     => time()
                            );
                            Db::name('deposit_inflation_record')->where(['id' => $inflationRecordId])->update($updateRecordData);
                        } catch (\Exception $e) {
                            //异常处理
                            $content   = "## 订金膨胀订单处理异常提示\n";
                            $content   .= "-订单号：" . $val['sub_order_no'] . "\n";
                            $content   .= "-期数：" . $val['period'] . "\n";
                            $content   .= "-套餐：" . $val['package_id'] . "\n";
                            $content   .= "-订金：" . $val['payment_amount'] . "\n";
                            $content   .= "-异常节点：订金订单支付回调处理异常异常" . "\n";
                            $content   .= "-异常原因：" . $e->getMessage() . "\n";
                            $data      = array(
                                'title' => '订金膨胀订单处理异常通知',
                                'text'  => $content
                            );
                            $queueData = array(
                                'access_token' => '873da4b2-1ac2-40e1-b78c-f3eb5e8d91b3',
                                'type'         => 'markdown',
                                'at'           => '',
                                'content'      => base64_encode(json_encode($data)),
                            );
                            $data      = base64_encode(json_encode($queueData));
                            $pushData  = array(
                                'exchange_name' => 'dingtalk',
                                'routing_key'   => 'dingtalk_sender',
                                'data'          => $data,
                            );
                            httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
                        }
                    } else {
                        //订金优惠券使用变更订金订单状态为已完成
                        if (isset($val['coupon_id']) && $val['coupon_id'] > 0) {
                            $depositRecord = Db::name('deposit_inflation_record')->where(['coupon_issue_id' => $val['coupon_id'], 'uid' => $val['uid']])->find();
                            if (!empty($depositRecord)) {
                                $refund_status = Db::name('flash_order')->where(['sub_order_no' => $depositRecord['sub_order_no'], 'uid' => $depositRecord['uid']])->value('refund_status');
                                if ($refund_status > 0) {//验证订金订单状态，已退款或退款中退款尾款订单
                                    $dingTalkService = new DingTalkService();
                                    $dingTalkService->TimeOutOrderCreateDingtalkVerify(['orderMain' => $orderMain, 'orderSub' => $orderSub, 'notify_param' => $params]);
                                    $queueData = array(
                                        'access_token' => '007b8136c66ac4a86349ee2dd9362a1781000ae8a494e3936e6f62016981a597',
                                        'type'         => 'text',
                                        'at'           => '18217627976',
                                        'content'      => base64_encode($main_order_no . '已发起退款审批，退款原因：订金订单-' . $depositRecord['sub_order_no'] . '已发起退款，尾款订单-' . $val['sub_order_no'] . '需做退款处理，因库存已消耗，如需调整请及时处理。'),
                                    );
                                    $data      = base64_encode(json_encode($queueData));
                                    $pushData  = array(
                                        'exchange_name' => 'dingtalk',
                                        'routing_key'   => 'dingtalk_sender',
                                        'data'          => $data,
                                    );
                                    httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
                                    Db::name('deposit_inflation_record')->where(['coupon_issue_id' => $val['coupon_id'], 'uid' => $val['uid']])->update(['status' => 3, 'update_time' => time()]);
                                    $this->updateOrderLog('wms', $main_order_no);
                                    $this->throwError($val['sub_order_no'] . '订金订单申请退款，尾款订单不退萌牙，做退款处理');
                                } else {//正常使用变更订金订单状态
                                    Db::name('flash_order')->where(['sub_order_no' => $depositRecord['sub_order_no'], 'uid' => $depositRecord['uid']])->update(['sub_order_status' => 3, 'update_time' => time()]);
                                }
                            }
                        }
                        //商品指令读取
                        $periodsArr    = array(
                            'index'  => ['periods'],
                            'match'  => [['id' => $val['period']]],
                            'source' => ['instruction']
                        );
                        $periodsEsData = $es->getDocumentList($periodsArr);
                        $instruction   = $periodsEsData['data'][0]['instruction'] ?? '';
                        //全局指令配置读取
                        $global_instruction = $this->httpGet(env('ITEM.PUSH_ORDERS_URL') . '/pushorders/v3/order/instruction', ['sub_order_no' => $val['sub_order_no'], 'order_type' => $val['order_type']]);
                        if (isset($global_instruction['data']['product_ids']) && !empty($global_instruction['data']['product_ids'])) {
                            if (!empty($instruction)) {
                                $instructions = $global_instruction['data']['product_ids'] . ',' . $instruction;
                            } else {
                                $instructions = $instruction;
                            }
                        } else {
                            $instructions = $instruction;
                        }
                        Db::name($order_type[intval($val['order_type'])]['table'])->where(['id' => $val['id']])->update(['instructions' => $instructions, 'update_time' => time()]);
                        $pushService->pushWms(['sub_order_no' => $val['sub_order_no'], 'order_type' => $val['order_type']]);
                        }
                    }
                }
            $this->updateOrderLog('wms', $main_order_no);
        }
        return true;
    }

    /**
     * Description:公共修改订单回调处理状态
     * Author: zrc
     * Date: 2022/4/12
     * Time: 12:27
     * @param $type
     * @param $main_order_no
     * @return bool
     * @throws \think\db\exception\DbException
     */
    public function updateOrderLog($type, $main_order_no)
    {
        $data['notify_deal_status->' . $type] = 1;
        Db::name('order_deal_log')->where(['main_order_no' => $main_order_no])->json(['notify_deal_status', true])->update($data);
        return true;
    }

    /**
     * Description:回调日志记录
     * Author: zrc
     * Date: 2021/8/18
     * Time: 11:13
     */
    public function notifyLog($data)
    {
        file_put_contents(root_path() . "/runtime/log/" . date('Ym') . "/" . date('Ymd') . 'notifyDeal' . '.log', $data . PHP_EOL, FILE_APPEND);
    }

    /**
     * Description:跨境代付支付回调处理
     * Author: zrc
     * Date: 2022/10/28
     * Time: 11:06
     * @param $params
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function payOnBehalf($params)
    {
        $orderMain     = $params['orderMain'];
        $orderSub      = $params['orderSub'];
        $main_order_no = $orderMain['main_order_no'];
        $behalfData    = Db::name('cross_pay_on_behalf')->where(['main_order_no' => $main_order_no])->find();
        if (empty($behalfData)) $this->behalfLog($main_order_no . '代付异步回调处理失败：未获取到代付记录');
        Db::startTrans();
        try {
            Db::name('cross_pay_on_behalf')->where(['id' => $behalfData['id']])->update(['status' => 1, 'update_time' => time(), 'pay_on_behalf_time' => time()]);
            Db::name('cross_pay_on_behalf')->where(['main_order_no' => $main_order_no, 'status' => 0])->update(['status' => 2, 'update_time' => time()]);
            foreach ($orderSub as &$val) {
                Db::name('cross_order')->where(['sub_order_no' => $val['sub_order_no']])->update(['realname' => $behalfData['realname'], 'id_card_no' => $behalfData['id_card_no'], 'behalf_uid' => $behalfData['behalf_uid'], 'update_time' => time()]);
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->behalfLog($main_order_no . '代付异步回调处理失败：' . $e->getMessage());
        }
        return true;
    }

    /**
     * Description:代付日志记录
     * Author: zrc
     * Date: 2022/9/29
     * Time: 17:02
     * @param $data
     */
    public function behalfLog($data)
    {
        file_put_contents(root_path() . "/runtime/log/" . date('Ym') . "/" . date('Ymd') . 'behalfDeal' . '.log', $data . PHP_EOL, FILE_APPEND);
    }
}