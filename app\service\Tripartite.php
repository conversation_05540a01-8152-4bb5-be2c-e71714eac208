<?php


namespace app\service;


use app\BaseService;
use app\service\Order as OrderService;
use app\service\Push as PushService;
use think\Exception;
use think\facade\Db;
use think\facade\Log;

class Tripartite extends BaseService
{
    /**
     * Description:三方订单录入
     * Author: zrc
     * Date: 2022/5/11
     * Time: 9:20
     * @param $requestparams
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function orderInput($requestparams)
    {
        $params              = $requestparams;
        $store_wh = [
            "662831451"  => "892",
            "452890329"  => "892",
            "558695549"  => "892",
            "566768114"  => "892",
            "419938814"  => "892",
            "227734657"  => "892",
            "541276454"  => "892",
            "10380783"   => "893",
            "10270164"   => "893",
            "1000398259" => "893"
        ];
        $swh_code = $store_wh[$params['owner_id']] ?? null;

        $trstore             = array_column(array_merge(config('config.tripartite_store'), config('config.tripartite_sg_store')), 'label', 'value');
        $params['storename'] = $trstore[$params['owner_id']] ?? $params['storename'];
        $paramOrder = $params['goodsOrder']['main'];
        $paramSon   = $params['goodsOrder']['son'];
        $address    = $params['address'];
        $is_force_write = $params['is_force_write'] ?? 0;
        $wms_up_data = [
            'receiver_name'  => $address['consignee'],
            'receiver_phone' => $address['cellphone'],
            'province'       => $address['province'],
            'city'           => $address['city'],
            'town'           => $address['district'],
            'address'        => $address['address'],

        ];
        //收货人、收货人电话加密储存
        $encrypt              = cryptionDeal(1, [$address['consignee'], $address['cellphone']], '15736175219', '宗仁川');
        $address['consignee'] = isset($encrypt[$address['consignee']]) ? $encrypt[$address['consignee']] : '';
        $address['cellphone'] = isset($encrypt[$address['cellphone']]) ? $encrypt[$address['cellphone']] : '';
        $mainData             = [
            'uid'               => '',
            'main_order_no'     => $paramOrder['orderno'],
            'main_order_status' => isset($paramOrder['status']) ? $paramOrder['status'] : 1,
            'payment_method'    => '',
            'payment_amount'    => $paramOrder['pay_money'],
            'payment_time'      => $paramOrder['payment_time'],
            'province_id'       => 0,
            'city_id'           => 0,
            'district_id'       => 0,
            'address'           => $address['address'],
            'consignee'         => $address['consignee'],
            'consignee_phone'   => $address['cellphone'],
            'created_time'      => $paramOrder['create_time'],
            'order_type'        => 7
        ];
        $receiveInfo = [];
        if ($params['platform'] == 31) {
            $mainData['address'] = $address['province'] . $address['city'] . $address['district'] . $address['address'];
            $sub_order_nos = array_column($paramSon, 'orderno');
            $hasReceiveInfo = Db::name('sub_order_receive_information')
                ->where([
                    ['sub_order_no', 'in', $sub_order_nos],
                ])->column('sub_order_no');
            $need_orders = array_values(array_diff($sub_order_nos, $hasReceiveInfo));
            if (!empty($need_orders)) {
                foreach ($need_orders as $need_order) {
                    $receiveInfo[] = [
                        'uid'             => 0,
                        'main_order_no'   => $mainData['main_order_no'],
                        'sub_order_no'    => $need_order,
                        'order_type'      => 7,
                        'province_id'     => 0,
                        'city_id'         => 0,
                        'district_id'     => 0,
                        'address'         => $address['address'],
                        'consignee'       => $address['consignee'],
                        'consignee_phone' => $address['cellphone'],

                        'province_name' => $address['province'],
                        'city_name'     => $address['city'],
                        'district_name' => $address['district'],
                        'update_time'   => time(),
                    ];
                }
            }
        }
        Db::startTrans();
        try {
            if (in_array($params['owner_id'], ["*********","*********", "18565487", "*********", "wx0fe6132e6a98d48a",  "650a60e17fa15200013acf16", "*********", "*********"])) {
                $params['company_id'] = 3;//木兰朵
            }
            $inputType = 1;//默认写入类型：添加
            $em_info = Db::name('order_main')->where(['main_order_no' => $paramOrder['orderno']])->find();
            $pid = $em_info['id'] ?? null;
            $up_wms_address = false;
            if (!empty($pid)) {
                #VHVT-10016  三方订单 - 三方平台订单写入中台后再收到信息，不修改产品明细，只修改状态
                $mainData = [
                    'main_order_status' => $mainData['main_order_status'],
                ];
                Db::name('order_main')->where('id', $pid)->update($mainData);
                $inputType = 2;//写入类型：修改

//                if (
//                    ($em_info['province_id'] != $mainData['province_id'])
//                    || ($em_info['city_id'] == $mainData['city_id'])
//                    || ($em_info['district_id'] == $mainData['district_id'])
//                    || ($em_info['address'] == $mainData['address'])
//                    || ($em_info['consignee'] == $mainData['consignee'])
//                    || ($em_info['consignee_phone'] == $mainData['consignee_phone'])
//                ) {
//                    $up_wms_address = true;
//                }
            } else {
                $pid = Db::name('order_main')->insertGetId($mainData);
            }
            if (!$pid) {
                Db::rollback();
                $this->throwError('主订单创建失败');
            }
            $tr_ordernos = array_column($paramSon,'orderno');
            $tr_orders = Db::name('tripartite_order')->where('sub_order_no', 'in', $tr_ordernos)->column('sub_order_no,push_wms_status','sub_order_no');
            foreach ($paramSon as $key => $value) {
                $productArr = [];
                //计算套餐的总瓶数
                $prod_num = 0;
                foreach ($value['product'] as &$va) {
                    if (!isset($va['is_gift'])) $va['is_gift'] = 0;//默认不是赠品
                    if ($va['is_gift'] == 0) {
                        $prod_num += $va['number'];
                    }
                }
                //重新排序，保证最后一条在均分金额时加上不能整除的余数
                $is_gift = array_column($value['product'], 'is_gift');
                array_multisort($is_gift, SORT_DESC, $value['product']);
                if (!empty($value['product'])) {
                    foreach ($value['product'] as $k => $v) {
                        if (empty($v['short_code']) || empty($v['number'])) {
                            Db::rollback();
                            $this->throwError('产品的简码和数量不能为空');
                        }
                        if ($prod_num == 0) {
                            $tprice = 0;
                        } else if ($prod_num < 1) {
                            $divisible_price = 0;
                            $tprice          = round($value['pay_money'] / $prod_num * $v['number'], 2);
                        } else {
                            $divisible_price = round((($value['pay_money'] * 100) % $prod_num) / 100, 2); //整除金额  ,如果不能整除，最后一个商品加上余数金额
                            if ($divisible_price > 0) {
                                $tprice = round(($value['pay_money'] - $divisible_price) / $prod_num * $v['number'], 2);
                            } else {
                                $tprice = round($value['pay_money'] / $prod_num * $v['number'], 2);
                            }
                        }
                        if ($v['is_gift'] == 1) {
                            $price = 0;
                        } else {
                            $price = $k + 1 == count($value['product']) ? $tprice + $divisible_price : $tprice;  //最后一个产品加上余数金额
                        }
                        $productArr[] = trim($v['short_code']) . '*' . $v['number'] . '*' . $price;
                    }
                    $items_info = implode(',', $productArr);
                } else {
                    $items_info = '';
                }
                $company_id = 0;
                if (isset($value['company_id'])) $company_id = $value['company_id'];
                if (isset($params['company_id'])) $company_id = $params['company_id'];
                $subOrderData = array(
                    'sub_order_no'          => $value['orderno'],
                    'sub_order_status'      => $value['status'],
                    'main_order_id'         => $pid,
                    'title'                 => $value['goodsname'] ?? '',
                    'items_info'            => $items_info,
                    'store_id'              => $params['owner_id'],
                    'store_name'            => $params['storename'] ?? '',
                    'warehouse_id'          => $swh_code ?? ($value['store_code'] ?? ''),
                    'order_from_thirdparty' => $params['platform'],
                    'order_qty'             => $value['pay_number'],
                    'payment_amount'        => $value['pay_money'],
                    'express_type'          => $address['express_type'] ?? 1,
                    'express_fee'           => $address['express_fee'],
                    'payment_time'          => $paramOrder['payment_time'],
                    'created_time'          => $paramOrder['create_time'] ?? time(),
                    'order_type'            => 7,
                    'province'              => $address['province'],
                    'city'                  => $address['city'],
                    'district'              => $address['district'],
                    'address'               => $address['address'],
                    'company_id'            => $company_id,
                    'user_remark'           => $paramOrder['remark'] ?? '',
                    'predict_time'          => $value['predict_time'] ?? 0,
                    'is_supplier_delivery'  => isset($value['is_supplier_delivery']) ? $value['is_supplier_delivery'] : 0,
                    'related_order_no'      => isset($value['related_order_no']) ? $value['related_order_no'] : '',
                    'refund_status'         => isset($value['refund_status']) ? $value['refund_status'] : 0,
                    'push_wms_status'       => $tr_orders[$value['orderno']]['push_wms_status'] ?? 0,
                );
                //订单状态
                if (($is_force_write != 1) && $inputType == 1 && ($subOrderData['sub_order_status'] != 1 || $subOrderData['refund_status'] != 0)) {
                    $refund_status_arr     = [
                        0 => "未退款",
                        1 => "退款中",
                        2 => "退款成功",
                        3 => "退款失败"
                    ];
                    $refund_status_text    = $refund_status_arr[$subOrderData['refund_status']] ?? $subOrderData['refund_status'];
                    $sub_order_status_arr  = [
                        "0" => "待支付", "1" => "已支付", "2" => "已发货", "3" => "已完成", "4" => "已取消", "5" => "已拒收"
                    ];
                    $sub_order_status_text = $sub_order_status_arr[$subOrderData['sub_order_status']] ?? $subOrderData['sub_order_status'];

                    $msg = "三方订单同步创建错误 
主单号: {$paramOrder['orderno']}
子单号: {$subOrderData['sub_order_no']}
订单状态: {$sub_order_status_text}
退款状态: {$refund_status_text}";
                    \Curl::wecomSend($msg, 'LongFei', 'text');
                    continue;
                }
                //门店、猫超不推萌牙
                if (in_array($params['platform'], [22, 23])) {
                    $subOrderData['push_wms_status'] = 3;
                    $subOrderData['delivery_time']   = $paramOrder['delivery_time'] ?? time();
                    $subOrderData['express_number']  = isset($address['express_number']) ? $address['express_number'] : 'MR123456';
                    if (empty($items_info)) $subOrderData['push_t_status'] = 3;
                }
                if (in_array($params['platform'], [27])) {
                    if (!empty($address['express_number'])) {
                        $subOrderData['delivery_time']  = $paramOrder['delivery_time'] ?? time();
                        $subOrderData['express_number'] = isset($address['express_number']) ? $address['express_number'] : 'MR123456';

                        if (!in_array($subOrderData['sub_order_status'], ['4']) && !in_array($subOrderData['refund_status'], ['2'])) {
                            $subOrderData['push_t_status'] = 0;
                            if (empty($items_info)) $subOrderData['push_t_status'] = 3;
                        }
                    }
                }
                //老外特殊处理 不推萌芽 需要根据收到的【订单状态】判断是否推送ERP
                if (in_array($params['platform'], [21])) {
                    $subOrderData['push_wms_status'] = 3; //不推萌芽
                    if ($value['status'] == 2) {
                        $subOrderData['delivery_time']  = $paramOrder['delivery_time'] ?? time();
                        $subOrderData['express_number'] = isset($address['express_number']) ? $address['express_number'] : 'MR123456';
                        $subOrderData['push_t_status']  = 0;
                    }
                    if (empty($items_info) || $value['status'] == 4) $subOrderData['push_t_status'] = 3;
                }
                //天猫国际、木兰朵天猫旗舰店、木兰朵酒类旗舰店不推erp
                if (in_array($params['platform'], [13, 24, 25, 28])) {
                    $subOrderData['push_t_status'] = 3;
                }
                //【小红书】木兰朵Mulando的店不推erp   【天猫】一大包零食不推erp
                if (in_array($params['owner_id'], ['650a60e17fa15200013acf16','*********','wx0fe6132e6a98d48a', '114968496', '18565487', '*********'])) {
                    $subOrderData['push_t_status'] = 3;
                }
                //:[快团团]惺忪 松鸽的Soul List不推erp
                if (in_array($params['owner_id'], ['517978682', '20369140079'])) {
                    $subOrderData['push_t_status'] = 3;
                }
                //:[快团团]惺忪 松鸽的Soul List 不推萌牙
                if (in_array($params['owner_id'], ['517978682', '20369140079'])) {
//                    $subOrderData['push_wms_status'] = 3;
                }
                //快团团代发不推萌牙
                if ($subOrderData['is_supplier_delivery'] == 1) {
                    $subOrderData['push_wms_status'] = 3;
                }
                $subData[] = $subOrderData;
            }
            if (empty($subData)) {
                Db::rollback();
                return true;
            }
            switch ($inputType) {
                case 1://添加
                    $sonRes = Db::name('tripartite_order')->insertAll($subData);
                    if (!$sonRes) {
                        Db::rollback();
                        $this->throwError('子订单创建失败');
                    }
                    break;
                case 2://修改
                    $orderData = Db::name('tripartite_order')->field('id,sub_order_no,created_time,push_wms_status,items_info,express_number')->where('main_order_id', $pid)->select()->toArray();
                    $tripartite_store = [
                        '京东'   => ["10136705","18565487","19051305", "1000398259", "10232541", "311029595"],
                        '快团团' => ["216379813", "20318455683", "20959411495", "20121190650", "20369140079", "517978682", "848265289"],
                        '拼多多' => ["*********", "827871079", "961036630", "662831451"],
                        '淘系'   => ["28073049081", "28073049081", "2807304908", "2807304908", "452890329", "227734657", "419938814", "5946418", "tianmaochaoshi20230518", "558695549", "566768114", "258788035", "541276454", "492964257", "wx91084bb0a5b66d53", "wx7ab502be70d73aa5", "114968496", "wx0fe6132e6a98d48a"],
                        '小红书' => ["62b98b750d601800010dc853", "65113b63effd830001ca90e0", "653b599dbffe730001559bd6", "650a60e17fa15200013acf16", "6426461c6fdda100018a7b76", "6542368b368edf00013dc6f5", "646b6293ebbcb20001b9ee01"],
                        '自研'   => ["68119621", "686511", "*********", "*********", "8911798", "store00013", "store00014", "store00016", "ldd_1"],
                    ];
                    $ors = [
                        '0' => '未退款',
                        '1' => '退款中',
                        '2' => '退款成功',
                        '3' => '退款失败'
                    ]; //订单退款状态
                    $osos = [
                        '0' => '待支付',
                        '1' => '已支付',
                        '2' => '已发货',
                        '3' => '已完成',
                        '4' => '已取消',
                    ]; //订单退款状态

                    foreach ($subData as &$item1) {
                        $isSure = 0;
                        foreach ($orderData as $item) {
                            if ($item['sub_order_no'] == $item1['sub_order_no']) {
                                //同步状态验证
                                if (in_array($item, [2, 3]) && $item1 == 1) {
                                    Db::rollback();
                                    $this->throwError($item['sub_order_no'] . '订单已发货或已完成，不允许同步为已支付');
                                }
                                $isSure = 1;

                                // 如果中台的订单已经有快递单号，也不接受状态修改
                                if (!empty($item['express_number'])) {
                                    continue;
                                }

                                //三方订单同步【已发货、已完成】消息到中台时，如果中台订单没有运单号，需要给龙飞中台发送消息
                                if (!empty($item1['sub_order_status']) && in_array($item1['sub_order_status'], [2, 3])) {
                                    try {
                                        $userid = !empty(env('APP_DEBUG')) ? 'XuanYiChang' : 'LongFei';
                                        $storename = $params['storename'] ?? '';
                                        $sub_order_no = $item1['sub_order_no'] ?? '';
                                        $content = "【{$storename}】{$sub_order_no} 已在店铺后台违规上传单号！";
                                        (new \app\service\WeChat())->weChatSendText($userid, $content);
                                    }  catch (\Exception $e) {
                                        Log::error('三方订单同步【已发货、已完成】消息到中台时，如果中台订单没有运单号，需要给龙飞中台发送消息失败：'.$e->getMessage());
                                    }
                                    
                                }

                                $item1['created_time'] = $item['created_time'];
                                $item1['update_time']  = time();
                                if ($item1['order_from_thirdparty'] == 17 && $item['push_wms_status'] == 1) $item1['items_info'] = $item['items_info'];
                                if (Db::name('order_remarks')
                                    ->where('sub_order_no', $item1['sub_order_no'])
                                    ->where('remarks', 'LIKE', '%, 更新后产品信息:%')
                                    ->count()) $item1['items_info'] = $item['items_info'];
                                #VHVT-10016 #三方平台订单写入中台后再收到信息，不修改产品明细，只修改状态和个人信息
                                $sub_update = $item1;
                                unset($sub_update['items_info']);
                                unset($sub_update['payment_amount']);
                                Db::name('tripartite_order')->where('id', $item['id'])->update($sub_update);
                                // Db::name('tripartite_order')->where('id', $item['id'])->update([
                                //     'sub_order_status' => $item1['sub_order_status'],
                                // ]);
                                if (in_array($params['owner_id'], $tripartite_store['淘系'])) {
                                    $remarks = array(
                                        'sub_order_no' => $item1['sub_order_no'],
                                        'order_type'   => 7, //
                                        'content'      => "订单号:{$item1['sub_order_no']},发货状态:" . ($osos[$item1['sub_order_status']] ?? '') . ",退款状态:" . ($ors[$item1['refund_status']] ?? '') . ",运单号:" . ($item1['express_number'] ?? ''),
                                        'admin_id'     => 0
                                    );
                                    (new OrderService())->createRemarks($remarks);
                                }
                            }
                        }
                        if ($isSure == 0) {
                            $sonRes = Db::name('tripartite_order')->insert($item1);
                            if (!$sonRes) {
                                Db::rollback();
                                $this->throwError('子订单创建失败');
                            }
                        }
                    }
                    break;
            }
            if (!empty($receiveInfo)) {
                Db::name('sub_order_receive_information')->insertAll($receiveInfo);
            }
            Db::commit();
            // 三方平台来源: 1-京东 2-淘宝 3-拼多多 4-天猫 13-天猫国际 15-淘宝代发 16-兔总天猫代发 17-小红书 18-抖店直播 19-微店直播 20-得物APP 21-老外买酒 22-门店 23-天猫超市 24-木兰朵天猫旗舰店 25-木兰朵酒类旗舰店
            //老外买酒、门店、猫超推送erp
            if (in_array($params['platform'], [22, 23]) || ($params['platform'] == '21' && $value['status'] == 2)) {
                foreach ($subData as &$val) {
                    if (!empty($val['items_info'])) {
                        $pushData = array(
                            'namespace' => 'orders',
                            'key'       => 'tripartite_' . $val['sub_order_no'],
                            'data'      => base64_encode(json_encode(['sub_order_no' => $val['sub_order_no']])),
                            'callback'  => env('ITEM.ORDERS_URL') . '/orders/v3/push/pushTplus',
                            'timeout'   => '30m',
                        );
                        httpPostString(env('ITEM.TIMEING_SERVICE_URL') . '/services/v3/timing/add', json_encode($pushData));
                    }
                }
            }
            //发放赠品
            if (!in_array($params['platform'], [15, 16, 21, 22, 23, 24])) {
                $sendGift = $this->httpPost(env('ITEM.FULLGIFT_URL') . '/fullgift/v3/activity/sendGift', ['main_order_no' => $paramOrder['orderno'], 'order_type' => 7]);
                if ($sendGift['error_code'] != 0) {
                    $this->throwError('满赠处理失败:' . $sendGift['error_msg']);
                }
            }
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('三方订单录入失败！请求参数：' . json_encode($params, JSON_UNESCAPED_UNICODE) . '，错误信息：' . $e->getMessage());
            $this->throwError($paramOrder['orderno'] . $e->getMessage());
        } finally {
            //添加三方订单录入日志
            $log = [
                'main_order_no'         => $paramOrder['orderno'],
                'order_from_thirdparty' => $params['platform'],
                'request_param'         => json_encode($params, JSON_UNESCAPED_UNICODE),
                'send_gift_result'      => isset($sendGift['error_code']) ? json_encode($sendGift, JSON_UNESCAPED_UNICODE) : '',
                'created_time'          => date('Y-m-d H:i:s', time()),
            ];
            Db::name('tripartite_order_input_log')->insert($log);
        }
        //订单售后处理
        foreach ($subData as &$values) {
            $push_wms_status = Db::name('tripartite_order')->where(['sub_order_no' => $values['sub_order_no']])->value('push_wms_status');
            if ($up_wms_address && $push_wms_status == 1) {
                try {
                    $wms_up_data['orderno'] = $values['sub_order_no'];//商家订单号
                    \Curl::receiptInfo($wms_up_data);
                } catch (Exception $ex) {
                    try {
                        Log::write('三方订单更新更新地址推送萌芽失败:' . $ex->getMessage() . json_encode($wms_up_data));
                        $res = \Curl::addrecord([
                            'docid'    => 'dcs6-TyGHcKYX0XaQgYHjNCb2fIjou8ljfdPCFztz2tSuGi-bGHU0URUgyNg3VKZX1DHXKqLasAFe0lHOnmzlNUQ',
                            'sheet_id' => 'rJ4PCd',
                            'records'  => [
                                [
                                    "values" => [
                                        "订单号"   => [
                                            [
                                                "type" => "text",
                                                "text" => strval($wms_up_data['orderno'])
                                            ]
                                        ],
                                        "店铺名称" => [
                                            [
                                                "type" => "text",
                                                "text" => strval($params['storename'] ?? '')
                                            ]
                                        ],
                                        "异常类型" => [
                                            [
                                                "type" => "text",
                                                "text" => strval('修改地址')
                                            ]
                                        ],
                                        "异常时间" => strval(round(microtime(true) * 1000)),
                                        "异常原因" => [
                                            [
                                                "type" => "text",
                                                "text" => strval($ex->getMessage())
                                            ]
                                        ],
                                        "处理人"   => [
                                            [
                                                "id" => "untreated"
                                            ]
                                        ],
                                        "处理结果" => [
                                            [
                                                "id" => "untreated"
                                            ]
                                        ],
                                    ]
                                ]
                            ],
                        ]);
                    } catch (Exception $iex) {
                    }
                }
            }
            switch ($values['refund_status']) {
                case 0://取消售后解冻结订单
                    curlRequest(env('ITEM.DISTRIBUTE_URL') . '/sync/receiveOrderFreeze', ['store_code' => env('ORDERS.STORE_CODE'), 'orderno' => $values['sub_order_no'], 'status' => 2]);
                    break;
                case 1://发起售后冻结订单
                    $freeze = curlRequest(env('ITEM.DISTRIBUTE_URL') . '/sync/receiveOrderFreeze', ['store_code' => env('ORDERS.STORE_CODE'), 'orderno' => $values['sub_order_no'], 'status' => 1]);
                    if ($inputType == 2 && $push_wms_status == 1) {//已推送萌牙返回错误信息
                        if (!isset($freeze['msg'])) $this->throwError($values['sub_order_no'] . '萌牙模块访问异常，冻结订单失败！');
                        if ($freeze['errorCode'] != 0) $this->throwError($values['sub_order_no'] . '冻结订单失败:' . $freeze['msg']);
                    }
                    break;
                case 2://确认退款撤单
                    $receiveOrderSync = curlRequest(env('ITEM.DISTRIBUTE_URL') . '/sync/receiveOrderSync', ['store_code' => env('ORDERS.STORE_CODE'), 'orderno' => $values['sub_order_no'], 'refund_status' => 1]);
                    if ($inputType == 2 && $push_wms_status == 1) {//已推送萌牙返回错误信息
                        if (!isset($receiveOrderSync['msg'])) {
                            $err_msg = $values['sub_order_no'] . '萌牙模块访问异常，撤单失败！';
                        } elseif ($receiveOrderSync['errorCode'] != 0) {
                            $err_msg = $values['sub_order_no'] . '撤单失败:' . $receiveOrderSync['msg'];
                        }

                        $remarks      = array(
                            'sub_order_no' => $values['sub_order_no'],
                            'order_type'   => 7,
                            'content'      => $err_msg ?? '萌芽撤单成功',
                            'admin_id'     => 0
                        );
                        $orderService = new OrderService();
                        $orderService->createRemarks($remarks);

                        if (!empty($err_msg)) {
                            $this->throwError($err_msg);
                        }
                    }
                    break;
            }
        }
        return true;
    }

    /**
     * Description:三方订单列表
     * Author: zrc
     * Date: 2023/2/3
     * Time: 11:22
     * @param $params
     * @return mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function orderList($params)
    {
        $result['list']  = [];
        $result['total'] = 0;
        $page            = !empty($params['page']) ? $params['page'] : 1;
        $limit           = !empty($params['limit']) ? $params['limit'] : 10;
        $offset          = ($page - 1) * $limit;
        $where           = [];
//        $where[]         = ['to.order_from_thirdparty', '<>', 24];
        if (!empty($params['sub_order_no'])) {
            $where[] = ["to.sub_order_no", "=", $params['sub_order_no']];
        }
        if (isset($params['sub_order_status']) && (strlen($params['sub_order_status']) > 0)) {
            $where[] = ["to.sub_order_status", "=", $params['sub_order_status']];
        }
        if (isset($params['company_id']) && (strlen($params['company_id']) > 0)) {
            $where[] = ["to.company_id", "=", $params['company_id']];
        }
        if (!empty($params['store_id'])) {
            $store_id_arr = explode(',', $params['store_id']);
            $where[]      = ["to.store_id", "in", $store_id_arr];
        }
        if (!empty($params['not_store_id'])) {
            $not_store_id_arr = explode(',', $params['not_store_id']);
            $where[]          = ["to.store_id", "not in", $not_store_id_arr];
        }
        if (!empty($params['empty_express_number']) && $params['empty_express_number'] == 1) {
            $where[] = ["to.express_number", "=", null];
        }
        if (isset($params['push_t_status']) && is_numeric($params['push_t_status'])) {
            $where[] = ['to.push_t_status', '=', $params['push_t_status']];
        }
        if (isset($params['is_ts']) && (strlen($params['is_ts']) > 0)) {
            $where[] = ['to.is_ts', '=', $params['is_ts']];
        }
        if (isset($params['items_info']) && (strlen($params['items_info']) > 0)) {
            $where[] = ['to.items_info', 'LIKE', "%{$params['items_info']}%"];
        }
        if (isset($params['push_wms_status']) && is_numeric($params['push_wms_status'])) {
            $where[] = ['to.push_wms_status', '=', $params['push_wms_status']];
        }
        if (isset($params['is_supplier_delivery']) && is_numeric($params['is_supplier_delivery'])) {
            $where[] = ['to.is_supplier_delivery', '=', $params['is_supplier_delivery']];
        }
        if (!empty($params['related_order_no'])) {
            $where[] = ["to.related_order_no", "=", $params['related_order_no']];
        }
        if (!empty($params['main_order_no'])) {
            $where[] = ["om.main_order_no", "=", $params['main_order_no']];
        }
        if (!empty($params['created_time_start'])) {
            $where[] = ['to.created_time', '>=', strtotime($params['created_time_start'])];
        }
        if (!empty($params['created_time_end'])) {
            $where[] = ['to.created_time', '<', strtotime($params['created_time_end'])];
        }
        if (!empty($params['warehouse_id'])) {
            $where[] = ['to.warehouse_id', "=", $params['warehouse_id']];
        }
        $totalNum = Db::name('tripartite_order')
            ->alias('to')
            ->leftJoin('order_main om', 'om.id=to.main_order_id')
            ->where($where)
            ->count();
        $lists    = Db::name('tripartite_order')
            ->alias('to')
            ->field('to.*,om.main_order_no')
            ->field('to.*,om.main_order_no,om.consignee,om.consignee_phone')
            ->leftJoin('order_main om', 'om.id=to.main_order_id')
            ->where($where)
            ->limit($offset, $limit)
            ->order('id desc')
            ->select()->toArray();

        $enc_data = array_values(array_unique(array_merge(array_column($lists, 'consignee'), array_column($lists, 'consignee_phone'))));
        $dec_data = cryptionDeal(2, $enc_data, '15736175219', '宗仁川');

        $warehouse_ids = array_values(array_unique(array_column($lists, 'warehouse_id')));
        $warehouse_map = Db::table('vh_push_t_plus.vh_erp_code_map')->where([
            ['type', '=', 5],
            ['vh_code', 'in', $warehouse_ids],
        ])->column('name', 'vh_code');

        $admin_id_arr = array_unique(array_column($lists, 'operator'));
        $admin_id_str = implode(',', $admin_id_arr);
        $adminInfo    = $this->httpGet(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info', ['admin_id' => $admin_id_str, 'field' => 'realname']);
        if (count($lists) > 0) {


            $trproducts = [];
            foreach ($lists as $val) {
                $goodsInfo = explode(',', $val['items_info']);
                foreach ($goodsInfo as $k => $v) {
                    $goods = explode('*', $v);
                    if (!empty($goods[0])) {
                        $trproducts[] = $goods[0];
                    }
                }
            }
            $wiki_products = Db::table('vh_wiki.vh_products')->where('short_code','in', $trproducts)->column('id,cn_product_name','short_code');

            foreach ($lists as $key => $val) {
                $lists[$key]['consignee']       = $dec_data[$val['consignee']] ?? '';
                $lists[$key]['consignee_phone'] = $dec_data[$val['consignee_phone']] ?? '';
                $lists[$key]['payment_time']    = $val['payment_time'] > 0 ? date('Y-m-d H:i:s', $val['payment_time']) : '';
                $lists[$key]['created_time']    = date('Y-m-d H:i:s', $val['created_time']);
                $lists[$key]['delivery_time']   = $val['delivery_time'] > 0 ? date('Y-m-d H:i:s', $val['delivery_time']) : '';
                $lists[$key]['operator']        = isset($adminInfo['data'][$val['operator']]) ? $adminInfo['data'][$val['operator']] : '';
                //明细处理
                $items_info = [];
                $goodsInfo  = explode(',', $val['items_info']);
                foreach ($goodsInfo as $k => $v) {
                    $goods                        = explode('*', $v);
                    $items_info[$k]['short_code'] = isset($goods[0]) ? $goods[0] : '';
                    $items_info[$k]['cn_product_name'] = $wiki_products[$items_info[$k]['short_code']]['cn_product_name'] ?? '';
                    $items_info[$k]['nums']       = isset($goods[1]) ? $goods[1] : '';
                    $items_info[$k]['price']      = isset($goods[2]) ? intval($goods[2]) : 0;
                }
                $lists[$key]['items_info'] = $items_info;
                $lists[$key]['warehouse_name'] = $warehouse_map[$val['warehouse_id']] ?? '';
            }
        }
        $result['list']  = $lists;
        $result['total'] = $totalNum;
        return $result;
    }

    public function orderListExport($params)
    {
        $userid  = \Curl::adminInfo(['admin_id' => $params['admin_id']])[$params['admin_id']]['userid'];
        $where   = [];
//        $where[] = ['to.order_from_thirdparty', '<>', 24];
        if (!empty($params['sub_order_no'])) {
            $where[] = ["to.sub_order_no", "=", $params['sub_order_no']];
        }
        if (!empty($params['sub_order_status'])) {
            $where[] = ["to.sub_order_status", "=", $params['sub_order_status']];
        }
        if (!empty($params['company_id'])) {
            $where[] = ["to.company_id", "=", $params['company_id']];
        }
        if (!empty($params['store_id'])) {
            $store_id_arr = explode(',', $params['store_id']);
            $where[]      = ["to.store_id", "in", $store_id_arr];
        }
        if (!empty($params['not_store_id'])) {
            $not_store_id_arr = explode(',', $params['not_store_id']);
            $where[]          = ["to.store_id", "not in", $not_store_id_arr];
        }
        if (!empty($params['empty_express_number']) && $params['empty_express_number'] == 1) {
            $where[] = ["to.express_number", "=", null];
        }
        if (isset($params['push_t_status']) && is_numeric($params['push_t_status'])) {
            $where[] = ['to.push_t_status', '=', $params['push_t_status']];
        }
        if (isset($params['is_ts']) && (strlen($params['is_ts']) > 0)) {
            $where[] = ['to.is_ts', '=', $params['is_ts']];
        }
        if (isset($params['items_info']) && (strlen($params['items_info']) > 0)) {
            $where[] = ['to.items_info', 'LIKE', "%{$params['items_info']}%"];
        }
        if (isset($params['push_wms_status']) && is_numeric($params['push_wms_status'])) {
            $where[] = ['to.push_wms_status', '=', $params['push_wms_status']];
        }
        if (isset($params['is_supplier_delivery']) && is_numeric($params['is_supplier_delivery'])) {
            $where[] = ['to.is_supplier_delivery', '=', $params['is_supplier_delivery']];
        }
        if (!empty($params['related_order_no'])) {
            $where[] = ["to.related_order_no", "=", $params['related_order_no']];
        }
        if (!empty($params['main_order_no'])) {
            $where[] = ["om.main_order_no", "=", $params['main_order_no']];
        }
        if (!empty($params['created_time_start'])) {
            $where[] = ['to.created_time', '>=', strtotime($params['created_time_start'])];
        }
        if (!empty($params['created_time_end'])) {
            $where[] = ['to.created_time', '<', strtotime($params['created_time_end'])];
        }
        if (!empty($params['warehouse_id'])) {
            $where[] = ['to.warehouse_id', "=", $params['warehouse_id']];
        }

        $total = Db::name('tripartite_order')
            ->alias('to')
            ->leftJoin('order_main om', 'om.id=to.main_order_id')
            ->where($where)
            ->count();

        if ($total > 0) {
            $limit = 3000;
            foreach (range(1, max(1, ceil($total / $limit))) as $page) {
                $offset = ($page - 1) * $limit;
                $lists  = Db::name('tripartite_order')
                    ->alias('to')
                    ->leftJoin('order_main om', 'om.id=to.main_order_id')
                    ->where($where)
                    ->order('to.id desc')
                    ->limit($offset, $limit)
                    ->column('to.sub_order_no,to.sub_order_status,to.order_from_thirdparty,to.express_number,to.payment_time,to.created_time,to.delivery_time,to.push_t_status,to.push_wms_status,to.user_remark,to.is_ts,to.payment_amount,to.express_type,to.province,to.city,to.district,to.address,to.items_info,om.consignee,om.consignee_phone,to.warehouse_id');

                $data                  = [];
                $config                = config();
                $sub_order_status      = array_column($config['config']['sub_order_status'], 'label', 'value');
                $tripartite_order_from = array_column($config['config']['tripartite_order_from'], 'label', 'value');
                $push_t_status         = array_column($config['config']['push_t_status'], 'label', 'value');
                $push_wms_status       = array_column($config['config']['push_wms_status'], 'label', 'value');
                $is_ts                 = array_column($config['config']['is_ts'], 'label', 'value');
                $delivery_express      = array_column($config['config']['delivery_express'], 'delivery_mode', 'express_type');

                $enc_data = array_values(array_unique(array_merge(array_column($lists, 'consignee'), array_column($lists, 'consignee_phone'))));
                $dec_data = cryptionDeal(2, $enc_data, '15736175219', '宗仁川');

                $warehouse_ids = array_values(array_unique(array_column($lists, 'warehouse_id')));
                $warehouse_map = Db::table('vh_push_t_plus.vh_erp_code_map')->where([
                    ['type', '=', 5],
                    ['vh_code', 'in', $warehouse_ids],
                ])->column('name', 'vh_code');

                foreach ($lists as $item) {
                    $goodsInfo = explode(',', $item['items_info']);
                    foreach ($goodsInfo as $k => $v) {
                        $goods        = explode('*', $v);
                        $g_short_code = isset($goods[0]) ? $goods[0] : '';
                        $g_nums       = isset($goods[1]) ? $goods[1] : '';
                        if (!empty($g_short_code)) {
                            $data[] = [
                                'sub_order_no'          => $item['sub_order_no'],
                                'sub_order_status'      => $sub_order_status[$item['sub_order_status']] ?? '',
                                'order_from_thirdparty' => $tripartite_order_from[$item['order_from_thirdparty']] ?? '',
                                'express_number'        => $item['express_number'],
                                'payment_time'          => empty($item['payment_time']) ? '' : date('Y-m-d H:i:s', $item['payment_time']),
                                'created_time'          => empty($item['created_time']) ? '' : date('Y-m-d H:i:s', $item['created_time']),
                                'delivery_time'         => empty($item['delivery_time']) ? '' : date('Y-m-d H:i:s', $item['delivery_time']),
                                'push_t_status'         => $push_t_status[$item['push_t_status']] ?? '',
                                'push_wms_status'       => $push_wms_status[$item['push_wms_status']] ?? '',
                                'user_remark'           => $item['user_remark'],
                                'is_ts'                 => $is_ts[$item['is_ts']] ?? '',
                                'payment_amount'        => $item['payment_amount'],
                                'express_type'          => $delivery_express[$item['express_type']] ?? '',
                                'province'              => $item['province'],
                                'city'                  => $item['city'],
                                'district'              => $item['district'],
                                'address'               => $item['address'],
                                'short_code'            => $g_short_code,
                                'nums'                  => $g_nums,
                                'consignee'             => $dec_data[$item['consignee']] ?? '',
                                'consignee_phone'       => $dec_data[$item['consignee_phone']] ?? '',
                                'warehouse_name'       => $warehouse_map[$item['warehouse_id']] ?? '',
                            ];
                        }
                    }
                }

                $header   = [
                    ['column' => 'sub_order_no', 'name' => '订单号', 'width' => 30],
                    ['column' => 'sub_order_status', 'name' => '订单状态', 'width' => 15],
                    ['column' => 'order_from_thirdparty', 'name' => '平台来源', 'width' => 15],
                    ['column' => 'express_number', 'name' => '快递单号', 'width' => 15],
                    ['column' => 'payment_time', 'name' => '支付时间', 'width' => 15],
                    ['column' => 'created_time', 'name' => '创建时间', 'width' => 15],
                    ['column' => 'delivery_time', 'name' => '发货时间', 'width' => 15],
                    ['column' => 'push_t_status', 'name' => 'ERP推送状态', 'width' => 15],
                    ['column' => 'push_wms_status', 'name' => '萌牙推送状态', 'width' => 15],
                    ['column' => 'user_remark', 'name' => '备注', 'width' => 15],
                    ['column' => 'is_ts', 'name' => '是否暂存', 'width' => 15],
                    ['column' => 'short_code', 'name' => '商品编码', 'width' => 15],
                    ['column' => 'nums', 'name' => '数量', 'width' => 15],
                    ['column' => 'payment_amount', 'name' => '订单金额', 'width' => 15],
                    ['column' => 'express_type', 'name' => '快递方式', 'width' => 15],
                    ['column' => 'consignee', 'name' => '收货人', 'width' => 15],
                    ['column' => 'consignee_phone', 'name' => '联系电话', 'width' => 15],
                    ['column' => 'province', 'name' => '省', 'width' => 15],
                    ['column' => 'city', 'name' => '市', 'width' => 15],
                    ['column' => 'district', 'name' => '区', 'width' => 15],
                    ['column' => 'address', 'name' => '详细地址', 'width' => 15],
                    ['column' => 'warehouse_name', 'name' => '仓库', 'width' => 15],
                ];
                $filename = "三方订单导出_$page";
                try {
                    $uploadUrl = exportSheelExcel($data, $header, $filename);
                    if (empty($uploadUrl)) $this->throwError('生成excel文件异常');
                    $file           = app()->getRootPath() . "public/storage/" . $uploadUrl;
                    $temp_file_info = \Curl::upTempFileToWechat($file);
                    unlink($file);
                    \Curl::wecomSend($temp_file_info['media_id'], $userid, 'file');
                } catch (\Exception $e) {
                    $this->throwError("导出失败：" . $e->getMessage());
                }
            }
        }
    }

    /**
     * Description:终止萌牙发货
     * Author: zrc
     * Date: 2023/2/3
     * Time: 16:58
     * @param $params
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function stopPushOrder($params)
    {
        $orderInfo = Db::name('tripartite_order')->field('sub_order_no,sub_order_status,push_wms_status')->where(['sub_order_no' => $params['sub_order_no']])->find();
        if (empty($orderInfo)) $this->throwError('未获取到订单信息');
        $updateData = [];
        switch ($orderInfo['push_wms_status']) {
            case 0:
            case 2:
                //取消发货重试
                if ($orderInfo['push_wms_status'] == 2) {
                    $cancelPush = $this->httpPost(env('ITEM.PUSH_ORDERS_URL') . '/pushorders/v3/quit/retryPushOrder', ['sub_order_no' => $params['sub_order_no']]);
                    if (!isset($cancelPush['error_code']) || $cancelPush['error_code'] != 0) $this->throwError('终止失败：取消发货重试异常：' . $cancelPush['error_msg']);
                }
                $updateData = array(
                    'push_wms_status' => 3,
                    'update_time'     => time(),
                );
                break;
            case 1:
                $revokeOrder = $this->httpPost(env('ITEM.PUSH_ORDERS_URL') . '/pushorders/v3/cancel/order', ['sub_order_no' => $params['sub_order_no']]);
                if (!isset($revokeOrder['error_code']) || $revokeOrder['error_code'] != 0) $this->throwError('终止失败：萌牙撤单异常：' . $revokeOrder['error_msg']);
                $updateData = array(
                    'push_wms_status' => 3,
                    'update_time'     => time(),
                );
                break;
            case 3:
                //老外买酒取消推送萌牙，走中台制单需要
                $cancelPush = $this->httpPost(env('ITEM.PUSH_ORDERS_URL') . '/pushorders/v3/quit/retryPushOrder', ['sub_order_no' => $params['sub_order_no']]);
                if (!isset($cancelPush['error_code']) || $cancelPush['error_code'] != 0) $this->throwError('终止失败：取消发货重试异常：' . $cancelPush['error_msg']);
                $updateData = array(
                    'push_wms_status' => 3,
                    'update_time'     => time(),
                );
                break;
        }
        if (!empty($updateData)) {
            Db::name('tripartite_order')->where(['sub_order_no' => $params['sub_order_no']])->update($updateData);
            //添加订单备注
            $remarks      = array(
                'sub_order_no' => $params['sub_order_no'],
                'order_type'   => 7,
                'content'      => $params['sub_order_no'] . '通过【' . $params['store_name'] . '】发货，终止萌牙推送',
                'admin_id'     => $params['admin_id']
            );
            $orderService = new OrderService();
            $orderService->createRemarks($remarks);
        }
        return true;
    }

    /**
     * Description:添加赠品
     * Author: zrc
     * Date: 2023/2/20
     * Time: 11:24
     * @param $params
     * @return array|bool|int|string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function addGift($params)
    {
        $remarks = "添加赠品，简码：{$params['short_code']}，数量：{$params['nums']}";
        try {
            Log::write('addGift 添加赠品 Params:' . json_encode($params));
            $orderInfo = Db::name('tripartite_order')->where(['sub_order_no' => $params['sub_order_no']])->find();
            if (empty($orderInfo)) $this->throwError('未获取到订单信息');
            if (!in_array($orderInfo['sub_order_status'], [1, 2, 3])) $this->throwError('订单状态异常');
            if (strpos($orderInfo['sub_order_no'], '-ZP') !== false) {
                $this->throwError('赠品订单不能添加赠品');
            }
            $result = '';

            $items_info_arr = explode(',', $orderInfo['items_info']);
            $short_codes    = [];
            foreach ($items_info_arr as &$val) {
                $val           = explode('*', $val);
                $short_codes[] = $val[0];
            }
            $short_codes[] = $params['short_code'];

            if ($orderInfo['push_wms_status'] == 1) {
                $items_info_arr[] = [$params['short_code'], $params['nums'], 0];

                $products = Db::table('vh_wiki.vh_products')->where('short_code', 'in', $short_codes)
                    ->column('bar_code,short_code,grape_picking_years,capacity', 'short_code');

                $push_product = [];
                foreach ($items_info_arr as $item) {
                    $product = $products[$item[0]] ?? [];
                    if (empty($product)) $this->throwError($item[0] . '产品不存在');

                    $push_product[] = [
                        'goods_id'    => 0,
                        'goods_code'  => $product['bar_code'] ?? '',
                        'number'      => bcmul($item[1],$orderInfo['order_qty']),
                        'short_code'  => $product['short_code'] ?? $item[0],
                        'goods_years' => $product['grape_picking_years'] ?? '',
                        'volume'      => $product['capacity'] ?? '',
                    ];
                }
                //同步更新萌芽暂存状态
                $wms_up_data = [
                    'orderno' => $orderInfo['sub_order_no'],//商家订单号
                    'product' => $push_product, //是否暂存（0否，1是）
                ];
                \Curl::receiptInfo($wms_up_data);
            }

            $new_items_info = trim("{$orderInfo['items_info']},{$params['short_code']}*{$params['nums']}*0", ',');
            $result         = Db::name('tripartite_order')->where(['id' => $orderInfo['id']])->update([
                'items_info'  => $new_items_info,
                'update_time' => time()
            ]);

            /*
            switch ($orderInfo['push_wms_status']) {
                case 0://未推送
                    $new_items_info_arr = [];
                    $items_info_arr     = explode(',', $orderInfo['items_info']);
                    foreach ($items_info_arr as &$val) {
                        $goods                = explode('*', $val);
                        $new_items_info_arr[] = $goods[0] . '*' . $goods[1] * $orderInfo['order_qty'] . '*' . $goods[2];
                    }
                    $new_items_info = implode(',', $new_items_info_arr) . ',' . $params['short_code'] . '*' . $params['nums'] . '*0';
                    $updateData     = array(
                        'items_info'  => $new_items_info,
                        'order_qty'   => 1,
                        'update_time' => time(),
                    );
                    $result         = Db::name('tripartite_order')->where(['id' => $orderInfo['id']])->update($updateData);
                    break;
                case 1://已推送
                    $result = $this->createGiftOrder($orderInfo, $params);
                    break;
                case 2://推送失败
                    $pushService = new PushService();
                    $pushWms     = $pushService->pushWms(['sub_order_no' => $params['sub_order_no'], 'order_type' => 7]);
                    if ($pushWms['error_code'] == 0) {
                        $result = $this->createGiftOrder($orderInfo, $params);
                    } else {
                        $new_items_info = $orderInfo['items_info'] . ',' . $params['short_code'] . '*' . $params['nums'] . '*0';
                        $result         = Db::name('tripartite_order')->where(['id' => $orderInfo['id']])->update(['items_info' => $new_items_info, 'update_time' => time()]);
                    }
                    break;
            }
            */

            $remarks .= !empty($result) ? "，成功" : "，失败";
            Db::name('order_remarks')->insert(['sub_order_no' => $params['sub_order_no'], 'admin_id' => $params['admin_id'], 'remarks' => $remarks, 'created_time' => time()]);
        } catch (\Exception $e) {
            $remarks .= "，失败：" . $e->getMessage();
            //【增加赠品】时需要在订单备注加入日志：https://devops.aliyun.com/projex/project/8ab3146db7bf4a79876e3a5333/task#viewIdentifier=1945bcda00c0cf935ebdd8a5&openWorkitemIdentifier=00e187573178127252dba12a84
            Db::name('order_remarks')->insert(['sub_order_no' => $params['sub_order_no'], 'admin_id' => $params['admin_id'], 'remarks' => $remarks, 'created_time' => time()]);
            throw new Exception("添加赠品失败: " . $e->getMessage());
        }

        return $result;
    }

    /**
     * Description:三方订单生成赠品订单号
     * Author: zrc
     * Date: 2023/2/20
     * Time: 10:50
     * @param $sub_order_no
     * @param int $num
     * @return string
     */
    public function createGiftOrderNo($sub_order_no, $num = 1)
    {
        $new_sub_order_no = $sub_order_no . '-ZP' . $num;
        $isset            = Db::name('tripartite_order')->where(['sub_order_no' => $new_sub_order_no])->count();
        if ($isset > 0) {
            $new_sub_order_no = $this->createGiftOrderNo($sub_order_no, $num + 1);
        }
        return $new_sub_order_no;
    }

    /**
     * Description:三方订单生成赠品订单
     * Author: zrc
     * Date: 2023/2/20
     * Time: 11:18
     * @param $orderInfo
     * @return string
     * @throws \Exception
     */
    public function createGiftOrder($orderInfo, $params)
    {
        $sub_order_no = $this->createGiftOrderNo($orderInfo['sub_order_no']);

        $receiveInfo = Db::name('sub_order_receive_information')->where(['sub_order_no' => $orderInfo['sub_order_no']])->find();
        if (!empty($receiveInfo)) {
            $orderInfo['province'] = $receiveInfo['province_name'];
            $orderInfo['city']     = $receiveInfo['city_name'];
            $orderInfo['district'] = $receiveInfo['district_name'];
            $orderInfo['address']  = $receiveInfo['address'];
            unset($receiveInfo['id']);
            $receiveInfo['sub_order_no'] = $sub_order_no;
            $receiveInfo['update_time']  = time();
            $receiveInfo['created_time'] = time();
            Db::name('sub_order_receive_information')->insert($receiveInfo);
        }

        $data         = array(
            'sub_order_no'          => $sub_order_no,
            'sub_order_status'      => 1,
            'main_order_id'         => $orderInfo['main_order_id'],
            'title'                 => $orderInfo['sub_order_no'] . '赠品',
            'items_info'            => $params['short_code'] . '*' . $params['nums'] . '*0',
            'store_id'              => $orderInfo['store_id'],
            'store_name'            => $orderInfo['store_name'],
            'warehouse_id'          => $orderInfo['warehouse_id'],
            'order_from_thirdparty' => $orderInfo['order_from_thirdparty'],
            'order_qty'             => 1,
            'payment_amount'        => 0,
            'express_type'          => $orderInfo['express_type'],
            'express_fee'           => 0,
            'payment_time'          => $orderInfo['payment_time'],
            'created_time'          => time(),
            'operator'              => $params['admin_id'],
            'province'              => $orderInfo['province'],
            'city'                  => $orderInfo['city'],
            'district'              => $orderInfo['district'],
            'address'               => $orderInfo['address'],
            'invoice_progress'      => $orderInfo['invoice_progress'],
        );
        $result       = Db::name('tripartite_order')->insert($data);
        if (empty($result)) $this->throwError('生成赠品订单失败');
        return $sub_order_no;
    }

    /**
     * Description:合并订单
     * Author: zrc
     * Date: 2023/2/20
     * Time: 17:50
     * @param $params
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function mergeOrder($params)
    {
        $orderNoArr = explode(',', $params['sub_order_no_str']);
        $field      = 'id,sub_order_no,payment_amount,sub_order_status,items_info,push_wms_status,order_from_thirdparty,store_id,warehouse_id,order_qty';
        $orderInfo  = Db::name('tripartite_order')->field($field)->where([['sub_order_no', 'in', $orderNoArr]])->select()->toArray();
        if (count($orderInfo) <= 1) $this->throwError('未获取到要合并的订单信息');
        $items_info            = '';
        $sub_order_no          = '';
        $sub_order_id          = '';
        $sub_order_money       = 0;
        $order_from_thirdparty = 0;
        $warehouse_id          = 0;
        $store_id              = 0;
        $orderService          = new OrderService();
        Db::startTrans();
        try {
            foreach ($orderInfo as $key => $val) {
                if ($val['sub_order_status'] != 1) $this->throwError($val['sub_order_no'] . '订单状态不是已支付，不能合并');
                if (strpos($val['sub_order_no'], '-合') !== false) $this->throwError($val['sub_order_no'] . '订单已合并');
                $sub_order_money += $val['payment_amount'];
                $new_goods       = [];
                $items_info_arr  = explode(',', $val['items_info']);
                foreach ($items_info_arr as &$v) {
                    $goods       = explode('*', $v);
                    $goods[1]    = $val['order_qty'] * $goods[1];
                    $new_goods[] = implode('*', $goods);
                }
                $new_items_info = implode(',', $new_goods);
                if ($key == 0) {
                    $order_from_thirdparty = $val['order_from_thirdparty'];
                    $warehouse_id          = $val['warehouse_id'];
                    $store_id              = $val['store_id'];
                    $items_info            = $new_items_info;
                    $sub_order_id          = $val['id'];
                    $sub_order_no          = $this->createMergeOrderNo($val['sub_order_no']);
                } else {
                    if ($val['order_from_thirdparty'] != $order_from_thirdparty) $this->throwError($val['sub_order_no'] . '订单平台不一致，不能合并');
                    if ($val['store_id'] != $store_id) $this->throwError($val['sub_order_no'] . '订单店铺不一致，不能合并');
                    if ($val['warehouse_id'] != $warehouse_id) $this->throwError($val['sub_order_no'] . '订单仓库不一致，不能合并');
                    $items_info = $items_info . ',' . $new_items_info;
                    //修改待合并订单信息
                    $updateData = array(
                        'sub_order_status' => 4,
                        'push_wms_status'  => 3,
                        'update_time'      => time(),
                        'operator'         => $params['admin_id'],
                    );
                    Db::name('tripartite_order')->where(['id' => $val['id']])->update($updateData);
                    //添加订单备注
                    $remark = array(
                        'sub_order_no' => $val['sub_order_no'],
                        'order_type'   => 7,
                        'content'      => '当前订单已合并至' . $sub_order_no,
                        'admin_id'     => $params['admin_id']
                    );
                    $orderService->createRemarks($remark);
                }
                //萌牙撤单计划任务
                $pushData = array(
                    'namespace' => "orders",
                    'key'       => "orders_wms_" . $val['sub_order_no'],
                    'data'      => base64_encode(json_encode(['sub_order_no' => $val['sub_order_no']])),
                    'callback'  => env('ITEM.PUSH_ORDERS_URL') . '/pushorders/v3/cancel/order',
                    'timeout'   => "1m",
                );
                $this->httpPost(env('ITEM.TIMEING_SERVICE_URL') . '/services/v3/timing/add', $pushData);
            }
            $mergeData = array(
                'sub_order_no'   => $sub_order_no,
                'items_info'     => $items_info,
                'order_qty'      => 1,
                'payment_amount' => $sub_order_money,
                'operator'       => $params['admin_id'],
                'update_time'    => time(),
            );
            $result    = Db::name('tripartite_order')->where(['id' => $sub_order_id])->update($mergeData);
            if (empty($result)) $this->throwError('合并订单失败');
            //添加订单备注
            $remark = array(
                'sub_order_no'    => $sub_order_no,
                'refund_status'   => 0,
                'push_wms_status' => 0,
                'order_type'      => 7,
                'content'         => '合并订单：' . $params['sub_order_no_str'],
                'admin_id'        => $params['admin_id']
            );
            $orderService->createRemarks($remark);
            Db::commit();
            //新订单推送萌牙
            $pushData = array(
                'exchange_name' => 'orders',
                'routing_key'   => 'push.wms',
                'data'          => base64_encode(json_encode(['sub_order_no' => $sub_order_no, 'order_type' => 7])),
            );
            Log::write("订单推送WMS队列 4: " . json_encode($pushData));
            httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
        } catch (\Exception $e) {
            Db::rollback();
            $this->throwError($e->getMessage());
        }
        return true;
    }

    /**
     * Description:三方订单生成合并订单号
     * Author: zrc
     * Date: 2023/2/20
     * Time: 17:26
     * @param $sub_order_no
     * @param int $num
     * @return string
     */
    public function createMergeOrderNo($sub_order_no, $num = 1)
    {
        $new_sub_order_no = $sub_order_no . '-合' . $num;
        $isset            = Db::name('tripartite_order')->where(['sub_order_no' => $new_sub_order_no])->count();
        if ($isset > 0) {
            $new_sub_order_no = $this->createGiftOrderNo($sub_order_no, $num + 1);
        }
        return $new_sub_order_no;
    }

    /**
     * @方法描述: 批量更新萌芽推送状态
     * <AUTHOR>
     * @Date 2023/12/7 17:12
     * @param $param
     * @return array
     * @throws \Exception
     */
    public function bathUpdateStatus($param)
    {
        Log::write("bathUpdateStatus  批量修改萌芽推送状态: " . json_encode($param));
        Db::startTrans();
        try {
            $count = Db::name('tripartite_order')->where([
                ['sub_order_no', 'in', $param['ids']],
                ['is_delete', '=', 0],
                ['push_wms_status', '=', 3],
            ])->update(['push_wms_status' => $param['push_wms_status']]);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::write("bathUpdateStatus  批量修改萌芽推送状态错误: " . $e->getMessage());
            $this->throwError($e->getMessage());
        }
        return compact('count');
    }

    public function trOrderPushTPlus($params)
    {
        Db::startTrans();
        try {
            $orderInfo = Db::name('tripartite_order')->where([
                ['sub_order_no', '=', $params['sub_order_no']],
            ])->field('id,sub_order_no,sub_order_status,push_t_status')->find();

            if (empty($orderInfo)) throw new Exception("未找到订单");
            if (!in_array($orderInfo['sub_order_status'], [2, 3])) throw new Exception("订单未完成或发货");
            if ($orderInfo['push_t_status'] == 3) throw new Exception("订单不推送ERP");
            $pushService = new PushService();
            $pushService->pushTplus(['sub_order_no' => $orderInfo['sub_order_no'], 'order_type' => 7]);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            return $e->getMessage();
        }
        return true;
    }


    public function productUpdate($params)
    {
        Db::startTrans();
        try {
            $orderInfo = Db::name('tripartite_order')->where([
                ['sub_order_no', '=', $params['sub_order_no']],
            ])->field('id,sub_order_no,order_qty,items_info,sub_order_status,push_wms_status,push_t_status')->find();

            if (empty($orderInfo)) throw new Exception("未找到订单");
            if (!in_array($orderInfo['sub_order_status'], [1])) throw new Exception("订单不是已支付未发货状态,不能更新商品详情");
            if ($orderInfo['push_t_status'] == 1) throw new Exception("订单已推送ERP,不能更新商品详情");

            $items_info    = [];
            $product       = [];
            $p_short_codes = array_column($params['items_info'], 'short_code');
            $wiki_products = Db::table('vh_wiki.vh_products')
                ->where('short_code', 'in', $p_short_codes)
                ->column('id,bar_code,short_code,grape_picking_years,capacity', 'short_code');

            $diff_short_codes = array_diff($p_short_codes, array_keys($wiki_products));
            if (!empty($diff_short_codes)) {
                throw new Exception("简码: " . implode(',', $diff_short_codes) . ' 未找到,请在磐石添加后再操作!');
            }

            foreach ($params['items_info'] as $item) {
                $items_info[] = "{$item['short_code']}*{$item['nums']}*{$item['price']}";

                $number       = bcmul($orderInfo['order_qty'], $item['nums']);
                $wiki_product = $wiki_products[$item['short_code']];
                $product[]    = array(
//                    'goods_id'    => '期数 todo ...', // todo ....
                    'goods_code'  => $wiki_product['bar_code'],
                    'number'      => $number,
                    'short_code'  => $wiki_product['short_code'],
                    'goods_years' => $wiki_product['grape_picking_years'],
                    'volume'      => $wiki_product['capacity'],
                );
            }
            $items_info_text = implode(',', $items_info);
            Db::name('tripartite_order')->where('id', $orderInfo['id'])->update(['items_info' => $items_info_text, 'update_time' => time()]);

            //添加订单备注
            $remarks      = array(
                'sub_order_no' => $params['sub_order_no'],
                'order_type'   => 7,
                'content'      => "原产品信息:{$orderInfo['items_info']}, 更新后产品信息:{$items_info_text} ",
                'admin_id'     => $params['admin_id']
            );
            $orderService = new OrderService();
            $orderService->createRemarks($remarks);

            if ($orderInfo['push_wms_status'] == 1) {
                $push_wms_data               = [];
                $push_wms_data['store_code'] = env('ORDERS.STORE_CODE');
                $push_wms_data['orderno']    = $params['sub_order_no'];
                $push_wms_data['product']    = $product;
                $syncRes                     = $this->httpPost(env("ITEM.DISTRIBUTE_URL") . "/sync/shiporder/receiptInfo", $push_wms_data, ["Content-Type" => "application/json"]);
                if (isset($syncRes['status'])) {
                    if ($syncRes['status'] == 'fail') {
                        throw new Exception('三方订单同步萌牙发货单信息异常：' . $syncRes['msg']);
                    }
                } else {
                    throw new Exception('三方订单同步萌牙发货单信息异常：萌牙模块访问异常');
                }
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            return $e->getMessage();
        }
        return true;
    }

    public function changeItemsInfo($params)
    {
        Db::startTrans();
        try {
            $orderInfo = Db::name('tripartite_order')->alias('t')
                ->join("order_main m", 't.main_order_id=m.id')
                ->where(['t.sub_order_no' => $params['sub_order_no']])->field('t.*,m.main_order_no')->find();

            if (empty($orderInfo)) $this->throwError('未获取到订单信息');
            $pass_status = [];
            if ($params['type'] == 1) {
                $pass_status = [1];
            } elseif ($params['type'] == 2) {
                $pass_status = [1];
            } elseif ($params['type'] == 3) {
                $pass_status = [1, 2, 3, 4];
            }
            if (!in_array($orderInfo['sub_order_status'], $pass_status)) $this->throwError('订单状态异常');

            $type_arr = ["1" => "添加赠品", "2" => "订单换绑", "3" => "补发商品"];

            $n_items_info_arr = [];
            $short_codes      = [];
            $n_price = 0;
            foreach ($params['product'] as &$p_product) {
                $short_codes[]      = $p_product['short_code'];
                $n_price = bcadd($n_price, $p_product['price'], 2);
                $n_items_info_arr[] = "{$p_product['short_code']}*{$p_product['nums']}*{$p_product['price']}";
            }
            $n_items_info = implode(',', $n_items_info_arr);
            if ($params['type'] == 2) {
                $o_price = 0;
                if (empty($orderInfo['items_info'])) {
                    $o_price = $orderInfo['payment_amount'];
                } else {
                    $old_items_info = explode(',', $orderInfo['items_info']);
                    foreach ($old_items_info as $oii) {
                        $oii_arr = explode('*', $oii);
                        $o_price = bcadd($o_price, $oii_arr['2'], 2);
                    }
                }
                if ($o_price != $n_price) {
                    $this->throwError('总金额不一致');
                }
            }

            $log = [
                'sub_order_no'   => $orderInfo['sub_order_no'],
                'old_items_info' => $orderInfo['items_info'],
                'items_info'     => $n_items_info,
                'type'           => $params['type'],
                'vh_uid'         => $params['admin_id'],
                'vh_name'        => $params['admin_name'],
                'created_time'   => date('Y-m-d H:i:s'),
            ];

            Db::name('tripartite_items_log')->insert($log);
            Db::name('order_remarks')->insert([
                'sub_order_no' => $params['sub_order_no'],
                'admin_id'     => $params['admin_id'],
                'remarks'      => $params['admin_name'] . ' 修改商品: ' . ($type_arr[$params['type']] ?? '未知类型'),
                'created_time' => time()
            ]);

            if (in_array($params['type'], [1, 2])) {
                Db::name('tripartite_order')->where(['id' => $orderInfo['id']])->update([
                    'items_info'  => $n_items_info,
                    'update_time' => time()
                ]);
                if ($orderInfo['push_wms_status'] == 1) {
                    $products = Db::table('vh_wiki.vh_products')->where('short_code', 'in', $short_codes)
                        ->column('bar_code,short_code,grape_picking_years,capacity', 'short_code');

                    $push_product = [];
                    foreach ($params['product'] as $item) {
                        $product = $products[$item['short_code']] ?? [];
                        if (empty($product)) $this->throwError($item['short_code'] . '产品不存在');

                        $push_product[] = [
                            'goods_id'    => 0,
                            'goods_code'  => $product['bar_code'] ?? '',
                            'number'      => bcmul($item['nums'], $orderInfo['order_qty']),
                            'short_code'  => $product['short_code'] ?? $item['short_code'],
                            'goods_years' => $product['grape_picking_years'] ?? '',
                            'volume'      => $product['capacity'] ?? '',
                        ];
                    }
                    //同步更新萌芽暂存状态
                    $wms_up_data = [
                        'orderno' => $orderInfo['sub_order_no'],//商家订单号
                        'product' => $push_product, //是否暂存（0否，1是）
                    ];
                    \Curl::receiptInfo($wms_up_data);
                }
            } else {
                //补发
                $this->createBfOrder($orderInfo,$n_items_info,$params);
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            if($e->getMessage() == '同步修改发货单信息错误: 订单已合并'){
                throw new Exception("修改商品失败: " . $e->getMessage(). ',请使用申请补发功能。');
            }
            throw new Exception("修改商品失败: " . $e->getMessage());
        }
        return true;
    }

    public function createBfOrder($orderInfo, $n_items_info,$params)
    {
        $bnum             = Db::name('tripartite_order')
            ->where('sub_order_no', 'like', "{$orderInfo['sub_order_no']}补%")->count();
        $sub_order_no = $orderInfo['sub_order_no'] . str_repeat("补", $bnum + 1);

        /*$receiveInfo = Db::name('sub_order_receive_information')->where(['sub_order_no' => $orderInfo['sub_order_no']])->find();
        if (!empty($receiveInfo)) {
            $orderInfo['province'] = $receiveInfo['province_name'];
            $orderInfo['city']     = $receiveInfo['city_name'];
            $orderInfo['district'] = $receiveInfo['district_name'];
            $orderInfo['address']  = $receiveInfo['address'];
            unset($receiveInfo['id']);
            $receiveInfo['sub_order_no'] = $sub_order_no;
            $receiveInfo['update_time']  = time();
            $receiveInfo['created_time'] = time();
            Db::name('sub_order_receive_information')->insert($receiveInfo);
        }*/

        $dec                       = \Curl::cryptionDeal([$params['consignee'], $params['consignee_phone']], 'E');
        $params['consignee']       = $dec[$params['consignee']] ?? $params['consignee'];
        $params['consignee_phone'] = $dec[$params['consignee_phone']] ?? $params['consignee_phone'];

//        $addressAiMatch          = \Curl::addressAiMatch(['address' => $params['full_address']]);
        $params['province_id']   = $addressAiMatch['province_id'] ?? 0;
        $params['city_id']       = $addressAiMatch['city_id'] ?? 0;
        $params['district_id']   = $addressAiMatch['town_id'] ?? 0;
        $params['address']       = $params['full_address'];
        $params['province_name'] = $addressAiMatch['province_name'] ?? '';
        $params['city_name']     = $addressAiMatch['city_name'] ?? '';
        $params['district_name'] = $addressAiMatch['town_name'] ?? '';

        $orderInfo['province'] = $params['province_name'];
        $orderInfo['city']     = $params['city_name'];
        $orderInfo['district'] = $params['district_name'];
        $orderInfo['address']  = $params['address'];

        $receiveInfo = [
            'main_order_no'   => $orderInfo['main_order_no'],
            'sub_order_no'    => $sub_order_no,
            'order_type'      => $orderInfo['order_type'],
            'province_id'     => $params['province_id'],
            'city_id'         => $params['city_id'],
            'district_id'     => $params['district_id'],
            'address'         => $params['address'],
            'consignee'       => $params['consignee'],
            'consignee_phone' => $params['consignee_phone'],
            'province_name'   => $params['province_name'],
            'city_name'       => $params['city_name'],
            'district_name'   => $params['district_name'],
            'update_time'     => time(),
            'created_time'    => time(),
        ];
        Db::name('sub_order_receive_information')->insert($receiveInfo);


        $data         = array(
            'sub_order_no'          => $sub_order_no,
            'sub_order_status'      => 1,
            'main_order_id'         => $orderInfo['main_order_id'],
            'title'                 => $orderInfo['sub_order_no'] . '补发',
            'items_info'            => $n_items_info,
            'store_id'              => $orderInfo['store_id'],
            'store_name'            => $orderInfo['store_name'],
            'warehouse_id'          => $orderInfo['warehouse_id'],
            'order_from_thirdparty' => $orderInfo['order_from_thirdparty'],
            'order_qty'             => 1,
            'payment_amount'        => 0,
            'express_type'          => $orderInfo['express_type'],
            'express_fee'           => 0,
            'payment_time'          => $orderInfo['payment_time'],
            'created_time'          => time(),
            'operator'              => request()->header('vinehoo-uid',0),
            'province'              => $orderInfo['province'],
            'city'                  => $orderInfo['city'],
            'district'              => $orderInfo['district'],
            'address'               => $orderInfo['address'],
            'invoice_progress'      => $orderInfo['invoice_progress'],
            'company_id'      => $orderInfo['company_id'],
        );
        $result       = Db::name('tripartite_order')->insert($data);
        if (empty($result)) $this->throwError('生成补发订单失败');
        return $sub_order_no;
    }


}