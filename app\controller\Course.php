<?php


namespace app\controller;


use app\BaseController;
use app\ErrorCode;
use app\Request;
use app\service\Course as CourseService;

class Course extends BaseController
{
    /**
     * Description:课程创建订单(暂时不用)
     * Author: zrc
     * Date: 2021/8/6
     * Time: 15:14
     * @param Request $request
     */
    public function create(Request $request)
    {
        $params        = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        if (!isset($params['uid'])) {
            $this->throwError('用户ID必传', ErrorCode::PARAM_ERROR);
        }
        if (!isset($params['course_id'])) {
            $this->throwError('课程ID必传', ErrorCode::PARAM_ERROR);
        }
        if (!isset($params['order_from'])) {
            $this->throwError('来源必传', ErrorCode::PARAM_ERROR);
        }
        if (!isset($params['realname'])) {
            $this->throwError('真实姓名必传', ErrorCode::PARAM_ERROR);
        }
        if (!isset($params['phone'])) {
            $this->throwError('手机号必传', ErrorCode::PARAM_ERROR);
        }
        if (!isset($params['wechat_account'])) {
            $this->throwError('微信号必传', ErrorCode::PARAM_ERROR);
        }
        $courseService = new CourseService();
        $result        = $courseService->createOrder($params);
        return $this->success($result);
    }
}