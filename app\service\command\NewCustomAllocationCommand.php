<?php


namespace app\service\command;


use app\service\PreSales as PreSalesService;
use think\facade\Db;

class NewCustomAllocationCommand
{
    public function exec()
    {
        $stime     = strtotime(date('Y-m-d 00:00:00', strtotime('-1 day')));
        $etime     = strtotime(date('Y-m-d 00:00:00', time()));
        $userInfo = Db::table('vh_user.vh_user')->field('uid')->where([['created_time', '>=', $stime],['created_time', '<', $etime]])->select()->toArray();
        $uidArr   = array_column($userInfo, 'uid');
        if (empty($uidArr)) {
            echo '本次定时任务未获取到满足条件的新用户' . PHP_EOL;
            return false;
        }
        $orderInfo = Db::name('order_main')->field('uid')->where([['uid', 'in', $uidArr], ['main_order_status', 'in', [1, 2, 3]]])->group('uid')->select()->toArray();
        if (!empty($orderInfo)) {
            $orderUserArr = array_column($orderInfo, 'uid');
            $uidArr       = array_diff($uidArr, $orderUserArr);
        }
        if (empty($uidArr)) {
            echo '本次定时任务未获取到满足条件的新用户' . PHP_EOL;
            return false;
        }
        $newUser   = Db::table('vh_user.vh_user')->field('uid,nickname,telephone,reg_from,tripartite_source,region,created_time')->where([['uid', 'in', $uidArr]])->where('reg_from','not in', [13,14])->select()->toArray();
        $telephone = array_column($newUser, 'telephone');
        $encrypt   = cryptionDeal(2, $telephone, '15736175219', '宗仁川');
        foreach ($newUser as &$value) {
            $value['telephone'] = isset($encrypt[$value['telephone']]) ? $encrypt[$value['telephone']] : '';
            //用户注册方式：0未知 1:android 2:ios 3:酒云网小程序 4:h5 5:PC 6:抖音小程序 7后台添加 8:酒历小程序 9:公社小程序 10:iPad 11:门店小程序
            switch ($value['reg_from']) {
                case 0:
                    $value['reg_from'] = '未知';
                    break;
                case 1:
                    $value['reg_from'] = 'android';
                    break;
                case 2:
                    $value['reg_from'] = 'ios';
                    break;
                case 3:
                    $value['reg_from'] = '酒云网小程序';
                    break;
                case 4:
                    $value['reg_from'] = 'h5';
                    break;
                case 5:
                    $value['reg_from'] = 'PC';
                    break;
                case 6:
                    $value['reg_from'] = '抖音小程序';
                    break;
                case 7:
                    $value['reg_from'] = '后台添加';
                    break;
                case 8:
                    $value['reg_from'] = '酒历小程序';
                    break;
                case 9:
                    $value['reg_from'] = '公社小程序';
                    break;
                case 10:
                    $value['reg_from'] = 'iPad';
                    break;
                case 11:
                    $value['reg_from'] = '门店小程序';
                    break;
            }
        }
        $PreSalesService = new PreSalesService();
        try {
            $created_time = $PreSalesService->allocationCustom(['type' => 1, 'user_info' => $newUser]);
            //发送分配excel到企微
            $PreSalesService->sendExcel(['created_time' => $created_time, 'type' => 1]);
        } catch (\Exception $e) {
            echo '分配失败，失败原因：' . $e->getMessage() . PHP_EOL;
            return false;
        }
        echo '分配完成，分配UID:' . json_encode($uidArr, true) . PHP_EOL;
        return true;
    }
}