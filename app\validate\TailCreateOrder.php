<?php


namespace app\validate;


class TailCreateOrder extends BaseValidate
{
    /**
     * 定义验证规则
     * 格式：'字段名'    =>    ['规则1','规则2'...]
     *
     * @var array
     */
    protected $rule = [
        'items_info'       => 'require',
        'order_from'       => 'require|in:0,1,2,3,4',
        'invoice_progress' => 'require|in:0,1',
        'is_cart'          => 'require|in:0,1',
        'province_id'      => 'require|number|>:0',
        'city_id'          => 'require|number|>:0',
        'district_id'      => 'require|number|>:0',
        'address'          => 'require',
        'consignee'        => 'require',
        'consignee_phone'  => 'require',
        'uid'              => 'require|number',
    ];

    /**
     * 定义错误信息
     * 格式：'字段名.规则名'    =>    '错误信息'
     *
     * @var array
     */
    protected $message = [
        'items_info.require'       => '商品信息必传',
        'order_from.require'       => '来源必传',
        'invoice_progress.require' => '是否开票必传',
        'is_cart.require'          => '是否购物车购买必传',
        'province_id.require'      => '省ID必传',
        'city_id.require'          => '市ID必传',
        'district_id.require'      => '区ID必传',
        'address.require'          => '详细地址必传',
        'consignee.require'        => '收货人必传',
        'consignee_phone.require'  => '收货人电话必传',
        'uid.require'              => '用户ID必传',
        'province_id.gt'           => '省ID必传',
        'city_id.gt'               => '市ID必传',
        'district_id.gt'           => '区ID必传',
    ];
}