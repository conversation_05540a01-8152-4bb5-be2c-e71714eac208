<?php


namespace app\controller;


use app\BaseController;
use app\ErrorCode;
use app\Request;
use app\validate\ListPagination;
use think\facade\Validate;
use app\service\OrderInvoice as OrderInvoiceService;

class OrderInvoice extends BaseController
{
    /**
     * Description:添加/修改订单开票记录
     * Author: zrc
     * Date: 2023/4/7
     * Time: 15:57
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function addInvoiceRecord(Request $request)
    {
        $params = $request->param();
        //参数验证
        $validate = Validate::rule([
            'sub_order_no|子订单号' => 'require',
            'order_type|订单类型'   => 'require|in:0,1,3,9,10',
            'invoice_type|开票类型' => 'require|in:1,2',
            'invoice_name|发票抬头' => 'require',
            'type_id|抬头类型'      => 'require|in:1,2',
            'email|邮箱'          => 'require',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        if ($params['invoice_type'] == 2 && $params['type_id'] == 1) $this->throwError('个人不能开专票', ErrorCode::PARAM_ERROR);
        if ($params['type_id'] == 2) {
            if (empty($params['taxpayer'])) $this->throwError('纳税人识别号不能为空', ErrorCode::PARAM_ERROR);
            $params['taxpayer'] = str_replace(" ", "", $params['taxpayer']);
        }
        $orderInvoiceService = new OrderInvoiceService();
        $result              = $orderInvoiceService->addInvoiceRecord($params);
        return $this->success($result);
    }

    /**
     * Description:订单开票
     * Author: zrc
     * Date: 2023/4/10
     * Time: 15:50
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function orderInvoice(Request $request)
    {
        $params        = $request->param();
        $params['uid'] = isset($params['uid']) ? $params['uid'] : $request->header('vinehoo-uid');
        //参数验证
        $validate = Validate::rule([
            'uid|用户ID'          => 'require|number',
            'order_info|订单信息'   => 'require',
            'invoice_type|开票类型' => 'require|in:1,2',
            'invoice_name|发票抬头' => 'require',
            'type_id|抬头类型'      => 'require|in:1,2',
            'email|邮箱'          => 'require',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $params['order_info'] = json_decode($params['order_info'], true);
        if ($params['invoice_type'] == 2 && $params['type_id'] == 1) $this->throwError('个人不能开专票', ErrorCode::PARAM_ERROR);
        foreach ($params['order_info'] as &$val) {
            if (!isset($val['sub_order_no'])) $this->throwError('订单号不能为空', ErrorCode::PARAM_ERROR);
            if (!isset($val['order_type']) || !in_array($val['order_type'], [0, 1, 3, 9, 10])) $this->throwError('订单类型错误', ErrorCode::PARAM_ERROR);
        }
        if ($params['type_id'] == 2) {
            if (empty($params['taxpayer'])) $this->throwError('纳税人识别号不能为空', ErrorCode::PARAM_ERROR);
            $params['taxpayer'] = str_replace(" ", "", $params['taxpayer']);
        }
        $orderInvoiceService = new OrderInvoiceService();
        $result              = $orderInvoiceService->orderInvoice($params);
        return $this->success($result);
    }

    /**
     * Description:订单开票回调处理
     * Author: zrc
     * Date: 2023/4/12
     * Time: 11:17
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function orderInvoiceCallBack(Request $request)
    {
        $params = $request->param();
        //日志记录
        file_put_contents(root_path() . "/runtime/log/" . date('Ym') . "/" . date('Ymd') . 'orderInvoiceCallBack' . '.log', json_encode($params, true) . PHP_EOL, FILE_APPEND);
        if (empty($params['data']) || !isset($params['data']['order_nos']) || !isset($params['data']['status'])) $this->throwError('回调参数异常', ErrorCode::PARAM_ERROR);
        $data                = array(
            'order_nos'    => $params['data']['order_nos'],
            'status'       => $params['data']['status'],
            'msg'          => $params['data']['msg'],
            'invoice_code' => $params['data']['invoice_code'],
        );
        $orderInvoiceService = new OrderInvoiceService();
        $result              = $orderInvoiceService->orderInvoiceCallBack($data);
        return $this->success($result);
    }

    /**
     * Description:获取订单开票信息
     * Author: zrc
     * Date: 2023/4/23
     * Time: 13:25
     * @param Request $request
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     */
    public function getOrderInvoiceInfo(Request $request)
    {
        $params = $request->param();
        if (!isset($params['sub_order_no'])) $this->throwError('订单号不能为空', ErrorCode::PARAM_ERROR);
        $orderInvoiceService = new OrderInvoiceService();
        $result              = $orderInvoiceService->getOrderInvoiceInfo($params);
        return $this->success($result);
    }

    /**
     * Description:获取开票中订单列表
     * Author: zrc
     * Date: 2023/7/26
     * Time: 10:45
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function getInvoicingOrderList(Request $request)
    {
        $params = $request->param();
        $validate = new ListPagination();
        if (!$validate->goCheck($params)) {//验证参数
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $orderInvoiceService = new OrderInvoiceService();
        $result              = $orderInvoiceService->getInvoicingOrderList($params);
        return $this->success($result);
    }
}