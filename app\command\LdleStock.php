<?php
declare (strict_types = 1);

namespace app\command;

use app\service\Cross;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;

class LdleStock extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('ldleStock')
            ->setDescription('the ldleStock command');
    }

    protected function execute(Input $input, Output $output)
    {
        (new Cross())->ldleStock();
        // 指令输出
        $output->writeln('success');
    }
}
