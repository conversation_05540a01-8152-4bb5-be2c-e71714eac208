<?php

namespace app\controller;

use app\BaseController;
use app\ErrorCode;
use app\Request;
use app\service\RepurchaseStatistics as RepurchaseStatisticService;
use think\facade\Validate;

class RepurchaseStatistics extends BaseController
{
    //列表
    public function list(Request $request)
    {
        $params = $request->param();

        //参数验证
        $validate = Validate::rule([
            'product_name|用户id' => 'max:100',
            'short_code|简码'     => 'max:30',
            'bar_code|条码'       => 'max:30',
        ]);
        if (!$validate->check($params)) $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);

        $data = (new RepurchaseStatisticService())->list($params);
        return $this->success($data);

    }

    //用户
    public function users(Request $request)
    {
        $params = $request->param();

        //参数验证
        $validate = Validate::rule([
            'short_codes|简码' => 'require|max:30',
        ]);
        if (!$validate->check($params)) $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);

        $data = (new RepurchaseStatisticService())->users($params);
        return $this->success($data);

    }

    //需求 订单 记录
    public function needlist(Request $request)
    {
        $params = $request->param();

        //参数验证
        $validate = Validate::rule([
            'uid|用户id'       => 'max:11',
            'short_codes|简码' => 'max:30',
            'period|期数'      => 'max:11',
            'user_phone|手机号' => 'max:20',
            'is_from|来源' => 'max:20',
        ]);
        if (!$validate->check($params)) $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);

        $data = (new RepurchaseStatisticService())->needlist($params);
        return $this->success($data);
    }

    //创建记录
    public function create(Request $request)
    {
        $params = $request->param();
        $params['vinehoo-client-version'] = $request->header('vinehoo-client-version'); # 版本号
        $params['vinehoo-client'] = $request->header('vinehoo-client'); # 客户端
        //参数验证
        $validate = Validate::rule([
            'sub_order_no|订单号' => 'require|max:30|alphaDash',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $data = (new RepurchaseStatisticService())->create($params);
        return $this->success($data);
    }

    public function putOnShelves(Request $request)
    {
        $params = $request->param();

        //参数验证
        $validate = Validate::rule([
            'short_codes|简码' => 'require|max:255',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        (new RepurchaseStatisticService())->putOnShelves($params);
        return $this->success((object)[]);
    }

    public function shortCodeSta(Request $request)
    {
        $params   = $request->param();
        $validate = Validate::rule([
            'short_code|简码' => 'require|max:255',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        (new RepurchaseStatisticService())->shortCodeSta($params);
        return $this->success((object)[]);
    }

    //复购期数上下架任务-数据库监控任务
    public function periodTask(Request $request)
    {
        $params           = $request->param();
        $params['period'] = $params['id'];
        unset($params['id']);
        (new RepurchaseStatisticService())->periodTask($params);
        return $this->success((object)[]);
    }

    public function period(Request $request)
    {
        $params = $request->param();

        //参数验证
        $validate = Validate::rule([
            'product_name|产品名称' => 'max:100',
            'short_code|简码'     => 'max:30',
            'bar_code|条码'       => 'max:30',
            'start_time|开始时间'       => 'date',
            'end_time|结束时间'       => 'date'
        ]);
        if (!$validate->check($params)) $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);

        $data = (new RepurchaseStatisticService())->period($params);
        return $this->success($data);
    }

    public function saverepurchmsg(Request $request)
    {
        $params = $request->param();

        //参数验证
        $validate = Validate::rule([
            'id|主键' => 'require|max:11',
            'is_msg|消息状态'     => 'require|max:1'
        ]);
        if (!$validate->check($params)) $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);

        $data = (new RepurchaseStatisticService())->saverepurchmsg($params);
        return $this->success($data);
    }

    public function remove(Request $request)
    {
        $params = $request->param();

        //参数验证
        $validate = Validate::rule([
            'id|主键' => 'require'
        ]);
        if (!$validate->check($params)) $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        $data = (new RepurchaseStatisticService())->remove($params);
        return $this->success($data);
    }

    /**
     * 心愿清单
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function wishlist(Request $request)
    {
        $params = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        //参数验证
        $validate = Validate::rule([
            'uid|用户请先登录：' => 'require|max:11'
        ]);
        if (!$validate->check($params)) $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        $data = (new RepurchaseStatisticService())->wishlist($params);
        return $this->success($data);
    }

    /**
     * 收藏
     * @param Request $request
     * @return void
     */
    public function wantCreate(Request $request)
    {
        $params = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        $params['vinehoo-client-version'] = $request->header('vinehoo-client-version'); # 版本号
        $params['vinehoo-client'] = $request->header('vinehoo-client'); # 客户端
        //参数验证
        $validate = Validate::rule([
            'period|期数' => 'require|max:11',
            'uid|用户请先登录：' => 'require|max:11'
        ]);
        if (!$validate->check($params)) $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        $data = (new RepurchaseStatisticService())->wantCreate($params);
        return $this->success($data);
    }

    /**
     * 收藏列表
     * @param Request $request
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function collectionList(Request $request)
    {
        $params = $request->param();
        $data = (new RepurchaseStatisticService())->collectionList($params);
        return $this->success($data);
    }

    /**
     * 用户列表
     * @param Request $request
     * @return \think\response\Json
     */
    public function userlist(Request $request)
    {
        $params = $request->param();

        $data = (new RepurchaseStatisticService())->userlist($params);
        return $this->success($data);
    }

    public function statisticsperiods()
    {
        RepurchaseStatisticService::statisticsperiods();
    }
}