<?php


namespace app\service;


use app\BaseService;
use app\service\Order as OrderService;
use think\facade\Db;

class MerchantSecond extends BaseService
{
    /**
     * Description:商家秒发创建订单
     * Author: zrc
     * Date: 2022/8/5
     * Time: 9:08
     * @param $sub_order_data
     * @param $orderIdMain
     * @param $params
     * @throws \Exception
     */
    public function createSubOrder($sub_order_data, $orderIdMain, $params, &$sub_order_nos=[])
    {
        //子订单数据写入
        $datas = [];
        foreach ($sub_order_data as &$v) {
            $temp_son = array_shift($sub_order_nos);
            if ($temp_son) {
                $sub_order_no = env('ORDERS.ORDER_SON') . $temp_son;
            } else {
                $sub_order_no = creatOrderNo(env('ORDERS.ORDER_SON'), $params['uid']);
            }
            $subData      = array(
                'uid'                    => $params['uid'],
                'sub_order_no'           => $sub_order_no,
                'sub_order_status'       => 0,
                'main_order_id'          => $orderIdMain,
                'period'                 => $v['period'],
                'package_id'             => $v['package_id'],
                'special_type'           => $params['special_type'],
                'special_price'          => $v['special_price'],
                'order_from'             => $params['order_from'],
                'order_qty'              => $v['nums'],
                'payment_amount'         => $v['goods_money'],
                'cash_amount'            => $v['cash_amount'],
                'preferential_reduction' => $v['discounted_price'],
                'express_fee'            => $v['express_fee'],
                'money_off_split_value'  => $v['money_off_split_value'],
                'coupon_id'              => $params['coupon_id'],
                'coupon_split_value'     => $v['coupon_split_value'],
                'invoice_progress'       => $params['invoice_progress'],
                'invoice_id'             => $params['invoice_id'],
                'express_type'           => 0,
                'express_number'         => '',
                'is_ts'                  => $v['is_ts'],
                'created_time'           => time(),
                'group_id'               => !empty($params['group_id']) ? $params['group_id'] : 0,
                'group_status'           => !empty($params['group_id']) ? 1 : 0,
                'order_type'             => 9,
                'express_coupon_id'      => $params['express_coupon_id'],
                'predict_time'           => $v['predict_delivery_time'],
                'warehouse_code'         => $v['erp_id'],
                'product_channel'        => $v['product_channel'],
                'merchant_id'            => $v['supplier_id'],
                'push_wms_status'        => 3,
            );
            //发货点、提货人处理
            foreach ($params['items_info'] as $val) {
                if ($v['package_id'] == $val['package_id']) {
                    $subData['delivery_store_id']   = $val['delivery_store_id'];
                    $subData['delivery_store_name'] = $val['delivery_store_name'];
                    $subData['merchant_id']         = $val['supplier_id'];
                    $subData['delivery_method']     = $val['express_type'];
                    if ($v['express_type'] == 3) {
                        $subData['delivery_consignee']       = trim($val['delivery_consignee']);
                        $subData['delivery_consignee_phone'] = trim($val['delivery_consignee_phone']);
                    }
                }
            }

            $exists_unpay_order_num = Db::name('merchant_second_order')->where([
                'uid'              => $params['uid'],
                'package_id'       => $v['package_id'],
                'sub_order_status' => 0,
                'is_delete'        => 0,
            ])->count();
            if ($exists_unpay_order_num) $this->throwError('您已有该商品的待支付订单，请先处理。');

            $addSubOrder = Db::name('merchant_second_order')->insert($subData);
            if (empty($addSubOrder)) $this->throwError('子订单写入失败');
            $datas[] = $subData;
        }
        return $datas;
    }

    /**
     * Description:商家秒发订单更换发货点（库存退还扣减）
     * Author: zrc
     * Date: 2022/8/9
     * Time: 16:24
     * @param $params
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function replaceDeliveryStore($params)
    {
        $field     = 'period,package_id,order_qty,sub_order_status,refund_status,delivery_store_id';
        $orderInfo = Db::name('merchant_second_order')->field($field)->where(['sub_order_no' => $params['sub_order_no']])->find();
        if (empty($orderInfo)) $this->throwError('未获取到订单信息');
        if ($orderInfo['sub_order_status'] != 1) $this->throwError('订单状态不是待发货，不允许修改');
        if (in_array($orderInfo['refund_status'], [1, 2])) $this->throwError('订单退款中或已退款，不允许修改');
        if ($orderInfo['delivery_store_id'] == $params['delivery_store_id']) $this->throwError('当前选择发货地址与订单原地址相同，不允许修改');
        Db::startTrans();
        try {
            $updateData  = array(
                'delivery_store_id' => $params['delivery_store_id'],
                'update_time'       => time(),
            );
            $updateOrder = Db::name('merchant_second_order')->where(['sub_order_no' => $params['sub_order_no']])->update($updateData);
            if (empty($updateOrder)) $this->throwError('订单修改发货点失败');
            //库存处理
            $changeData = array(
                'orderno'               => $params['sub_order_no'],
                'old_delivery_store_id' => $orderInfo['delivery_store_id'],
                'items'                 => [[
                    'period'            => $orderInfo['period'],
                    'set_id'            => $orderInfo['package_id'],
                    'delivery_store_id' => intval($params['delivery_store_id']),
                    'buy_num'           => $orderInfo['order_qty']
                ]]
            );
            $stockDeal  = httpPostString(env('ITEM.VMALL_STOCK_URL') . '/vmall_inventory_service/v3/inventory/change', json_encode($changeData));
            if (!isset($stockDeal['error_code']) || $stockDeal['error_code']) $this->throwError($stockDeal['error_msg']);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->throwError($e->getMessage());
        }
        //添加备注
        $orderService = new OrderService();
        $remark       = array(
            'sub_order_no' => $params['sub_order_no'],
            'order_type'   => 9,
            'content'      => '更换订单发货点：' . $orderInfo['delivery_store_id'] . '-->' . $params['delivery_store_id'],
            'admin_id'     => $params['admin_id']
        );
        $orderService->createRemarks($remark);
        return true;
    }

    /**
     * Description:发货/配送/提货
     * Author: zrc
     * Date: 2022/8/9
     * Time: 16:43
     * @param $params
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function goShip($params)
    {
        $field     = 'sub_order_no,sub_order_status,refund_status,delivery_method,invoice_progress,uid,invoice_id,payment_amount,merchant_id,group_status,product_channel,refund_money';
        $orderInfo = Db::name('merchant_second_order')->field($field)->where(['sub_order_no' => $params['sub_order_no']])->find();
        if (empty($orderInfo)) $this->throwError('未获取到订单信息');
        if ($orderInfo['group_status'] == 1) $this->throwError('订单拼团中');
        if ($orderInfo['group_status'] == 3) $this->throwError('订单拼团失败');
        switch ($params['type']) {
            case 1:
                if ($orderInfo['delivery_method'] != 1) $this->throwError('订单发货方式不是物流配送，不允许发货');
                if ($orderInfo['sub_order_status'] != 1) $this->throwError('订单状态不是待发货，不允许发货');
                if (in_array($orderInfo['refund_status'], [1, 2])) $this->throwError('订单退款中或已退款，不允许发货');
                $updateData = array(
                    'sub_order_status'      => 2,
                    'express_name'          => isset($params['express_name']) ? trim($params['express_name']) : '',
                    'express_type'          => $params['express_type'],
                    'express_number'        => trim($params['express_number']),
                    'delivery_person_name'  => isset($params['delivery_person_name']) ? trim($params['delivery_person_name']) : '',
                    'delivery_person_phone' => isset($params['delivery_person_phone']) ? trim($params['delivery_person_phone']) : '',
                    'delivery_time'         => time(),
                    'update_time'           => time(),
                );
                break;
            case 2:
                if ($orderInfo['delivery_method'] != 2) $this->throwError('订单发货方式不是同城配送，不允许配送');
                if ($orderInfo['sub_order_status'] != 1) $this->throwError('订单状态不是待配送，不允许配送');
                if (in_array($orderInfo['refund_status'], [1, 2])) $this->throwError('订单退款中或已退款，不允许配送');
                $updateData = array(
                    'sub_order_status'      => 2,
                    'delivery_person_name'  => trim($params['delivery_person_name']),
                    'delivery_person_phone' => trim($params['delivery_person_phone']),
                    'delivery_time'         => time(),
                    'update_time'           => time(),
                );
                break;
            case 3:
                if ($orderInfo['delivery_method'] != 3) $this->throwError('订单发货方式不是自提订单，不允许提货');
                if ($orderInfo['sub_order_status'] != 1) $this->throwError('订单状态不是待提货，不允许提货');
                if (in_array($orderInfo['refund_status'], [1, 2])) $this->throwError('订单退款中或已退款，不允许提货');
                $updateData = array(
                    'sub_order_status'   => 3,
                    'delivery_time'      => time(),
                    'goods_receipt_time' => time(),
                    'update_time'        => time(),
                );
                break;
        }
        $updateOrder = Db::name('merchant_second_order')->where(['sub_order_no' => $params['sub_order_no']])->update($updateData);
        if (empty($updateOrder)) $this->throwError('订单发货失败');
        //去开票
        if ($params['type'] == 3 && isset($orderInfo['invoice_progress']) && $orderInfo['invoice_progress'] == 1) {
            $invoiceRecord = Db::name('invoice_record')->where(['id' => $orderInfo['invoice_id'], 'sub_order_no' => $params['sub_order_no']])->find();
            if (!empty($invoiceRecord)) {
                $merchantInfo = curlRequest(env('ITEM.VMALL_URL') . '/vmall/v3/merchant/detail', ['id' => $orderInfo['merchant_id']], [], 'GET');
                //商家秒发(非直营商家，产品渠道为酒云采购的由商家自己开票)
                if (isset($merchantInfo['data']['is_self_support']) && $merchantInfo['data']['is_self_support'] == 0 && $orderInfo['product_channel'] == 3) {
                    $merchant_second_order[] = [
                        'order_sn'    => $orderInfo['sub_order_no'],
                        'amount'      => $orderInfo['payment_amount'] - $orderInfo['refund_money'],
                        'finish_time' => date('Y-m-d H:i:s', time()),
                        'mid'         => $orderInfo['merchant_id']
                    ];
                    $invoiceData             = [
                        'uid'              => $orderInfo['uid'],
                        'invoice_type_id'  => 1,
                        'invoice_title_id' => $invoiceRecord['id'],
                        'type_id'          => $invoiceRecord['type_id'],
                        'invoice_name'     => $invoiceRecord['invoice_name'],
                        'taxpayer'         => $invoiceRecord['taxpayer'],
                        'email'            => $invoiceRecord['email'],
                        'orders'           => $merchant_second_order,
                        'company_address'  => isset($invoiceRecord['company_address']) ? $invoiceRecord['company_address'] : '',
                        'company_tel'      => isset($invoiceRecord['company_tel']) ? $invoiceRecord['company_tel'] : '',
                        'opening_bank'     => isset($invoiceRecord['opening_bank']) ? $invoiceRecord['opening_bank'] : '',
                        'bank_account'     => isset($invoiceRecord['bank_account']) ? $invoiceRecord['bank_account'] : '',
                        'consignee'        => isset($invoiceRecord['consignee']) ? $invoiceRecord['consignee'] : '',
                        'province_id'      => isset($invoiceRecord['province_id']) ? $invoiceRecord['province_id'] : '',
                        'city_id'          => isset($invoiceRecord['city_id']) ? $invoiceRecord['city_id'] : '',
                        'town_id'          => isset($invoiceRecord['town_id']) ? $invoiceRecord['town_id'] : '',
                        'province_name'    => isset($invoiceRecord['province_name']) ? $invoiceRecord['province_name'] : '',
                        'city_name'        => isset($invoiceRecord['city_name']) ? $invoiceRecord['city_name'] : '',
                        'town_name'        => isset($invoiceRecord['town_name']) ? $invoiceRecord['town_name'] : '',
                        'address'          => isset($invoiceRecord['address']) ? $invoiceRecord['address'] : '',
                    ];
                    $dealInvoice             = curlRequest(env('ITEM.VMALL_URL') . '/vmall/v3/invoice/invoice/creates', $invoiceData, []);
                } else {//酒云开票
                    $invoiceData = array(
                        'uid'             => intval($orderInfo['uid']),
                        'order_info'      => json_encode([['sub_order_no' => $params['sub_order_no'], 'order_type' => 9]], true),
                        'invoice_type'    => 1,
                        'invoice_name'    => $invoiceRecord['invoice_name'],
                        'type_id'         => $invoiceRecord['type_id'],
                        'email'           => $invoiceRecord['email'],
                        'taxpayer'        => $invoiceRecord['taxpayer'],
                        'telephone'       => $invoiceRecord['telephone'],
                        'company_address' => $invoiceRecord['company_address'] ?? '',
                        'company_tel'     => $invoiceRecord['company_tel'] ?? '',
                        'opening_bank'    => $invoiceRecord['opening_bank'] ?? '',
                        'bank_account'    => $invoiceRecord['bank_account'] ?? ''
                    );
                    $dealInvoice = curlRequest(env('ITEM.ORDERS_URL') . '/orders/v3/orderInvoice/orderInvoice', $invoiceData, []);
                }
                //任务日志
                $taskLog = '开票日志：' . $orderInfo['sub_order_no'] . '请求参数：' . json_encode($invoiceData, JSON_UNESCAPED_UNICODE) . '返回参数：' . json_encode($dealInvoice, JSON_UNESCAPED_UNICODE);
                file_put_contents(root_path() . "/runtime/log/" . date('Ym') . "/" . date('Ymd') . 'merchantSecondOrderInvoiceLog' . '.log', $taskLog . PHP_EOL, FILE_APPEND);
            }
        }
        //推送T+
        $pushData = array(
            'exchange_name' => 'orders',
            'routing_key'   => 'push.erp',
            'data'          => base64_encode(json_encode(['sub_order_no' => $params['sub_order_no'], 'operator' => $params['admin_id'], 'order_type' => 9])),
        );
        httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
        return true;
    }

    /**
     * Description:修改快递信息
     * Author: zrc
     * Date: 2023/5/10
     * Time: 16:23
     * @param $params
     * @return int
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updateExpressInfo($params)
    {
        $orderInfo = Db::name('merchant_second_order')->field('id,sub_order_status,express_type,express_number')->where(['sub_order_no' => $params['sub_order_no']])->find();
        if (empty($orderInfo)) $this->throwError('未获取到订单信息');
        if ($orderInfo['sub_order_status'] != 2) $this->throwError('订单状态不是已发货，不能修改快递信息');
        //快递名称处理
        switch ($params['express_type']) {
            case 2:
                $express_name = '顺丰快递';
                break;
            case 4:
            case 5:
                $express_name = '京东快递';
                break;
            case 10:
                $express_name = '京东物流';
                break;
            case 65:
                $express_name = $params['express_name'];
                break;
            case 100:
                $express_name = '闪送';
                break;
            default:
                $express_name = '其他快递';
        }
        $updateData = array(
            'express_type'   => $params['express_type'],
            'express_name'   => $express_name,
            'express_number' => trim($params['express_number']),
            'operator'       => $params['admin_id'],
            'update_time'    => time()
        );
        $result     = Db::name('merchant_second_order')->where(['id' => $orderInfo['id']])->update($updateData);
        $msg        = '订单快递信息调整：' . $orderInfo['express_type'] . '-' . $orderInfo['express_number'] . '-->' . $params['express_type'] . '-' . $params['express_number'];
        //添加订单备注
        $remarks      = array(
            'sub_order_no' => $params['sub_order_no'],
            'order_type'   => 9,
            'content'      => $msg,
            'admin_id'     => isset($params['admin_id']) ? $params['admin_id'] : 0
        );
        $orderService = new OrderService();
        $orderService->createRemarks($remarks);
        return $result;
    }
}