<?php
declare (strict_types = 1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;


/**
 * 跨境自动推单每日统计
 * Class CrossAutoPushOrderDailyGatherCommand
 * @package app\command
 */
class CrossAutoPushOrderDailyGatherCommand extends Command
{
    protected $service;

    protected function configure()
    {
        // 指令配置
        $this->setName('CrossAutoPushOrderDailyGatherCommand')
            ->setDescription('the CrossAutoPushOrderDailyGatherCommand command');
    }

    protected function execute(Input $input, Output $output)
    {
    	// 指令输出
        $this->init();
        $this->service->exec();
    }

    protected function init(){
      $this->service = new \app\service\command\CrossAutoPushOrderDailyGatherCommand();
    }
}
