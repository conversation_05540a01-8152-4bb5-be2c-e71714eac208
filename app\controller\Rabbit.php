<?php


namespace app\controller;


use app\BaseController;
use app\Request;
use app\ErrorCode;
use app\service\Rabbit as RabbitService;
use app\validate\ListPagination;
use app\validate\RabbitCreateOrder;

class Rabbit extends BaseController
{
    /**
     * Description:兔头实物创建订单
     * Author: zrc
     * Date: 2021/8/3
     * Time: 17:02
     * @param Request $request
     */
    public function create(Request $request)
    {
        $params        = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        $validate      = new RabbitCreateOrder();
        if (!$validate->goCheck($params)) {//验证参数
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        //商品信息参数验证
        foreach ($params['items_info'] as &$val) {
            if (empty($val['period']) || empty($val['package_id']) || empty($val['nums']) || empty($val['predict_time'])) {
                $this->throwError('商品信息异常', ErrorCode::PARAM_ERROR);
            }
            $val['predict_time'] = strtotime($val['predict_time']);
        }
        $crossService = new RabbitService();
        $result       = $crossService->createOrder($params);
        return $this->success($result);
    }

    /**
     * Description:兔头兑换实物创建订单队列处理
     * Author: zrc
     * Date: 2022/6/24
     * Time: 11:34
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function createRabbitOrderDeal(Request $request)
    {
        $params = file_get_contents('php://input');
        if (empty($params)) {
            $this->throwError('未获取到队列数据', ErrorCode::PARAM_ERROR);
        }
        $crossService = new RabbitService();
        $result       = $crossService->createRabbitOrderDeal($params);
        return $this->success($result);
    }

    /**
     * Description:兔头实物订单修改
     * Author: zrc
     * Date: 2021/8/4
     * Time: 14:14
     * @param Request $request
     */
    public function update(Request $request)
    {
        $params             = $request->param();
        $params['operator'] = $request->header('vinehoo-uid');
        if (empty($params['sub_order_no'])) {
            $this->throwError('请传入子订单号', ErrorCode::PARAM_ERROR);
        }
        if (!isset($params['operator'])) {
            $this->throwError('请传入操作人ID', ErrorCode::PARAM_ERROR);
        }
        $rabbitService = new RabbitService();
        $result        = $rabbitService->updateOrder($params);
        return $this->success($result);
    }

    /**
     * Description:兔头实物订单列表
     * Author: zrc
     * Date: 2021/8/4
     * Time: 14:19
     * @param Request $request
     */
    public function orderList(Request $request)
    {
        $params   = $request->param();
        $validate = new ListPagination();
        if (!$validate->goCheck($params)) {//验证参数
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $rabbitService = new RabbitService();
        $result        = $rabbitService->orderList($params);
        return $this->success($result);
    }
}