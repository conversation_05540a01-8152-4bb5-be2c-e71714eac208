<?php
declare (strict_types = 1);

namespace app\model;

use think\db\exception\DbEventException;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\Log;
use think\Model;

/**
 * @mixin \think\Model
 */
class FreightManagement extends Model
{
    protected $name = 'freight_management';

    protected function getTableName()
    {
        return Db::name($this->name);
    }
    public function getFreightList($params, $page = 1, $limit = 10)
    {
        $model = new FreightManagement();
        $offset = ($page - 1) * $limit;
        $where=[];
        if (isset($params['status']) && !empty($params['status'])) {
            $where[]=['status','=',$params['status']];
        }
        $count=$model->where($where)->count();
        $list=$model->where($where)->order("id desc")
            ->limit($offset,intval($limit))
            ->select()->toArray();

        foreach ($list as &$temp){
            $temp['created_time']=date("Y-m-d H:i:s",$temp['created_time']);
            $temp['update_time']=date("Y-m-d H:i:s",$temp['update_time']);
            $temp['region']=$this->getRegion($temp);
            $temp['condition']=$this->getCondition($temp);
           // $temp['condition']=Db::name("freight_condition")->field("id,condition,base_quantity,base_price")->where([['freight_id','=',$temp['id']],['status','=',$temp['status']]])->select()->toArray();
        }
        $result['list']=$list;
        $result['total']=$count;
        return $result;
    }

    public function getRegion($temp)
    {
        $region=[];
        $region=Db::name("freight_region")->field("id,region,base_quantity,base_price,add_quantity,add_price")->where([['freight_id','=',$temp['id']],['status','=',$temp['status']]])->select()->toArray();
        foreach ($region as &$value){
            $region_name='';
            if (strstr($value['region'],'0') || $value['region'] == '0') {
                $region_name='全国,';
            }
            $region_name.=getRegion($value['region']);
            //判断是否是全国
            if ( $value['region'] == '0') {
                $value['is_all'] = true;
            }else{
                $value['is_all'] = false;
            }
            $value['region_name']=$region_name;
        }
        return $region;

    }

    public function getCondition($temp)
    {
        $condition=Db::name("freight_condition")->field("id,condition,base_quantity,base_price")->where([['freight_id','=',$temp['id']],['status','=',$temp['status']]])->select()->toArray();
        foreach ($condition as &$v){
            $condition_name='';
            if (strstr($v['condition'],'0') || $v['condition'] == '0') {
                $condition_name='全国,';
            }
            $condition_name.=getRegion($v['condition']);
            //判断是否是全国
            if ($v['condition'] == '0') {
                $v['is_all'] = true;
            }else{
                $v['is_all'] = false;
            }
            $v['condition_name']=$condition_name;
        }
        return $condition;
        
    }

    public function createFreight($params)
    {
        $freightData=[
            'name'=>$params['name'],
            'calculate_type'=>$params['calculate_type'],
            'base_quantity'=>$params['base_quantity'],
            'base_price'=>$params['base_price'],
            'add_quantity'=>$params['add_quantity'],
            'add_price'=>$params['add_price'],
            'status'=>isset($params['status'])?$params['status']:1,
            'created_time'=>time(),
            'update_time'=>time(),
        ];
        $regionData = isset($params['region'])?$params['region']:[];
        $conditionData = isset($params['condition'])?$params['condition']:[];
        Db::startTrans();
        try {
            $freight_id = self::insertGetId($freightData);

            if (empty($freight_id)) {
                Db::rollback();
                return false;
            }
            foreach ($regionData as &$tempRegion){
                if ($tempRegion['region']== '' || empty($tempRegion['base_quantity']) ||empty($tempRegion['base_price']) || empty($tempRegion['add_quantity']) || empty($tempRegion['add_price']) ) {
                    throw new ValidateException('指定地区参数配置参数不能为空');
                }
                $tempRegion['freight_id'] =$freight_id;
                $tempRegion['created_time'] =time();
                $tempRegion['update_time'] =time();
                $tempRegion['status'] =isset($params['status'])?$params['status']:1;
            }
            foreach ($conditionData as &$tempCondition){
                if ($tempCondition['condition'] == '' || empty($tempCondition['base_quantity']) ||empty($tempCondition['base_price'])) {
                    throw new ValidateException('条件包邮配置参数不能为空');
                }
                $tempCondition['freight_id'] =$freight_id;
                $tempCondition['created_time'] =time();
                $tempCondition['update_time'] =time();
                $tempCondition['status'] =isset($params['status'])?$params['status']:1;
            }
            if (!empty($regionData)) {
                $addRegion = Db::name('freight_region')->insertAll($regionData);
                if ($addRegion <1 ) {
                    Db::rollback();
                    return false;
                }
            }
            if (!empty($conditionData)) {
                $addCondition = Db::name('freight_condition')->insertAll($conditionData);
                if ($addCondition<1 ) {
                    Db::rollback();
                    return false;
                }
            }
            Db::commit();
            return ['freight_id' => $freight_id];
        } catch (\Exception $e) {
            Log::error('运费模板添加失败：' . $e->getMessage());
            Db::rollback();
            throw new ValidateException($e->getMessage());
        }

    }

    /**
     *
     * 获取单条数据
     * @param $id
     * @return FreightManagement|array|mixed|Model
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getOne($id)
    {
      $res=  self::where('id','=',$id)->find();
        if (empty($res)){
            return  [];
        }
        $result =$res;
        $result['region']=Db::name("freight_region")->field("id,region,base_quantity,base_price,add_quantity,add_price")->where([['freight_id','=',$id],['status','=',$res->status]])->select()->toArray();
        $result['condition']=Db::name("freight_condition")->field("id,condition,base_quantity,base_price")->where([['freight_id','=',$id],['status','=',$res->status]])->select()->toArray();
        return  $result;
    }

    /**
     * 状态变更
     * @param $param
     * @return bool|string
     */

    public function setStatus($param)
    {
        Db::startTrans();
        try {
            //把之前的修改为禁用
            Db::name('freight_management')->where('status','<>',3)->save(['status'=>1]);
            Db::name('freight_region')->where('status','<>',3)->save(['status'=>1]);
            Db::name('freight_condition')->where('status','<>',3)->save(['status'=>1]);
            $res =  Db::name('freight_management')->where('id','=',$param['id'])->where('status','<>',3)->save(['status'=>2]);
            if ($res<0){
                Db::rollback();
                return false;
            }
            $region =  Db::name('freight_region')->where('freight_id','=',$param['id'])->where('status','<>',3)->save(['status'=>2]);
            $condition =  Db::name('freight_condition')->where('freight_id','=',$param['id'])->where('status','<>',3)->save(['status'=>2]);
            if ($region <0|| $condition<0){
                Db::rollback();
                return false;
            }
            Db::commit();
            return true;
        }catch (\Exception $e) {
            #回滚事务
            Db::rollback();
            return  $e->getMessage();
        }

    }
}
