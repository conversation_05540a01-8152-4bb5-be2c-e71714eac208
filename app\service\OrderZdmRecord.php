<?php
declare (strict_types = 1);

namespace app\service;

use app\BaseService;
use app\service\elasticsearch\ElasticSearchService;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\Log;
use \app\model\OrderZdmRecord as OrderZdmRecordModel;

class OrderZdmRecord  extends BaseService
{
    public function remove($prams)
    {
        try {
            //查询记录
            $info =  Db::name('order_zdm_record')
                ->where('sub_order_no', $prams['sub_order_no'])
                ->findOrEmpty();
            if (!empty($info) && isset($prams['sub_order_status'])) {
                // 不推送
                if ($info['push_zdm_status'] == 3) {
                    return true;
                }

                $model = new OrderZdmRecordModel();
                if (intval($prams['sub_order_status']) === 0) {// 推送订单
                    $model->pushZdmOrder($info);

                } else {// 推送订单状态
                    $model->pushZdmOrderStatus($info, $prams['sub_order_status']);
                }
            }
            return true;
        } catch (\Exception $e) {
            throw new ValidateException("操作失败".$e->getMessage());
        }
    }

    /**
     * @param $prams
     * @return mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function list($prams)
    {
        try {
            $result = (new OrderZdmRecordModel())->list($prams);
            $uid = array_column($result['list'],'uid');
            $uid = implode(",",$uid);
            $filed='uid,realname,nickname,telephone,created_time';
            $userInfo = getUserInfoByUids($uid,$filed);
            foreach ($result['list'] as &$value){
                if (!empty($value['consignee_phone'])) {
                    $value['consignee_phone_encryption'] = substr_replace($value['consignee_phone'], '****', 3, 4);
                }
                if (!empty($value['consignee'])) {
                    $value['consignee_encryption'] = string_cut($value['consignee']);
                }
                $value['created_time'] =  date("Y-m-d H:i:s",$value['created_time']);
                $value['user'] = isset($userInfo[$value['uid']])?$userInfo[$value['uid']]:[];

            }
        } catch (\Exception $e) {
            throw new ValidateException("列表获取失败".$e->getMessage());
        }
        return $result;
        
    }

    /**
     * 导出
     * @param $prams
     * @return string
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function export($prams)
    {

            $where[] = ['is_del','=',0];
            if (isset($params['start_time']) && !empty($params['start_time']) && isset($params['end_time']) && !empty($params['end_time'])) {
                $where[] = ['created_time', 'between',[strtotime($params['start_time']),strtotime($params['end_time'])] ];
            }
            if ( isset($prams['uid']) && !empty($params['uid'])) {
                $where[] = ['uid' ,'=', $params['uid']];
            }
            if ( isset($prams['sub_order_no']) && !empty($params['sub_order_no'])) {
                $where[] = ['sub_order_no' ,'=', $params['sub_order_no']];
            }
            if ( isset($prams['period']) && !empty($params['period'])) {
                $where[] = ['period' ,'=', $params['period']];
            }
            if ( isset($prams['source_event']) && !empty($params['source_event'])) {
                $where[] = ['source_event' ,'=', $params['source_event']];
            }

            $list= Db::name("order_zdm_record")->where($where)->select()->toArray();
            $uid = array_column($list,'uid');
            $uid = implode(",",$uid);
            $filed='uid,realname,nickname,telephone,created_time';
            $userInfo = getUserInfoByUids($uid,$filed);
            # 获取商品信息
            $es = new ElasticSearchService();
            $periods = array_column($list,'periods');
            $arr = array(
                'index'  => ['periods'],
                'terms'  => [['id' => array_unique($periods)]],
                'source' => ['id','title'],
            );
            $periodsData = $es->getDocumentList($arr);
            $periodsInfo = [];
            if (!empty($periodsData['data'])){
                foreach ($periodsData['data'] as $temp){
                    $periodsInfo[$temp['id']] = $temp['title'];
                }
            }
            $data = [];
            foreach ($list as &$value){
                $value['created_time'] =  date("Y-m-d H:i:s",$value['created_time']);
                if (!empty($value['consignee_phone'])) {
                    $value['consignee_phone_encryption'] = substr_replace($value['consignee_phone'], '****', 3, 4);
                }
                if (!empty($value['consignee'])) {
                    $value['consignee_encryption'] = string_cut($value['consignee']);
                }
                $oneData =[
                    "nickname"=>isset($userInfo[$value['uid']]) ? $userInfo[$value['uid']]['nickname'] : '',
                    "telephone"=>isset($userInfo[$value['uid']]) ? $userInfo[$value['uid']]['telephone'] : '',
                    "user_created_time"=>isset($userInfo[$value['uid']]) ? $userInfo[$value['uid']]['created_time'] : '',
                    "created_time"=>$value['created_time'],
                    "period"=>$value['period'],
                    'title' => isset($periodsInfo[$value['period']])?$periodsInfo[$value['period']]:"",
                    "order_qty"=>$value['order_qty'],
                    "payment_amount"=>$value['payment_amount'],
                ];
                $data[] = $oneData;
            }
            $filename = "订单列表";
            $header = array(
                array('column' => 'nickname', 'name' => '用户昵称', 'width' => 30),
                array('column' => 'uid', 'name' => '用户id', 'width' => 15),
                array('column' => 'user_created_time', 'name' => '手机号', 'width' => 15),
                array('column' => '注册时间', 'name' => '注册时间', 'width' => 15),
                array('column' => 'created_time', 'name' => '下单时间', 'width' => 15),
                array('column' => 'period', 'name' => '产品期数', 'width' => 15),
                array('column' => 'title', 'name' => '产品名称', 'width' => 15),
                array('column' => 'order_qty', 'name' => '下单数量', 'width' => 15),
                array('column' => 'payment_amount', 'name' => '下单金额', 'width' => 15),
            );
            try {
                $uploadUrl = exportSheelExcel($data, $header, $filename);
                $OssUrl = OssUpload(public_path() . 'storage/' . $uploadUrl, $uploadUrl);
                return $OssUrl;
            } catch (\Exception $e) {
                Log::error($e->getMessage());
                throw new ValidateException("导出失败：" . $e->getMessage());
            }

    }

    /**
     * 记录创建
     * @param $params
     * @return int|string
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function recordCreate($params)
    {
        try {
            $result = (new OrderZdmRecordModel())->recordCreate($params);
            return $result;
        }catch (\Exception $e) {
            Log::error($e->getMessage());
            throw new ValidateException("添加失败：" . $e->getMessage());
        }

    }

}
