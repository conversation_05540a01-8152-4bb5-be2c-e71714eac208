<?php
declare (strict_types=1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use think\facade\Log;

class BalanceAutoRefund extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('balanceAutoRefund')
            ->setDescription('the balanceAutoRefund command');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('余额退款服务已启动 - ' . date('Y-m-d H:i:s'));

        // 设置信号处理，实现优雅退出
        $running = true;
        if (function_exists('pcntl_signal')) {
            pcntl_signal(SIGTERM, function () use (&$running) {
                $running = false;
            });
            pcntl_signal(SIGINT, function () use (&$running) {
                $running = false;
            });
        }

        while ($running) {
            // 处理信号
            if (function_exists('pcntl_signal_dispatch')) {
                pcntl_signal_dispatch();
            }

            try {
                // Check database connection and reconnect if needed
                try {
                    // Simple query to check connection
                    Db::query("SELECT 1");
                } catch (\Exception $dbException) {
                    $output->writeln('数据库连接已断开，尝试重新连接 - ' . date('Y-m-d H:i:s'));
                    Log::error('数据库连接已断开: ' . $dbException->getMessage());
                    // Improved reconnection logic with multiple attempts
                    $maxRetries = 3;
                    $retryCount = 0;
                    $reconnected = false;
                    
                    while (!$reconnected && $retryCount < $maxRetries) {
                        try {
                            $retryCount++;
                            sleep(2);
                            // Force reconnection
                            Db::connect('mysql', true);
                            // Verify connection is working
                            Db::query("SELECT 1");
                            $reconnected = true;
                            $output->writeln('数据库重新连接成功 - ' . date('Y-m-d H:i:s'));
                        } catch (\Exception $reconnectEx) {
                            $output->writeln('重连尝试 ' . $retryCount . ' 失败: ' . $reconnectEx->getMessage() . ' - ' . date('Y-m-d H:i:s'));
                            Log::error('数据库重连尝试 ' . $retryCount . ' 失败: ' . $reconnectEx->getMessage());
                            // Increase wait time between retries
                            sleep(3);
                        }
                    }
                    
                    if (!$reconnected) {
                        throw new \Exception('无法重新连接到数据库，已尝试 ' . $maxRetries . ' 次');
                    }
                }
                
                // 1. 处理待处理的记录，只查询request不为空的记录
                $records = Db::name('balance_pay')
                    ->where('status', 0) // 待处理
                    ->where('retry_count', '<', 3) // 最多重试3次
                    ->whereNotNull('request') // 只查询request不为空的记录
                    ->where('request', '<>', '') // 确保request不是空字符串
                    ->order('created_time ASC') // 先进先出
                    ->limit(10) // 批量处理，避免一次处理太多
                    ->select()
                    ->toArray();

                if (!empty($records)) {
                    $output->writeln('找到 ' . count($records) . ' 个待处理余额记录 - ' . date('Y-m-d H:i:s'));

                    foreach ($records as $record) {
                        $this->processRecord($record, $output);
                    }
                }

                // 2. 检查处理中但超时的记录（5分钟未完成）
                $stuck_count = Db::name('balance_pay')
                    ->where('status', 3) // 处理中
                    ->where('updated_time', '<', time() - 300) // 5分钟前
                    ->update([
                        'status' => 0, // 重置为待处理
                        'retry_count' => Db::raw('retry_count + 1'),
                        'updated_time' => time(),
                        'error_message' => '处理超时，自动重置'
                    ]);

                if ($stuck_count > 0) {
                    $output->writeln('重置了 ' . $stuck_count . ' 个卡住的记录 - ' . date('Y-m-d H:i:s'));
                }

                // 3. 重试失败的记录（1小时前失败且重试次数少于3次）
                $retry_count = Db::name('balance_pay')
                    ->where('status', 2) // 失败
                    ->where('retry_count', '<', 3) // 未达重试上限
                    ->whereNotNull('request') // 只重试request不为空的记录
                    ->where('request', '<>', '') // 确保request不是空字符串
                    ->where(function ($query) {
                        $query->whereNull('last_retry_time')
                            ->whereOr('last_retry_time', '<', time() - 3600); // 1小时前
                    })
                    ->update([
                        'status' => 0, // 重置为待处理
                        'updated_time' => time(),
                        'error_message' => Db::raw("CONCAT(IFNULL(error_message,''), ' | 自动重试')")
                    ]);

                if ($retry_count > 0) {
                    $output->writeln('重置了 ' . $retry_count . ' 个失败记录准备重试 - ' . date('Y-m-d H:i:s'));
                }

                // 4. 内存管理
                if (function_exists('gc_collect_cycles') && memory_get_usage() > 50 * 1024 * 1024) {
                    gc_collect_cycles();
                }

            } catch (\Exception $e) {
                $output->writeln('发生异常: ' . $e->getMessage() . ' - ' . date('Y-m-d H:i:s'));
                Log::error('余额退款异常: ' . $e->getMessage());
            }

            // 每次循环后固定休眠5秒
            sleep(5);
        }

        $output->writeln('余额退款服务已退出 - ' . date('Y-m-d H:i:s'));
    }

    /**
     * 处理单条退款记录
     *
     * @param array $record 退款记录
     * @param Output $output 输出对象
     */
    protected function processRecord($record, $output)
    {
        $output->writeln('处理记录: ' . $record['id'] . ', UID: ' . $record['uid'] . ' - ' . date('Y-m-d H:i:s'));

        // 标记为处理中
        $updated = Db::name('balance_pay')
            ->where('id', $record['id'])
            ->where('status', 0) // 确保状态未变
            ->update([
                'status' => 3, // 处理中
                'updated_time' => time()
            ]);

        if (!$updated) {
            $output->writeln('记录状态已变更，跳过处理 - ' . date('Y-m-d H:i:s'));
            return;
        }

        try {
            // 直接使用record中的request字段作为请求参数
            $request_data = json_decode($record['request'], true);

            if (empty($request_data)) {
                throw new \Exception('请求参数解析失败');
            }

            // 调用余额变更接口
            $result = \Curl::balanceChange($request_data);

            // 检查接口返回结果
            if (empty($result) || (isset($result['error_code']) && $result['error_code'] != 0)) {
                throw new \Exception('API返回错误: ' . (isset($result['error_msg']) ? $result['error_msg'] : '未知错误'));
            }

            // 更新为处理成功
            Db::name('balance_pay')
                ->where('id', $record['id'])
                ->update([
                    'status' => 1, // 成功
                    'processed_time' => time(),
                    'updated_time' => time(),
                    'resp' => json_encode($result, JSON_UNESCAPED_UNICODE),
                    'error_message' => null
                ]);

            $output->writeln('处理成功: ' . $record['id'] . ' - ' . date('Y-m-d H:i:s'));

        } catch (\Exception $e) {
            // 更新为处理失败
            Db::name('balance_pay')
                ->where('id', $record['id'])
                ->update([
                    'status' => 2, // 失败
                    'retry_count' => Db::raw('retry_count + 1'),
                    'last_retry_time' => time(),
                    'updated_time' => time(),
                    'error_message' => substr($e->getMessage(), 0, 255)
                ]);

            $output->writeln('处理失败: ' . $record['id'] . ', 错误: ' . $e->getMessage() . ' - ' . date('Y-m-d H:i:s'));
            Log::error("余额处理失败: {$record['id']}, 错误: {$e->getMessage()}");
        }
    }
}
