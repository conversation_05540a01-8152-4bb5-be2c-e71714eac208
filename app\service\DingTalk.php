<?php


namespace app\service;


use app\BaseService;
use app\service\Order as OrderService;
use app\service\Push as PushService;
use think\facade\Db;
use think\facade\Log;

class DingTalk extends BaseService
{
    /**
     * Description:退款审批回调处理
     * Author: zrc
     * Date: 2022/4/7
     * Time: 11:55
     * @param $requestparams
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function refundCallBack($requestparams)
    {
        Log::record('钉钉退款审批日志', json_encode($requestparams));
        $params       = $requestparams['process_instance'];
        $sub_order_no = '';
        $ticket_id    = '';
        foreach ($params['form_component_values'] as &$val) {
            if (isset($val['name'])) {
                if ($val['name'] == '订单号') $sub_order_no = trim($val['value']);
                if ($val['name'] == '工单ID') $ticket_id = trim($val['value']);
            }
        }
        if (empty($sub_order_no) || empty($ticket_id)) {
            $this->dingSendText($params['operation_records'][0]['userid'], '未获取到订单号或工单ID,审批流处理失败!');
            $this->throwError('未获取到订单号或工单ID,审批流处理失败!');
        }
        //审批通过处理
        if ($params['result'] == 'agree') {
            $after_sale_log = Db::name('order_after_sale_log')->field('id,order_type,status,ticket_type')->where(['sub_order_no' => $sub_order_no, 'ticket_id' => $ticket_id])->find();
            if (empty($after_sale_log)) {
                $this->dingSendText($params['operation_records'][0]['userid'], $sub_order_no . ':未获取到售后信息!');
                $this->throwError($sub_order_no . ':未获取到售后信息!');
            }
            Db::startTrans();
            try {
                //修改订单状态
                $config_order_type = config('config')['order_type'];//订单频道获取
                $updateOrder       = Db::name($config_order_type[$after_sale_log['order_type']]['table'])->where(['sub_order_no' => $sub_order_no])->update(['refund_status' => 2, 'update_time' => time()]);
                if (empty($updateOrder)) $this->throwError($sub_order_no . ':修改订单退款状态失败!');
                //修改售后记录，完结工单
                $updateAfterSale = Db::name('order_after_sale_log')->where(['sub_order_no' => $sub_order_no, 'ticket_id' => $ticket_id])->update(['status' => 6, 'end_time' => time()]);
                if (empty($updateAfterSale)) $this->throwError($sub_order_no . ':修改售后记录状态失败!');
                $ticketData = array(
                    'gd_id'           => $ticket_id,
                    'work_order_type' => $after_sale_log['ticket_type']
                );
                httpPostString(env('ITEM.WORK_URL') . '/work/v3/GdCommon/gdFinish', json_encode($ticketData));
                $logData = array(
                    'gd_id'   => $ticket_id,
                    'content' => $sub_order_no . ':退款审批已通过'
                );
                $this->httpPost(env('ITEM.WORK_URL') . '/work/v3/GdCommon/addLog', $logData);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                $this->dingSendText($params['operation_records'][0]['userid'], $sub_order_no . ':' . $e->getMessage());
                $this->throwError($e->getMessage());
            }
        }
        if ($params['result'] == 'refuse' || $params['status'] == 'TERMINATED') {
            $logData = array(
                'gd_id'   => $ticket_id,
                'content' => $sub_order_no . ':退款审批已被拒或被撤销，请重新发起钉钉审批'
            );
            $this->httpPost(env('ITEM.WORK_URL') . '/work/v3/GdCommon/addLog', $logData);
        }
        return true;
    }

    /**
     * Description:换货审批回调处理
     * Author: zrc
     * Date: 2022/4/7
     * Time: 13:58
     * @param $requestparams
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function changeGoodsCallBack($requestparams)
    {
        Log::record('钉钉换货审批日志', json_encode($requestparams));
        $params       = $requestparams['process_instance'];
        $sub_order_no = '';
        $ticket_id    = '';
        foreach ($params['form_component_values'] as &$val) {
            if (isset($val['name'])) {
                if ($val['name'] == '订单号') $sub_order_no = trim($val['value']);
                if ($val['name'] == '工单ID') $ticket_id = trim($val['value']);
            }
        }
        if (empty($sub_order_no) || empty($ticket_id)) {
            $this->dingSendText($params['operation_records'][0]['userid'], '未获取到订单号或工单ID,审批流处理失败!');
            $this->throwError('未获取到订单号或工单ID,审批流处理失败!');
        }
        //审批通过处理
        if ($params['result'] == 'agree') {
            //更改工单状态
            $updateData = array(
                'gd_id'        => $ticket_id,
                'gd_do_status' => 12//钉钉审批成功,允许换货发货
            );
            $this->httpPost(env('ITEM.WORK_URL') . '/work/v3/GdCommon/updateGdDoStatus', $updateData);
            //添加工单日志
            $ticketData = array(
                'gd_id'   => $ticket_id,
                'content' => $sub_order_no . ':换货审批已通过，请安排发货'
            );
            $this->httpPost(env('ITEM.WORK_URL') . '/work/v3/GdCommon/addLog', $ticketData);
        }
        if ($params['result'] == 'refuse' || $params['status'] == 'TERMINATED') {
            $logData = array(
                'gd_id'   => $ticket_id,
                'content' => $sub_order_no . ':换货审批已被拒或被撤销，请重新发起钉钉审批'
            );
            $this->httpPost(env('ITEM.WORK_URL') . '/work/v3/GdCommon/addLog', $logData);
        }
        return true;
    }

    /**
     * Description:推送钉钉消息
     * Author: zrc
     * Date: 2021/11/16
     * Time: 11:22
     * @param $pushData
     */
    public function dingSendText($dingtalk_uid, $text)
    {
        $pushData = array(
            'dingtalk_uid' => $dingtalk_uid,
            'text'         => $text . date('y-m-d H:i:s', time()),
        );
        httpPostString(env('DING_TALK_PUSH') . '/dingtalk/v3/sysnotice/sendtext', json_encode($pushData));
    }

    /**
     * Description:样酒申请创建钉钉审批
     * Author: zrc
     * Date: 2022/5/26
     * Time: 15:36
     * @param $requestparams
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function offlineCreateDingtalkVerify($requestparams)
    {
        $params = $requestparams;
        //获取发起人钉钉信息
        $userInfo = $this->httpGet(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info', ['admin_id' => $params['admin_id'], 'field' => 'userid,dept_id']);
        if ($userInfo['error_code'] != 0 || !isset($userInfo['data'][$params['admin_id']])) $this->throwError('发起钉钉审批失败：未获取到钉钉用户信息');
        $userid   = $userInfo['data'][$params['admin_id']]['userid'];//钉钉userid
        $dept_arr = json_decode($userInfo['data'][$params['admin_id']]['dept_id']);
        $dept_id  = $dept_arr[0];//部门ID(多个部门取第一个)
        //审批表单数据处理
        $goodsInfo = [];
        //商品详情处理
        foreach ($params['items_info'] as $key => $val) {
            $goodsInfo[$key] = array(
                array(
                    'name'  => '简码',
                    'value' => strval($val['short_code']),
                ),
                array(
                    'name'  => '商品名称',
                    'value' => $val['product_name'],
                ),
                array(
                    'name'  => '条码',
                    'value' => strval($val['bar_code']),
                ),
                array(
                    'name'  => '年份',
                    'value' => '-',
                ),
                array(
                    'name'  => '单价',
                    'value' => strval($val['price']),
                ),
                array(
                    'name'  => '规格型号',
                    'value' => strval($val['unit']),
                ),
                array(
                    'name'  => '数量',
                    'value' => strval($val['nums']),
                ),
            );
        }
        $form_component_values = array(
            array(
                'name'  => '模板ID',
                'value' => $params['order_no'],
            ),
            array(
                'name'  => '运输方式',
                'value' => $params['delivery_mode'],
            ),
            array(
                'name'  => '送货地址',
                'value' => $params['address'],
            ),
            array(
                'name'  => '收件人',
                'value' => $params['consignee'],
            ),
            array(
                'name'  => '收件人手机号',
                'value' => $params['consignee_phone'],
            ),
            array(
                'name'  => '仓库',
                'value' => $params['warehouse'],
            ),
            array(
                'name'  => '仓库编码',
                'value' => $params['warehouse_code'],
            ),
            array(
                'name'  => '数据来源',
                'value' => '线下业务',
            ),
            array(
                'name'  => '备注',
                'value' => isset($params['memo']) ? $params['memo'] : '',
            ),
            array(
                'name'  => '商品详情',
                'value' => $goodsInfo
            ),
        );
        $pushData              = array(
            'form_component_values' => $form_component_values,
            'process_code'          => 'PROC-31D9F26B-9452-481E-924B-681E6607DB97',
            'dept_id'               => $dept_id,
            'originator_user_id'    => $userid
        );
        $result                = $this->httpPost(env('ITEM.DINGTALK_APPROVAL_URL') . '/dingtalk/v3/process/instance/create', $pushData);
        if ($result['error_code'] != 0) $this->throwError('发起钉钉审批失败：' . $result['error_msg']);
        return true;
    }

    /**
     * Description:样酒审批回调处理
     * Author: zrc
     * Date: 2022/5/26
     * Time: 16:43
     * @param $requestparams
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function offlineVerifyCallBack($requestparams)
    {
        Log::record('钉钉样酒审批日志', json_encode($requestparams));
        $params       = $requestparams['process_instance'];
        $sub_order_no = '';
        foreach ($params['form_component_values'] as &$val) {
            if (isset($val['name'])) {
                if ($val['name'] == '模板ID') $sub_order_no = trim($val['value']);
            }
        }
        if (empty($sub_order_no)) {
            $this->dingSendText($params['operation_records'][0]['userid'], '未获取到订单号,审批流处理失败!');
            $this->throwError('未获取到订单号,审批流处理失败!');
        }
        //审批通过处理
        if ($params['result'] == 'agree') {
            $updateData = array(
                'dingtalk_status' => 2,
                'update_time'     => time()
            );
            //推送萌牙
            $pushService = new PushService();
            $pushService->pushWms(['sub_order_no' => $sub_order_no, 'order_type' => 8]);
        }
        if ($params['result'] == 'refuse' || $params['status'] == 'TERMINATED') {
            $updateData = array(
                'dingtalk_status' => 3,
                'update_time'     => time()
            );
        }
        //更改订单钉钉审批状态
        $result = Db::name('offline_order')->where(['sub_order_no' => $sub_order_no])->update($updateData);
        return $result;
    }

    /**
     * Description:超时订单支付异步回调审批退款
     * Author: zrc
     * Date: 2022/9/16
     * Time: 11:20
     * @param $params
     * @return bool
     */
    public function TimeOutOrderCreateDingtalkVerify($params)
    {
        $orderMain    = $params['orderMain'];
        $orderSub     = $params['orderSub'];
        $notify_param = $params['notify_param'];
        if (empty($orderMain) || empty($orderSub)) $this->throwError('未获取到订单信息');
        $order_type = config('config')['order_type'];//订单频道获取
        //验证是否已经提交审批
        $refund_status = Db::name($order_type[intval($orderSub[0]['order_type'])]['table'])->where(['sub_order_no' => $orderSub[0]['sub_order_no']])->value('refund_status');
        if ($refund_status != 0) return true;
        Db::startTrans();
        try {
            //主订单处理
            $mainUpdateData = array(
                'payment_time' => time(),
                'tradeno'      => $notify_param['seqId'],
                'update_time'  => time()
            );
            $updateMain     = Db::name('order_main')->where(['id' => $orderMain['id']])->update($mainUpdateData);
            if (empty($updateMain)) $this->throwError('主订单状态修改失败');
            //子订单退款状态处理
            foreach ($orderSub as &$val) {
                $subUpdateData = array(
                    'payment_time'      => time(),
                    'refund_status'     => 1,
                    'refund_start_time' => time(),
                    'update_time'       => time()
                );
                $updateSub     = Db::name($order_type[intval($val['order_type'])]['table'])->where(['id' => $val['id']])->update($subUpdateData);
                if (empty($updateSub)) $this->throwError('子订单状态修改失败');
            }
            //生成退款订单
            $refund_order_no = creatOrderNo(env('ORDERS.REFUND'), $orderMain['uid']);
            $insertData      = array(
                'refund_order_no' => $refund_order_no,
                'main_order_no'   => $orderMain['main_order_no'],
                'refund_status'   => 0,
                'sub_order_no'    => $orderSub[0]['sub_order_no'],
                'payment_method'  => $orderMain['payment_method'],
                'payment_subject' => $orderMain['payment_subject'],
                'refund_amount'   => $orderMain['payment_amount'],
                'created_time'    => time(),
            );
            $addRefund       = Db::name('refund_order')->insert($insertData);
            if (empty($addRefund)) $this->throwError('生成退款订单失败');
            $payment_subject = config('config')['payment_subject'];//支付主体获取
            $payment_method  = config('config')['payment_method'];//支付方式获取
            //发起退款审批
            $form_component_values = array(
                array(
                    'name'  => '订单号',
                    'value' => $orderMain['main_order_no'],
                ),
                array(
                    'name'  => '用户ID',
                    'value' => strval($orderMain['uid']),
                ),
                array(
                    'name'  => '退款订单号',
                    'value' => $refund_order_no,
                ),
                array(
                    'name'  => '支付主体',
                    'value' => $payment_subject[intval($orderMain['payment_subject'])]['label'],
                ),
                array(
                    'name'  => '支付方式',
                    'value' => $payment_method[intval($orderMain['payment_method']) + 1]['label'],
                ),
                array(
                    'name'  => '支付时间',
                    'value' => date('Y-m-d H:i:s', time()),
                ),
                array(
                    'name'  => '支付金额',
                    'value' => strval($orderMain['payment_amount']),
                )
            );
            //获取发起人信息
            $userInfo = $this->httpGet(env('ITEM.WORK_URL') . '/work/v3/GdCommon/getDutyUser');
            if ($userInfo['error_code'] != 0 || !isset($userInfo['data']['uid'])) $this->throwError('发起企业微信审批失败：未获取到工单当前值班人信息');
            $userid   = $userInfo['data']['uid'];//企业微信userid
            $dept_id  = $userInfo['data']['main_department'];//部门ID
            $pushData = array(
                'form_component_values' => $form_component_values,
                'process_code'          => env('ORDERS.time_out_order_create_verify_code'),
                'dept_id'               => $dept_id,
                'originator_user_id'    => $userid
            );
            $result   = httpPostString(env('ITEM.WECHART_URL') . '/wechat/v3/wecom/approval/create', json_encode($pushData, JSON_UNESCAPED_UNICODE));
            if ($result['error_code'] != 0) $this->throwError('发起企业微信审批失败：' . $result['error_msg']);
            //推送短信消息
            $getTelephone = httpGet(env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo', ['uid' => $orderMain['uid'], 'field' => 'telephone_encrypt']);
            if (isset($getTelephone['data']['list'][0]['telephone_encrypt'])) {
                $encrypt   = cryptionDeal(2, [$getTelephone['data']['list'][0]['telephone_encrypt']], $orderMain['uid'], '前端用户');
                $telephone = isset($encrypt[$getTelephone['data']['list'][0]['telephone_encrypt']]) ? $encrypt[$getTelephone['data']['list'][0]['telephone_encrypt']] : '';
            }
            if (isset($telephone)) {
                $this->httpPost(env('ITEM.SMS_URL') . '/sms/v3/group/sendSms', ['telephone' => $telephone, 'content' => '您的订单(' . $orderMain['main_order_no'] . ')因超时取消，待系统确认后支付金额原路退回。拒收请回复R。']);
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            //添加订单备注
            $orderService = new OrderService();
            foreach ($orderSub as &$v) {
                $remark = array(
                    'sub_order_no' => $v['sub_order_no'],
                    'order_type'   => $v['order_type'],
                    'content'      => '发起退款审批失败：' . $e->getMessage(),
                    'admin_id'     => 0
                );
                $orderService->createRemarks($remark);
            }
            $this->throwError($orderMain['main_order_no'] . '发起退款审批失败：' . $e->getMessage());
        }
        return true;
    }

    /**
     * Description:超时订单退款审批回调处理
     * Author: zrc
     * Date: 2022/9/26
     * Time: 10:43
     * @param $requestparams
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function timeOutOrderRefundCallBack($requestparams)
    {
        Log::record('超时订单退款审批日志', json_encode($requestparams));
        $params          = $requestparams['process_instance'];
        $main_order_no   = '';
        $refund_order_no = '';
        foreach ($params['form_component_values'] as &$val) {
            if (isset($val['name'])) {
                if ($val['name'] == '订单号') $main_order_no = trim($val['value']);
                if ($val['name'] == '退款订单号') $refund_order_no = trim($val['value']);
            }
        }
        if (empty($main_order_no)) {
            $this->dingSendText($params['operation_records'][0]['userid'], '未获取到订单号,审批流处理失败!');
            $this->throwError('未获取到订单号,审批流处理失败!');
        }
        $orderMain      = Db::name('order_main')
            ->field('id,uid,recharge_balance,cash_amount,bonus_balance,main_order_no,main_order_status,payment_method,payment_subject,payment_amount,order_type')
            ->where(['main_order_no' => $main_order_no])
            ->find();
        $order_type     = config('config')['order_type'];//订单频道获取
        $field          = 'so.*,om.consignee,om.consignee_phone,om.province_id,om.city_id,om.district_id,om.address';
        $order_type_arr = explode(',', $orderMain['order_type']);
        $orderSub       = [];
        foreach ($order_type_arr as &$val) {
            $order    = Db::name($order_type[intval($val)]['table'])
                ->alias('so')
                ->field($field)
                ->leftJoin('order_main om', 'om.id=so.main_order_id')
                ->where(['main_order_id' => $orderMain['id']])
                ->select()->toArray();
            $orderSub = array_merge($orderSub, $order);
        }
        if (empty($orderMain) || empty($orderSub)) {
            $this->dingSendText($params['operation_records'][0]['userid'], '未获取到订单号,审批流处理失败!');
            $this->throwError('未获取到订单信息,审批流处理失败!');
        }
        $orderService = new OrderService();
        if ($params['result'] == 'agree') {
            Db::startTrans();
            try {
                //修改订单状态
                $balance_logs = [];
                foreach ($orderSub as &$v) {
                    if (($v['recharge_balance'] > 0) || ($v['bonus_balance'] > 0)) {
                        // Record refund log
                        $balance_logs[] = [
                            'uid' => $v['uid'],
                            'main_order_no' => $orderMain['main_order_no'],
                            'sub_order_no' => $v['sub_order_no'],
                            'recharge_balance' => floatval(bcsub($v['recharge_balance'], $v['refund_recharge_balance'], 2)),
                            'bonus_balance' => floatval(bcsub($v['bonus_balance'], $v['refund_bonus_balance'], 2)),
                            'created_time' => time(),
                            'remark' => '超时取消订单审批退还余额 ',
                            'updated_time' => time(),
                            'type' => 1,
                            'status' => 0,
                            'request' => json_encode([
                                'uid' => intval($orderMain['uid']),
                                'type' => 1, // 增加
                                'related_no' => $orderMain['main_order_no'],
                                'related_type' => 2, // 商品订单
                                'operation_type' => 5, // 商品购买
                                'recharge_balance' => floatval(bcsub($v['recharge_balance'], $v['refund_recharge_balance'], 2)),
                                'bonus_balance' => floatval(bcsub($v['bonus_balance'], $v['refund_bonus_balance'], 2)),
                                'change_time' => time(),
                                'change_name' => '系统',
                                'unique_code' => "REF" . $v['sub_order_no'],
                            ]),
                        ];
                    }

                    $editOrder = Db::name($order_type[intval($v['order_type'])]['table'])->where(['id' => $v['id']])->update([
                        'refund_recharge_balance' => Db::raw('recharge_balance'),
                        'refund_bonus_balance' => Db::raw('bonus_balance'),
                        'sub_order_status' => 4,
                        'refund_status' => 2,
                        'update_time' => time()
                    ]);
                    if (empty($editOrder)) $this->throwError('修改订单状态失败');
                    $remark = array(
                        'sub_order_no' => $v['sub_order_no'],
                        'order_type'   => $v['order_type'],
                        'content'      => '订单超时自动退款成功，退款订单号：' . $refund_order_no,
                        'admin_id'     => 0
                    );
                    $orderService->createRemarks($remark);
                }
//                Db::name('balance_pay')->insertAll($balance_logs); // 超时订单取消预退款,无需再次退
                //修改退款订单状态
                $updateRefund = Db::name('refund_order')->where(['refund_order_no' => $refund_order_no, 'main_order_no' => $main_order_no])->update(['refund_status' => 1, 'update_time' => time()]);
                if (empty($updateRefund)) $this->throwError('修改退款订单失败');
                switch ($orderMain['payment_subject']) {
                    case 1:
                    case 2:
                        //发起银联退款
                        $pushData = array(
                            'main_order_no' => $orderMain['payment_method'] . $main_order_no,//支付方式+主订单号为银联交易订单号
                            'payment_method' => $orderMain['payment_method'],
                            'refund_amount' => $orderMain['cash_amount'],
                            'refund_order_no' => $refund_order_no,
                            'subject' => $orderMain['payment_subject'],
                            'is_cross' => $orderMain['order_type'] == 2 ? 1 : 0,
                            'order_type' => -1,//子订单全退给-1
                        );
                        $orderRefund = $this->httpPost(env('ITEM.PAYMENT_URL') . '/payment/v3/ums/refund', $pushData);
                        if (!isset($orderRefund['error_code']) || $orderRefund['error_code'] != 0) $this->throwError('发起退款失败：' . $orderRefund['error_msg']);
                        break;
                    case 4:
                        //支付宝微信退款
                        $method = 'alipay';
                        if (in_array($orderMain['payment_method'], [3, 4, 5, 7, 8, 9])) $method = 'wechat';
                        $pushData = array(
                            'main_order_no' => $main_order_no,
                            'refund_order_no' => $refund_order_no,
                            'payment_method' => $orderMain['payment_method'],
                            'method' => $method,
                            'payment_amount' => $orderMain['cash_amount'],
                            'refund_amount' => $orderMain['cash_amount'],
                            'refund_desc' => '超时订单退款'
                        );
                        $orderRefund = $this->httpPost(env('ITEM.PAYMENT_URL') . '/payment/v3/weAndAli/refund', $pushData);
                        if (!isset($orderRefund['error_code']) || $orderRefund['error_code'] != 0) $this->throwError('发起退款失败：' . $orderRefund['error_msg']);
                        if ($orderRefund['data']['code'] != '10000') $this->throwError('发起退款失败：' . $orderRefund['error_msg']);
                        break;
                }
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                $msg = '订单超时自动退款失败:' . $e->getMessage();
                //添加订单备注
                foreach ($orderSub as &$v) {
                    $remark = array(
                        'sub_order_no' => $v['sub_order_no'],
                        'order_type'   => $v['order_type'],
                        'content'      => $msg,
                        'admin_id'     => 0
                    );
                    $orderService->createRemarks($remark);
                }
                $this->throwError($msg, 1);
            }
        }
        if ($params['result'] == 'refuse' || $params['status'] == 'TERMINATED') {
            $msg = '订单超时自动退款失败:退款审批异常，请核实重新审批';
            if ($params['result'] == 'refuse') $msg = '订单超时自动退款失败:退款审批被拒，请核实重新审批';
            if ($params['status'] == 'TERMINATED') $msg = '订单超时自动退款失败:退款审批已撤销，请核实重新审批';
            //添加订单备注
            foreach ($orderSub as &$v) {
                $remark = array(
                    'sub_order_no' => $v['sub_order_no'],
                    'order_type'   => $v['order_type'],
                    'content'      => $msg,
                    'admin_id'     => 0
                );
                $orderService->createRemarks($remark);
            }
        }
        return true;
    }
}