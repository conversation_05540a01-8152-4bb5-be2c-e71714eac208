APP_DEBUG = true
# oss域名
ALIURL = https://images.wineyun.com

[ITEM]
#萌牙分发
DISTRIBUTE_WMS = 'http://*************:30661'
#门店
STORE_URL='http://*************:30711'
#优惠券模块
COUPON_URL = 'http://tp6-coupon'
#营销模块
MARKET_URL = 'http://tp6-marketing'
#产品模块
COMMODITIES_URL = 'http://tp6-commodities'
#加密模块
CRYPTION_ADDRESS = 'http://vinehoo-des-server-go'
# 用户模块
USER_URL = 'http://tp6-user'
#订单模块
ORDERS_URL = 'http://tp6-orders'
#活动模块
ACTIVITIES_URL = 'http://tp6-activities'
#队列模块
QUEUE_URL = 'http://node-queueservice/services/v3/queue/push'
#秒级计划任务模块
SLS_URL= 'http://node-second-level-scheduler'
#钉钉审批
DINGTALK_APPROVAL_URL = 'http://tp6-dingtalk-approval'
#社区模块
COMMUNITY_URL = 'http://tp6-community'
#直播websocket
LIVE_ROBOT_URL = "http://node-live-websocket-server:10302"
LIVE_ROOM_URL = "http://node-live-websocket-server:10303"


#短视频
[SHORTVIDEO]
#分享地址
SHARE_URL = 'https://h5.wine-talk.cn/web-static/details/videoDetails.html?id='
#阿里云视频点播配置
VIDEO_ACCESSKEYID = 'LTAI4GCZW2qdgVhkpsnfWw54'
VIDEO_ACCESSKEYSECRET = '******************************'

#日志
[LOGISTICS]
TIMEOUT = 10

#后台日志
[ADMINLOG]
VINEHOO_CLIENT = admin-log


[COMMUNITY]
posts_token = 4978b602251958b063ebb7fad20f6f47c9b988f6017fcee42c2104203de67ad7
battle_token = 75d86830454edf7649c81ebd7f95f9392dc48e51e98647884a9746f81448b459

#app推送
[APP-PUSH]
TIMEOUT = 10
IS_PRODUCT = 0

#订单
[ORDERS]
pay_time_out = '300'
#订单号前缀
ORDER_MAIN=VHM
ORDER_SON=VHS
WINEPARTY=VHP
RABBIT=VHR
COURCE=VHC
#基准快递费收取标准：小于99元收取9元快递费
MIN_MONEY=99
COURIER_FEE=9
#银联验签key
key = rRfKYjr7AFiykDnfBmeitWnTa6t2iQb74MBKpcH4eMGnnce3

#优惠券
[COUPON]
APPROVAL_PROCESS_CODE = 'PROC-4153BC23-35C8-40CC-9CF6-DA7A34B55A80'
HTTP_REQUEST_VINEHOO_CLIENT = 'coupon'
HTTP_REQUEST_VINEHOO_CLIENT_VERSION = 'v3'

#钉钉审批
[APPROVAL]
CORP_ID = ding8ea462d639c047e6ffe93478753d9884
APP_KEY = dinglvbcw4cgiagxlily
APP_SECRET = tFSFhIq-UxiWYeq2QGgYTEW-lBZRJ_0MwmSCQ9GFZ1xggG1osmddDIC9E3XVAwJk
TOKEN = 44Ru9qI3wjXk
AES_KEY = K4LlA0RsItSvqc2MhLKeW6IT4ArJPu7eOumSLvVivkl
TEST_URL = 123.com
HTTP_REQUEST_VINEHOO_CLIENT = 'approval'

#支付
[PAYMENT]
common_url = https://qr.chinaums.com/netpay-route-server/api/
official_h5_url = https://qr.chinaums.com/netpay-portal/webpay/pay.do
key = rRfKYjr7AFiykDnfBmeitWnTa6t2iQb74MBKpcH4eMGnnce3
msgSrc = WWW.CQYJBN.COM
msgSrcId = 12F2
signType = SHA256
subAppId = wxfc6864f04e3d19d3
mini_mid = 89832015921G110
mini_tid = A0460480
app_mid = 89832015921G113
app_tid = A0460483
pc_mid = 89832015921G112
pc_tid = A0460482
h5_mid = 89832015921G111
h5_tid = A0460481
appid = wxfc6864f04e3d19d3
secret = d023d48f25d5e0736e0395c3c87c7e10

#钉钉推送
[SENDER]
HTTP_REQUEST_VINEHOO_CLIENT = dingtalk-sender
CORP_ID = ding8ea462d639c047e6ffe93478753d9884
APP_KEY = dinglvbcw4cgiagxlily
APP_SECRET = tFSFhIq-UxiWYeq2QGgYTEW-lBZRJ_0MwmSCQ9GFZ1xggG1osmddDIC9E3XVAwJk
TOKEN = 44Ru9qI3wjXk
AES_KEY = K4LlA0RsItSvqc2MhLKeW6IT4ArJPu7eOumSLvVivkl
TEST_URL = 123.com


#直播
[LIVE]
accesskey_user = <EMAIL>
accesskey_id  = LTAI4FzbtXvSksW9sezPekyG
accesskey_secret = ******************************
domain_push = livepush.wineyun.com
domain_name = live.wineyun.com
source_url = http://wineyun.com/
app_name = test3
#app_name = live-wineyun
static_url = http://52.83.60.235:10381

#发票
[INVOICE]
appId = 13fb0cf43135a4940c233fe16de1c05cb0eca6e0bc85819be0c78a7850d314ff
contentPassword = 56B27A5C3D404069
XSF_NSRSBH = 91500240MA5UA0LK57
XSF_MC = 重庆云酒佰酿电子商务有限公司
XSF_YHZH = 民生银行重庆江北支行155175348
XSF_DZDH = '重庆市石柱土家族自治县南宾镇城南居委白岩组(工业孵化楼509-41) 023-63360736'
client_id = 160810214421
client_secret = e0eacd983971634327ae1819ea8b6214
base_amount = 11300
alcohol_rate = 0.13
farmer_rate = 0.09
cocktail_rate = 0.06

#酒会
[WINEPARTY]
GAODE_KEY = '0c09e15cb33d08fe1b4d808090ad5e9c'
#银联验签key
key = rRfKYjr7AFiykDnfBmeitWnTa6t2iQb74MBKpcH4eMGnnce3
WINEPARTY=VHP
OWN = 5
WINE = 12


#权限
[AUTHORITY]
ADMIN_KEY = d7e958de4a6071ba
DD_APPID  = dinglvbcw4cgiagxlily
DD_SECRET = tFSFhIq-UxiWYeq2QGgYTEW-lBZRJ_0MwmSCQ9GFZ1xggG1osmddDIC9E3XVAwJk

[APP]
DEFAULT_TIMEZONE = Asia/Shanghai

[DATABASE_AUTHORITY]
#权限
TYPE = mysql
HOSTNAME = ************
DATABASE = vh_authority
USERNAME = vinehoodev
PASSWORD = vinehoo123
HOSTPORT = 3306
CHARSET = utf8mb4
DEBUG = true
PREFIX = vh_


[DATABASE_COMMUNTIY]
#社区
TYPE = mysql
HOSTNAME = ************
DATABASE = vh_community
USERNAME = vinehoodev
PASSWORD = vinehoo123
HOSTPORT = 3306
CHARSET = utf8
PREFIX = vh_
DEBUG = true
[DATABASE_INVOICE]
#发票
TYPE = mysql
HOSTNAME = ************
DATABASE = vh_invoice
USERNAME = vinehoodev
PASSWORD = vinehoo123
HOSTPORT = 3306
CHARSET = utf8
PREFIX = vh_
DEBUG = flase

[DATABASE_APPPUSH]
#推送
TYPE = mysql
HOSTNAME = ************
DATABASE = vh_app_push
USERNAME = vinehoodev
#USERNAME =
PASSWORD = vinehoo123
#PASSWORD =
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = false



[DATABASE_SHORTVIDEO]
#短视频
TYPE = mysql
HOSTNAME = ************
DATABASE = vh_short_video
USERNAME = vinehoodev
PASSWORD = vinehoo123
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = true

[DATABASE_DINGTALK]
#钉钉
TYPE = mysql
driver = mysql
HOSTNAME = ************
DATABASE = vh_dingtalk
USERNAME = vinehoodev
PASSWORD = vinehoo123
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = false

[DATABASE_COUPON]
#优惠券
TYPE = mysql
HOSTNAME = ************
DATABASE = vh_coupon
USERNAME = vinehoodev
PASSWORD = vinehoo123
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = false

[DATABASE_FULLGIFT]
#满赠
TYPE = mysql
HOSTNAME = ************
DATABASE = vh_full_gift
USERNAME = vinehoodev
PASSWORD = vinehoo123
HOSTPORT = 3306
CHARSET = utf8mb4
DEBUG = true
PREFIX = vh_

[DATABASE_COMMODITIES]
#商品
TYPE = mysql
HOSTNAME = ************
DATABASE = vh_commodities
USERNAME =  vinehoodev
PASSWORD = vinehoo123
HOSTPORT = 3306
CHARSET = utf8
PREFIX = vh_
DEBUG = true

[DATABASE_MARKETING]
#营销
TYPE = mysql
HOSTNAME = ************
DATABASE = vh_marketing
USERNAME =  vinehoodev
PASSWORD = vinehoo123
HOSTPORT = 3306
CHARSET = utf8
PREFIX = vh_
DEBUG = false

[DATABASE_USER]
#用户
TYPE = mysql
HOSTNAME = ************
DATABASE = vh_user
USERNAME = vinehoodev
PASSWORD = vinehoo123
HOSTPORT = 3306
CHARSET = utf8mb4
DEBUG = false
PREFIX = vh_

[DATABASE_WIKI]
#磐石
TYPE = mysql
HOSTNAME = ************
DATABASE = vh_wiki
USERNAME = root
PASSWORD = 123456
HOSTPORT = 3306
CHARSET = utf8
DEBUG = true

[DATABASE_WINEPARTY]
#酒会
TYPE = mysql
HOSTNAME = ************
DATABASE = vh_wineparty
USERNAME = vinehoodev
PASSWORD = vinehoo123
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = true

[DATABASE_ORDERS]
#订单
TYPE = mysql
HOSTNAME = ************
DATABASE = vh_orders
USERNAME = vinehoodev
PASSWORD = vinehoo123
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = false

[DATABASE_NEWS]
#酒闻
TYPE = mysql
HOSTNAME = ************
DATABASE = vh_news
USERNAME = vinehoodev
PASSWORD = vinehoo123
HOSTPORT = 3306
CHARSET = utf8mb4
DEBUG = false


[DATABASE_SMS]
#短信群发
TYPE = mysql
HOSTNAME = ************
DATABASE = vh_sms
USERNAME = vinehoodev
PASSWORD = vinehoo123
HOSTPORT = 3306
CHARSET = utf8mb4
DEBUG = false


[DATABASE_LOGISTICS]
#日志
TYPE = mysql
HOSTNAME = ************
DATABASE = vh_logistics
USERNAME = vinehoodev
PASSWORD = vinehoo123
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = false

[DATABASE_LIVE]
#直播
TYPE = mysql
HOSTNAME = ************
DATABASE = vh_live
USERNAME = vinehoodev
PASSWORD = vinehoo123
HOSTPORT = 3306
CHARSET = utf8
PREFIX = vh_
DEBUG = false

[NACOS]
URL = 'http://************:8848/nacos/v1/cs/configs'
TENANT = 'd9fd09cf-6569-4a2d-a623-5df9999dd91a'
USERNAME = 'nacos'
PASSWORD = 'vinehoo666'

[ES]
HOST = ************
PORT = 9200
USER = elastic
PASS = vinehoo666
PREFIX = 'vinehoo.'
SCHEME = 'http'


[CACHE]
DRIVER = redis
HOST = ************
PASSWORD = vh@123
PORT = 6379
prefix = vh.
db = 1

[NEO4J]
HOST = ************
PORT = 7687
USER = neo4j
PASS = vinehoo666

[RABBIMQ]
IP = ************
PORT = 5672
NAME = admin
PSWORD = vinehoo666
VHOST = /

[OSS]
ACCESSKEYID = LTAI5t88V4fhDjxmuuvmmV5r
ACCESSKEYSECRET = ******************************
ENDPOINT = http://vinehoo.oss-cn-zhangjiakou-internal.aliyuncs.com
BUCKET = vinehoo-test
ALIURL = https://images.wineyun.com

[LANG]
default_lang = zh-cn