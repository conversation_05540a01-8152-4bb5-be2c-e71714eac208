<?php

use PHPMailer\PHPMailer\PHPMailer;

use think\Exception;
use think\facade\Log;

class Mailer
{
    static public function send($param)
    {
//        $send_param = [
//            'to'         => [
//                [
//                    'address' => '<EMAIL>',
//                    'name'    => '跨境采购组'
//                ],
//            ],
//            'attachment' => [
//                $file_path,
//            ],
//            'title'      => $title,
//            'content'    => $title
//        ];
        try {
            $mail = new PHPMailer(true);
            //服务器配置
            $mail->CharSet   = "UTF-8";                     //设定邮件编码
            $mail->SMTPDebug = 0;                        // 调试模式输出
            $mail->isSMTP();                             // 使用SMTP
            $mail->Host       = env('MAILER_CONFIG.host');                // SMTP服务器smtp.qq.com.mail.qq.com
            $mail->SMTPAuth   = true;                      // 允许 SMTP 认证
            $mail->Username   = env('MAILER_CONFIG.email');                // SMTP 用户名  即邮箱的用户名
            $mail->Password   = env('MAILER_CONFIG.code');             // SMTP 密码  部分邮箱是授权码(例如163邮箱)tbmihxvytxhljbjj.iazyxitwboxybadh
            $mail->SMTPSecure = 'ssl';                    // 允许 TLS 或者ssl协议
            $mail->Port       = 465;                            // 服务器端口 25 或者465 具体要看邮箱服务器支持
            $mail->setFrom(env('MAILER_CONFIG.email'), env('MAILER_CONFIG.email_name'));  //发件人
            $mail->addReplyTo(env('MAILER_CONFIG.email'), 'all'); //回复的时候回复给哪个邮箱 建议和发件人一致

            foreach ($param['to'] as $to) {
                $mail->addAddress($to['address'], $to['name']);  // 收件人
            }

            if (!empty($param['attachment'])) {
                foreach ($param['attachment'] as $attachment) {
                    $mail->addAttachment($attachment, basename($attachment));  // 添加附件
                }
            }

            $mail->isHTML(true); // 是否以HTML文档格式发送  发送后客户端可直接显示对应HTML内容
            $mail->Subject = $param['title'];
            $mail->Body    = $param['content'];
            $mail->AltBody = $param['content'];
            $mail->send();
        } catch (\Exception $e) {
            $msg = '邮件发送失败：' . $e->getMessage() . ' line: '.$e->getLine();
            log::write($msg);
            return false;
        }
        return true;
    }


}