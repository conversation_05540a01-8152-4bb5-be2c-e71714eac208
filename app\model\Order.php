<?php


namespace app\model;


use app\service\elasticsearch\ElasticSearchService;
use app\service\es\Es;
use think\facade\Db;
use think\Model;

class Order extends Model
{
    protected $name = 'order_main';

    protected function getTableName()
    {
        return Db::name($this->name);
    }

    /**
     * Description:全部订单列表
     * Author: zrc
     * Date: 2021/9/6
     * Time: 10:22
     * @param $requestparams
     */
    public function getOrderList($requestparams, $page = 1, $limit = 10)
    {
        $params       = $requestparams;
        $where        = [];
        $range        = [];
        $terms        = [];
        $match_phrase = [];
        $es           = new ElasticSearchService();
        if (!empty($params['order_no'])) {
            if (strpos($params['order_no'], 'VHM') !== false || strpos($params['order_no'], 'WYM') !== false) {
                $where[] = ['main_order_no' => $params['order_no']];
            } else {
                //客户订单搜索耽误客服处理速度（技术部工单）
                //$where[] = ['sub_order_no.keyword' => $params['order_no']];
                //还原
                //$match_phrase[] = ['sub_order_no' => $params['order_no']];

                //订单号的长度来判断 如果是完整的使用精准查询 其他使用匹配模式
                if(strlen($params['order_no']) == 21){
                    //精准查询
                    $where[] = ['sub_order_no.keyword' => $params['order_no']];
                }else{
                    //匹配查询
                    $match_phrase[] = ['sub_order_no' => $params['order_no']];
                }
            }
        }
        if (isset($params['order_type']) && is_numeric($params['order_type'])) {
            $where[] = ['order_type' => $params['order_type']];
            //跨境订单支付单推送状态条件
            if ($params['order_type'] == 2 && isset($params['payment_doc']) && is_numeric($params['payment_doc'])) {
                $where[] = ['payment_doc' => $params['payment_doc']];
            }
        }
        if (!empty($params['period'])) {
            $where[] = ['period' => $params['period']];
        }
        if (isset($params['order_status']) && (strlen($params['order_status']) > 0)) {
            $p_order_status = explode(',', $params['order_status']);
            if (in_array(10,$p_order_status)) {
                $p_order_status = array_diff($p_order_status,[10]);
                unset($params['order_status']);
                $params['lock_status'] = 1;
            }
            if(!empty($p_order_status)){
                $terms[] = ['sub_order_status' => array_values($p_order_status)];
            }
        }
        if (isset($params['special_type']) && is_numeric($params['special_type'])) {
            if ($params['special_type'] == 34) {// 代 发
                $terms[] = ['warehouse_code' => GetDfWarehouseCode()];
            } else {
                $where[] = ['special_type' => $params['special_type']];
            }
        }
        if (isset($params['refund_status']) && is_numeric($params['refund_status'])) {
            $where[] = ['refund_status' => $params['refund_status']];
        }
        if (!empty($params['package_id'])) {
            $where[] = ['package_id' => $params['package_id']];
        }
        if (!empty($params['title'])) {
            $match_phrase[] = ['title' => $params['title']];
        }
        if (!empty($params['uid'])) {
            $where[] = ['uid' => $params['uid']];
        }
        if (!empty($params['nickname'])) {
            $match_phrase[] = ['nickname' => $params['nickname']];
        }
        if (!empty($params['consignee'])) {
            //收件人加密查询
            $encrypt             = cryptionDeal(1, [$params['consignee']], '15736175219', '宗仁川');
            $params['consignee'] = isset($encrypt[$params['consignee']]) ? $encrypt[$params['consignee']] : '';
            $where[]             = ['consignee' => $params['consignee']];
        }
        if (!empty($params['consignee_phone'])) {
            //收件人加密查询
            $encrypt                   = cryptionDeal(1, [$params['consignee_phone']], '15736175219', '宗仁川');
            $params['consignee_phone'] = isset($encrypt[$params['consignee_phone']]) ? $encrypt[$params['consignee_phone']] : '';
            $where[]                   = ['consignee_phone' => $params['consignee_phone']];
        }
        if (isset($params['express_type']) && is_numeric($params['express_type'])) {
            $where[] = ['express_type' => $params['express_type']];
        }
        if (!empty($params['express_number'])) {
            $where[] = ['express_number' => $params['express_number']];
        }
        if (isset($params['order_from']) && is_numeric($params['order_from'])) {
            $where[] = ['order_from' => $params['order_from']];
        }
        if (isset($params['warehouse_code']) && strlen($params['warehouse_code']) > 0) {
            $where[] = ['warehouse_code' => $params['warehouse_code']];
        }
        if (isset($params['payment_method']) && is_numeric($params['payment_method'])) {
            $where[] = ['payment_method' => $params['payment_method']];
        }
        if (isset($params['push_wms_status']) && is_numeric($params['push_wms_status'])) {
            $where[] = ['push_wms_status' => $params['push_wms_status']];
        }
        if (isset($params['push_t_status']) && is_numeric($params['push_t_status'])) {
            $where[] = ['push_t_status' => $params['push_t_status']];
        }
        if (isset($params['province_id']) && is_numeric($params['province_id'])) {
            $where[] = ['province_id' => $params['province_id']];
        }
        if (isset($params['city_id']) && is_numeric($params['city_id'])) {
            $where[] = ['city_id' => $params['city_id']];
        }
        if (isset($params['is_ts']) && is_numeric($params['is_ts'])) {
            $where[] = ['is_ts' => $params['is_ts']];
        }
        if (isset($params['invoice_progress']) && is_numeric($params['invoice_progress'])) {
            $where[] = ['invoice_progress' => $params['invoice_progress']];
        }
        if (!empty($params['stime'])) {
            $range[] = ['created_time' => ['gte' => $params['stime']]];
        }
        if (!empty($params['etime'])) {
            $range[] = ['created_time' => ['lte' => $params['etime']]];
        }
        if (!empty($params['s_delivery_time'])) {
            $range[] = ['predict_time' => ['gte' => $params['s_delivery_time']]];
        }
        if (!empty($params['e_delivery_time'])) {
            $range[] = ['predict_time' => ['lte' => $params['e_delivery_time']]];
        }
        if (!empty($params['begin_payment_amount'])) {
            $range[] = ['payment_amount' => ['gte' => $params['begin_payment_amount']]];
        }
        if (!empty($params['end_payment_amount'])) {
            $range[] = ['payment_amount' => ['lte' => $params['end_payment_amount']]];
        }
        if (isset($params['is_after_sale']) && is_numeric($params['is_after_sale'])) {
            $where[] = ['is_after_sale' => $params['is_after_sale']];
        }
        if (!empty($params['merchant_id'])) {
            $where[] = ['merchant_id' => $params['merchant_id']];
            if (!isset($params['is_after_sale'])) $terms[] = ['work_order_status' => [0, 2]];
        }
        if (!empty($params['delivery_store_id'])) {
            $where[] = ['delivery_store_id' => $params['delivery_store_id']];
        }
        if (isset($params['product_channel']) && is_numeric($params['product_channel'])) {
            $where[] = ['product_channel' => $params['product_channel']];
        }
        if (isset($params['delivery_method']) && is_numeric($params['delivery_method'])) {
            $where[] = ['delivery_method' => $params['delivery_method']];
        }
        if (isset($params['is_work_order']) && $params['is_work_order'] == 1) {
            $terms[] = ['order_type' => [0, 1, 2, 3, 9, 11]];
        } else {
            $terms[] = ['order_type' => [0, 1, 2, 3, 9]];
        }
        if (isset($params['lock_status']) && $params['lock_status'] == 1) {
            $terms[]         = ['main_order_id' => CrossLockOrder::where('status',1)->column('order_main_id')];
        }
        if (!empty($params['short_code'])) {
            $periodsData = $es->getDocumentList(['index' => ['periods'], 'match' => [['short_code' => trim($params['short_code'])]], 'source' => ['id'], 'limit' => 10000]);
            if (count($periodsData['data']) > 0) {
                $periodsArr = array_column($periodsData['data'], 'id');
                $terms[] = ['period' => $periodsArr];
            } else {
                $result['list']  = [];
                $result['total'] = 0;
                return $result;
            }
        }

        // 管理员权限验证
        if (!empty($params['admin_id'])) {
            $admins_roles = Db::table('vh_authority.vh_admins_roles')
                ->alias('ar')
                ->leftJoin('vh_authority.vh_roles r', 'ar.role_id = r.id')
                ->where('ar.admin_id', $params['admin_id'])
                ->column('r.id,r.company_codes,r.warehouse_codes');
            $role_ids = array_column($admins_roles, 'id');
            if (!in_array(1, $role_ids)) {// 非超级管理员
                $company_codes = $warehouse_codes = [];
                foreach ($admins_roles as $v) {
                    $s_company_codes = json_decode($v['company_codes'] ?? '', true);
                    $company_codes = array_merge($company_codes, $s_company_codes ?? []);
                    $s_warehouse_codes = json_decode($v['warehouse_codes'] ?? '', true);
                    $warehouse_codes = array_merge($warehouse_codes, $s_warehouse_codes ?? []);
                }
                if (!empty($company_codes)) {// 公司权限
                    $company_codes = array_values(array_unique($company_codes));
                    $payment_subject = Db::name('collecting_company')
                        ->whereIn('corp_new', $company_codes)
                        ->column('id');
                    $terms[] = ['payment_subject' => $payment_subject];
                }
                if (!empty($warehouse_codes)) {// 仓库
                    $terms[] = ['warehouse_code' => array_values(array_unique($warehouse_codes))];
                }
            }
        }

        $order = [['created_time' => 'desc']];
        $arr   = array(
            'index'        => ['orders'],
            'match'        => $where,
            'match_phrase' => $match_phrase,
            'range'        => $range,
            'terms'        => $terms,
            'page'         => $page,
            'limit'        => $limit,
            'sort'         => $order
        );
        $data  = $es->getDocumentList($arr);
        if (count($data['data']) > 0) {
            // 查询代发仓库code
            $df_warehouse_code = GetDfWarehouseCode();
            $lock_orders = CrossLockOrder::where('main_order_no', 'in', array_column($data['data'], 'main_order_no'))
                ->where('status', 1)
                ->column('remark,main_order_no');

            $cross_split = Db::name('cross_split')
                ->where('origin_main_order_no', 'in', Db::name('cross_split')
                    ->where('main_order_no', 'in', array_column($data['data'], 'main_order_no'))
                    ->column('origin_main_order_no'))
                ->column('origin_main_order_no,main_order_no');

            $cssbs = Db::name('cross_order')->alias('o')
                ->field('om.main_order_no,o.sub_order_no')
                ->leftJoin('order_main om', 'om.id=o.main_order_id')
                ->where(['om.main_order_no' => array_column($cross_split,'main_order_no')])
                ->select()->toArray();
            $cssbs = array_column($cssbs,'sub_order_no', 'main_order_no');

            $cross_split_g = [];
            foreach ($cross_split as $scpg) {
                $cross_split_g[$scpg['origin_main_order_no']][] = $scpg['main_order_no'];
            }
            $cross_split_gg = [];
            foreach ($cross_split_g as $scpgg) {
                foreach ($scpgg as $scpggg) {
                    $cross_split_gg[$scpggg] = $scpgg;
                }
            }



            $lock_orders = array_column($lock_orders, null, 'main_order_no');
            $pay_main_orders = Db::name('cross_split')->where('main_order_no', 'in', array_column($data['data'], 'main_order_no'))
                ->column('origin_main_order_no','main_order_no');
            $pay_main_orders = array_column($pay_main_orders, null, 'main_order_no');

            $main_order_nos = array_values(array_unique(array_column($data['data'], 'main_order_no')));
            $transfers      = Db::name('transfer_log')->where('main_order_no', 'in', $main_order_nos)->column('main_order_no,attachment,status', 'main_order_no');
            foreach ($transfers as &$transfer) {
                $transfer['attachment'] = image_full_path($transfer['attachment']);
            }

            $sub_orders = array_column($data['data'], 'sub_order_no');
            $ts_info = Db::name('order_ts')
                ->where('sub_order_no','in', $sub_orders)
                ->where('status','<>', 3)
                ->column('sub_order_no,ts_time,remarks,status','sub_order_no');

            $main_order_nos = array_values(array_unique(array_column($data['data'], 'main_order_no')));
            $transfers      = Db::name('transfer_log')->where('main_order_no', 'in', $main_order_nos)->column('main_order_no,attachment,status', 'main_order_no');
            foreach ($transfers as &$transfer) {
                $transfer['attachment'] = image_full_path($transfer['attachment']);
            }

            foreach ($data['data'] as &$val) {
                $val['ts_time'] = '';
                if ($val['is_ts'] ?? 0) {
                    $val['ts_time'] = empty($ts_info[$val['sub_order_no']]['ts_time']) ? '' : date('Y-m-d', $ts_info[$val['sub_order_no']]['ts_time']);
                }
                $val['transfer_status']     = $transfers[$val['main_order_no']]['status'] ?? 0;
                $val['transfer_attachment'] = $transfers[$val['main_order_no']]['attachment'] ?? '';

                $val['lock_status'] = 0;
                $val['lock_remark'] = '';
                if (isset($lock_orders[$val['main_order_no']])) {
                    $val['lock_status'] = 1;
                    $val['lock_remark'] = $lock_orders[$val['main_order_no']]['remark'];
                }

                $combination = $cross_split_gg[$val['main_order_no']] ?? [];
                foreach ($combination as &$com1) {
                    $com1 = $cssbs[$com1] ?? $com1;
                }
                $val['combination'] = $combination;

                // 是否代发
                $val['is_supplier_delivery'] = 0;
                if (!empty($val['warehouse_code']) && in_array($val['warehouse_code'], $df_warehouse_code)) {
                    $val['is_supplier_delivery'] = 1;
                }

                $val['pay_main_order_no'] = $pay_main_orders[$val['main_order_no']] ?? $val['main_order_no'];

                //售后状态：1：不允许售后 2：售后进行中 3：可申请售后
                $val['after_sale_status'] = 1;
                if (in_array($val['order_type'], [0, 1, 2, 3, 9])) {
                    if (($val['sub_order_status'] == 1 || $val['sub_order_status'] == 2 || $val['sub_order_status'] == 3 || ($val['sub_order_status'] == 4 && strtotime($val['payment_time']) > 0)) && (strpos($val['sub_order_no'], 'VHS') !== false) || strpos($val['sub_order_no'], 'WYS') !== false || $val['period'] == 71502) {
//                        if (($val['order_type'] == 2) && (($val['payment_doc'] ?? 0) == 1)) {
//                            $forbidden_reason = '您的订单已向海关申报，由保税区安排发货中，详询客服！';//不允许售后原因
//                        } elseif(Db::name('cross_split')->where('main_order_no', ($val['main_order_no'] ?? ''))->count()) {
//                            $forbidden_reason = '组合套餐售后请联系客服！';//不允许售后原因
//                        } else {
                            $val['after_sale_status'] = 3;
//                        }
                    }
                }
                if (isset($val['work_order_status']) && $val['work_order_status'] == 1) {
                    $val['after_sale_status'] = 2;
                }
                //总售价处理
                if (empty($val['package_price'])) $val['package_price'] = 0;
                $val['total_selling_price'] = isset($val['special_price']) && $val['special_price'] > 0 ? round($val['special_price'] * $val['order_qty'], 2) : round($val['package_price'] * $val['order_qty'], 2);
                if ($val['order_type'] == 9) {
                    //快递名称处理
                    $express_name = '';
                    switch ($val['express_type']) {
                        case 2:
                            $express_name = '顺丰快递';
                            break;
                        case 4:
                        case 5:
                            $express_name = '京东快递';
                            break;
                        case 10:
                            $express_name = '京东物流';
                            break;
                        case 65:
                            $express_name = $val['express_name'];
                            break;
                    }
                    $val['express_name'] = $express_name;
                }
                //图片处理
                $val['banner_img'] = imagePrefix($val['banner_img']);
                //订单取消时间
                $val['cancel_time'] = date('Y-m-d H:i:s', strtotime($val['created_time']) + env('ORDERS.pay_time_out'));
            }
        }
        $totalNum        = $data['total']['value'];
        $result['list']  = $data['data'];
        $result['total'] = $totalNum;
        return $result;
    }

    /**
     * Description:获取15秒未支付未取消订单
     * Author: zrc
     * Date: 2021/8/9
     * Time: 12:26
     * @param $params
     */
    public function getUnpaidOrder()
    {
        $time   = time() - 15;
        $fields = "main_order_no,payment_method";
        $where  = "main_order_status = 0 and created_time <= {$time}";
        $data   = $this->getTableName()->field($fields)->where($where)->select()->toArray();
        return $data;
    }

    /**
     * Description:个人中心订单列表
     * Author: zrc
     * Date: 2021/12/2
     * Time: 16:20
     * @param $requestparams
     * @param int $page
     * @param int $limit
     * @return mixed
     * type 列表类型:0-全部 1-待支付 2-待拼团 3-待发货 4-待收货 5-已完成 6-售后
     */
    public function personalList($requestparams, $page = 1, $limit = 10)
    {
        $params = $requestparams;
        $result = [];
        $es     = new ElasticSearchService();
        //关键词搜索走es
        if (!empty($params['keyword'])) {
            $where[] = ['term' => ['uid' => $params['uid']]];
            $where[] = ['term' => ['is_delete' => 0]];
            if ($params['is_show_auction'] == 0) {
                $where[] = ['terms' => ['order_type' => [0, 1, 2, 3, 4, 9]]];
            }
            $where[] = ['bool' => ['should' => [['match_phrase' => ['sub_order_no' => $params['keyword']]], ['match_phrase' => ['main_order_no' => $params['keyword']]], ['match_phrase' => ['title' => $params['keyword']]]]]];
            $data    = esGetList('vinehoo.orders', $where, [['created_time' => 'desc']], 0, 1000);
            $list    = [];
            if (isset($data['hits']['hits'])) {
                foreach ($data['hits']['hits'] as &$val) {
                    $orderData = $val['_source'];
                    #region 兼容
                    $orderData['recharge_balance']        = $orderData['recharge_balance'] ?? 0;
                    $orderData['bonus_balance']           = $orderData['bonus_balance'] ?? 0;
                    $orderData['refund_recharge_balance'] = $orderData['refund_recharge_balance'] ?? 0;
                    $orderData['refund_bonus_balance']    = $orderData['refund_bonus_balance'] ?? 0;
                    $orderData['cash_amount']             = $orderData['cash_amount'] ?? ($orderData['payment_amount'] ?? 0);
                    #endregion 兼容

                    if (in_array($params['type'], [1, 2, 3, 4, 5, 11]) && isset($orderData['is_after_sale']) && $orderData['is_after_sale'] == 1) {
                        continue;
                    }
                    if ($params['type'] == 3 && isset($orderData['group_status']) && in_array($orderData['group_status'], [1, 3])) {
                        continue;
                    }
                    if ($params['type'] == 3 && isset($orderData['refund_status']) && in_array($orderData['refund_status'], [1, 2])) {
                        continue;
                    }
                    if ($orderData['sub_order_status'] == 0 && isset($orderData['main_order_id'])) {
                        if (isset($list[$orderData['main_order_id']])) {
                            $list[$orderData['main_order_id']]['goodsInfo'][] = [
                                'period'        => $orderData['period'],
                                'package_id'    => $orderData['package_id'],
                                'goods_img'     => imagePrefix($orderData['banner_img']),
                                'goods_title'   => $orderData['title'],
                                'package_name'  => $orderData['package_name'],
                                'order_qty'     => $orderData['order_qty'],
                                'package_price' => !empty($orderData['special_price']) ? $orderData['special_price'] : $orderData['package_price'],
                                'periods_type'  => $orderData['order_type'],
                                'label'         => isset($orderData['delivery_method']) ? $orderData['delivery_method'] == 1 ? 2 : 1 : 0
                            ];
                            $list[$orderData['main_order_id']]['total_qty']   = $list[$orderData['main_order_id']]['total_qty'] + $orderData['order_qty'];
                        } else {
                            if ($orderData['order_type'] == 2 && isset($orderData['is_replace_pay']) && $orderData['is_replace_pay'] == 1) {
                                $countdown = env('ORDERS.replace_pay_time_out') - (time() - strtotime($orderData['created_time']));
                            } else if (strpos($orderData['sub_order_no'], 'VHG') !== false) {
                                $countdown = intval(env('ORDERS.AFTER_SALES_ORDER_TIMEOUT')) * 60 - (time() - strtotime($orderData['created_time']));
                            } else if (in_array($orderData['payment_method'], [10, 11])) {
                                $countdown = intval(env('ORDERS.transfer_pay_time_out')) - (time() - strtotime($orderData['created_time']));
                            } else if (strpos($orderData['sub_order_no'], 'VHA') !== false) {
                                $orderData['predict_time'] = '';
                                $countdown                 = env('ORDERS.AUCTION_ORDER_TIMEOUT') - (time() - $orderData['created_time']);
                            } else {
                                $liveCount = Db::table('vh_minilive.vh_goods')->where([['period_id', '=', $orderData['period']]])->count();
                                if ($liveCount > 0) {
                                    $countdown = env('ORDERS.LIVE_ORDER_TIMEOUT') - (time() - strtotime($orderData['created_time']));
                                } else if (in_array($orderData['period'], ["142927", "143361"])) {
                                    $countdown = env('ORDERS.SPECIAL_PAY_TIME_OUT') - (time() - strtotime($orderData['created_time']));
                                } else {
                                    $countdown = env('ORDERS.pay_time_out') - (time() - strtotime($orderData['created_time']));
                                }
                            }
                            $list[$orderData['main_order_id']] = [
                                'order_no'       => $orderData['main_order_no'],
                                'status'         => $orderData['sub_order_status'],
                                'goodsInfo'      => [[
                                    'period'        => $orderData['period'],
                                    'package_id'    => $orderData['package_id'],
                                    'goods_img'     => imagePrefix($orderData['banner_img']),
                                    'goods_title'   => $orderData['title'],
                                    'package_name'  => $orderData['package_name'],
                                    'order_qty'     => $orderData['order_qty'],
                                    'package_price' => !empty($orderData['special_price']) ? $orderData['special_price'] : $orderData['package_price'],
                                    'periods_type'  => $orderData['order_type'],
                                    'label'         => isset($orderData['delivery_method']) ? $orderData['delivery_method'] == 1 ? 2 : 1 : 0
                                ]],
                                'total_qty'      => $orderData['order_qty'],
                                'payment_amount' => $orderData['order_type'] == 4 ? $orderData['main_rabbit_payment_amount'] : $orderData['main_payment_amount'],
                                'cash_amount'           => $orderData['order_type'] == 4 ? 0 : ($orderData['cash_amount'] ?? 0),
                                'recharge_balance'        => $orderData['recharge_balance'] ?? 0,
                                'bonus_balance'        => $orderData['bonus_balance'] ?? 0,
                                'created_time'   => $orderData['created_time'],
                                'payment_method' => $orderData['payment_method'],
                                'predict_time'   => $orderData['predict_time'],
                                'order_type'     => $orderData['order_type'],
                                'countdown'      => $countdown <= 0 ? 0 : $countdown,
                                'express_type'   => $orderData['express_type'],
                                'express_number' => $orderData['express_number'],
                                'is_replace_pay' => isset($orderData['is_replace_pay']) ? $orderData['is_replace_pay'] : 0,
                                'is_comment'     => 1,
                                'comment_period' => $orderData['period'],
                            ];
                        }
                    } else {
                        //订单状态处理 订单状态 0-待支付 1-已支付 2-已发货 3-已完成 4-已取消 5-拼团中 6-已暂存 7-退款中 8-退款成功
                        //group_status拼团状态：0-不拼团 1-拼团中 2-拼团成功 3-拼团失败
                        if (isset($orderData['group_status']) && $orderData['group_status'] == 1 && $orderData['sub_order_status'] == 1) $orderData['sub_order_status'] = 5;
                        if (isset($orderData['is_ts']) && $orderData['is_ts'] == 1 && $orderData['sub_order_status'] == 1) $orderData['sub_order_status'] = 6;
                        if (isset($orderData['refund_status']) && $orderData['refund_status'] == 1) $orderData['sub_order_status'] = 7;
                        if (isset($orderData['refund_status']) && $orderData['refund_status'] == 2) $orderData['sub_order_status'] = 8;
                        //待拼团数据列表处理
                        $group_last_num  = 0;
                        $group_share_url = '';
                        $group_id        = 0;
                        if (($params['type'] == 0 || $params['type'] == 2) && isset($orderData['group_id']) && $orderData['group_id'] > 0) {
                            $groupInfo = Db::name('order_group')->field('id,group_limit_nums,group_join_nums')->where(['id' => $orderData['group_id']])->find();
                            if ($groupInfo) {
                                $group_last_num  = $groupInfo['group_limit_nums'] - $groupInfo['group_join_nums'] <= 0 ? 0 : $groupInfo['group_limit_nums'] - $groupInfo['group_join_nums'];
                                $group_share_url = env('ORDERS.group_share_url') . '拼团信息地址待完善';
                                $group_id        = $groupInfo['id'];
                            }
                        }
                        //售后状态处理
                        $after_sale_status = 0;//默认不允许售后
                        $forbidden_reason  = '';//不允许售后原因
                        if (isset($orderData['is_after_sale']) && $orderData['is_after_sale'] == 1) {
                            $after_sale_status = 2;//显示售后详情
                        } else if (in_array($orderData['sub_order_status'], [1, 2, 6]) && empty($orderData['related_order_no']) && $orderData['order_type'] != 4) {
                            if (($orderData['order_type'] == 2) && (($orderData['payment_doc'] ?? 0) == 1)) {
                                $forbidden_reason = '您的订单已向海关申报，由保税区安排发货中，详询客服！';//不允许售后原因
                            } elseif(Db::name('cross_split')->where('main_order_no', $orderData['main_order_no'])->count()) {
                                $forbidden_reason = '组合套餐售后请联系客服！';//不允许售后原因
                            } else {
                                $after_sale_status = 1;//允许申请
                            }
                        } else if ($orderData['sub_order_status'] == 3 && empty($orderData['related_order_no']) && $orderData['order_type'] != 4) {
                            if (strtotime($orderData['goods_receipt_time']) > time() - 30 * 86400 || strtotime($orderData['goods_receipt_time']) == 0) {
                                $after_sale_status = 1;//允许申请
                            } else {
                                $forbidden_reason = "确认收货已超30天";
                            }
                        } else {
                            if ($orderData['sub_order_status'] == 0) $forbidden_reason = "订单未支付";
                            if ($orderData['sub_order_status'] == 4) $forbidden_reason = "订单已取消";
                            if ($orderData['sub_order_status'] == 5) $forbidden_reason = "订单拼团中";
                            if ($orderData['sub_order_status'] == 7) $forbidden_reason = "订单退款中";
                            if ($orderData['sub_order_status'] == 8) $forbidden_reason = "订单已退款完成";
                            if (!empty($orderData['related_order_no'])) $forbidden_reason = "售后订单";
                            if ($orderData['order_type'] == 4) $forbidden_reason = "兔头订单";
                        }
                        //拍卖是否评价处理
                        $is_comment = 1;
                        if ($orderData['order_type'] == 11) {
                            if (isset($orderData['buyer_is_evaluate']) && $orderData['buyer_is_evaluate'] == 0) $is_comment = 0;
                            $orderData['predict_time'] = '';
                        }
                        $list[] = [
                            'order_no'              => $orderData['sub_order_no'],
                            'status'                => $orderData['sub_order_status'],
                            'goodsInfo'             => [[
                                'period'        => $orderData['period'],
                                'package_id'    => $orderData['package_id'],
                                'goods_img'     => imagePrefix($orderData['banner_img']),
                                'goods_title'   => $orderData['title'],
                                'package_name'  => $orderData['package_name'],
                                'order_qty'     => $orderData['order_qty'],
                                'package_price' => !empty($orderData['special_price']) ? $orderData['special_price'] : $orderData['package_price'],
                                'periods_type'  => $orderData['order_type'],
                                'label'         => isset($orderData['delivery_method']) ? $orderData['delivery_method'] == 1 ? 2 : 1 : 0
                            ]],
                            'total_qty'             => $orderData['order_qty'],
                            'payment_amount'        => $orderData['order_type'] == 4 ? $orderData['rabbit_payment_amount'] : $orderData['payment_amount'],
                            'cash_amount'           => $orderData['order_type'] == 4 ? 0 : ($orderData['cash_amount'] ?? 0),
                            'recharge_balance'        => $orderData['recharge_balance'] ?? 0,
                            'bonus_balance'        => $orderData['bonus_balance'] ?? 0,
                            'created_time'          => $orderData['created_time'],
                            'payment_method'        => $orderData['payment_method'],
                            'predict_time'          => $orderData['predict_time'],
                            'order_type'            => $orderData['order_type'],
                            'express_type'          => $orderData['express_type'],
                            'express_number'        => $orderData['express_number'],
                            'group_id'              => $group_id,
                            'group_status'          => isset($orderData['group_status']) ? $orderData['group_status'] : 0,
                            'group_last_num'        => $group_last_num,
                            'group_share_url'       => $group_share_url,
                            'after_sale_status'     => $after_sale_status,
                            'forbidden_reason'      => $forbidden_reason,
                            'work_order_status'     => isset($orderData['work_order_status']) ? $orderData['work_order_status'] : 0,
                            'delivery_method'       => isset($orderData['delivery_method']) ? $orderData['delivery_method'] : 0,
                            'delivery_person_name'  => isset($orderData['delivery_person_name']) ? $orderData['delivery_person_name'] : '',
                            'delivery_person_phone' => isset($orderData['delivery_person_phone']) ? $orderData['delivery_person_phone'] : '',
                            'is_replace_pay'        => isset($orderData['is_replace_pay']) ? $orderData['is_replace_pay'] : 0,
                            'is_comment'            => $is_comment,
                            'comment_period'        => $orderData['period'],
                            'auction_type'          => isset($orderData['auction_type']) ? $orderData['auction_type'] : 0
                        ];
                    }
                }
            }
            $created_time = array_column($list, 'created_time');
            array_multisort($created_time, SORT_DESC, $list);
            $offset          = ($page - 1) * $limit;
            $result['list']  = array_slice($list, $offset, $limit);
            $result['total'] = count($list);
        } else {//列表走数据库
            $result       = [];
            $list         = [];
            $offset       = ($page - 1) * $limit;
            $auctionCount = 0;
            $searchData   = array(
                'uid'    => $params['uid'],
                'filter' => $params['type'] + 1,
                'page'   => $page,
                'count'  => $limit,
            );
            $orderLists   = curlRequest(env('ITEM.MYSQL_BATCH_SEARCH') . '/services/v3/batchsearch/orders/list', $searchData, [], 'GET');
            //未支付订单处理
            if ($page == 1 && isset($orderLists['unpaid_orders']) && count($orderLists['unpaid_orders']) > 0) {
                foreach ($orderLists['unpaid_orders'] as &$v) {
                    $unpaid_orders = $es->getDocumentList(['index' => ['orders'], 'match' => [['main_order_no' => $v['unpaid_main_order_no']]], 'limit' => 100]);
                    if (isset($unpaid_orders['data'][0])) {
                        #region 兼容
                        $unpaid_orders['data'][0]['recharge_balance']        = $unpaid_orders['data'][0]['recharge_balance'] ?? 0;
                        $unpaid_orders['data'][0]['bonus_balance']           = $unpaid_orders['data'][0]['bonus_balance'] ?? 0;
                        $unpaid_orders['data'][0]['refund_recharge_balance'] = $unpaid_orders['data'][0]['refund_recharge_balance'] ?? 0;
                        $unpaid_orders['data'][0]['refund_bonus_balance']    = $unpaid_orders['data'][0]['refund_bonus_balance'] ?? 0;
                        $unpaid_orders['data'][0]['cash_amount']             = $unpaid_orders['data'][0]['cash_amount'] ?? ($unpaid_orders['data'][0]['payment_amount'] ?? 0);
                        #endregion 兼容

                        $orderData = $unpaid_orders['data'][0];
                        if (isset($orderData['special_type']) && $orderData['special_type'] == 4) continue;//过滤订金订单
                        $goodsInfo = [];
                        foreach ($unpaid_orders['data'] as &$val) {
                            $goodsInfo[] = array(
                                'period'        => $val['period'],
                                'package_id'    => $val['package_id'],
                                'goods_img'     => imagePrefix($val['banner_img']),
                                'goods_title'   => $val['title'],
                                'package_name'  => $val['package_name'],
                                'order_qty'     => $val['order_qty'],
                                'package_price' => !empty($val['special_price']) ? $val['special_price'] : $val['package_price'],
                                'periods_type'  => $val['order_type'],
                                'label'         => isset($val['delivery_method']) ? $val['delivery_method'] == 1 ? 2 : 1 : 0
                            );
                        }
                        if ($orderData['order_type'] == 2 && isset($orderData['is_replace_pay']) && $orderData['is_replace_pay'] == 1) {
                            $countdown = env('ORDERS.replace_pay_time_out') - (time() - strtotime($orderData['created_time']));
                        } else if (strpos($orderData['sub_order_no'], 'VHG') !== false) {
                            $countdown = intval(env('ORDERS.AFTER_SALES_ORDER_TIMEOUT')) * 60 - (time() - strtotime($orderData['created_time']));
                        } else if (in_array($orderData['payment_method'], [10, 11])) {
                            $countdown = intval(env('ORDERS.transfer_pay_time_out')) - (time() - strtotime($orderData['created_time']));
                        } else {
                            $liveCount = Db::table('vh_minilive.vh_goods')->where([['period_id', '=', $val['period']]])->count();
                            if ($liveCount > 0) {
                                $countdown = env('ORDERS.LIVE_ORDER_TIMEOUT') - (time() - strtotime($orderData['created_time']));
                            } else if (in_array($orderData['period'], ["142927", "143361"])) {
                                $countdown = env('ORDERS.SPECIAL_PAY_TIME_OUT') - (time() - strtotime($orderData['created_time']));
                            } else {
                                $countdown = env('ORDERS.pay_time_out') - (time() - strtotime($orderData['created_time']));
                            }
                        }
                        $list[] = [
                            'order_no'       => $orderData['main_order_no'],
                            'status'         => $orderData['sub_order_status'],
                            'goodsInfo'      => $goodsInfo,
                            'total_qty'      => $orderData['order_qty'],
                            'payment_amount' => $orderData['order_type'] == 4 ? $orderData['main_rabbit_payment_amount'] : $orderData['main_payment_amount'],
                            'cash_amount'           => $orderData['order_type'] == 4 ? 0 : ($orderData['cash_amount'] ?? 0),
                            'recharge_balance'        => $orderData['recharge_balance'] ?? 0,
                            'bonus_balance'        => $orderData['bonus_balance'] ?? 0,
                            'created_time'   => $orderData['created_time'],
                            'payment_method' => $orderData['payment_method'],
                            'predict_time'   => $orderData['predict_time'],
                            'order_type'     => $orderData['order_type'],
                            'countdown'      => $countdown <= 0 ? 0 : $countdown,
                            'express_type'   => $orderData['express_type'],
                            'express_number' => $orderData['express_number'],
                            'is_replace_pay' => isset($orderData['is_replace_pay']) ? $orderData['is_replace_pay'] : 0,
                            'is_comment'     => 1,
                            'comment_period' => $orderData['period'],
                        ];
                    }
                }
            }
            //非未支付订单处理
            if (isset($orderLists['data']) && count($orderLists['data']) > 0) {
                $sub_order_no_arr = array_column($orderLists['data'], 'sub_order_no');
                $terms[]          = ['sub_order_no.keyword' => $sub_order_no_arr];
                $arr              = array(
                    'index' => ['orders'],
                    'terms' => $terms,
                    'limit' => 100
                );
                $esData           = $es->getDocumentList($arr);
                if (isset($esData['data'][0])) {
                    foreach ($esData['data'] as &$vv) {
                        #region 兼容
                        $order['recharge_balance']        = $order['recharge_balance'] ?? 0;
                        $order['bonus_balance']           = $order['bonus_balance'] ?? 0;
                        $order['refund_recharge_balance'] = $order['refund_recharge_balance'] ?? 0;
                        $order['refund_bonus_balance']    = $order['refund_bonus_balance'] ?? 0;
                        $order['cash_amount']             = $order['cash_amount'] ?? ($order['payment_amount'] ?? 0);
                        #endregion 兼容

                        $orderData = $vv;
                        //数据库查询数据覆盖es查询数据
                        foreach ($orderLists['data'] as &$vvv) {
                            if ($vvv['sub_order_no'] == $orderData['sub_order_no']) {
                                $orderData['sub_order_status'] = $vvv['sub_order_status'];
                                $orderData['refund_status']    = isset($vvv['refund_status']) ? $vvv['refund_status'] : 0;
                                $orderData['is_ts']            = isset($vvv['is_ts']) ? $vvv['is_ts'] : 0;
                                $orderData['is_delete']        = $vvv['is_delete'];
                                $orderData['is_after_sale']    = isset($vvv['is_after_sale']) ? $vvv['is_after_sale'] : 0;
                                $orderData['group_status']     = isset($vvv['group_status']) ? $vvv['group_status'] : 0;
                                $orderData['freeze_status']    = isset($vvv['freeze_status']) ? $vvv['freeze_status'] : 0;
                            }
                        }
                        //订单状态处理 订单状态 0-待支付 1-已支付 2-已发货 3-已完成 4-已取消 5-拼团中 6-已暂存 7-退款中 8-退款成功
                        //group_status拼团状态：0-不拼团 1-拼团中 2-拼团成功 3-拼团失败
                        if (isset($orderData['group_status']) && $orderData['group_status'] == 1 && $orderData['sub_order_status'] == 1) $orderData['sub_order_status'] = 5;
                        if (isset($orderData['is_ts']) && $orderData['is_ts'] == 1 && $orderData['sub_order_status'] == 1) $orderData['sub_order_status'] = 6;
                        if (isset($orderData['refund_status']) && $orderData['refund_status'] == 1) $orderData['sub_order_status'] = 7;
                        if (isset($orderData['refund_status']) && $orderData['refund_status'] == 2) $orderData['sub_order_status'] = 8;
                        //待拼团数据列表处理
                        $group_last_num  = 0;
                        $group_share_url = '';
                        $group_id        = 0;
                        if (($params['type'] == 0 || $params['type'] == 2) && isset($orderData['group_id']) && $orderData['group_id'] > 0) {
                            $groupInfo = Db::name('order_group')->field('id,group_limit_nums,group_join_nums')->where(['id' => $orderData['group_id']])->find();
                            if ($groupInfo) {
                                $group_last_num  = $groupInfo['group_limit_nums'] - $groupInfo['group_join_nums'] <= 0 ? 0 : $groupInfo['group_limit_nums'] - $groupInfo['group_join_nums'];
                                $group_share_url = env('ORDERS.group_share_url') . '拼团信息地址待完善';
                                $group_id        = $groupInfo['id'];
                            }
                        }
                        //售后状态处理
                        $after_sale_status = 0;//默认不允许售后
                        $forbidden_reason  = '';//不允许售后原因
                        if (isset($orderData['is_after_sale']) && $orderData['is_after_sale'] == 1) {
                            $after_sale_status = 2;//显示售后详情
                        } else if (in_array($orderData['sub_order_status'], [1, 2, 6]) && empty($orderData['related_order_no']) && $orderData['order_type'] != 4) {
                            if (($orderData['order_type'] == 2) && (($orderData['payment_doc'] ?? 0) == 1)) {
                                $forbidden_reason = '您的订单已向海关申报，由保税区安排发货中，详询客服！';//不允许售后原因
                            } elseif(Db::name('cross_split')->where('main_order_no', $orderData['main_order_no'])->count()) {
                                $forbidden_reason = '组合套餐售后请联系客服！';//不允许售后原因
                            } else {
                                $after_sale_status = 1;//允许申请
                            }
                        } else if ($orderData['sub_order_status'] == 3 && empty($orderData['related_order_no']) && $orderData['order_type'] != 4) {
                            if (strtotime($orderData['goods_receipt_time']) > time() - 30 * 86400 || strtotime($orderData['goods_receipt_time']) == 0) {
                                $after_sale_status = 1;//允许申请
                            } else {
                                $forbidden_reason = "确认收货已超30天";
                            }
                        } else {
                            if ($orderData['sub_order_status'] == 0) $forbidden_reason = "订单未支付";
                            if ($orderData['sub_order_status'] == 4) $forbidden_reason = "订单已取消";
                            if ($orderData['sub_order_status'] == 5) $forbidden_reason = "订单拼团中";
                            if ($orderData['sub_order_status'] == 7) $forbidden_reason = "订单退款中";
                            if ($orderData['sub_order_status'] == 8) $forbidden_reason = "订单已退款完成";
                            if (!empty($orderData['related_order_no'])) $forbidden_reason = "售后订单";
                            if ($orderData['order_type'] == 4) $forbidden_reason = "兔头订单";
                        }

                        $list[] = [
                            'order_no'              => $orderData['sub_order_no'],
                            'status'                => $orderData['sub_order_status'],
                            'goodsInfo'             => [[
                                'period'        => $orderData['period'],
                                'package_id'    => $orderData['package_id'],
                                'goods_img'     => imagePrefix($orderData['banner_img']),
                                'goods_title'   => $orderData['title'],
                                'package_name'  => $orderData['package_name'],
                                'order_qty'     => $orderData['order_qty'],
                                'package_price' => !empty($orderData['special_price']) ? $orderData['special_price'] : $orderData['package_price'],
                                'periods_type'  => $orderData['order_type'],
                                'label'         => isset($orderData['delivery_method']) ? $orderData['delivery_method'] == 1 ? 2 : 1 : 0
                            ]],
                            'total_qty'             => $orderData['order_qty'],
                            'payment_amount'        => $orderData['order_type'] == 4 ? $orderData['rabbit_payment_amount'] : $orderData['payment_amount'],
                            'cash_amount'           => $orderData['order_type'] == 4 ? 0 : ($orderData['cash_amount'] ?? 0),
                            'recharge_balance'        => $orderData['recharge_balance'] ?? 0,
                            'bonus_balance'        => $orderData['bonus_balance'] ?? 0,
                            'created_time'          => $orderData['created_time'],
                            'payment_method'        => $orderData['payment_method'],
                            'predict_time'          => $orderData['predict_time'],
                            'order_type'            => $orderData['order_type'],
                            'express_type'          => $orderData['express_type'],
                            'express_number'        => $orderData['express_number'],
                            'group_id'              => $group_id,
                            'group_status'          => isset($orderData['group_status']) ? $orderData['group_status'] : 0,
                            'group_last_num'        => $group_last_num,
                            'group_share_url'       => $group_share_url,
                            'after_sale_status'     => $after_sale_status,
                            'forbidden_reason'      => $forbidden_reason,
                            'work_order_status'     => isset($orderData['work_order_status']) ? $orderData['work_order_status'] : 0,
                            'delivery_method'       => isset($orderData['delivery_method']) ? $orderData['delivery_method'] : 0,
                            'delivery_person_name'  => isset($orderData['delivery_person_name']) ? $orderData['delivery_person_name'] : '',
                            'delivery_person_phone' => isset($orderData['delivery_person_phone']) ? $orderData['delivery_person_phone'] : '',
                            'is_replace_pay'        => isset($orderData['is_replace_pay']) ? $orderData['is_replace_pay'] : 0,
                            'is_comment'            => 1,
                            'comment_period'        => $orderData['period'],
                        ];
                    }
                }
            }
            //拍卖订单处理
            if ($params['is_show_auction'] == 1) {
                $limit = $limit * 2;
                if ($params['type'] != 2) {
                    $auctionWhere   = [];
                    $auctionWhere[] = ['uid', '=', $params['uid']];
                    $auctionWhere[] = ['is_delete', '=', 0];
                    switch ($params['type']) {
                        case 1:
                            $auctionWhere[] = ['order_status', '=', 0];
                            break;
                        case 3:
                            $auctionWhere[] = ['order_status', '=', 1];
                            break;
                        case 4:
                            $auctionWhere[] = ['order_status', '=', 2];
                            break;
                        case 5:
                            $auctionWhere[] = ['order_status', '=', 3];
                            break;
                        case 6:
                            $auctionWhere[] = ['is_after_sale', '=', 1];
                            break;
                    }
                    $auctionData  = Db::table('vh_auction.vh_orders')->where($auctionWhere)->limit($offset, $limit)->select()->toArray();
                    $auctionCount = Db::table('vh_auction.vh_orders')->where($auctionWhere)->count();
                    if (!empty($auctionData)) {
                        foreach ($auctionData as &$vv) {
                            if (isset($vv['refund_status']) && $vv['refund_status'] == 1) $vv['order_status'] = 7;
                            if (isset($vv['refund_status']) && $vv['refund_status'] == 2) $vv['order_status'] = 8;
                            //售后状态处理
                            $after_sale_status = 0;//默认不允许售后
                            $forbidden_reason  = '';//不允许售后原因
                            if (isset($vv['is_after_sale']) && $vv['is_after_sale'] == 1) {
                                $after_sale_status = 2;//显示售后详情
                            } else if (in_array($vv['order_status'], [1, 2])) {
                                $after_sale_status = 1;//允许申请
                            } else {
                                if ($vv['order_status'] == 0) $forbidden_reason = "订单未支付";
                                if ($vv['order_status'] == 3) $forbidden_reason = "订单已完成";
                                if ($vv['order_status'] == 4) $forbidden_reason = "订单已取消";
                                if ($vv['order_status'] == 7) $forbidden_reason = "订单退款中";
                                if ($vv['order_status'] == 8) $forbidden_reason = "订单已退款完成";
                            }
                            $countdown = env('ORDERS.AUCTION_ORDER_TIMEOUT') - (time() - $vv['created_time']);
                            $list[]    = [
                                'order_no'              => $vv['order_no'],
                                'status'                => $vv['order_status'],
                                'goodsInfo'             => [[
                                    'period'        => $vv['goods_id'],
                                    'package_id'    => 0,
                                    'goods_img'     => imagePrefix($vv['goods_img']),
                                    'goods_title'   => $vv['goods_name'],
                                    'package_name'  => '',
                                    'order_qty'     => 1,
                                    'package_price' => $vv['payment_amount'],
                                    'periods_type'  => 11,
                                    'label'         => 0
                                ]],
                                'total_qty'             => 1,
                                'payment_amount'        => $vv['payment_amount'],
                                'created_time'          => date('Y-m-d H:i:s', $vv['created_time']),
                                'payment_method'        => $vv['payment_method'],
                                'predict_time'          => '',
                                'order_type'            => 11,
                                'express_type'          => $vv['express_type'],
                                'express_number'        => $vv['express_number'],
                                'group_id'              => 0,
                                'group_last_num'        => 0,
                                'group_share_url'       => '',
                                'after_sale_status'     => $after_sale_status,
                                'forbidden_reason'      => $forbidden_reason,
                                'work_order_status'     => $vv['work_order_status'],
                                'delivery_method'       => 0,
                                'delivery_person_name'  => '',
                                'delivery_person_phone' => '',
                                'is_replace_pay'        => 0,
                                'is_comment'            => $vv['buyer_is_evaluate'] == 0 ? 0 : 1,
                                'countdown'             => $countdown <= 0 ? 0 : $countdown,
                                'auction_type'          => $vv['auction_type'],
                                'is_liquor'             => 1,//是否酒类
                            ];
                        }
                    }
                }
            }
            $created_time = array_column($list, 'created_time');
            array_multisort($created_time, SORT_DESC, $list);
            $result['list']  = array_slice($list, 0, $limit);
            $result['total'] = $orderLists['total'] + $auctionCount;
        }
        //酒评处理
        if (in_array($params['type'], [0, 4, 5])) {
            $comment_period = array_values(array_unique(array_column($list, 'comment_period')));
            $periodInfo     = $es->getDocumentList(['index' => ['periods'], 'terms' => [['id' => $comment_period]], 'source' => ['id', 'product_category', 'product_main_category'], 'limit' => 100]);
            //获取用户酒评信息
            $userOrderCommentInfo = getUserOrderCommentInfo($params['uid']);
            if (count($periodInfo['data']) > 0) {
                $time = time() - 365 * 86400;
                foreach ($result['list'] as &$value) {
                    $value['is_liquor'] = 0;//默认不是酒类
                    if ($value['status'] == 3 && strtotime($value['created_time']) > $time && !in_array($value['order_no'], $userOrderCommentInfo)) {
                        if ($value['order_type'] != 11) {
                            foreach ($periodInfo['data'] as &$vvv) {
                                if ($value['comment_period'] == $vvv['id'] && in_array('酒类', $vvv['product_main_category'])) {
                                    $value['is_comment'] = 0;
                                    $pkg_ids             = array_unique(array_values(array_column(($value['goodsInfo'] ?? []), 'package_id')));
                                    if (!empty($pkg_ids)) {
                                        $pkg_ps      = Es::name(Es::PERIODS_PACKAGE)->where([['id', 'in', $pkg_ids]])->field('associated_products')->select()->toArray();
                                        $pkg_ps_pids = [];
                                        foreach ($pkg_ps as $pkg_ps_item) {
                                            $associated_products = json_decode($pkg_ps_item['associated_products'], true);
                                            $package_pids        = [];
                                            foreach ($associated_products as $associated_product) {
                                                if (is_array($associated_product['product_id'])) {
                                                    $package_pids = array_merge($package_pids, $associated_product['product_id']);
                                                } else {
                                                    $package_pids[] = $associated_product['product_id'];
                                                }
                                            }
                                            $pkg_ps_pids = array_merge($pkg_ps_pids, $package_pids);
                                        }
                                        $p_types = Es::name(Es::PRODUCTS)->where([['id', 'in', $pkg_ps_pids]])->field('product_category')->select()->toArray();
                                        $p_types = implode(',', array_values(array_unique(array_column($p_types, 'product_category'))));
                                        if ($p_types == '6') {
                                            $value['is_comment'] = 1;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        //物流异常提醒处理
        if (in_array($params['type'], [0, 3])) {
            $orderNoArr       = array_column($list, 'order_no');
            $unshipped_reason = Db::name('sub_order_extend')->field('sub_order_no,unshipped_reason')->where([['sub_order_no', 'in', $orderNoArr], ['unshipped_reason', '<>', '']])->select()->toArray();
            if (count($unshipped_reason) > 0) {
                foreach ($unshipped_reason as &$values) {
                    foreach ($result['list'] as &$vv) {
                        if (!isset($vv['unshipped_reason'])) $vv['unshipped_reason'] = '';
                        if ($values['sub_order_no'] == $vv['order_no'] && !empty($values['unshipped_reason'])) {
                            $vv['unshipped_reason'] = $values['unshipped_reason'];
                        }
                    }
                }
            }
        }
        return $result;
    }

    /**
     * Description:个人中心订单详情
     * Author: zrc
     * Date: 2021/12/2
     * Time: 16:52
     * @param $requestparams
     * @return mixed
     */
    public function personalDetail($requestparams)
    {
        $params = $requestparams;
        $es     = new ElasticSearchService();
        $result = [];
        if (strpos($params['order_no'], 'VHM') !== false || strpos($params['order_no'], 'WYM') !== false) {
            $arr  = array(
                'index' => ['orders'],
                'match' => [['main_order_no' => $params['order_no']], ['uid' => $params['uid']], ['is_delete' => 0]]
            );
            $data = $es->getDocumentList($arr);
            if (isset($data['data'][0])) {
                #region 兼容
                $data['data'][0]['recharge_balance']        = $data['data'][0]['recharge_balance'] ?? 0;
                $data['data'][0]['bonus_balance']           = $data['data'][0]['bonus_balance'] ?? 0;
                $data['data'][0]['refund_recharge_balance'] = $data['data'][0]['refund_recharge_balance'] ?? 0;
                $data['data'][0]['refund_bonus_balance']    = $data['data'][0]['refund_bonus_balance'] ?? 0;
                $data['data'][0]['cash_amount']             = $data['data'][0]['cash_amount'] ?? ($data['data'][0]['payment_amount'] ?? 0);
                #endregion 兼容
                // 查询专题活动套餐价
                $activity_package_price = Db::name('sub_order_extend')
                    ->where('sub_order_no', $data['data'][0]['sub_order_no'])
                    ->value('activity_package_price');
                if (!empty($activity_package_price) && $activity_package_price > 0 ) {
                    $data['data'][0]['package_price'] = $activity_package_price;
                }
                $encrypt         = cryptionDeal(2, [$data['data'][0]['consignee'], $data['data'][0]['consignee_phone']], $params['uid'], '前端用户');
                $consignee       = isset($encrypt[$data['data'][0]['consignee']]) ? $encrypt[$data['data'][0]['consignee']] : '';
                $consignee_phone = isset($encrypt[$data['data'][0]['consignee_phone']]) ? $encrypt[$data['data'][0]['consignee_phone']] : '';
                if ($data['data'][0]['order_type'] == 2 && isset($data['data'][0]['is_replace_pay']) && $data['data'][0]['is_replace_pay'] == 1) {
                    $countdown = env('ORDERS.replace_pay_time_out') - (time() - strtotime($data['data'][0]['created_time']));
                } else if (strpos($data['data'][0]['sub_order_no'], 'VHG') !== false) {
                    $countdown = intval(env('ORDERS.AFTER_SALES_ORDER_TIMEOUT')) * 60 - (time() - strtotime($data['data'][0]['created_time']));
                } else if (in_array($data['data'][0]['payment_method'], [10, 11])) {
                    $countdown = intval(env('ORDERS.transfer_pay_time_out')) - (time() - strtotime($data['data'][0]['created_time']));
                } else {
                    $liveCount = Db::table('vh_minilive.vh_goods')->where([['period_id', '=', $data['data'][0]['period']]])->count();
                    if ($liveCount > 0) {
                        $countdown = env('ORDERS.LIVE_ORDER_TIMEOUT') - (time() - strtotime($data['data'][0]['created_time']));
                    } else if (in_array($data['data'][0]['period'], ["142927", "143361"])) {
                        $countdown = env('ORDERS.SPECIAL_PAY_TIME_OUT') - (time() - strtotime($data['data'][0]['created_time']));
                    } else {
                        $countdown = env('ORDERS.pay_time_out') - (time() - strtotime($data['data'][0]['created_time']));
                    }
                }

                if (strtotime($data['data'][0]['created_time']) >= 1724428800) {
                    $express_name = '快递包邮';
                    switch ($data['data'][0]['express_type']) {
                        case 3:
                            $express_name = '顺丰冷链';
                            break;
                        case 31:
                            $express_name = '顺丰温控包裹';
                            break;
                    }
                } else {
                    switch ($data['data'][0]['express_type']) {
                        case 2:
                            $express_name = '顺丰快递';
                            break;
                        case 3:
                            $express_name = '顺丰冷链';
                            break;
                        case 4:
                        case 5:
                            $express_name = '京东快递';
                            break;
                        case 31:
                            $express_name = '顺丰温控包裹';
                            break;
                        default:
                            $express_name = '其他快递';
                            break;
                    }
                }

                //发票名称处理
                $invoice_name = '';
                if (isset($data['data'][0]['invoice_progress']) && $data['data'][0]['invoice_progress'] > 0) {
                    $invoice_name = Db::name('invoice_record')->where(['main_order_no' => $params['order_no']])->value('invoice_name');
                    $invoice_name = !empty($invoice_name) ? $invoice_name : '';
                }
                $result                           = array(
                    'order_no'         => $params['order_no'],
                    'status'           => $data['data'][0]['sub_order_status'],
                    'payment_method'   => $data['data'][0]['payment_method'],
                    'payment_amount'   => $data['data'][0]['main_payment_amount'],
                    'cash_amount'   => $data['data'][0]['cash_amount'],
                    'recharge_balance'   => $data['data'][0]['recharge_balance'],
                    'bonus_balance'   => $data['data'][0]['bonus_balance'],
                    'created_time'     => $data['data'][0]['created_time'],
                    'countdown'        => $countdown <= 0 ? 0 : $countdown,
                    'province_id'      => $data['data'][0]['province_id'],
                    'city_id'          => $data['data'][0]['city_id'],
                    'district_id'      => $data['data'][0]['district_id'],
                    'province_name'    => $data['data'][0]['province_name'],
                    'city_name'        => $data['data'][0]['city_name'],
                    'district_name'    => $data['data'][0]['district_name'],
                    'address'          => $data['data'][0]['address'],
                    'express_type'     => $data['data'][0]['express_type'],
                    'express_name'     => $express_name,
                    'express_number'   => $data['data'][0]['express_number'],
                    'goods_price'      => 0,
                    'express_fee'      => 0,
                    'money_off_value'  => 0,
                    'coupon_value'     => 0,
                    'total_qty'        => 0,
                    'consignee'        => $consignee,
                    'consignee_phone'  => $consignee_phone,
                    'payment_time'     => $data['data'][0]['payment_time'],
                    'invoice_progress' => isset($data['data'][0]['invoice_progress']) ? $data['data'][0]['invoice_progress'] : 0,
                    'invoice_id'       => isset($data['data'][0]['invoice_id']) ? $data['data'][0]['invoice_id'] : 0,
                    'invoice_name'     => $invoice_name,
                    'order_type'       => $data['data'][0]['order_type'],
                    'predict_time'     => date('Y-m-d', strtotime($data['data'][0]['predict_time'])),
                    'is_replace_pay'   => isset($data['data'][0]['is_replace_pay']) ? $data['data'][0]['is_replace_pay'] : 0,
                    'is_raffle'        => 0,
                    'warehouse_code'   => isset($data['data'][0]['warehouse_code']) ? $data['data'][0]['warehouse_code'] : '',
                );
                $result['preferential_reduction'] = 0;
                $result['money_off_value']        = 0;
                $result['coupon_value']           = 0;
                if (in_array($result['payment_method'], [0, 1, 6])) {
                    $payment_method_name = '支付宝';
                } else if (in_array($result['payment_method'], [3, 4, 5, 7, 8])) {
                    $payment_method_name = '微信';
                } else if (in_array($result['payment_method'], [10, 11])) {
                    $payment_method_name = '对公转账';
                } else if ($result['payment_method'] == 2) {
                    $payment_method_name = '扫码支付';
                } else {
                    $config_payment_method = config('config')['payment_method'];//订单支付方式获取
                    $payment_method_name   = isset($config_payment_method[intval($result['payment_method']) + 1]['label']) ? $config_payment_method[intval($result['payment_method'] + 1)]['label'] : '';
                }
                $result['payment_name'] = $payment_method_name;
                foreach ($data['data'] as &$val) {
                    $result['goodsInfo'][] = array(
                        'period'        => $val['period'],
                        'package_id'    => $val['package_id'],
                        'goods_img'     => imagePrefix($val['banner_img']),
                        'goods_title'   => $val['title'],
                        'package_name'  => $val['package_name'],
                        'order_qty'     => $val['order_qty'],
                        'package_price' => (!empty($val['special_price']) && $val['special_price'] > 0)? $val['special_price'] : $val['package_price'],
                        'periods_type'  => $val['order_type'],
                        'label'         => isset($val['delivery_method']) ? $val['delivery_method'] == 1 ? 2 : 1 : 0
                    );
                    $result['goods_price'] = bcadd($result['goods_price'], bcmul($val['package_price'], $val['order_qty'], 2),2);
                    $result['express_fee'] += $val['express_fee'];
                    if (isset($val['money_off_split_value'])) $result['money_off_value'] += $val['money_off_split_value'];
                    if (isset($val['coupon_split_value'])) $result['coupon_value'] += $val['coupon_split_value'];
                    if (isset($val['preferential_reduction'])) $result['preferential_reduction'] += $val['preferential_reduction'];
                    $result['total_qty'] += $val['order_qty'];
                }
                //对公转账信息
                if ($result['payment_method'] == 11) {
                    $esPeriodsData           = $es->getDocumentList(['index' => ['periods'], 'match' => [['id' => $result['goodsInfo'][0]['period']]], 'source' => ['payee_merchant_id'], 'limit' => 1]);
                    $payee_merchant_id       = isset($esPeriodsData['data'][0]['payee_merchant_id']) ? $esPeriodsData['data'][0]['payee_merchant_id'] : 2;
                    $collecting_company      = Db::name('collecting_company')->where(['id' => $payee_merchant_id])->find();
                    $result['transfer_info'] = array(
                        'transfer_name'       => isset($collecting_company['transfer_name']) ? $collecting_company['transfer_name'] : '',
                        'transfer_account'    => isset($collecting_company['transfer_account']) ? $collecting_company['transfer_account'] : '',
                        'transfer_bank'       => isset($collecting_company['transfer_bank']) ? $collecting_company['transfer_bank'] : '',
                        'transfer_interbank'  => isset($collecting_company['transfer_interbank']) ? $collecting_company['transfer_interbank'] : '',
                        'transfer_remittance' => $data['data'][0]['main_order_no'],
                    );
                } else if ($result['payment_method'] == 10) {
                    $illustrate              = Db::name('order_text')->where(['type' => 18])->value('content');
                    $result['transfer_info'] = array(
                        'illustrate' => $illustrate
                    );
                }
            }
        } else if (strpos($params['order_no'], 'VHA') !== false) {
            $data = Db::table('vh_auction.vh_orders')->where(['order_no' => $params['order_no'], 'uid' => $params['uid'], 'is_delete' => 0])->find();
            if (!empty($data)) {
                $status = $data['order_status'];
                if (isset($data['refund_status']) && $data['refund_status'] == 1) $status = 7;
                if (isset($data['refund_status']) && $data['refund_status'] == 2) $status = 8;
                $countdown       = env('ORDERS.AUCTION_ORDER_TIMEOUT') - (time() - $data['created_time']);
                $receipt_time    = $data['delivery_time'] > 0 ? date('Y-m-d H:i:s', $data['delivery_time'] + env('ORDERS.receipt_time') * 86400) : date('Y-m-d H:i:s', $data['created_time'] + env('ORDERS.receipt_time') * 86400);
                $encrypt         = cryptionDeal(2, [$data['consignee'], $data['consignee_phone']], $params['uid'], '前端用户');
                $consignee       = isset($encrypt[$data['consignee']]) ? $encrypt[$data['consignee']] : '';
                $consignee_phone = isset($encrypt[$data['consignee_phone']]) ? $encrypt[$data['consignee_phone']] : '';

                if ($data['created_time'] >= 1724428800) {
                    $express_name = '快递包邮';
                    switch ($data['express_type']) {
                        case 3:
                            $express_name = '顺丰冷链';
                            break;
                        case 31:
                            $express_name = '顺丰温控包裹';
                            break;
                    }
                } else {
                    switch ($data['express_type']) {
                        case 2:
                            $express_name = '顺丰快递';
                            break;
                        case 3:
                            $express_name = '顺丰冷链';
                            break;
                        case 4:
                        case 5:
                            $express_name = '京东快递';
                            break;
                        case 31:
                            $express_name = '顺丰温控包裹';
                            break;
                        default:
                            $express_name = '其他快递';
                            break;
                    }
                }

                //售后状态处理
                $after_sale_status = 0;//默认不允许售后
                $forbidden_reason  = '';//不允许售后原因
                if (isset($data['is_after_sale']) && $data['is_after_sale'] == 1) {
                    $after_sale_status = 2;//显示售后详情
                } else if (in_array($data['order_status'], [1, 2])) {
                    $after_sale_status = 1;//允许申请
                } else {
                    if ($data['order_status'] == 0) $forbidden_reason = "订单未支付";
                    if ($data['order_status'] == 3) $forbidden_reason = "订单已完成";
                    if ($data['order_status'] == 4) $forbidden_reason = "订单已取消";
                    if ($data['order_status'] == 7) $forbidden_reason = "订单退款中";
                    if ($data['order_status'] == 8) $forbidden_reason = "订单已退款完成";
                }
                $result = array(
                    'order_no'               => $params['order_no'],
                    'status'                 => $status,
                    'payment_method'         => $data['payment_method'],
                    'cash_amount'           => $data['cash_amount'],
                    'recharge_balance'       => $data['recharge_balance'],
                    'bonus_balance'         => $data['bonus_balance'],
                    'payment_amount'         => $data['payment_amount'],
                    'created_time'           => date('Y-m-d H:i:s', $data['created_time']),
                    'countdown'              => $countdown <= 0 ? 0 : $countdown,
                    'receipt_time'           => $receipt_time,
                    'province_id'            => $data['province_id'],
                    'city_id'                => $data['city_id'],
                    'district_id'            => $data['district_id'],
                    'province_name'          => $data['province_name'],
                    'city_name'              => $data['city_name'],
                    'district_name'          => $data['district_name'],
                    'address'                => $data['address'],
                    'goods_price'            => $data['payment_amount'],
                    'express_fee'            => 0,
                    'express_type'           => $data['express_type'],
                    'express_name'           => $express_name,
                    'express_number'         => $data['express_number'],
                    'money_off_value'        => 0,
                    'coupon_value'           => 0,
                    'preferential_reduction' => 0,
                    'total_qty'              => $data['order_qty'],
                    'consignee'              => $consignee,
                    'consignee_phone'        => $consignee_phone,
                    'refund_status'          => $data['refund_status'],
                    'refund_start_time'      => '',
                    'refund_end_time'        => '',
                    'refund_reject_time'     => '',
                    'payment_time'           => date('Y-m-d H:i:s', $data['payment_time']),
                    'invoice_progress'       => 0,
                    'invoice_id'             => 0,
                    'invoice_name'           => '',
                    'order_type'             => $data['order_type'],
                    'predict_time'           => '',
                    'goods_receipt_time'     => '',
                    'work_order_status'      => isset($data['work_order_status']) ? $data['work_order_status'] : 0,
                    'delivery_method'        => 0,
                    'delivery_person_name'   => '',
                    'delivery_person_phone'  => '',
                    'is_replace_pay'         => 0,
                    'goodsInfo'              => array(
                        array(
                            'period'        => $data['goods_id'],
                            'package_id'    => 0,
                            'goods_img'     => imagePrefix($data['goods_img']),
                            'goods_title'   => $data['goods_name'],
                            'package_name'  => '',
                            'order_qty'     => $data['order_qty'],
                            'package_price' => $data['payment_amount'],
                            'periods_type'  => $data['order_type'],
                        )
                    ),
                    'comment_type'           => 2,
                    'is_raffle'              => 0,
                    'warehouse_code'         => $data['uec_code'],
                    'after_sale_status'      => $after_sale_status,
                    'forbidden_reason'       => $forbidden_reason,
                );
            }
        } else if (strpos($params['order_no'], 'VHL') !== false) {
            $card_order = Db::table('vh_gift_card.vh_recharge_order')->where('order_no', $params['order_no'])->find();
            if (empty($card_order)) $this->throwError('未获取到订单详情');
            $result = array(
                'uid'                 => $card_order['uid'],
                'main_order_no'                 => $card_order['order_no'],
                'order_type'                    => 60,
                'main_order_status'                 => $card_order['order_status'],
                'sub_order_status'                 => $card_order['order_status'],
                'countdown'                 => max(($card_order['created_time'] + 300) - time(), 0),
                'group_id'                 => "",
                'main_payment_amount' => $card_order['payment_amount'],
            );
        } else {
            $arr  = array(
                'index' => ['orders'],
                'match' => [['sub_order_no.keyword' => $params['order_no']], ['uid' => $params['uid']], ['is_delete' => 0]],
                'limit' => 1,
            );
            $data = $es->getDocumentList($arr);
            if (isset($data['data'][0])) {
                #region 兼容
                $data['data'][0]['recharge_balance']        = $data['data'][0]['recharge_balance'] ?? 0;
                $data['data'][0]['bonus_balance']           = $data['data'][0]['bonus_balance'] ?? 0;
                $data['data'][0]['refund_recharge_balance'] = $data['data'][0]['refund_recharge_balance'] ?? 0;
                $data['data'][0]['refund_bonus_balance']    = $data['data'][0]['refund_bonus_balance'] ?? 0;
                $data['data'][0]['cash_amount']             = $data['data'][0]['cash_amount'] ?? ($data['data'][0]['payment_amount'] ?? 0);
                #endregion 兼容

                // 专题活动套餐价
                $activity_package_price = Db::name('sub_order_extend')
                    ->where('sub_order_no', $params['order_no'])
                    ->value('activity_package_price');
                if (!empty($activity_package_price) && $activity_package_price > 0) {
                    $data['data'][0]['package_price'] = $activity_package_price;
                }

                $encrypt         = cryptionDeal(2, [$data['data'][0]['consignee'], $data['data'][0]['consignee_phone']], $params['uid'], '前端用户');
                $consignee       = isset($encrypt[$data['data'][0]['consignee']]) ? $encrypt[$data['data'][0]['consignee']] : '';
                $consignee_phone = isset($encrypt[$data['data'][0]['consignee_phone']]) ? $encrypt[$data['data'][0]['consignee_phone']] : '';
                $receipt_time    = strtotime($data['data'][0]['delivery_time']) > 0 ? date('Y-m-d H:i:s', strtotime($data['data'][0]['delivery_time']) + env('ORDERS.receipt_time') * 86400) : date('Y-m-d H:i:s', strtotime($data['data'][0]['predict_time']) + env('ORDERS.receipt_time') * 86400);
                $status          = $data['data'][0]['sub_order_status'];
                //订单状态处理 订单状态 0-待支付 1-已支付 2-已发货 3-已完成 4-已取消 5-拼团中 6-已暂存 7-退款中 8-退款成功
                //group_status拼团状态：0-不拼团 1-拼团中 2-拼团成功 3-拼团失败
                if (isset($data['data'][0]['group_status']) && $data['data'][0]['group_status'] == 1 && $data['data'][0]['sub_order_status'] == 1) $status = 5;
                if (isset($data['data'][0]['is_ts']) && $data['data'][0]['is_ts'] == 1 && $data['data'][0]['sub_order_status'] == 1) $status = 6;
                if (isset($data['data'][0]['refund_status']) && $data['data'][0]['refund_status'] == 1) $status = 7;
                if (isset($data['data'][0]['refund_status']) && $data['data'][0]['refund_status'] == 2) $status = 8;

                if (strtotime($data['data'][0]['created_time']) >= 1724428800) {
                    $express_name = '快递包邮';
                    switch ($data['data'][0]['express_type']) {
                        case 3:
                            $express_name = '顺丰冷链';
                            break;
                        case 31:
                            $express_name = '顺丰温控包裹';
                            break;
                    }
                } else {
                    switch ($data['data'][0]['express_type']) {
                        case 2:
                            $express_name = '顺丰快递';
                            break;
                        case 3:
                            $express_name = '顺丰冷链';
                            break;
                        case 4:
                        case 5:
                            $express_name = '京东快递';
                            break;
                        case 31:
                            $express_name = '顺丰温控包裹';
                            break;
                        default:
                            $express_name = '其他快递';
                            break;
                    }
                }
                if ($data['data'][0]['order_type'] == 2 && isset($data['data'][0]['is_replace_pay']) && $data['data'][0]['is_replace_pay'] == 1) {
                    $countdown = env('ORDERS.replace_pay_time_out') - (time() - strtotime($data['data'][0]['created_time']));
                } else if (strpos($data['data'][0]['sub_order_no'], 'VHG') !== false) {
                    $countdown = intval(env('ORDERS.AFTER_SALES_ORDER_TIMEOUT')) * 60 - (time() - strtotime($data['data'][0]['created_time']));
                } else if (in_array($data['data'][0]['payment_method'], [10, 11])) {
                    $countdown = intval(env('ORDERS.transfer_pay_time_out')) - (time() - strtotime($data['data'][0]['created_time']));
                } else {
                    $liveCount = Db::table('vh_minilive.vh_goods')->where([['period_id', '=', $data['data'][0]['period']]])->count();
                    if ($liveCount > 0) {
                        $countdown = env('ORDERS.LIVE_ORDER_TIMEOUT') - (time() - strtotime($data['data'][0]['created_time']));
                    } else if (in_array($data['data'][0]['period'], ["142927", "143361"])) {
                        $countdown = env('ORDERS.SPECIAL_PAY_TIME_OUT') - (time() - strtotime($data['data'][0]['created_time']));
                    } else {
                        $countdown = env('ORDERS.pay_time_out') - (time() - strtotime($data['data'][0]['created_time']));
                    }
                }
                //发票名称处理
                $invoice_name = '';
                if (isset($data['data'][0]['invoice_progress']) && $data['data'][0]['invoice_progress'] > 0) {
                    $invoice_name = Db::name('invoice_record')->where(['sub_order_no' => $params['order_no']])->value('invoice_name');
                    $invoice_name = !empty($invoice_name) ? $invoice_name : '';
                }
                if (empty($data['data'][0]['package_price'])) $data['data'][0]['package_price'] = 0;
                //酒评类型处理
                $comment_type = 0;//默认不酒评
                $classify     = ['红葡萄酒', '干红葡萄酒', '半干红葡萄酒', '半甜红葡萄酒', '甜红葡萄酒', '白葡萄酒', '干白葡萄酒', '半干白葡萄酒', '半甜白葡萄酒', '甜白葡萄酒', '桃红葡萄酒', '干桃红葡萄酒', '半干桃红葡萄酒', '半甜桃红葡萄酒', '甜桃红葡萄酒', '起泡酒', '香槟', '加强酒', '利口酒', '橙酒', '自然酒'];
                $periodInfo   = $es->getDocumentList(['index' => ['periods'], 'match' => [['id' => $data['data'][0]['period']]], 'source' => ['id', 'product_category', 'product_main_category', 'latest_storage_time'], 'limit' => 1]);
                if (count($periodInfo['data']) > 0) {
                    if (in_array('酒类', $periodInfo['data'][0]['product_main_category'])) {
                        if (array_intersect($classify, $periodInfo['data'][0]['product_category']) != []) {
                            $comment_type = 1;//葡萄酒酒类
                        } else {
                            $comment_type = 2;//非葡萄酒酒类
                        }
                    }
                }
                //是否能抽奖处理 0-否 1-是
                $is_raffle = 0;
                if (in_array($data['data'][0]['sub_order_status'], [1, 2, 3]) && (($data['data'][0]['special_type'] ?? 0) != 1) ) {
                    $raffleInfo = httpGet(env('ITEM.MARKET_CONF_URL') . '/marketing-conf/v3/game/getUserOrderStatus', ['orderno' => $data['data'][0]['main_order_no']]);
                    if (isset($raffleInfo['data']) && $raffleInfo['data'] == true) $is_raffle = 1;
                }
                //售后状态处理
                $after_sale_status = 0;//默认不允许售后
                $forbidden_reason  = '';//不允许售后原因
                if (isset($data['data'][0]['is_after_sale']) && $data['data'][0]['is_after_sale'] == 1) {
                    $after_sale_status = 2;//显示售后详情
                } else if (in_array($data['data'][0]['sub_order_status'], [1, 2, 6]) && empty($data['data'][0]['related_order_no']) && $data['data'][0]['order_type'] != 4) {
                    if (($data['data'][0]['order_type'] == 2) && (($data['data'][0]['payment_doc'] ?? 0) == 1)) {
                        $forbidden_reason = '您的订单已向海关申报，由保税区安排发货中，详询客服！';//不允许售后原因
                    } elseif(Db::name('cross_split')->where('main_order_no', ($data['data'][0]['main_order_no'] ?? ''))->count()) {
                        $forbidden_reason = '组合套餐售后请联系客服！';//不允许售后原因
                    } else {
                        $after_sale_status = 1;//允许申请
                    }
                } else if ($data['data'][0]['sub_order_status'] == 3 && empty($data['data'][0]['related_order_no']) && $data['data'][0]['order_type'] != 4) {
                    if (strtotime($data['data'][0]['goods_receipt_time']) > time() - 30 * 86400 || strtotime($data['data'][0]['goods_receipt_time']) == 0) {
                        $after_sale_status = 1;//允许申请
                    } else {
                        $forbidden_reason = "确认收货已超30天";
                    }
                } else {
                    if ($data['data'][0]['sub_order_status'] == 0) $forbidden_reason = "订单未支付";
                    if ($data['data'][0]['sub_order_status'] == 4) $forbidden_reason = "订单已取消";
                    if ($data['data'][0]['sub_order_status'] == 5) $forbidden_reason = "订单拼团中";
                    if ($data['data'][0]['sub_order_status'] == 7) $forbidden_reason = "订单退款中";
                    if ($data['data'][0]['sub_order_status'] == 8) $forbidden_reason = "订单已退款完成";
                    if (!empty($data['data'][0]['related_order_no'])) $forbidden_reason = "售后订单";
                    if ($data['data'][0]['order_type'] == 4) $forbidden_reason = "兔头订单";
                }
                $ts_time = Db::name('order_ts')->where(['sub_order_no' => $data['data'][0]['sub_order_no']])->value('ts_time') ?? 0;
                if($ts_time > 0){
                    $ts_time = date('Y-m-d', $ts_time);
                }else{
                    $ts_time = '';
                }
                $result = array(
                    'order_no'               => $params['order_no'],
                    'status'                 => $status,
                    'payment_method'         => $data['data'][0]['payment_method'],
                    'payment_amount'         => $data['data'][0]['order_type'] == 4 ? $data['data'][0]['rabbit_payment_amount'] : $data['data'][0]['payment_amount'],
                    'cash_amount' => $data['data'][0]['order_type'] == 4 ? 0 : ($data['data'][0]['cash_amount'] ?? 0),
                    'recharge_balance' => $data['data'][0]['recharge_balance'],
                    'bonus_balance' => $data['data'][0]['bonus_balance'],
                    'created_time'           => $data['data'][0]['created_time'],
                    'countdown'              => $countdown <= 0 ? 0 : $countdown,
                    'receipt_time'           => $receipt_time,
                    'province_id'            => $data['data'][0]['province_id'],
                    'city_id'                => $data['data'][0]['city_id'],
                    'district_id'            => $data['data'][0]['district_id'],
                    'province_name'          => $data['data'][0]['province_name'],
                    'city_name'              => $data['data'][0]['city_name'],
                    'district_name'          => $data['data'][0]['district_name'],
                    'address'                => $data['data'][0]['address'],
                    'goods_price'            => bcmul($data['data'][0]['package_price'] , $data['data'][0]['order_qty'],2),
                    'express_fee'            => isset($data['data'][0]['express_fee']) ? $data['data'][0]['express_fee'] : 0,
                    'express_type'           => $data['data'][0]['express_type'],
                    'express_name'           => $express_name,
                    'express_number'         => $data['data'][0]['express_number'],
                    'money_off_value'        => isset($data['data'][0]['money_off_split_value']) ? $data['data'][0]['money_off_split_value'] : 0,
                    'coupon_value'           => isset($data['data'][0]['coupon_split_value']) ? $data['data'][0]['coupon_split_value'] : 0,
                    'preferential_reduction' => isset($data['data'][0]['preferential_reduction']) ? $data['data'][0]['preferential_reduction'] : 0,
                    'total_qty'              => $data['data'][0]['order_qty'],
                    'consignee'              => $consignee,
                    'consignee_phone'        => $consignee_phone,
                    'refund_status'          => isset($data['data'][0]['refund_status']) ? $data['data'][0]['refund_status'] : 0,
                    'refund_start_time'      => isset($data['data'][0]['refund_start_time']) ? $data['data'][0]['refund_start_time'] : '',
                    'refund_end_time'        => isset($data['data'][0]['refund_end_time']) ? $data['data'][0]['refund_end_time'] : '',
                    'refund_reject_time'     => isset($data['data'][0]['refund_reject_time']) ? $data['data'][0]['refund_reject_time'] : '',
                    'payment_time'           => $data['data'][0]['payment_time'],
                    'invoice_progress'       => isset($data['data'][0]['invoice_progress']) ? $data['data'][0]['invoice_progress'] : 0,
                    'invoice_id'             => isset($data['data'][0]['invoice_id']) ? $data['data'][0]['invoice_id'] : 0,
                    'invoice_name'           => $invoice_name,
                    'order_type'             => $data['data'][0]['order_type'],
                    'predict_time'           => date('Y-m-d', strtotime($data['data'][0]['predict_time'])),
                    'goods_receipt_time'     => $data['data'][0]['goods_receipt_time'],
                    'work_order_status'      => isset($data['data'][0]['work_order_status']) ? $data['data'][0]['work_order_status'] : 0,
                    'delivery_method'        => isset($data['data'][0]['delivery_method']) ? $data['data'][0]['delivery_method'] : 0,
                    'delivery_person_name'   => isset($data['data'][0]['delivery_person_name']) ? $data['data'][0]['delivery_person_name'] : '',
                    'delivery_person_phone'  => isset($data['data'][0]['delivery_person_phone']) ? $data['data'][0]['delivery_person_phone'] : '',
                    'is_replace_pay'         => isset($data['data'][0]['is_replace_pay']) ? $data['data'][0]['is_replace_pay'] : 0,
                    'goodsInfo'              => array(
                        array(
                            'period'        => $data['data'][0]['period'],
                            'package_id'    => $data['data'][0]['package_id'],
                            'goods_img'     => imagePrefix($data['data'][0]['banner_img']),
                            'goods_title'   => $data['data'][0]['title'],
                            'package_name'  => $data['data'][0]['package_name'],
                            'order_qty'     => $data['data'][0]['order_qty'],
                            'package_price' => (!empty($data['data'][0]['special_price']) && $data['data'][0]['special_price'] > 0) ? $data['data'][0]['special_price'] : $data['data'][0]['package_price'],
                            'periods_type'  => $data['data'][0]['order_type'],
                        )
                    ),
                    'comment_type'           => $comment_type,
                    'is_raffle'              => $is_raffle,
                    'warehouse_code'         => isset($data['data'][0]['warehouse_code']) ? $data['data'][0]['warehouse_code'] : '',
                    'after_sale_status'      => $after_sale_status,
                    'forbidden_reason'       => $forbidden_reason,
                    'ts_time'       => $ts_time,
                );
                if ($data['data'][0]['order_type'] == 9) {
                    $result['goodsInfo'][0]['label'] = 1;
                    if ($data['data'][0]['delivery_method'] == 1) $result['goodsInfo'][0]['label'] = 2;
                }
                if (in_array($result['payment_method'], [0, 1, 6])) {
                    $payment_method_name = '支付宝';
                } else if (in_array($result['payment_method'], [3, 4, 5, 7, 8])) {
                    $payment_method_name = '微信';
                } else if (in_array($result['payment_method'], [10, 11])) {
                    $payment_method_name = '对公转账';
                } else if ($result['payment_method'] == 2) {
                    $payment_method_name = '扫码支付';
                } else {
                    $config_payment_method = config('config')['payment_method'];//订单支付方式获取
                    $payment_method_name   = isset($config_payment_method[intval($result['payment_method']) + 1]['label']) ? $config_payment_method[intval($result['payment_method'] + 1)]['label'] : '';
                }
                $result['payment_name'] = $payment_method_name;
                //拼团信息处理
                if (isset($data['data'][0]['group_id']) && $data['data'][0]['group_id'] > 0) {
                    $result['groupInfo'] = [];
                    $groupInfo           = Db::name('order_group')->field('id,period,package_id,group_status,group_limit_nums,group_join_nums,group_status,over_time,complete_time,head_uid,member_uids')->where(['id' => $data['data'][0]['group_id']])->find();
                    if (!empty($groupInfo)) {
                        //剩余拼团时间
                        $remaining_time = $groupInfo['over_time'] - time();
                        if ($remaining_time <= 0) {
                            $group_remaining_time = '0小时0分钟';
                        } else {
                            $hour                 = intval($remaining_time / 3600);
                            $minute               = intval(($remaining_time / 3600 - $hour) * 60);
                            $group_remaining_time = $hour . '小时' . $minute . '分钟';
                        }
                        //拼团剩余人数
                        $group_last_num = $groupInfo['group_limit_nums'] - $groupInfo['group_join_nums'] <= 0 ? 0 : $groupInfo['group_limit_nums'] - $groupInfo['group_join_nums'];
                        //参与拼团的用户信息
                        $user_head_img = [];
                        $user_id_str   = $groupInfo['head_uid'];
                        $userInfo      = httpGet(env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo', ['uid' => $user_id_str, 'info_type' => 1]);
                        if ($userInfo['error_code'] == 0 && isset($userInfo['data']['list'][0]['avatar_image'])) {
                            $user_head_img[] = imagePrefix($userInfo['data']['list'][0]['avatar_image']);
                        }
                        if (!empty($groupInfo['member_uids'])) {
                            $userInfo = httpGet(env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo', ['uid' => $groupInfo['member_uids'], 'info_type' => 1]);
                            if ($userInfo['error_code'] == 0 && count($userInfo['data']['list']) > 0) {
                                foreach ($userInfo['data']['list'] as &$val) {
                                    $user_head_img[] = imagePrefix($val['avatar_image']);
                                }
                            }
                        }
                        $result['groupInfo'] = array(
                            'group_id'        => $groupInfo['id'],
                            'period'          => $groupInfo['period'],
                            'package_id'      => $groupInfo['package_id'],
                            'group_status'    => $groupInfo['group_status'],
                            'over_time'       => date('Y-m-d H:i:s', $groupInfo['over_time']),
                            'remaining_time'  => $group_remaining_time,
                            'countdown'       => $remaining_time,
                            'group_last_num'  => $group_last_num,
                            'user_head_img'   => $user_head_img,
                            'group_share_url' => env('ORDERS.group_share_url') . '拼团信息地址待完善'
                        );
                        //拼团失败退款进度处理
                        if ($groupInfo['group_status'] == 3) {
                            $refund_schedule[] = ['title' => '拼团失败', 'describe' => '您的订单拼团失败，系统稍后将为您退款', 'time' => date('Y-m-d H:i:s', $groupInfo['over_time'])];
                            $refund_order      = Db::name('refund_order')->field('refund_status,refund_amount,update_time,created_time')->where(['sub_order_no' => $data['data'][0]['sub_order_no']])->find();
                            if ($refund_order) {
                                $refund_schedule[] = ['title' => '退款中', 'describe' => '您的退款将于1-7个工作日退回您的支付账户', 'time' => date('Y-m-d H:i:s', $refund_order['created_time'])];
                                if ($refund_order['refund_status'] == 1) {
                                    $refund_schedule[] = ['title' => '退款成功', 'describe' => '¥' . $refund_order['refund_amount'] . '&nbsp;已成功退款至您的支付账户', 'time' => date('Y-m-d H:i:s', $refund_order['update_time'])];
                                } else if ($refund_order['refund_status'] == 2) {
                                    $refund_schedule[] = ['title' => '退款失败', 'describe' => '¥' . $refund_order['refund_amount'] . '&nbsp;退款失败请联系客服处理', 'time' => date('Y-m-d H:i:s', $refund_order['update_time'])];
                                }
                            } else {
                                $refund_schedule[] = ['title' => '退款中', 'describe' => '您的退款将于1-7个工作日退回您的支付账户', 'time' => date('Y-m-d H:i:s', time())];
                            }
                            $time = array_column($refund_schedule, 'time');
                            array_multisort($time, SORT_DESC, $refund_schedule);
                            $result['groupInfo']['refund_schedule'] = $refund_schedule;
                        }
                    }
                }
                //商家秒发处理
                if ($data['data'][0]['order_type'] == 9) {
                    //店铺信息
                    $result['storeInfo'] = [];
                    $storeInfo           = httpGet(env('ITEM.VMALL_URL') . '/vmall/v3/shops/detail', ['id' => $data['data'][0]['delivery_store_id']]);
                    if (isset($storeInfo['error_code']) && $storeInfo['error_code'] == 0) {
                        $result['storeInfo'] = $storeInfo['data'];
                    }
                    //配送方式
                    switch ($data['data'][0]['delivery_method']) {
                        case 1:
                            if ($data['data'][0]['express_type'] == 65) {
                                $result['express_name'] = $data['data'][0]['express_name'];
                            }
                            break;
                        case 2:
                            $result['express_name'] = '送货上门';
                            if (strtotime($data['data'][0]['predict_time']) > time() && time() + 3 * 60 * 60 > strtotime($data['data'][0]['predict_time'])) {
                                $result['predict_time'] = '三小时内';
                            } else {
                                $result['predict_time'] = $data['data'][0]['predict_time'];
                            }
                            break;
                        case 3:
                            $result['express_name']    = '到店自提';
                            $result['consignee']       = isset($data['data'][0]['delivery_consignee']) ? $data['data'][0]['delivery_consignee'] : $result['consignee'];
                            $result['consignee_phone'] = isset($data['data'][0]['delivery_consignee_phone']) ? $data['data'][0]['delivery_consignee_phone'] : $result['consignee_phone'];
                            break;
                    }
                }
                //对公转账信息
                if ($result['payment_method'] == 11) {
                    $esPeriodsData           = $es->getDocumentList(['index' => ['periods'], 'match' => [['id' => $result['goodsInfo'][0]['period']]], 'source' => ['payee_merchant_id'], 'limit' => 1]);
                    $payee_merchant_id       = isset($esPeriodsData['data'][0]['payee_merchant_id']) ? $esPeriodsData['data'][0]['payee_merchant_id'] : 2;
                    $collecting_company      = Db::name('collecting_company')->where(['id' => $payee_merchant_id])->find();
                    $result['transfer_info'] = array(
                        'transfer_name'       => isset($collecting_company['transfer_name']) ? $collecting_company['transfer_name'] : '',
                        'transfer_account'    => isset($collecting_company['transfer_account']) ? $collecting_company['transfer_account'] : '',
                        'transfer_bank'       => isset($collecting_company['transfer_bank']) ? $collecting_company['transfer_bank'] : '',
                        'transfer_interbank'  => isset($collecting_company['transfer_interbank']) ? $collecting_company['transfer_interbank'] : '',
                        'transfer_remittance' => $data['data'][0]['main_order_no'],
                    );
                }
                //是否支持升级冷链、暂存处理
                if (in_array($data['data'][0]['order_type'], [0, 1, 2, 3]) && in_array($status, [1, 6])) {
                    $result['is_support_upgrade_cold'] = 0;
                    $result['is_support_change_ts']    = 0;
                    $result['ts_err_msg']    = '请联系在线客服处理！';
                    $tst = Db::table('vh_commodities.vh_periods_ts_template')
                        ->whereBetweenTimeField('start_time', 'end_time')
                        ->where('is_enabled', 1)
                        ->find();
                    $result['tstime_is_enable'] = $tst['tstime_is_enable'] ?? 0;//ts_longtime 是否启用暂存最晚发货时间:0=否,1=是
                    $begin_storage_time = date('Y-m-d', strtotime('+1day'));//暂存开始时间
                    if (!empty($data['data'][0]['predict_time'])) {
                        $o_predict_time = date('Y-m-d', strtotime($data['data'][0]['predict_time']));
                        if (strtotime($o_predict_time) >= strtotime($begin_storage_time)) {
                            $begin_storage_time = $o_predict_time;
                        }
                    }
                    $result['begin_storage_time'] = $begin_storage_time;//暂存开始时间
                    $periodInfo                        = esGetOne($data['data'][0]['period'], 'vinehoo.periods');
                    $result['latest_storage_time'] = !empty($periodInfo['latest_storage_time']) ? $periodInfo['latest_storage_time'] : (!empty($tst['ts_longtime']) ? date('Y-m-d', $tst['ts_longtime']) : '');
                    $p_predict_time = date('Y-m-d', strtotime($data['data'][0]['predict_time']));
                    if (!empty($periodInfo) && !in_array($data['data'][0]['express_type'], [3, 31]) && $data['data'][0]['is_original_package'] == 0 && $status == 1) {
                        if ($periodInfo['is_cold_chain'] == 1 && Db::name('regional_fee')->where('rid',$result['city_id'])->where('type', 31)->count()) {
                            $difference = 9999;
                            $cross_courier_fee = Db::name('cross_sfll_area')->where(['regional_id' => $data['data'][0]['province_id']])->value('courier_fee');
                            if ($data['data'][0]['order_type'] == 2 && !empty($cross_courier_fee)) {
                                $difference                        = round($cross_courier_fee - $data['data'][0]['express_fee'], 2);
                                // 跨境订单不支持升级冷链:https://devops.aliyun.com/projex/project/8ab3146db7bf4a79876e3a5333/task#viewIdentifier=7f5f68b23b9da320b7b70a1f3b&openWorkitemIdentifier=73f062b86de125d19520e3dfc6
                                // $result['is_support_upgrade_cold'] = 1;
                                // $result['upgrade_cold_fee']        = $difference;
                            } else {
                                $month = date('m');
                                if (5 <= $month && $month < 11) {
                                    $packageInfo         = esGetOne($data['data'][0]['package_id'], 'vinehoo.periods_set');
                                    $associated_products = json_decode($packageInfo['associated_products'], true);
                                    $capacity            = 0;
                                    foreach ($associated_products as &$vv) {
                                        $productsCapacity = Db::table('vh_wiki.vh_products')->where(['id' => $vv['product_id']])->value('capacity');
                                        $capacity         += (int)$productsCapacity * $vv['nums'] * $data['data'][0]['order_qty'];
                                    }
                                    $freight                           = calcFreight($data['data'][0]['city_id'], $capacity);
                                    $difference                        = round($freight - $data['data'][0]['express_fee'], 2);
                                    $result['is_support_upgrade_cold'] = 1;
                                    $result['upgrade_cold_fee']        = $difference;
                                }
                            }
                        }
                    }
                    //暂存处理
                    if (!empty($periodInfo) && $data['data'][0]['is_ts'] == 0 && !empty($tst)) {
                        if ($periodInfo['is_support_ts'] == 1) {
                            $ts_template = Db::table('vh_commodities.vh_periods_ts_template')->where(['is_enabled' => 1])->find();
                            if (!empty($ts_template)) {
                                if ($ts_template['start_time'] < time() && $ts_template['end_time'] > time() && in_array($data['data'][0]['province_id'], explode(',', $ts_template['area'])) && in_array($periodInfo['product_category'][0], explode(',', $ts_template['category']))) {
                                    if (
                                        ($result['tstime_is_enable'] == 1 && strtotime($result['latest_storage_time'] ?? '1970-01-01') < strtotime($p_predict_time)) //期数时间小于预计发货时间
                                        || ($result['tstime_is_enable'] == 1 && (strtotime($p_predict_time) < time())) //期数时间小于 当前时间
                                    ){
                                        $result['is_support_change_ts'] = 0;
                                    }else{
                                        $result['is_support_change_ts'] = 1;
                                    }
                                }
                            }
                        }
                    }
                    if (
                        ($result['tstime_is_enable'] == 1 && strtotime($result['latest_storage_time'] ?? '1970-01-01') < strtotime($p_predict_time)) //期数时间小于预计发货时间
                        || ($result['tstime_is_enable'] == 1 && (strtotime($p_predict_time) < time())) //期数时间小于 当前时间
                    ){
                        $result['tstime_is_enable'] = 0;
                    }
                }
            }
        }
        
        $result['express_title'] = '温控包裹';
        return $result;
    }

    /**
     * Description:已售\已购按钮获取列表
     * Author: zrc
     * Date: 2021/12/10
     * Time: 14:58
     * @param $requestparams
     * @param int $page
     * @param int $limit
     * @return mixed
     */
    public function getSoldPurchasedOrderList($requestparams, $page = 1, $limit = 10)
    {
        // 用户属性：0-普通用户，1-新用户，2-无效用户，3-活跃用户，4-一级沉默用户，5-二级沉默用户，6-三级沉默用户
        $user_attribute_map = ['普通用户', '新用户', '无效用户', '活跃用户', '一级沉默用户', '二级沉默用户', '三级沉默用户'];

        $offset = ($page - 1) * $limit;
        $user_nums = $order_user = [];

        $params = $requestparams;
        $es     = new ElasticSearchService();
        if ($params['type'] == 1) {//已售
            if ($params['periods_type'] == 4) {
                $source = ['sub_order_no', 'payment_time', 'nickname', 'province_name', 'city_name', 'district_name', 'address', 'package_name', 'order_qty', 'package_price', 'rabbit_payment_amount', 'sub_order_status', 'uid'];
                $where  = [['period' => $params['period']]];
            } else if ($params['periods_type'] == 2 || $params['periods_type'] == 3) {
                $source = ['sub_order_no', 'payment_time', 'nickname', 'province_name', 'city_name', 'district_name', 'address', 'package_name', 'order_qty', 'package_price', 'express_fee', 'payment_amount', 'sub_order_status', 'uid'];
                $where  = [['period' => $params['period']]];
            } else {
                $source = ['sub_order_no', 'payment_time', 'nickname', 'province_name', 'city_name', 'district_name', 'address', 'package_name', 'order_qty', 'package_price', 'money_off_split_value', 'coupon_split_value', 'express_fee', 'payment_amount', 'sub_order_status', 'uid'];
                $where  = [['period' => $params['period']]];
            }
            $arr  = array(
                'index'  => ['orders'],
                'match'  => $where,
                'range'  => [['sub_order_status' => ['gt' => 0]], ['sub_order_status' => ['lt' => 4]]],
                'page'   => 1,
                'limit'  => 10000,
                'sort'   => [['created_time' => 'desc']],
                'source' => $source,
            );
            $data = $es->getDocumentList($arr);
            
            if (isset($data['data'])) {
                $order_nos = array_column($data['data'], 'sub_order_no');
                // 查询用户属性：0-普通用户，1-新用户，2-无效用户，3-活跃用户，4-一级沉默用户，5-二级沉默用户，6-三级沉默用户
                $user_attribute = Db::name('sub_order_extend')
                    ->whereIn('sub_order_no', $order_nos)
                    ->column('user_attribute', 'sub_order_no');

                foreach ($data['data'] as &$val) {
                    $val['user_attribute'] = $user_attribute[$val['sub_order_no']] ?? 0;
                    $user_attribute_name = $user_attribute_map[$val['user_attribute']] ?? '';

                    if (empty($val['user_attribute_name'])) {
                        $val['user_attribute_name'] = [$user_attribute_name];
                    } else if (!in_array($user_attribute_name, $val['user_attribute_name'])) {
                        $val['user_attribute_name'][] = $user_attribute_name;
                    }
                    $order_user[$val['uid']] = $val['user_attribute_name'];

                    
                    // 用户统计
                    $user_type = [100];
                    if (in_array($val['user_attribute'], [4, 5, 6])) {
                        $user_type[] = 4;
                    } else {
                        $user_type[] = $val['user_attribute'];
                    }
                    foreach ($user_type as $v1) {
                        if (empty($user_nums[$v1])) {
                            $user_nums[$v1] = [$val['uid']];
                        } else {
                            if (!in_array($val['uid'], $user_nums[$v1])) {
                                $user_nums[$v1][] = $val['uid'];
                            }
                        }
                    }

                    $val['address_info'][] = array(
                        'province_name' => $val['province_name'],
                        'city_name'     => $val['city_name'],
                        'district_name' => $val['district_name'],
                        'address'       => $val['address'],
                    );
                    unset($val['province_name']);
                    unset($val['city_name']);
                    unset($val['district_name']);
                    unset($val['address']);
                    $val['package_price'] = empty($val['package_price']) ? 0 : $val['package_price'];
                    $val['order_qty']   = empty($val['order_qty']) ? 1 : $val['order_qty'];
                    $val['goods_price'] = round($val['package_price'] * $val['order_qty'], 2);
                    unset($val['package_price']);
                    if ($params['periods_type'] == 4) {
                        $val['payment_amount'] = $val['rabbit_payment_amount'];
                        $val['express_fee']    = 0;
                    }
                }
            }

            $list = [];
            $totalNum = 0;
            if (!empty($params['sum_details'])) {//汇总明细
                // -1:总人数，1:默认用户，2:新用户，3:无效用户，4:活跃用户，5:沉默用户
                $sum_details = $params['sum_details'] == -1 ? 100 : $params['sum_details'] - 1;
                $uids = $user_nums[$sum_details] ?? [];
                if (!empty($uids)) {
                    $uids = implode(',', $uids);
                    $files = 'uid,avatar_image,nickname,created_time,reg_city,user_level';
                    $user_info = getUserInfoByUids($uids, $files);
                    $list = array_values($user_info);
                    $list = array_map(function ($val) use ($order_user) {
                        $val['user_attribute_name'] = $order_user[$val['uid']] ?? [];
                        return $val;
                    }, $list);
                    $totalNum = count($list);
                }

            } else {//订单列表
                $statistics = [
                    'total_users' => !empty($user_nums[100]) ? count($user_nums[100]) : 0,
                    'default_users' => !empty($user_nums[0]) ? count($user_nums[0]) : 0,
                    'new_users' => !empty($user_nums[1]) ? count($user_nums[1]) : 0,
                    'invalid_users' => !empty($user_nums[2]) ? count($user_nums[2]) : 0,
                    'active_users' => !empty($user_nums[3]) ? count($user_nums[3]) : 0,
                    'silent_users' => !empty($user_nums[4]) ? count($user_nums[4]) : 0,
                ];
                $totalNum = $data['total']['value'];
                $result['statistics'] = $statistics;
                $list = $data['data'] ?? [];
            }
            
            $result['list']  = array_slice($list, $offset, $limit);
            $result['total'] = $totalNum;
            return $result;
        } else {//已购
            if ($params['periods_type'] == 4) {
                $source = ['sub_order_no', 'uid', 'package_id', 'nickname', 'province_name', 'city_name', 'district_name', 'address', 'order_qty', 'package_price', 'rabbit_payment_amount'];
                $where  = [['period' => $params['period']]];
            } else if ($params['periods_type'] == 2 || $params['periods_type'] == 3) {
                $source = ['sub_order_no', 'uid', 'package_id', 'nickname', 'province_name', 'city_name', 'district_name', 'address', 'order_qty', 'package_price', 'express_fee', 'payment_amount'];
                $where  = [['period' => $params['period']]];
            } else {
                $source = ['sub_order_no', 'uid', 'package_id', 'nickname', 'province_name', 'city_name', 'district_name', 'address', 'order_qty', 'package_price', 'money_off_split_value', 'coupon_split_value', 'express_fee', 'payment_amount'];
                $where  = [['period' => $params['period']]];
            }
            $arr  = array(
                'index'  => ['orders'],
                'match'  => $where,
                'range'  => [['sub_order_status' => ['gt' => 0]], ['sub_order_status' => ['lt' => 4]]],
                'source' => $source,
                'page'   => 1,
                'limit'  => 10000,
            );
            $data = $es->getDocumentList($arr);
            $list = [];
            $new_user_nums = 0;
            $user_info = [];
            if (!empty($data['data'])) {
                $uids = array_values(array_unique(array_column($data['data'], 'uid')));
                // 查询用户首单
                $FirstOrder = $this->getUserFirstOrder($uids);
                $uids = array_unique(array_column($data['data'], 'uid'));
                $files = 'uid,avatar_image,nickname,created_time,reg_city,user_level,user_attribute';
                $user_info = getUserInfoByUidsBatches($uids, $files);
                // 查询订单用户属性
                $sub_order_nos = array_column($data['data'], 'sub_order_no');
                $user_attribute = Db::name('sub_order_extend')
                    ->whereIn('sub_order_no', $sub_order_nos)
                    ->column('user_attribute','sub_order_no');


                foreach ($data['data'] as &$val) {
                    $s_user = $user_info[$val['uid']] ?? [];
                    $val['user_attribute'] = $user_attribute[$val['sub_order_no']] ?? 0;
                    $user_attribute_name = $user_attribute_map[$val['user_attribute']] ?? '';
                    if (empty($s_user['user_attribute_name'])) {
                        $s_user['user_attribute_name'] = [$user_attribute_name];
                    } else if (!in_array($user_attribute_name, $s_user['user_attribute_name'])) {
                        $s_user['user_attribute_name'][] = $user_attribute_name;
                    }
                    $user_info[$val['uid']] = $s_user;

                    // 用户统计
                    $user_type = [100];
                    if (in_array($val['user_attribute'], [4, 5, 6])) {
                        $user_type[] = 4;
                    } else {
                        $user_type[] = $val['user_attribute'];
                    }
                    foreach ($user_type as $v1) {
                        if (empty($user_nums[$v1])) {
                            $user_nums[$v1] = [$val['uid']];
                        } else {
                            if (!in_array($val['uid'], $user_nums[$v1])) {
                                $user_nums[$v1][] = $val['uid'];
                            }
                        }
                    }

                    if ($params['periods_type'] == 4) {
                        $val['payment_amount'] = $val['rabbit_payment_amount'];
                        $val['express_fee']    = 0;
                    }
                    $val['package_price'] = empty($val['package_price']) ? 0 : $val['package_price'];
                    $val['order_qty']   = empty($val['order_qty']) ? 1 : $val['order_qty'];

                    if (isset($list[$val['uid']])) {
                        if (!in_array(['province_name' => $val['province_name'], 'city_name' => $val['city_name'], 'district_name' => $val['district_name'], 'address' => $val['address']], $list[$val['uid']]['address_info'])) {
                            $list[$val['uid']]['address_info'][] = array(
                                'province_name' => $val['province_name'],
                                'city_name'     => $val['city_name'],
                                'district_name' => $val['district_name'],
                                'address'       => $val['address'],
                            );
                        }
                        $list[$val['uid']]['order_qty']       += $val['order_qty'];
                        $list[$val['uid']]['goods_price']     += $val['package_price'] * $val['order_qty'];
                        $list[$val['uid']]['goods_price']     = round($list[$val['uid']]['goods_price'], 2);
                        $list[$val['uid']]['money_off_value'] += isset($val['money_off_split_value']) ? $val['money_off_split_value'] : 0;
                        $list[$val['uid']]['coupon_value']    += isset($val['coupon_split_value']) ? $val['coupon_split_value'] : 0;
                        $list[$val['uid']]['payment_amount']  += $val['payment_amount'];
                        
                        if (!in_array($user_attribute_name, $list[$val['uid']]['user_attribute_name'])) {
                            $list[$val['uid']]['user_attribute_name'][] = $user_attribute_name;
                        }
                        
                    } else {
                        // 新购用户
                        $is_new_user = 0;
                        $first_orders = $FirstOrder[$val['uid']] ?? [];
                        $first_order_period = array_values(array_column($first_orders, 'period'));
                        if (in_array($params['period'], $first_order_period)) {
                            $is_new_user = 1;
                            $new_user_nums += 1;
                        }

                        $list[$val['uid']] = array(
                            'uid'             => $val['uid'],
                            'nickname'        => $val['nickname'],
                            'address_info'    => [[
                                'province_name' => $val['province_name'],
                                'city_name'     => $val['city_name'],
                                'district_name' => $val['district_name'],
                                'address'       => $val['address'],
                            ]],
                            'order_qty'       => $val['order_qty'],
                            'goods_price'     => $val['package_price'] * $val['order_qty'],
                            'money_off_value' => isset($val['money_off_split_value']) ? $val['money_off_split_value'] : 0,
                            'coupon_value'    => isset($val['coupon_split_value']) ? $val['coupon_split_value'] : 0,
                            'express_fee'     => $val['express_fee'],
                            'payment_amount'  => $val['payment_amount'],
                            'is_new_user'     => $is_new_user,
                            'user_attribute' => $val['user_attribute'],
                            'user_attribute_name' => [$user_attribute_name],
                            'user_level' => $s_user['user_level'] ?? 0,
                        );
                    }
                    $list[$val['uid']]['payment_amount'] = round($list[$val['uid']]['payment_amount'], 2);
                }
                unset($val);
            }

            if (!empty($params['sum_details'])) {//汇总明细
                // -1:总人数，1:默认用户，2:新用户，3:无效用户，4:活跃用户，5:沉默用户
                $sum_details = $params['sum_details'] == -1 ? 100 : $params['sum_details'] - 1;
                $uids = $user_nums[$sum_details] ?? [];
                $list = [];
                if (!empty($uids)) {
                    $list = [];
                    foreach ($uids as $uid) {
                        $list[] = $user_info[$uid];
                    }
                    // $list = array_map(function ($val) use ($order_user) {
                    //     $val['user_attribute'] = $order_user[$val['uid']] ?? 0;
                    //     return $val;
                    // }, $list);
                }
                
            } else {
                $statistics = [
                    'total_users' => !empty($user_nums[100]) ? count($user_nums[100]) : 0,
                    'default_users' => !empty($user_nums[0]) ? count($user_nums[0]) : 0,
                    'new_users' => !empty($user_nums[1]) ? count($user_nums[1]) : 0,
                    'invalid_users' => !empty($user_nums[2]) ? count($user_nums[2]) : 0,
                    'active_users' => !empty($user_nums[3]) ? count($user_nums[3]) : 0,
                    'silent_users' => !empty($user_nums[4]) ? count($user_nums[4]) : 0,
                ];
                $result['statistics'] = $statistics;
                $result['new_user_nums'] = $new_user_nums;
            }
            
            $result['list']  = array_slice($list, $offset, $limit);
            $result['total'] = count($list);
            return $result;
        }
    }

    /**
     * Description:查询用户首单（如果是购物车下单，第一个订单里面的几个商品都算进去，因此要以主订单为标准）
     * Author: gangh
     * Date: 2024/03/14
     * @param $requestparams
     * @param int $page
     * @param int $limit
     * @return mixed
     */
    public function getUserFirstOrder($uids)
    {
        $data = Es::name('orders')->execute([
            'size' => 0,
            'query' => [
                'bool' => [
                    'filter' => [
                        ['terms' => ['uid' => $uids]],
                        ['terms' => ['sub_order_status' => [1, 2, 3]]],
                    ],
                ]
            ],
            'aggs'  => [
                'first_order' => [
                    'terms' => ['field' => 'uid', 'size' => 10000],
                    'aggs' => [
                        'first_order_id' => [
                            'top_hits' => [
                                'size' => 1,
                                '_source' => ['id', 'uid', 'main_order_id'],
                                'sort' => [['created_time' => 'asc']],
                            ],
                        ],
                    ],
                ],
            ],
        ]);
        $main_order_id = [];
        if (!empty($data['aggregations']['first_order']['buckets'])) {
            foreach ($data['aggregations']['first_order']['buckets'] as $v) {
                $info = $v['first_order_id']['hits']['hits'][0]['_source'] ?? [];
                if (!empty($info['main_order_id'])) {
                    $main_order_id[] = $info['main_order_id'];
                }
            }
        }
        if (empty($main_order_id)) {
            return [];
        }

        $main_order_id = array_values(array_unique($main_order_id));
        $data = Es::name('orders')
            ->where([
                ['main_order_id', 'in', $main_order_id],
                ['sub_order_status', 'in', [1, 2, 3]]
            ])
            ->field('sub_order_no,uid,main_order_id,period,package_id,main_order_no')
            ->select()->toArray();
        $result = [];
        foreach ($data as $v) {
            $result[strval($v['uid'])][] = [
                'sub_order_no' => strval($v['sub_order_no']),
                'main_order_id' => intval($v['main_order_id']),
                'period' => intval($v['period']),
                'package_id' => intval($v['package_id']),
                'main_order_no' => strval($v['main_order_no']),
            ];
        }

        return $result;
    }

}