<?php
declare (strict_types=1);

namespace app\service\command;

use app\service\Cross as CrossService;
use GuzzleHttp\Exception\RequestException;
use think\facade\Db;
use think\facade\Log;

/**
 * CrossCardnoYearMoneyCommand
 * Class CrossCardnoYearMoneyCommand
 * @package app\service\command
 */
class CrossCardnoYearMoneyCommand
{
    public function exec()
    {
        return true;
        $todaytime = strtotime(date('Y-m-d'));
        $yesterdaytime = strtotime(date('Y-m-d',strtotime('-1 day')));
        $nextyeartime = strtotime(date('Y-1-1',strtotime('+1 year')));
        $year = date('Y', time());
        //每年第一天不累计金额
        if($todaytime == $nextyeartime){
            return true;
        }
        //查询前一天跨境支付订单
        $orderData = Db::name('cross_order')->field('id_card_no,payment_amount')->where("created_time>='{$yesterdaytime}' and created_time<'{$todaytime}' and (sub_order_status=1 or sub_order_status=2 or sub_order_status=3)")->select()->toArray();
        if($orderData){
            foreach($orderData as &$val){
                //身份证解密
                $encrypt = cryptionDeal(2,[$val['id_card_no']],'15736175219','宗仁川');
                $val['id_card_no'] = isset($encrypt[$val['id_card_no']])?$encrypt[$val['id_card_no']]:'';
                if(!empty($val['id_card_no'])){
                    $record = Db::name('cross_year_amount')->where(['id_card_no'=>$val['id_card_no']])->find();
                    if($record){
                        if($record['year'] == $year){
                            $newmoney = $val['payment_amount']+$record['year_money'];
                            if($newmoney>=26000){
                                $quotaParams = array(
                                    'type'=>1,
                                    'id_card_no'=>$val['id_card_no'],
                                    'note'=>'跨境自然年身份证消费金额满2.6W元定时任务录入'
                                );
                                $crossService = new CrossService();
                                $crossService->inputBlackList($quotaParams);
                            }
                            Db::name('cross_year_amount')->where(['id_card_no'=>$val['id_card_no']])->update(['year_money'=>$newmoney,'update_time'=>time()]);
                        }else{
                            Db::name('cross_year_amount')->where(['id_card_no'=>$val['id_card_no']])->update(['year_money'=>$val['payment_amount'],'year'=>$year,'update_time'=>time()]);
                        }
                    }else{
                        Db::name('cross_year_amount')->insert(['id_card_no'=>$val['id_card_no'],'year_money'=>$val['payment_amount'],'year'=>$year,'update_time'=>time()]);
                    }
                }
            }
        }
        return true;
    }
}
