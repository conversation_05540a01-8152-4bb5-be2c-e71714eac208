<?php


namespace app\controller;


use app\BaseController;
use app\ErrorCode;
use app\model\CrossLockOrder;
use app\Request;
use app\service\es\Es;
use app\service\Push as PushService;
use think\facade\Db;
use think\facade\Validate;

class Push extends BaseController
{
    /**
     * Description:获取订单推送萌牙/T+数据
     * Author: zrc
     * Date: 2022/4/7
     * Time: 17:24
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function getOrderPushData(Request $request)
    {
        $params = $request->param();
        if (empty($params['sub_order_no'])) $this->throwError('未获取到子订单号', ErrorCode::PARAM_ERROR);
        if (!isset($params['order_type'])) $this->throwError('未获取到订单类型', ErrorCode::PARAM_ERROR);
        if (!in_array($params['order_type'], [0, 1, 2, 3, 4, 7, 8, 9, 11])) $this->throwError('订单类型错误', ErrorCode::PARAM_ERROR);
        $pushService = new PushService();
        $result      = $pushService->getOrderPushData($params);
        return $this->success($result);
    }

    /**
     * Description:订单推送萌牙
     * Author: zrc
     * Date: 2022/4/28
     * Time: 11:39
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function pushWms(Request $request)
    {
        $params = $request->param();
        if (empty($params['sub_order_no'])) $this->throwError('未获取到子订单号', ErrorCode::PARAM_ERROR);
        $pushService = new PushService();
        $result      = $pushService->pushWms($params);
        return $this->success($result);
    }

    /**
     * Description:订单推送T+
     * Author: zrc
     * Date: 2022/5/5
     * Time: 16:15
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function pushTplus(Request $request)
    {
        $params = $request->param();
        if (empty($params['sub_order_no'])) $this->throwError('未获取到子订单号', ErrorCode::PARAM_ERROR);
        $pushService = new PushService();
        $result      = $pushService->pushTplus($params);
        return $this->success($result);
    }

    /**
     * Description:跨境南沙仓清关状态回传
     * Author: zrc
     * Date: 2022/4/24
     * Time: 11:33
     * @param Request $request
     * @return array|false|string
     * @throws \think\db\exception\DbException
     */
    public function borderStatusPushBack(Request $request)
    {
        $params = $request->param();
        if (empty($params)) return json_encode(['status' => 2, 'notes' => '未获取到回调参数'], JSON_UNESCAPED_UNICODE);
        $pushService = new PushService();
        $result      = $pushService->borderStatusPushBack($params);
        return $result;
    }

    /**
     * Description:跨境南沙仓物流信息回推接口
     * Author: zrc
     * Date: 2022/5/5
     * Time: 9:01
     * @param Request $request
     * @return false|int[]|string
     * @throws \think\db\exception\DbException
     */
    public function logisticsBack(Request $request)
    {
        $params = $request->param();
        if (empty($params)) return json_encode(['status' => 2, 'notes' => '未获取到回调参数'], JSON_UNESCAPED_UNICODE);
        $pushService = new PushService();
        $result      = $pushService->logisticsBack($params);
        return $result;
    }

    /**
     * Description:跨境订单推送代发仓
     * Author: zrc
     * Date: 2022/5/5
     * Time: 15:34
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function crossPushWarehouse(Request $request)
    {
        $params = $request->param();
        if (empty($params['main_order_no'])) $this->throwError('未获取到主订单号', ErrorCode::PARAM_ERROR);
        $lock_order = CrossLockOrder::where([
            'main_order_no' => $params['main_order_no'],
            'status'        => 1,
        ])->find();
        if ($lock_order) {
            $this->throwError('该订单已锁定,锁定原因:'.($lock_order['remark'] ?? ''), ErrorCode::PARAM_ERROR);
        }
        $pushService = new PushService();
        try {
            //查询订单详情
            $orderInfo = $pushService->crossGetOrderInfo($params['main_order_no']);
            //订单验证
            $pushService->checkCrossOrder($orderInfo);
        } catch (\Exception $e) {
            $content   = "## 跨境推单异常提示\n";
            $content   .= "-主订单号：" . $params['main_order_no'] . "\n";
            $content   .= "-异常原因：" . $e->getMessage() . "\n";
            $data      = array(
                'title' => '跨境推单异常通知',
                'text'  => $content
            );
            $queueData = array(
                'access_token' => env('ORDERS.cross_token'),
                'type'         => 'markdown',
                'at'           => '',
                'content'      => base64_encode(json_encode($data, true)),
            );
            $data      = base64_encode(json_encode($queueData, true));
            $pushData  = array(
                'exchange_name' => 'dingtalk',
                'routing_key'   => 'dingtalk_sender',
                'data'          => $data,
            );
            httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData, true));
            $this->throwError($e->getMessage());
        }
        $params['order_info'] = $orderInfo;

        $is_channel = Db::table('vh_commodities.vh_periods_cross')->where('id', $orderInfo['period'])->value('is_channel');
        $excess_num = $excess_id = 0;
        if ($is_channel == 1) {
            //锁定数量
            $excess = Db::name('cross_excess')
                ->where([
                    ['goods_barcode', '=', $orderInfo['goods_barcode']],
                    ['store_type', '=', $orderInfo['store_type']],
                    ['status', '=', 1],
                ])->column('id,num');

            $excess_id  = $excess[0]['id'] ?? 0;
            $excess_num = array_sum(array_column($excess, 'num'));
        }
//        $params['excess_id'] = $excess_id;

        $result               = [];
        if ($orderInfo['store_type'] == 1) {
            $result = $pushService->crossPushGuSiTi($params);
        } else if ($orderInfo['store_type'] == 2) {
            $result = $pushService->crossPushNanSha($params);
        }
        return $this->success($result);
    }

    /**
     * Description:跨境支付单推送
     * Author: zrc
     * Date: 2022/5/6
     * Time: 14:37
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function customsDeclare(Request $request)
    {
        $params = $request->param();
        if (empty($params['main_order_no'])) $this->throwError('未获取到主订单号', ErrorCode::PARAM_ERROR);
        $params['payment_subject'] = 0;
        $params['payment_subject'] = Db::name('order_main')->where(['main_order_no' => $params['main_order_no']])->value('payment_subject');
        $pushService               = new PushService();
        if (in_array($params['payment_subject'], [3, 4])) {//支付宝微信
            $result = $pushService->customsDeclare($params);
        } else if ($params['payment_subject'] < 3) {//银联
            $result = $pushService->UnionPayCustomsDeclare($params);
        }

        return $this->success($result);
    }

    /**
     * Description:支付信息海关申报回调-银联
     * Author: zrc
     * Date: 2022/5/9
     * Time: 10:09
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function declareNotify(Request $request)
    {
        $str = file_get_contents('php://input');
        file_put_contents(root_path() . "/runtime/log/" . date('Ym') . "/" . date('Ymd') . 'declareNotify' . '.log', '支付信息海关申报回调参数:' . $str . PHP_EOL, FILE_APPEND);
        parse_str($str, $params);
        if (empty($params)) $this->throwError('未获取到回调参数', ErrorCode::PARAM_ERROR);
        $pushService = new PushService();
        $result      = $pushService->declareNotify($params);
        return $this->success($result);
    }

    /**
     * Description:后台萌牙发货-订单号/期数推萌牙
     * Author: zrc
     * Date: 2022/5/9
     * Time: 16:12
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function wmsShip(Request $request)
    {
        $params             = $request->param();
        $params['operator'] = $request->header('vinehoo-uid');
        //数据验证
        $validate = Validate::rule([
            'type|类型'              => 'require|in:1,2',
            'orderOrPeriod|订单号/期数' => 'require'
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $pushService = new PushService();
        $result      = $pushService->wmsShip($params);
        return $this->success($result);
    }

    /**
     * Description:获取后台期数批量推送萌牙回执信息
     * Author: zrc
     * Date: 2022/7/26
     * Time: 14:58
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function getWmsPeriodPushReceipt(Request $request)
    {
        $params = $request->param();
        //数据验证
        $validate = Validate::rule([
            'orderOrPeriod|期数' => 'require'
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $pushService = new PushService();
        $result      = $pushService->getWmsPeriodPushReceipt($params);
        return $this->success($result);
    }

    /**
     * Description:后台恢复销售单-订单号/期数推T+
     * Author: zrc
     * Date: 2022/5/9
     * Time: 17:45
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function resumeSalesOrder(Request $request)
    {
        $params             = $request->param();
        $params['operator'] = $request->header('vinehoo-uid');
        //数据验证
        $validate = Validate::rule([
            'type|类型'              => 'require|in:1,2',
            'orderOrPeriod|订单号/期数' => 'require'
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $pushService = new PushService();
        $result      = $pushService->resumeSalesOrder($params);
        return $this->success($result);
    }

    /**
     * Description:获取后台期数批量推送T+回执信息
     * Author: zrc
     * Date: 2022/7/26
     * Time: 14:58
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function getErpPeriodPushReceipt(Request $request)
    {
        $params = $request->param();
        //数据验证
        $validate = Validate::rule([
            'orderOrPeriod|期数' => 'require'
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $pushService = new PushService();
        $result      = $pushService->getErpPeriodPushReceipt($params);
        return $this->success($result);
    }

    /**
     * Description:订单批量推送代发仓
     * Author: zrc
     * Date: 2022/5/9
     * Time: 18:02
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function batchCrossPushWarehouse(Request $request)
    {
        $params = $request->param();
        if (empty($params['file'])) $this->throwError('缺少上传内容', ErrorCode::PARAM_ERROR);
        //获取文件路径
        $path   = env('ALIURL') . $params['file'];
        $startI = 1;
        #下载文件
        $local_path = download_image($path, 'xls');
        #解析文件
        $data = getExcelData($local_path, $startI);
        @unlink($local_path);
        if ($data['error_code'] != 0) $this->throwError('excel解析失败');
        if (count($data['data']) == 0) $this->throwError('未获取到excel内容');
        $excelData = $data['data'];
        if ($excelData[$startI][0] != '订单号') {
            $this->throwError('格式有误，请下载模板操作');
        }
        unset($excelData[$startI]);
        $pushService = new PushService();
        $result      = $pushService->batchCrossPushWarehouse($excelData);
        return $this->success($result);
    }

    /**
     * Description:订单批量推送支付单
     * Author: zrc
     * Date: 2022/5/10
     * Time: 9:19
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function batchCustomsDeclare(Request $request)
    {
        $params = $request->param();
        if (empty($params['file'])) $this->throwError('缺少上传内容', ErrorCode::PARAM_ERROR);
        //获取文件路径
        $path   = env('ALIURL') . $params['file'];
        $startI = 1;
        #下载文件
        $local_path = download_image($path, 'xls');
        #解析文件
        $data = getExcelData($local_path, $startI);
        @unlink($local_path);
        if ($data['error_code'] != 0) $this->throwError('excel解析失败');
        if (count($data['data']) == 0) $this->throwError('未获取到excel内容');
        $excelData = $data['data'];
        if ($excelData[$startI][0] != '订单号') {
            $this->throwError('格式有误，请下载模板操作');
        }
        unset($excelData[$startI]);
        $pushService = new PushService();
        $result      = $pushService->batchCustomsDeclare($excelData);
        return $this->success($result);
    }

    /**
     * Description:跨境订单批量直推
     * Author: zrc
     * Date: 2022/7/11
     * Time: 13:19
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function batchCustomsDirectPush(Request $request)
    {
        $params = $request->param();
        if (empty($params['file'])) $this->throwError('缺少上传内容', ErrorCode::PARAM_ERROR);
        //获取文件路径
        $path   = env('ALIURL') . $params['file'];
        $startI = 1;
        #下载文件
        $local_path = download_image($path, 'xls');
        #解析文件
        $data = getExcelData($local_path, $startI);
        @unlink($local_path);
        if ($data['error_code'] != 0) $this->throwError('excel解析失败');
        if (count($data['data']) == 0) $this->throwError('未获取到excel内容');
        $excelData = $data['data'];
        if ($excelData[$startI][0] != '订单号') {
            $this->throwError('格式有误，请下载模板操作');
        }
        unset($excelData[$startI]);
        $pushService = new PushService();
        $result      = $pushService->batchCustomsDirectPush($excelData);
        return $this->success($result);
    }

    /**
     * Description:跨境直推接口（支付单+代发仓）
     * Author: zrc
     * Date: 2022/6/8
     * Time: 16:07
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function customsDirectPush(Request $request)
    {
        $params = $request->param();
        if (empty($params['main_order_no'])) $this->throwError('未获取到主订单号', ErrorCode::PARAM_ERROR);
        $pushService = new PushService();
        $result      = $pushService->customsDirectPush($params);
        return $this->success($result);
    }

    /**
     * Description:萌牙/T+推送成功结果处理
     * Author: zrc
     * Date: 2022/6/29
     * Time: 17:04
     * @param Request $request
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     */
    public function pushSuccessDeal(Request $request)
    {
        $params = $request->param();
        //验证参数
        $validate = Validate::rule([
            'type|类型'          => 'require|in:1,2,3,4',
            'order_no_str|订单号' => 'require',
            'order_type|订单类型'  => 'require'
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        if (isset($params['push_status']) && is_numeric($params['push_status'])) {
            if (empty($params['remarks'])) $this->throwError('备注必填', ErrorCode::PARAM_ERROR);
            if (!in_array($params['push_status'], [0, 1, 2, 3])) $this->throwError('推送状态取值范围为0到3', ErrorCode::PARAM_ERROR);
        }
        $pushService = new PushService();
        $result      = $pushService->pushSuccessDeal($params);
        return $this->success($result);
    }

    /**
     * Description:贵重物品信息
     * Author: gangh
     * Date: 2024/12/4
     * @param Request $request
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     */
    public function valuableInfo(Request $request)
    {
        $params = $request->param();
        $pushService = new PushService();
        $result      = $pushService->valuableInfo($params);
        return $this->success($result);
    }

    /**
     * Description:设置贵重物品信息
     * Author: gangh
     * Date: 2024/12/4
     * @param Request $request
     * @return \think\response\Json
     * @throws \think\db\exception\DbException
     */
    public function setValuableInfo(Request $request)
    {
        $params = $request->param();
        //验证参数
        $validate = Validate::rule([
            'amount|金额'                => 'float|>=:0',
            'sub_order_amount|子订单金额' => 'float|>=:0',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $pushService = new PushService();
        $result      = $pushService->setValuableInfo($params);
        return $this->success();
    }
}