<?php


namespace app\model;


use app\service\elasticsearch\ElasticSearchService;
use think\facade\Db;
use think\Model;

class Rabbit extends Model
{
    protected $name = 'rabbit_order';

    protected function getTableName()
    {
        return Db::name($this->name);
    }

    /**
     * Description: 获取订单列表
     * Author: zrc
     * Date: 2021/8/4
     * Time: 14:24
     * @param $params
     * @param int $page
     * @param int $limit
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getOrderList($params, $page = 1, $limit = 10)
    {
        $where[] = ['order_type' => 4];
        $range   = [];
        $match_phrase   = [];
        if (!empty($params['order_no'])) {
            if (strpos($params['order_no'], 'VHM') !== false || strpos($params['order_no'], 'WYM') !== false) {
                $where[] = ['main_order_no' => $params['order_no']];
            } else {
                $where[] = ['sub_order_no.keyword' => $params['order_no']];
            }
        }
        if (isset($params['order_status']) && is_numeric($params['order_status'])) {
            $where[] = ['sub_order_status' => $params['order_status']];
        }
        if (isset($params['refund_status']) && is_numeric($params['refund_status'])) {
            $where[] = ['refund_status' => $params['refund_status']];
        }
        if (!empty($params['period'])) {
            $where[] = ['period' => $params['period']];
        }
        if (!empty($params['title'])) {
            $where[] = ['title' => $params['title']];
        }
        if (!empty($params['nickname'])) {
            $where[] = ['nickname' => $params['nickname']];
        }
        if (!empty($params['consignee'])) {
            //收件人加密查询
            $encrypt             = cryptionDeal(1, [$params['consignee']], '15736175219', '宗仁川');
            $params['consignee'] = isset($encrypt[$params['consignee']]) ? $encrypt[$params['consignee']] : '';
            $where[]             = ['consignee' => $params['consignee']];
        }
        if (!empty($params['consignee_phone'])) {
            //收件人加密查询
            $encrypt                   = cryptionDeal(1, [$params['consignee_phone']], '15736175219', '宗仁川');
            $params['consignee_phone'] = isset($encrypt[$params['consignee_phone']]) ? $encrypt[$params['consignee_phone']] : '';
            $where[]                   = ['consignee_phone' => $params['consignee_phone']];
        }
        if (isset($params['express_type']) && is_numeric($params['express_type'])) {
            $where[] = ['express_type' => $params['express_type']];
        }
        if (!empty($params['express_number'])) {
            $where[] = ['express_number' => $params['express_number']];
        }
        if (isset($params['order_from']) && is_numeric($params['order_from'])) {
            $where[] = ['order_from' => $params['order_from']];
        }
        if (isset($params['payment_method']) && is_numeric($params['payment_method'])) {
            $where[] = ['payment_method' => $params['payment_method']];
        }
        if (isset($params['is_ts']) && is_numeric($params['is_ts'])) {
            $where[] = ['is_ts' => $params['is_ts']];
        }
        if (isset($params['invoice_progress']) && is_numeric($params['invoice_progress'])) {
            $where[] = ['invoice_progress' => $params['invoice_progress']];
        }
        if (!empty($params['stime'])) {
            $range[] = ['created_time' => ['gte' => $params['stime']]];
        }
        if (!empty($params['etime'])) {
            $range[] = ['created_time' => ['lte' => $params['etime']]];
        }
        $order           = [['created_time' => 'desc']];
        $es              = new ElasticSearchService();
        $arr             = array(
            'index' => ['orders'],
            'match' => $where,
            'match_phrase' => $match_phrase,
            'range' => $range,
            'page'  => $page,
            'limit' => $limit,
            'sort'  => $order
        );
        $data            = $es->getDocumentList($arr);
        $totalNum        = $data['total']['value'];
        $result['list']  = $data['data'];
        $result['total'] = $totalNum;
        return $result;
    }
}