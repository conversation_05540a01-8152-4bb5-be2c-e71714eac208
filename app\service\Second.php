<?php


namespace app\service;


use app\BaseService;
use app\ErrorCode;
use app\model\Second as SecondModel;
use think\facade\Db;

class Second extends BaseService
{
    /**
     * Description:秒发创建订单
     * Author: zrc
     * Date: 2021/7/30
     * Time: 15:16
     * @param $requestparams
     */
    public function createSubOrder($sub_order_data, $orderIdMain, $params, &$sub_order_nos=[])
    {
        //子订单数据写入
        $datas = $items = [];
        foreach ($sub_order_data as &$v) {
            $temp_son = array_shift($sub_order_nos);
            if ($temp_son) {
                $sub_order_no = env('ORDERS.ORDER_SON') . $temp_son;
            } else {
                $sub_order_no = creatOrderNo(env('ORDERS.ORDER_SON'), $params['uid']);
            }
            $subData = array(
                'uid'                    => $params['uid'],
                'sub_order_no'           => $sub_order_no,
                'sub_order_status'       => 0,
                'main_order_id'          => $orderIdMain,
                'period'                 => $v['period'],
                'package_id'             => $v['package_id'],
                'special_type'           => $params['special_type'],
                'special_price'          => $v['special_price'],
                'order_from'             => $params['order_from'],
                'order_qty'              => $v['nums'],
                'payment_amount'         => $v['goods_money'],
                'cash_amount'            => $v['goods_money'],
                'preferential_reduction' => $v['discounted_price'],
                'express_fee'            => $v['express_fee'],
                'money_off_split_value'  => $v['money_off_split_value'],
                'coupon_id'              => $params['coupon_id'],
                'coupon_split_value'     => $v['coupon_split_value'],
                'invoice_progress'       => $params['invoice_progress'],
                'invoice_id'             => $params['invoice_id'],
                'express_type'           => $v['express_type'],
                'express_number'         => '',
                'is_ts'                  => $v['is_ts'],
                'created_time'           => time(),
                'group_id'               => !empty($params['group_id']) ? $params['group_id'] : 0,
                'group_status'           => !empty($params['group_id']) ? 1 : 0,
                'order_type'             => 1,
                'express_coupon_id'      => $params['express_coupon_id'],
                'predict_time'           => strtotime(predictTimeDeal(strtotime($v['predict_time']), 1, $v['express_type'], $v['erp_id'])),
                'warehouse_code'         => $v['erp_id'],
                'is_original_package'    => $v['is_original_package'] == 1 ? 1 : 0,
            );

            $exists_unpay_order_num = Db::name('second_order')->where([
                'uid'              => $params['uid'],
                'package_id'       => $v['package_id'],
                'sub_order_status' => 0,
                'is_delete'        => 0,
            ])->count();
            if ($exists_unpay_order_num) $this->throwError('您已有该商品的待支付订单，请先处理。');

            //加购套餐处理
            if ($v['is_add_purchase'] == 1) $subData['special_type'] = 3;
            $addSubOrder = Db::name('second_order')->insert($subData);
            if (empty($addSubOrder)) $this->throwError('子订单写入失败');
            //对值得买订单进行进行记录
            $subData['periods_type'] = $v['periods_type'];
            (new \app\model\OrderZdmRecord())->addRecord($subData, $params);

            //暂存订单记录日志方便客服后续排查暂存情况
            if ($v['is_ts'] == 1) {
                Db::name('order_remarks')->insert(['sub_order_no' => $subData['sub_order_no'], 'admin_id' => -1, 'remarks' => '创建订单用户选择暂存', 'created_time' => time()]);
            }
            $items[] = array(
                'genre'         => 'second_goods',
                'feedback_type' => 'buy',
                'item_id'       => $v['period'],
            );
            $datas[] = $subData;
        }
        //秒发商品反馈
        if (!empty($items)) {
            curlRequest(env('ITEM.COMMODITIES_URL') . '/commodities/v3/userPortrait/batchFeedback', json_encode(['items' => $items], true), ['vinehoo-uid:' . $params['uid']], 'POST');
        }
        return $datas;
    }

    /**
     * Description:秒发订单修改
     * Author: zrc
     * Date: 2021/8/2
     * Time: 14:50
     * @param $requestparams
     */
    public function updateOrder($requestparams)
    {
        $params    = $requestparams;
        $orderInfo = Db::name('second_order')->field('id,sub_order_status,main_order_id')->where(['sub_order_no' => $params['sub_order_no']])->find();
        if (empty($orderInfo)) {
            $this->throwError('未获取到订单信息');
        }
        if (isset($params['sub_order_status']) && is_numeric($params['sub_order_status'])) $updateData['sub_order_status'] = $params['sub_order_status'];
        if (isset($params['is_ts']) && is_numeric($params['is_ts'])) $updateData['is_ts'] = $params['is_ts'];
        if (isset($params['express_type']) && is_numeric($params['express_type'])) $updateData['express_type'] = $params['express_type'];
        if (!empty($params['consignee'])) {
            //用户信息加密处理
            $consignee                   = trim($params['consignee']);
            $encrypt                     = cryptionDeal(1, [$consignee], $params['operator'], '前端用户');
            $consignee                   = isset($encrypt[$consignee]) ? $encrypt[$consignee] : '';
            $updateMainData['consignee'] = $consignee;
        }
        if (!empty($params['consignee_phone'])) {
            //用户信息加密处理
            $phone                             = trim($params['consignee_phone']);
            $encrypt                           = cryptionDeal(1, [$phone], $params['operator'], '前端用户');
            $phone                             = isset($encrypt[$phone]) ? $encrypt[$phone] : '';
            $updateMainData['consignee_phone'] = $phone;
        }
        if (!empty($params['province_id'])) $updateMainData['province_id'] = $params['province_id'];
        if (!empty($params['city_id'])) $updateMainData['city_id'] = $params['city_id'];
        if (!empty($params['district_id'])) $updateMainData['district_id'] = $params['district_id'];
        if (!empty($params['address'])) $updateMainData['address'] = trim($params['address']);
        if (!empty($params['return_number'])) $updateData['return_number'] = trim($params['return_number']);
        if (!empty($params['invoice_progress'])) $updateData['invoice_progress'] = trim($params['invoice_progress']);
        if (isset($updateData)) {
            $result = Db::name('second_order')->where(['id' => $orderInfo['id']])->update($updateData);
            if (empty($result)) $this->throwError('修改订单信息失败');
        }
        if (isset($updateMainData)) {
            $result = Db::name('order_main')->where(['id' => $orderInfo['main_order_id']])->update($updateMainData);
            if (empty($result)) $this->throwError('修改订单信息失败');
        }
        return true;
    }

    /**
     * Description:秒发订单列表
     * Author: zrc
     * Date: 2021/8/3
     * Time: 9:45
     * @param $requestpqrams
     * @return \think\Response
     */
    public function orderList($requestparams)
    {
        $page        = !empty($requestparams['page']) ? $requestparams['page'] : 1;
        $limit       = !empty($requestparams['limit']) ? $requestparams['limit'] : 10;
        $params      = $requestparams;
        $secondModel = new SecondModel();
        $orderLists  = $secondModel->getOrderList($params, $page, $limit);
        return $orderLists;
    }

    /**
     * Description:秒发订单详情
     * Author: zrc
     * Date: 2021/8/3
     * Time: 9:57
     * @param $requestparams
     */
    public function orderDetail($requestparams)
    {
        $params      = $requestparams;
        $secondModel = new SecondModel();
        $result      = $secondModel->getOrderDetail($params);
        if (empty($result)) $this->throwError('获取订单详情失败');
        return $result;
    }
}