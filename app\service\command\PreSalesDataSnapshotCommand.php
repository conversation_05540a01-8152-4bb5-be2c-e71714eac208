<?php


namespace app\service\command;


use app\service\PreSales as PreSalesService;
use think\facade\Db;

class PreSalesDataSnapshotCommand
{
    public function exec()
    {
        $date            = date('Y-m-1', strtotime('-1 month'));
        $stime           = strtotime(date('Y-m-1', strtotime('-1 month')));
        $etime           = strtotime(date('Y-m-1', time())) - 1;
        $PreSalesService = new PreSalesService();
        $result          = $PreSalesService->preSalesDataStats(['is_snapshot' => 1, 'stime' => $stime, 'etime' => $etime]);
        if (!empty($result)) {
            $data  = array(
                'date'          => $date,
                'snapshot_json' => json_encode($result, JSON_UNESCAPED_UNICODE),
                'created_time'  => time(),
            );
            $isset = Db::name('presales_data_snapshot')->where(['date' => $date])->count();
            if ($isset == 0) {
                Db::name('presales_data_snapshot')->insert($data);
            } else {
                Db::name('presales_data_snapshot')->where(['date' => $date])->update($data);
            }
        }
        echo '执行完成';
        exit;
    }
}