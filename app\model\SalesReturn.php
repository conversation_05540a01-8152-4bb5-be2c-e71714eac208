<?php

namespace app\model;
use think\Model;
class SalesReturn extends Model
{
    protected $defaultSoftDelete = 0;
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    protected $json = ['detail_json'];

    public function getBillDateAttr($vaule,$data)
    {
        return date("Y-m-d H:i:s",$vaule);
    }

    public function getMediaUrlAttr($vaule,$data)
    {
        if($vaule == '') return [];
        $dataurl = explode(',',$vaule);
        return array_map(function ($v){
            return env('ALIURL').$v;
        },$dataurl);
    }
}