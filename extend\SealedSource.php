<?php


class SealedSource
{

    const CODE_OFFSET  = 537;
    const ORDER_OFFSET = 831;

    protected function get_short_timestamp()
    {
        // 获取当前时间戳的最后3位字符串
        $timestamp = time() % 1000;
        return str_pad($timestamp, 3, "0", STR_PAD_LEFT);
    }

    protected function generate_random_digits($length)
    {
        // 生成指定长度的随机数字字符串
        $digits = '';
        for ($i = 0; $i < $length; $i++) {
            $digits .= mt_rand(0, 9);
        }
        return $digits;
    }

    protected function pad_and_combine($region_code, $code, $order)
    {
        // 确保区域编码是固定3位
        $region_str = str_pad($region_code, 3, "0", STR_PAD_LEFT);

        // 混淆客户代码和订单编号
        $obscured_code  = ($code + self::CODE_OFFSET) % 100000; // 确保是5位
        $obscured_order = ($order + self::ORDER_OFFSET) % 100000; // 确保是5位

        // 确保混淆后的值是固定长度的字符串
        $code_str  = str_pad($obscured_code, 5, "0", STR_PAD_LEFT); // code固定为5位
        $order_str = str_pad($obscured_order, 5, "0", STR_PAD_LEFT); // order固定为5位

        // 获取时间戳的最后3位
        $timestamp_str = $this->get_short_timestamp();

        // 生成1位的随机数
        $random_digit = $this->generate_random_digits(1);

        // 将所有部分组合成一个字符串
        $combined_str = $region_str . $code_str . $order_str . $timestamp_str . $random_digit;

        // 混合顺序以增加复杂性
        $combined_list = str_split($combined_str);
        shuffle($combined_list);
        $combined_str = implode('', $combined_list);

        // 截取前12位，如果不够12位，添加更多随机数
        if (strlen($combined_str) > 12) {
            return substr($combined_str, 0, 12);
        } elseif (strlen($combined_str) < 12) {
            $combined_str .= $this->generate_random_digits(12 - strlen($combined_str));
        }

        return $combined_str;
    }

    public function encode_with_fixed_length($region_code, $code, $order)
    {
        $so = \think\facade\Db::name('offline_sealed_source')->where('main_order_id', $order)->find();
        if (empty($so)) {
            // 生成12位的唯一数字字符串
            do {
                $combined_str = $this->pad_and_combine($region_code, $code, $order);
                $exists_so    = \think\facade\Db::name('offline_sealed_source')->where('sealed_source_code', $combined_str)->count();
            } while ($exists_so > 0);
            $so = [
                'main_order_id'      => $order,
                'customer_code'     => $code,
                'region_code'       => $region_code,
                'sealed_source_code' => $combined_str,
                'created_time'      => time(),
            ];
            \think\facade\Db::name('offline_sealed_source')->insert($so);
        } else if ($so['customer_code'] != $code || $so['region_code'] != $region_code) {
            //更新
            // 生成12位的唯一数字字符串
            do {
                $combined_str = $this->pad_and_combine($region_code, $code, $order);
                $exists_so    = \think\facade\Db::name('offline_sealed_source')->where('sealed_source_code', $combined_str)->count();
            } while ($exists_so > 0);
            $so['customer_code']     = $code;
            $so['region_code']       = $region_code;
            $so['sealed_source_code'] = $combined_str;
            \think\facade\Db::name('offline_sealed_source')->where('id', $so['id'])->update($so);
        }
        return $so['sealed_source_code'];
    }

    public function decode_with_fixed_length($encoded_str)
    {
        return \think\facade\Db::name('offline_sealed_source')->where('sealed_source_code', $encoded_str)->find();
    }


    public function decode_with_delete($where)
    {
        return \think\facade\Db::name('offline_sealed_source')->where($where)->delete();
    }

}