<?php


namespace app\controller;

use app\service\AfterSales as AfterSalesService;
use app\BaseController;
use app\ErrorCode;
use app\Request;
use think\facade\Validate;

class AfterSales extends BaseController
{
    /**
     * Description:获取订单可退款金额
     * Author: zrc
     * Date: 2022/3/2
     * Time: 10:34
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function getOrderRefundMoney(Request $request)
    {
        $params = $request->param();
        //数据验证
        $validate = Validate::rule([
            'sub_order_no|子订单号' => 'require',
            'order_type|订单类型'   => 'require|in:0,1,2,3,4,9,11',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $afterSalesService = new AfterSalesService();
        $result            = $afterSalesService->getOrderRefundMoney($params);
        return $this->success($result);
    }

    /**
     * Description:创建工单计算运费差价
     * Author: zrc
     * Date: 2021/11/10
     * Time: 12:03
     * @param Request $request
     * @return \think\Response
     */
    public function freightDifference(Request $request)
    {
        $params = $request->param();
        if (empty($params['sub_order_no'])) $this->throwError('未获取到需要补差价的子订单号', ErrorCode::PARAM_ERROR);
        if (!isset($params['ticket_type']) || !in_array($params['ticket_type'], [1, 2])) $this->throwError('工单类型错误', ErrorCode::PARAM_ERROR);
        if ($params['ticket_type'] == 1) {
            if (!isset($params['express_type']) || !in_array($params['express_type'], [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 21, 22, 23, 31])) $this->throwError('物流类型错误', ErrorCode::PARAM_ERROR);
        }
        if ($params['ticket_type'] == 2) {
            if (empty($params['province_id'])) $this->throwError('未获取到省ID', ErrorCode::PARAM_ERROR);
            if (empty($params['city_id'])) $this->throwError('未获取到市ID', ErrorCode::PARAM_ERROR);
        }
        $afterSalesService = new AfterSalesService();
        $result            = $afterSalesService->freightDifference($params);
        return $this->success($result);
    }

    /**
     * Description:冻结/解冻订单
     * Author: zrc
     * Date: 2022/3/3
     * Time: 16:33
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function freezeOrder(Request $request)
    {
        $params = $request->param();
        if (empty($params['sub_order_no'])) $this->throwError('未获取到子订单号', ErrorCode::PARAM_ERROR);
        if (!isset($params['order_type']) || !in_array($params['order_type'], [0, 1, 2, 3, 4, 7, 8, 9, 11])) $this->throwError('未获取到订单类型', ErrorCode::PARAM_ERROR);
        if (!isset($params['type']) || !in_array($params['type'], [1, 2])) $this->throwError('未获取到类型', ErrorCode::PARAM_ERROR);
        $afterSalesService = new AfterSalesService();
        $result            = $afterSalesService->freezeOrder($params);
        return $this->success($result);
    }

    /**
     * Description:订单自动退款
     * Author: zrc
     * Date: 2022/4/30
     * Time: 10:00
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function orderAutomaticRefund(Request $request)
    {
        $params = $request->param();
        //数据验证
        $validate = Validate::rule([
            'sub_order_no|子订单号' => 'require',
            'order_type|订单类型'   => 'require|in:0,1,2,3,9,11'
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $afterSalesService = new AfterSalesService();
        if ($params['order_type'] == 11) {
            $result = $afterSalesService->orderAutomaticRefundAuction($params);
        } else {
            $result = $afterSalesService->orderAutomaticRefund($params);
        }
        return $this->success(true, $result);
    }

    /**
     * Description:手动创建订单
     * Author: zrc
     * Date: 2022/5/10
     * Time: 14:28
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function manualCreateOrder(Request $request)
    {
        $params = $request->param();
        //参数验证
        $validate = Validate::rule([
            'admin_id|后台用户ID'    => 'require|number',
            'sub_order_no|子订单号'  => 'require',
            'order_type|订单类型'    => 'require|in:0,1,2,3,7,8,9',
            'type|类型'            => 'require|in:1,2,3',
            'work_order_id|工单ID' => 'require',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        switch ($params['type']) {
            case 1:
                $validate = Validate::rule([
                    'express_type|物流类型'         => 'require|in:2,3,4,5,6,7,8,9,10,21,22,23,31',
                    'freight_difference|快递差价金额' => 'require|float',
                ]);
                if (!$validate->check($params)) {
                    $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
                }
                break;
            case 2:
                $validate = Validate::rule([
                    'consignee|收货人姓名'           => 'require',
                    'consignee_phone|收货人电话'     => 'require',
                    'province_id|省ID'           => 'require|number',
                    'city_id|市ID'               => 'require|number',
                    'district_id|区ID'           => 'require|number',
                    'address|详细地址'              => 'require',
                    'freight_difference|快递差价金额' => 'require|float',
                ]);
                if (!$validate->check($params)) {
                    $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
                }
                break;
            case 3:
                $validate = Validate::rule([
                    'consignee|收货人姓名'          => 'require',
                    'consignee_phone|收货人电话'    => 'require',
                    'province_id|省ID'          => 'require|number',
                    'city_id|市ID'              => 'require|number',
                    'district_id|区ID'          => 'require|number',
                    'address|详细地址'             => 'require',
                    'different_money|差价金额'     => 'require|float',
                    'new_package_id|换货/补发套餐ID' => 'require|number',
                    'nums|换货/补发套餐数量'           => 'require|number',
                    'erp_push_amount|ERP推单金额'  => 'require|float',
                ]);
                if (!$validate->check($params)) {
                    $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
                }
                break;
        }
        $afterSalesService = new AfterSalesService();
        $result            = $afterSalesService->manualCreateOrder($params);
        return $this->success($result);
    }

    /**
     * Description:创建工单记录售后信息(累计工单数，标记售后订单)
     * Author: zrc
     * Date: 2022/5/13
     * Time: 14:55
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function addAfterSalesLog(Request $request)
    {
        $params = $request->param();
        // 参数验证
        $validate = Validate::rule([
            'sub_order_no|子订单号' => 'require',
            'order_type|订单类型'   => 'require|in:0,1,2,3,9,11',
            'type|工单类型'         => 'require|in:1,2,3,4,5,6,7,9,11',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $afterSalesService = new AfterSalesService();
        if ($params['order_type'] == 11) {
            $result = $afterSalesService->addAfterSalesLogAuction($params);
        } else {
            $result = $afterSalesService->addAfterSalesLog($params);
        }
        return $this->success($result);
    }
}