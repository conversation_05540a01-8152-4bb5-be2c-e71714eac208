<?php
declare (strict_types = 1);

namespace app\command;

use app\service\Cross;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;

class LockedOrder extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('lockedOrder')
            ->setDescription('the lockedOrder command');
    }

    protected function execute(Input $input, Output $output)
    {
        (new Cross())->lockedOrder();
        // 指令输出
        $output->writeln('success');
    }
}
