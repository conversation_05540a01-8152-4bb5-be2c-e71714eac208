<?php


namespace app\controller;


use app\BaseController;
use app\ErrorCode;
use app\Request;
use app\service\MerchantSecond as MerchantSecondService;
use app\SSEHandler;
use think\facade\Validate;

class MerchantSecond extends BaseController
{
    /**
     * Description:商家秒发订单更换发货点（库存退还扣减）
     * Author: zrc
     * Date: 2022/8/9
     * Time: 15:28
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function replaceDeliveryStore(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        //数据验证
        $validate = Validate::rule([
            'admin_id|后台用户ID'         => 'require|number',
            'sub_order_no|子订单号'       => 'require',
            'delivery_store_id|发货点ID' => 'require|number',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $merchantSecondService = new MerchantSecondService();
        $result                = $merchantSecondService->replaceDeliveryStore($params);
        return $this->success($result);
    }

    /**
     * Description:发货/配送/提货
     * Author: zrc
     * Date: 2022/8/9
     * Time: 16:35
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function goShip(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        //数据验证
        $validateData = [
            'admin_id|后台用户ID'   => 'require|number',
            'sub_order_no|子订单号' => 'require',
            'type|类型'           => 'require|in:1,2,3',
        ];
        $validate     = Validate::rule($validateData);
        switch ($params['type']) {
            case 1:
                $validateData['express_type|物流公司']   = 'require|in:2,4,10,65,100';
                $validateData['express_number|物流单号'] = 'require';
                if ($params['express_type'] == 65 && empty($params['express_name'])) $this->throwError('物流名称必填');
                break;
            case 2:
                $validateData['delivery_person_name|配送联系人']   = 'require';
                $validateData['delivery_person_phone|配送联系电话'] = 'require';
                break;
        }
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $merchantSecondService = new MerchantSecondService();
        $result                = $merchantSecondService->goShip($params);
        return $this->success($result);
    }

    /**
     * Description:eventStream推送订单消息
     * Author: zrc
     * Date: 2023/4/26
     * Time: 10:01
     */
    public function getRedisConfig()
    {
        $data = array(
            'host'     => env('CACHE.HOST'),
            'post'     => env('CACHE.PORT'),
            'password' => env('CACHE.PASSWORD'),
        );
        return $this->success($data);
    }

    /**
     * Description:修改快递信息
     * Author: zrc
     * Date: 2023/5/10
     * Time: 15:42
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function updateExpressInfo(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        //数据验证
        $validateData = [
            'admin_id|后台用户ID'   => 'require|number',
            'sub_order_no|子订单号' => 'require',
            'express_type|物流类型'           => 'require|in:2,4,10,65,100',
            'express_number|物流单号'           => 'require',

        ];
        $validate     = Validate::rule($validateData);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        if ($params['express_type'] == 65 && empty($params['express_name'])) $this->throwError('物流名称必填');
        $merchantSecondService = new MerchantSecondService();
        $result                = $merchantSecondService->updateExpressInfo($params);
        return $this->success($result);
    }
}