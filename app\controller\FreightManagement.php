<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use app\ErrorCode;
use app\Request;
use app\service\FreightManagement as FreightManagementService;
use app\validate\ListPagination;

class FreightManagement extends BaseController
{
    /**
     * Description：快递运费管理模板列表
     * Author: zjl
     * Date: 2022/5/5
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function freightList(Request $request)
    {
        $params   = $request->param();
        $validate = new ListPagination();
        if (!$validate->goCheck($params)) {//验证参数
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $result = (new FreightManagementService())->freightList($params);
        return $this->success($result);
    }

    /**
     * Description：添加快递运费管理模板
     * Author: zjl
     * Date: 2022/5/5
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function create(Request $request)
    {
        $params = $request->param();
        if (!isset($params['name']) || empty($params['name'])) {
            $this->throwError('模板名称不能为空',ErrorCode::PARAM_ERROR);
        }
        $result= (new FreightManagementService())->createFreight($params);
        return  $this->success($result,"添加成功");
    }

    /**
     * Description：修改快递运费管理模板
     * Author: zjl
     * Date: 2022/5/5
     * @param Request $request
     * @return \think\response\Json
     */
    public function update(Request $request)
    {
        $params = $request->param();
        $result= (new FreightManagementService())->updateFreight($params);
        return  $this->success($result,"修改成功");
    }

    /**
     * 状态变更
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function setStatus(Request $request)
    {
        $params = $request->param();
        $result= (new FreightManagementService())->setStatus($params);
        if ($result){
            return  $this->success($result,"修改成功");
        }
        $this->throwError("修改失败");

    }

}
