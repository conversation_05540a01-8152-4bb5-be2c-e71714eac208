<?php


namespace app\service;


use app\BaseService;

class Course extends BaseService
{
    public function createOrder($requestparams)
    {
        $params = $requestparams;
        //用户信息加密处理
        $realname = trim($params['realname']);
        $phone    = trim($params['phone']);
        $encrypt  = cryptionDeal(1, [$realname, $phone], $params['uid'], '前端用户');
        $phone    = isset($encrypt[$phone]) ? $encrypt[$phone] : '';
        $realname = isset($encrypt[$realname]) ? $encrypt[$realname] : '';

    }
}