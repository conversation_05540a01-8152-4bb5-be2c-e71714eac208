<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Route;

Route::get('/', function () {
    return 'ok-tp6-orders';
});
Route::get('think', function () {
    return 'hello,ThinkPHP6!';
});
Route::group('/orders/v3', function () {
    /**rabbitMQ队列处理 start**/
    Route::post('/order/notifyDealOrderStatus', 'Notify/notifyDealOrderStatus');//队列处理异步回调订单状态+抽奖记录+补差价订单+已购数量
    Route::post('/order/notifyDealInvoice', 'Notify/notifyDealInvoice');//队列处理异步回调订单添加开票记录
    Route::post('/order/notifyDealGroup', 'Notify/notifyDealGroup');//队列处理异步回调订单拼团
    Route::post('/order/notifyDealFullGift', 'Notify/notifyDealFullGift');//队列处理异步回调订单满赠
    Route::post('/order/notifyDealPushWMS', 'Notify/notifyDealPushWMS');//队列处理异步回调订单推送萌牙
    Route::post('/order/timeOutOrderDeal', 'Order/timeOutOrderDeal');//超时订单队列处理
    Route::post('/order/groupOrderTimeOutDeal', 'Order/groupOrderTimeOutDeal');//拼团超时订单队列处理
    Route::post('/additional/logisticSyncDeal', 'Additional/logisticSyncDeal');//物流同步队列处理
    Route::post('/rabbit/createRabbitOrderDeal', 'Rabbit/createRabbitOrderDeal');//兔头兑换实物创建订单队列处理
    Route::post('/additional/periodPurchasedPushQueueData', 'Additional/periodPurchasedPushQueueData');//期数已购推送队列数据
    Route::post('/additional/periodPurchasedStatistics', 'Additional/periodPurchasedStatistics');//期数已购统计队列处理
    Route::post('/cross/exportInventoryMqDeal', 'Cross/exportInventoryMqDeal');//导出库存信息记录队列处理
    Route::post('/offline/exportSalesOrderMqDeal', 'offline/exportSalesOrderMqDeal');//导出中台销售单队列处理
    /**rabbitMQ队列处理 end**/
    /**回调 start**/
    Route::post('/order/notify', 'Notify/notify');//银联支付订单异步回调
    Route::post('/wms/WmsOrderReceive', 'Wms/WmsOrderReceive');//接收萌牙订单回调处理
    Route::post('/push/declareNotify', 'Push/declareNotify');//支付信息海关申报回调-银联
    Route::post('/push/borderStatusPushBack', 'Push/borderStatusPushBack');//跨境南沙仓清关状态回传
    Route::post('/push/logisticsBack', 'Push/logisticsBack');//跨境南沙仓物流信息回推接口
    Route::post('/cross/jiaChuangNotify', 'Cross/jiaChuangNotify');//嘉创异步回执
    Route::post('/dingTalk/offlineVerifyCallBack', 'DingTalk/offlineVerifyCallBack');//样酒审批回调处理
    Route::post('/dingTalk/timeOutOrderRefundCallBack', 'DingTalk/timeOutOrderRefundCallBack');//超时订单退款审批回调处理
    Route::post('/weChat/ordinarySaleOrderVerifyCallBack', 'WeChat/ordinarySaleOrderVerifyCallBack');//销售单审批回调处理
    Route::post('/weChat/changeStaffCallback', 'WeChat/changeStaffCallback');//批量修改客户自动化审批回调处理
    Route::post('/weChat/predictTimeVerifyCallBack', 'WeChat/predictTimeVerifyCallBack');//预计发货时间修改审批回调处理
    Route::post('/logistics/unShipCallBack', 'Logistics/unShipCallBack');//未发货提醒审批回调处理
    Route::post('/cross/inputBlackListCallBack', 'Cross/inputBlackListCallBack');//跨境黑名单录入审批回调处理
    Route::post('/additional/depositRefundDeal', 'Additional/depositRefundDeal');//订金订单批量退款队列回调处理
    Route::post('/weChat/offlineTransferVerifyCallBack', 'WeChat/offlineTransferVerifyCallBack');//对公转账-线下转账审批回调处理
    /**回调 end**/
    /**公共 start**/
    Route::get('/order/list', 'Order/list');//全部订单列表
    Route::post('/order/create', 'Order/create');//公共创建订单
    Route::post('/order/balancePay', 'Order/balancePay');//余额支付
    Route::get('/order/getUnpaidOrder', 'Order/getUnpaidOrder');//获取15秒未支付未取消订单
    Route::get('/order/detail', 'Order/orderDetail');//公共获取订单详情
    Route::post('/order/update', 'Order/update');//公共订单修改
    Route::get('/order/getConfig', 'Order/getConfig');//获取订单系统配置信息
    Route::get('/order/optionsConfig', 'Order/optionsConfig');//获取配置信息
    Route::post('/order/goodsReceipt', 'Order/goodsReceipt');//订单确认收货处理
    Route::get('/order/getOrderDetailByMainOrderNo', 'Order/getOrderDetailByMainOrderNo');//通过主订单获取订单信息（满赠需要）
    Route::post('/order/fullGiftDeal', 'Order/fullGiftDeal');//满赠处理创建关联子订单
    Route::post('/order/updatePaymentMethod', 'Order/updatePaymentMethod');//修改订单支付方式、支付主体
    Route::get('/additional/getOrderText', 'Additional/getOrderText');//获取订单相关文本
    Route::get('/additional/calcFreight', 'Additional/calcFreight');//普通商品冷链快递费计算(内部订单金额计算go模块使用)
    Route::get('/order/getGroupInfo', 'Order/getGroupInfo');//获取拼团信息（分享商品详情页使用）
    Route::get('/order/getMysteryBoxLog', 'Order/getMysteryBoxLog');//获取订单盲盒随机信息
    Route::post('/additional/batchUpdateInvoice', 'Additional/batchUpdateInvoice');//批量修改订单发票状态
    Route::post('/additional/subOrderReturnInventory', 'Additional/subOrderReturnInventory');//子订单退还库存
    Route::post('/additional/getInvoiceInfo', 'Additional/getInvoiceInfo');//获取订单开票信息
    Route::post('/additional/depositRefund', 'Additional/depositRefund');//期数下架未付尾款的订金订单批量退款
    Route::post('/additional/supplierDeliveryOrderExport', 'Additional/supplierDeliveryOrderExport');//导出前天11:00点到今天11:00点代发订单
    Route::post('/additional/supplierDeliveryOrderExportTsh', 'Additional/supplierDeliveryOrderExportTsh');//导出前天11:00点到今天11:00点代发订单
    Route::post('/order/upTsTime', 'Order/upTsTime');
    Route::post('/order/transferApprove', 'Order/transferApprove');
    Route::post('/order/getUserFirstOrder', 'Order/getUserFirstOrder');// 获取用户首单订单
    /**公共 end**/
    /**用户个人中心 start**/
    Route::get('/order/personalList', 'Order/personalList');//个人中心订单列表
    Route::get('/order/personalDetail', 'Order/personalDetail');//个人中心订单详情
    Route::get('/order/onlineServiceOrderList', 'Order/onlineServiceOrderList');//在线客服订单列表
    Route::post('/order/cancelDeleteOrder', 'Order/cancelDeleteOrder');//用户取消、删除订单
    Route::get('/order/personalInvoiceOrderList', 'Order/personalInvoiceOrderList');//个人中心可开发票订单列表
    Route::get('/order/orderStatistics', 'Order/orderStatistics');//订单统计待支付数、已支付数、已发货数、待拼单数
    Route::get('/order/depositList', 'Order/depositList');//订金订单列表
    Route::get('/order/depositDetail', 'Order/depositDetail');//订金订单详情
    Route::get('/order/getUserTopThreeOrderNums', 'Order/getUserTopThreeOrderNums');//获取用户首三单订单个数
    Route::post('/order/stagingOrder', 'Order/stagingOrder');//用户暂存订单
    Route::post('/order/upgradeColdChain', 'Order/upgradeColdChain');//用户开启冷链配送创建订单
    /**用户个人中心 end**/
    /**后台订单 start**/
    Route::get('/order/getSoldPurchasedOrderList', 'Order/getSoldPurchasedOrderList');//已售\已购按钮获取列表
    Route::post('/order/rebindOrderPackage', 'Order/rebindOrderPackage');//订单套餐重绑
    Route::get('/remarks/list', 'Order/remarksList');//订单备注列表
    Route::post('/remarks/create', 'Order/createRemarks');//添加订单备注
    Route::get('/routing/list', 'Order/routingList');//获取订单萌牙路由列表
    Route::get('/order/invoiceOrderList', 'Order/invoiceOrderList');//可开票订单列表
    Route::post('/additional/logisticSync', 'Additional/logisticSync');//物流同步推送队列
    Route::get('/additional/inquireOrderPay', 'Additional/inquireOrderPay');//查询订单银联支付情况
    Route::get('/additional/getExchangeOrderList', 'Additional/getExchangeOrderList');//查询换绑仓库订单列表
    Route::post('/additional/changeOrderWarehouse', 'Additional/changeOrderWarehouse');//换绑订单仓库
    Route::get('/additional/workGetOrderInfo', 'Additional/workGetOrderInfo');//工单系统获取订单信息
    Route::get('/push/getWmsPeriodPushReceipt', 'Push/getWmsPeriodPushReceipt');//获取后台期数批量推送萌牙回执信息
    Route::get('/push/getErpPeriodPushReceipt', 'Push/getErpPeriodPushReceipt');//获取后台期数批量推送T+回执信息
    Route::get('/additional/getLogisticSyncReceipt', 'Additional/getLogisticSyncReceipt');//获取后台物流同步回执信息
    Route::post('/logistics/addEditReason', 'Logistics/addEditReason');//添加/编辑未发货提醒
    Route::get('/logistics/reasonList', 'Logistics/reasonList');//未发货提醒列表
    Route::get('/logistics/getPeriodInfo', 'Logistics/getPeriodInfo');//未发货提醒获取期数信息
    Route::post('/logistics/submitUnShipApproval', 'Logistics/submitUnShipApproval');//提交未发货提醒审批
    Route::post('/regionExpressFee/query', 'RegionExpressFee/query');//快递费用查询
    /**后台订单 end**/
    /**售后 start**/
    Route::post('/afterSales/freezeOrder', 'AfterSales/freezeOrder');//冻结/解冻订单
    Route::get('/afterSales/getOrderRefundMoney', 'AfterSales/getOrderRefundMoney');//获取订单可退款金额
    Route::get('/afterSales/freightDifference', 'AfterSales/freightDifference');//创建工单计算运费差价
    Route::post('/afterSales/orderAutomaticRefund', 'AfterSales/orderAutomaticRefund');//订单自动退款
    Route::post('/afterSales/manualCreateOrder', 'AfterSales/manualCreateOrder');//手动创建订单
    Route::post('/afterSales/addAfterSalesLog', 'AfterSales/addAfterSalesLog');//创建工单记录售后信息(累加工单数，标记售后订单)
    /**售后 end**/
    /**购物车 start**/
    Route::post('/cart/addShoppingCart', 'Cart/addShoppingCart');//添加到购物车
    Route::get('/cart/shoppingCartList', 'Cart/shoppingCartList');//购物车列表
    Route::post('/cart/changeNums', 'Cart/changeNums');//购物车商品数量变更
    Route::post('/cart/delete', 'Cart/delete');//购物车商品删除
    Route::get('/cart/count', 'Cart/count');//购物车商品计数
    Route::post('/cart/calculateGoodsMoney', 'Cart/calculateGoodsMoney');//购物车商品勾选金额计算
    /**购物车 end**/
    /**秒发 start**/
    Route::post('/second/update', 'Second/update');//秒发订单修改
    Route::get('/second/list', 'Second/orderList');//秒发订单列表
    Route::get('/second/detail', 'Second/orderDetail');//秒发订单详情
    /**秒发 end**/
    /**闪购 start**/
    Route::post('/flash/update', 'Flash/update');//闪购订单修改
    Route::get('/flash/list', 'Flash/orderList');//闪购订单列表
    Route::get('/flash/detail', 'Flash/orderDetail');//闪购订单详情
    /**闪购 end**/
    /**尾货 start**/
    Route::post('/tail/update', 'Tail/update');//尾货订单修改
    Route::get('/tail/list', 'Tail/orderList');//尾货订单列表
    Route::get('/tail/detail', 'Tail/orderDetail');//尾货订单详情
    /**尾货 end**/
    /**跨境 start**/
    Route::post('/cross/update', 'Cross/update');//跨境订单修改
    Route::get('/cross/list', 'Cross/orderList');//跨境订单列表
    Route::get('/cross/detail', 'Cross/orderDetail');//跨境订单详情
    Route::post('/cross/inputBlackList', 'Cross/inputBlackList');//钉钉审批跨境限额黑名单录入
    Route::post('/cross/blackListCheck', 'Cross/blackListCheck');//跨境黑名单验证
    Route::post('/cross/dingTalkDealBlackList', 'Cross/dingTalkDealBlackList');//钉钉审批跨境退款-原因是限额的录入自然年黑名单，其他原因返还当前自然年消费金额
    Route::get('/cross/blackList', 'Cross/blackList');//跨境黑名单列表
    Route::get('/cross/pushPoolList', 'Cross/pushPoolList');//跨境订单推送池列表
    Route::get('/cross/goodsRecordInformationList', 'Cross/goodsRecordInformationList');//跨境商品备案信息列表
    Route::get('/cross/goodsRecordInformationDetail', 'Cross/goodsRecordInformationDetail');//跨境商品备案信息详情
    Route::post('/cross/importInformationList', 'Cross/importInformationList');//跨境商品备案信息excel批量录入
    Route::post('/cross/updateInformation', 'Cross/updateInformation');//跨境商品备案信息修改
    Route::get('/cross/pushWarehouseLogList', 'Cross/pushWarehouseLogList');//跨境订单推送代发仓记录列表
    Route::get('/cross/declareRecordList', 'Cross/declareRecordList');//海关申报异常记录列表
    Route::get('/cross/getExceptionDetails', 'Cross/getExceptionDetails');//获取海关申报异常明细
    Route::get('/cross/declareLogList', 'Cross/declareLogList');//支付单记录列表
    Route::get('/cross/declareQuery', 'Cross/declareQuery');//支付单查询
    Route::get('/cross/stockManagementList', 'Cross/stockManagementList');//库存管理列表
    Route::post('/cross/stockManagementUpdate', 'Cross/stockManagementUpdate');//库存管理编辑
    Route::post('/cross/addRemarks', 'Cross/addRemarks');//添加库存管理备注
    Route::get('/cross/remarksList', 'Cross/remarksList');//库存管理备注列表
    Route::post('/cross/importInventory', 'Cross/importInventory');//库存记录导入
    Route::post('/cross/delInventory', 'Cross/delInventory');//删除库存记录
    Route::post('/cross/importInventoryNums', 'Cross/importInventoryNums');//入库数量导入
    Route::post('/cross/batchUpdate', 'Cross/batchUpdate');//批量修改库存记录信息
    Route::get('/cross/exportInventory', 'Cross/exportInventory');//导出库存信息记录
    Route::post('/cross/crossOrderMonitorDeal', 'Cross/crossOrderMonitorDeal');//跨境订单监听处理
    Route::post('/cross/crossPeriodMonitorDeal', 'Cross/crossPeriodMonitorDeal');//跨境商品监听处理
    Route::get('/cross/inventoryChangeLog', 'Cross/inventoryChangeLog');//跨境库存变动日志
    Route::get('/cross/orderOnBehalfDetail', 'Cross/orderOnBehalfDetail');//订单代付详情
    Route::post('/cross/orderOnBehalfSubmit', 'Cross/orderOnBehalfSubmit');//订单代付提交
    Route::get('/cross/checkCustoms', 'Cross/checkCustoms');//179跨境订单原始支付信息获取
    Route::post('/cross/verifyCrossStock', 'Cross/verifyCrossStock');//验证跨境库存信息
    Route::post('/crossAutoPush/autoPushConfigUpdate', 'CrossAutoPush/autoPushConfigUpdate');//自动推单配置修改
    Route::get('/crossAutoPush/getAutoPushConfig', 'CrossAutoPush/getAutoPushConfig');//自动推单配置获取
    Route::post('/crossAutoPush/crossAutoPushDeal', 'CrossAutoPush/crossAutoPushDeal');//自动推单处理+简码获取可推送订单
    Route::post('/crossAutoPush/setOrderStopAutoPush', 'CrossAutoPush/setOrderStopAutoPush');//设置订单不自动推送
    Route::get('/crossAutoPush/autoPushOrderGatherList', 'CrossAutoPush/autoPushOrderGatherList');//自动推单统计列表/导出
    Route::post('/cross/excessPastDue', 'Cross/excessPastDue');//超额过期
    Route::post('/cross/excessAutoRefund', 'Cross/excessAutoRefund');//海关返回超额自动退款
    Route::get('/cross/monitorMaxNum', 'Cross/monitorMaxNum');//监控最大数量
    Route::post('/cross/monitorAnomaly', 'Cross/monitorAnomaly');//海关返回超额自动退款
    Route::post('/cross/stockSalesCallback', 'Cross/stockSalesCallback');//库存销售导出
    Route::get('/cross/monitorList', 'Cross/monitorList');//库存销售导出
    /**跨境 end**/
    /**兔头实物 start**/
    Route::post('/rabbit/create', 'Rabbit/create');//兔头实物创建订单
    Route::post('/rabbit/update', 'Rabbit/update');//兔头实物订单修改
    Route::get('/rabbit/list', 'Rabbit/orderList');//兔头实物订单列表
    /**兔头实物 end**/
    /**课程 start**/
    Route::post('/course/create', 'Course/create');//课程创建订单（暂未使用）
    Route::get('/course/list', 'Course/orderList');//课程订单列表（暂未使用）
    /**课程 end**/
    /**三方订单 start**/
    Route::post('/tripartite/create', 'Tripartite/orderInput');//三方订单录入
    Route::post('/tripartite/orderMonitorDeal', 'Tripartite/orderMonitorDeal');//三方订单监听处理
    Route::get('/tripartite/orderList', 'Tripartite/orderList');//三方订单列表
    Route::post('/tripartite/stopPushOrder', 'Tripartite/stopPushOrder');//终止萌牙发货
    Route::post('/tripartite/addGift', 'Tripartite/addGift');//添加赠品
    Route::post('/tripartite/mergeOrder', 'Tripartite/mergeOrder');//合并订单
    Route::post('/tripartite/import', 'Tripartite/import');//三方订单导入
    Route::post('/tripartite/importDeal', 'Tripartite/importDeal');//三方订单导入
    Route::post('/tripartite/trOrderPushTPlus', 'Tripartite/trOrderPushTPlus');//三方订单导入
    Route::post('/tripartite/productUpdate', 'Tripartite/productUpdate');//产品更新
    Route::post('/tripartite/changeItemsInfo', 'Tripartite/changeItemsInfo');//添加赠品
    /**三方订单 end**/
    /**线下订单 start**/
    Route::get('/offline/createOrderNo', 'Offline/createOrderNo');//生成单据编号
    Route::post('/offline/createSaleOrder', 'Offline/createSaleOrder');//线下样酒申请
    Route::get('/offline/saleOrderList', 'Offline/saleOrderList');//样酒销售单列表
    Route::post('/offline/updateSaleOrder', 'Offline/updateSaleOrder');//编辑推送失败的样酒销售单 
    Route::post('/offline/newAddSaleOrder', 'Offline/newAddSaleOrder');//新增普通销售单
    Route::get('/offline/ordinarySaleOrderList', 'Offline/ordinarySaleOrderList');//普通销售单列表/导出
    Route::post('/offline/updateOrdinarySaleOrder', 'Offline/updateOrdinarySaleOrder');//编辑普通销售单
    Route::post('/offline/uploadWeiXinTemporary', 'Offline/uploadWeiXinTemporary');//企业微信上传临时素材
    Route::post('/offline/rejectSaleOrder', 'Offline/rejectSaleOrder');//弃审普通销售单
    Route::post('/offline/addEditMaterial', 'Offline/addEditMaterial');//添加编辑物料
    Route::get('/offline/materialList', 'Offline/materialList');//物料列表
    Route::post('/offline/deleteMaterial', 'Offline/deleteMaterial');//物料删除
    Route::post('/offline/documentTypeChange', 'Offline/documentTypeChange');//单据类型转换
    Route::post('/offline/buildTraceCode', 'Offline/buildTraceCode');//生成溯源码
    Route::post('/offline/auditRejection', 'Offline/auditRejection');//弃审
    Route::get('/offline/collectionTypes', 'Offline/collectionTypes');
    Route::get('/offline/productDetail', 'Offline/productDetail');//简码详情
    Route::get('/offline/inventoryRelationList', 'Offline/inventoryRelationList');//简码详情
    Route::post('/offline/inventoryRelationCreated', 'Offline/inventoryRelationCreated');//简码详情
    Route::post('/offline/inventoryRelationUpdate', 'Offline/inventoryRelationUpdate');//简码详情
    Route::post('/offline/inventoryRelationDelete', 'Offline/inventoryRelationDelete');//简码详情
    Route::post('/offline/timeOutSaleOrder', 'Offline/timeOutSaleOrder');
    Route::post('/offline/arapOrder', 'Offline/arapOrder'); //新增单据
    /**线下订单 end**/
    /**订单推送 start**/
    Route::get('/push/getOrderPushData', 'Push/getOrderPushData');//获取订单推送萌牙/T+数据
    Route::post('/push/pushWms', 'Push/pushWms');//订单推送萌牙
    Route::post('/push/pushTplus', 'Push/pushTplus');//订单推送T+
    Route::post('/push/crossPushWarehouse', 'Push/crossPushWarehouse');//跨境订单推送代发仓
    Route::post('/push/customsDeclare', 'Push/customsDeclare');//跨境订单支付单推送
    Route::post('/push/wmsShip', 'Push/wmsShip');//后台萌牙发货-订单号/期数推萌牙
    Route::post('/push/resumeSalesOrder', 'Push/resumeSalesOrder');//后台恢复销售单-订单号/期数推T+
    Route::post('/push/batchCrossPushWarehouse', 'Push/batchCrossPushWarehouse');//订单批量推送代发仓
    Route::post('/push/batchCustomsDeclare', 'Push/batchCustomsDeclare');//订单批量推送支付单
    Route::post('/push/batchCustomsDirectPush', 'Push/batchCustomsDirectPush');//订单批量直推
    Route::post('/push/customsDirectPush', 'Push/customsDirectPush');//跨境直推接口（支付单+代发仓）
    Route::post('/push/pushSuccessDeal', 'Push/pushSuccessDeal');//萌牙/T+推送成功结果处理
    Route::get('/push/valuableInfo', 'Push/valuableInfo');//贵重物品信息
    Route::post('/push/setValuableInfo', 'Push/setValuableInfo');//设置贵重物品信息
    /**订单推送 end**/

    /**订单开票 start**/
    Route::post('/orderInvoice/addInvoiceRecord', 'orderInvoice/addInvoiceRecord');//添加/修改订单开票记录
    Route::post('/orderInvoice/orderInvoice', 'orderInvoice/orderInvoice');//订单开票
    Route::post('/orderInvoice/orderInvoiceCallBack', 'orderInvoice/orderInvoiceCallBack');//订单开票异步处理
    Route::get('/orderInvoice/getOrderInvoiceInfo', 'orderInvoice/getOrderInvoiceInfo');//获取订单开票信息
    Route::get('/orderInvoice/getInvoicingOrderList', 'orderInvoice/getInvoicingOrderList');//获取开票中订单列表(已弃用)
    /**订单开票 end**/

    /** 快递运费管理 start**/
    Route::get('/freight/list', 'FreightManagement/freightList');//快递运费管理列表
    Route::post('/freight/create', 'FreightManagement/create');//快递运费管理添加
    Route::post('/freight/edit', 'FreightManagement/update');//快递运费管理修改
    Route::post('/freight/setstatus', 'FreightManagement/setStatus');//快递运费管理修改
    /** 快递运费管理 end**/

    /** 快递方式管理start**/
    Route::get('/express/list', 'Express/index');//快递方式列表
    Route::get('/express/webList', 'Express/webList');//快递方式添加商品调用
    Route::post('/express/create', 'Express/create');//快递方式添加
    Route::post('/express/edit', 'Express/update');//快递方式修改
    Route::post('/express/setstatus', 'Express/setStatus');//状态修改
    /** 快递方式管理end**/

    /** 商家秒发 start**/
    Route::post('/MerchantSecond/replaceDeliveryStore', 'MerchantSecond/replaceDeliveryStore');//商家秒发订单更换发货点（库存退还扣减）
    Route::post('/MerchantSecond/goShip', 'MerchantSecond/goShip');//发货/配送/提货
    Route::get('/MerchantSecond/getRedisConfig', 'MerchantSecond/getRedisConfig');//获取redis配置
    Route::post('/MerchantSecond/updateExpressInfo', 'MerchantSecond/updateExpressInfo');//修改快递信息
    /** 商家秒发 end**/

    /** 什么值得买订单记录  start**/
    Route::get('/zdmorder/list', 'OrderZdmRecord/lists');//什么值得买订单列表
    Route::rule('/zdmorder/remove', 'OrderZdmRecord/remove');//什么值得买订单记录移除
    Route::post('/zdmorder/export', 'OrderZdmRecord/export');//什么值得买订单记录导出
    Route::post('/zdmorder/create', 'OrderZdmRecord/recordCreate');//什么值得买订单记录创建
    /** 什么值得买订单记录 end**/

    /** 复购统计  start**/
    Route::post('/repurchasestatistics/create', 'RepurchaseStatistics/create');//创建复购记录
    Route::get('/repurchasestatistics/list', 'RepurchaseStatistics/list');//复购需求统计列表
    Route::get('/repurchasestatistics/needlist', 'RepurchaseStatistics/needlist');//复购需求列表
    Route::get('/repurchasestatistics/userlist', 'RepurchaseStatistics/users');//复购需求用户列表
    Route::POST('/repurchasestatistics/putonshelves', 'RepurchaseStatistics/putOnShelves');//复购需求用户短信发送
    Route::post('/repurchasestatistics/periodTask', 'RepurchaseStatistics/periodTask');//复购期数上下架任务-数据库监控任务
    Route::get('/repurchasestatistics/period', 'RepurchaseStatistics/period');//再来一单期数列表
    Route::post('/repurchasestatistics/saveperiodrepurchmsg', 'RepurchaseStatistics/saverepurchmsg');//修改再来一单期数消息状态
    Route::post('/repurchasestatistics/remove', 'RepurchaseStatistics/remove');//修改再来一单期数统计状态
    Route::get('/repurchasestatistics/wishlist', 'RepurchaseStatistics/wishlist');//心愿清单
    Route::post('/repurchasestatistics/want/create', 'RepurchaseStatistics/wantCreate');//想要
    Route::get('/repurchasestatistics/collection/list', 'RepurchaseStatistics/collectionList');//收藏列表
    Route::get('/repurchasestatistics/collection/userlist', 'RepurchaseStatistics/userlist');//收藏列表
    Route::get('/repurchasestatistics/statisticsperiods', 'RepurchaseStatistics/statisticsperiods');//收藏列表
    /** 复购统计 end**/

    /** 发票  start**/
    Route::get('/invoice/list', 'Invoice/list');//发票列表
    Route::post('/invoice/save', 'Invoice/save');//发票创建
    Route::post('/invoice/approval', 'Invoice/approval');//发票审核
    Route::get('/invoice/getSalesDocumentsList', 'Invoice/getSalesDocumentsList'); # 筛选需要开票的销售单据
    Route::get('/invoice/getSalesDocumentsAllList', 'Invoice/getSalesDocumentsAllList'); # 筛选需要开票的销售单据 全部
    Route::get('/invoice/export', 'Invoice/export'); # 导出开票
    Route::post('/invoice/callback', 'Invoice/callback'); # 开票回调
    /** 发票 end**/

    /** 销售退货  start**/
    Route::get('/salesreturn/list', 'SalesReturn/list');//销售退货列表
    Route::post('/salesreturn/create', 'SalesReturn/create');//销售退货创建
    Route::post('/salesreturn/update', 'SalesReturn/update');//销售退货创建
    Route::post('/salesreturn/approval', 'SalesReturn/approval');//销售退货审核
    Route::get('/salesreturn/getSalesDocumentsList', 'SalesReturn/getSalesDocumentsList'); # 筛选需要开票的销售单据
    Route::post('/salesreturn/getcorpbyorderno', 'SalesReturn/getCorpByOrderNo'); # 获取订单所属公司
    Route::post('/salesreturn/export', 'SalesReturn/export'); # 销售退货导出
    Route::get('/salesreturn/rerundata', 'SalesReturn/rerundata'); # 销售退货审批时间重跑
    Route::post('/salesreturn/salesReturnBatchImport', 'SalesReturn/salesReturnBatchImport'); # 销售退货批量导入
    Route::post('/salesreturn/salesReturnBatchImportDeal', 'SalesReturn/salesReturnBatchImportDeal'); # 销售退货批量导入异步处理
    Route::post('/salesreturn/pushErp', 'SalesReturn/SalesReturnPushErp');//销售退货推送ERP
    Route::post('/salesreturn/rejectSalesReturn', 'SalesReturn/rejectSalesReturn');//销售退货弃审
    Route::post('/salesreturn/updatePushErpTime', 'SalesReturn/updatePushErpTime');//销售退货修改推送ERP时间
    Route::post('/salesreturn/pushWms', 'SalesReturn/SalesReturnPushWms');//销售退货推送萌牙WMS
    Route::post('/salesreturn/updateStatusUp', 'SalesReturn/updateStatusUp');//WMS更新销售退货状态
    Route::post('/salesreturn/updateByInventory', 'SalesReturn/updateByInventory');//仓库制单,清点
    Route::post('/salesreturn/revokeReturns', 'SalesReturn/revokeReturns');//向WMS系统推送退货单撤销指令
    Route::post('/salesreturn/priceSplit', 'SalesReturn/priceSplit');//价格拆分
    Route::post('/salesreturn/checkWaybill', 'SalesReturn/checkWaybill');//检查运单

    /** 销售退货 end**/

    /** 售前数据统计  start**/
    Route::get('/preSales/adminList', 'PreSales/adminList');//售前客户列表
    Route::get('/preSales/addUpdateAdmin', 'PreSales/addUpdateAdmin');//添加/修改售前客户
    Route::get('/preSales/preSalesDataStats', 'PreSales/preSalesDataStats');//售前数据统计
    Route::post('/preSales/recordPreSalesShare', 'PreSales/recordPreSalesShare');//记录售前分享标识
    Route::get('/preSales/clickNumsGetInfo', 'PreSales/clickNumsGetInfo');//点击数值显示统计具体信息
    /** 售前数据统计 end**/

    /** cct  start**/
    Route::post('/order/applyBilling', 'Order/applyBilling');//申请开票
    Route::get('/order/findProduct', 'Order/findProduct');
    Route::post('/order/saveQuoteInfo', 'Order/saveQuoteInfo');
    Route::post('/order/quotationAdd', 'Order/quotationAdd');
    Route::get('/order/lastOrderDetail', 'Order/lastOrderDetail');//最新订单详情
    Route::get('/order/getGroupShareByPeriod', 'Order/getGroupShareByPeriod');//获取分享详情
    Route::post('/tripartite/bathUpdateStatus', 'Tripartite/bathUpdateStatus');//批量更新状态
    Route::post('/tripartite/bathUpdateWhCode', 'Tripartite/bathUpdateWhCode');//批量更新仓库
    Route::post('/tripartite/bathUpdateAddress', 'Tripartite/bathUpdateAddress');//批量更新地址
    Route::post('/tripartite/export', 'Tripartite/export');//导出发起审批
    Route::any('/tripartite/salesList', 'Tripartite/salesList');//导出发起审批
    Route::post('/cross/lock', 'Cross/lock');//跨境订单修改
    Route::get('/cross/inspection', 'Cross/inspection');//检查订单是否异常

    Route::post('/payment-statistics/write', '/PaymentStatistics/write');//写入收款/退款数据
    Route::get('/payment-statistics/today', '/PaymentStatistics/today');//获取当日数据
    Route::get('/payment-statistics/list', '/PaymentStatistics/list');//获取历史数据
    Route::post('/payment-statistics/sync', '/PaymentStatistics/syncData');//手动同步数据
    /** cct end**/
});