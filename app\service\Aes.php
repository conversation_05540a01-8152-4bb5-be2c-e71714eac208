<?php

namespace app\service;

class Aes
{
    public function encrypt($data, $key, $iv)
    {
        $res = openssl_encrypt($data, 'aes-128-cbc', $key, 1, $iv);
        $res = base64_encode($res);
        return $res;
    }

    private function pkcs5_pad($text, $blocksize)
    {
        $pad = $blocksize - (strlen($text) % $blocksize);
        return $text . str_repeat(chr($pad), $pad);
    }

    public function decrypt($data, $key, $iv)
    {
        $data      = base64_decode($data);
        $cryptText = openssl_decrypt($data, 'aes-128-cbc', $key, 1, $iv);
        return $cryptText;
    }

    public function hexToStr($hex)
    {
        $string = '';
        for ($i = 0; $i < strlen($hex) - 1; $i += 2) {
            $string .= chr(hexdec($hex[$i] . $hex[$i + 1]));
        }
        return $string;
    }

}