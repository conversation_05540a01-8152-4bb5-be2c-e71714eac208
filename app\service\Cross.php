<?php


namespace app\service;


use app\BaseService;
use app\ErrorCode;
use app\model\Cross as CrossModel;
use app\model\CrossLockOrder;
use app\service\elasticsearch\ElasticSearchService;
use app\service\es\Es;
use app\service\Order as OrderService;
use app\service\Push as PushService;
use app\service\WeChat as WeChatService;
use think\facade\Db;
use think\facade\Log;
use think\facade\Validate;
use Vtiful\Kernel\Excel;

class Cross extends BaseService
{
    private $secret = 'b9e1539cb63741cd';
    private $iv     = '0102030405060708';

    /**
     * Description:跨境创建订单
     * Author: zrc
     * Date: 2021/7/30
     * Time: 15:16
     * @param $requestparams
     */
    public function createSubOrder($sub_order_data, $orderIdMain, $params, &$sub_order_nos = [])
    {
        $ts_remarks = '创建订单用户选择暂存';
        if (isset($params['is_replace_pay']) && $params['is_replace_pay'] == 1) {
            $realname   = '';
            $id_card_no = '';
        } else {
            //少数名族真实姓名含有全角符号的点替换成半角符号的点
            $params['realname'] = str_replace("．", "·", $params['realname']);
            $params['realname'] = str_replace(".", "·", $params['realname']);
            $params['realname'] = str_replace("。", "·", $params['realname']);
            $params['realname'] = str_replace("•", "·", $params['realname']);
            //用户信息加密处理
            $realname   = trim($params['realname']);
            $id_card_no = trim($params['id_card_no']);
            $encrypt    = cryptionDeal(1, [$realname, $id_card_no], $params['uid'], '前端用户');
            $realname   = isset($encrypt[$realname]) ? $encrypt[$realname] : '';
            $id_card_no = isset($encrypt[$id_card_no]) ? $encrypt[$id_card_no] : '';
        }
        $datas = [];
        //子订单数据写入
        foreach ($sub_order_data as &$v) {
            //获取订单归属虚拟仓
            $ppData          = array(
                'period'     => $v['period'],
                'product_id' => $params['associated_products'][0]['product_id'],
                'field'      => 'erp_id',
            );
            $periods_product = $this->httpGet(env('ITEM.COMMODITIES_URL') . '/commodities/v3/getPackageProductInfo', $ppData);
            if ($periods_product['error_code'] != 0) $this->throwError('未获取订单归属虚拟仓');
            $store_type = 1;
            if ($periods_product['data']['erp_id'] == '021') $store_type = 1;
            if ($periods_product['data']['erp_id'] == '028') $store_type = 2;
            if ($store_type == 2 && $v['is_ts'] != 1 && in_array(intval($params['province_id']), [20, 19, 21, 22, 15, 14])) {
                //广东、湖南、广西、海南、江西、福建
//                $v['is_ts'] = 1;
//                $ts_remarks = '南沙订单收货地址为 广东、湖南、广西、海南、江西、福建 默认暂存';
            }
            if (($store_type == 2) && (strtotime('2024-10-01 00:00:00') > strtotime($v['predict_time']))) {
                if (($v['is_ts'] != 1) && ($v['express_type'] != 3)) {
                    $this->throwError('因发货地气温较高，此跨境订单暂时只能选择暂存或冷链发货。');
                }
            }
            $now = time();
            if ($store_type == 1) {
                if ($v['express_type'] == 3) {
                    $ptime = strtotime("2024-10-11 23:59:59");
                    if (strtotime($v['predict_time']) < $ptime) {
                        $v['predict_time'] = date('Y-m-d H:i:s', $ptime);
                    }
                } else {
                    $ptime = strtotime("2024-10-08 23:59:59");
                    if (($now >= strtotime('2024-09-30 15:00:00')) && (strtotime($v['predict_time']) < $ptime)) {
                        $v['predict_time'] = date('Y-m-d H:i:s', $ptime);
                    }
                }
            }
            $temp_son = array_shift($sub_order_nos);
            if ($temp_son) {
                $sub_order_no = env('ORDERS.ORDER_SON') . $temp_son;
            } else {
                $sub_order_no = creatOrderNo(env('ORDERS.ORDER_SON'), $params['uid']);
            }
            $subData = array(
                'uid'                 => $params['uid'],
                'sub_order_no'        => $sub_order_no,
                'sub_order_status'    => 0,
                'main_order_id'       => $orderIdMain,
                'period'              => $v['period'],
                'package_id'          => $v['package_id'],
                'order_from'          => $params['order_from'],
                'order_qty'           => $v['nums'],
                'payment_amount'      => $v['goods_money'],
                'cash_amount'      => $v['goods_money'],
                'express_fee'         => $v['express_fee'],
                'express_type'        => $v['express_type'],
                'express_number'      => '',
                'is_ts'               => $v['is_ts'],
                'realname'            => $realname,
                'id_card_no'          => $id_card_no,
                'created_time'        => time(),
                'order_type'          => 2,
                'express_coupon_id'   => $params['express_coupon_id'],
                'predict_time'        => strtotime(predictTimeDeal(strtotime($v['predict_time']), 2)),
                'store_type'          => $store_type,
                'warehouse_code'      => $periods_product['data']['erp_id'],
                'is_original_package' => $v['is_original_package'] == 1 ? 1 : 0,
                'is_replace_pay'      => $params['is_replace_pay'],
                'guid'                => buildGuid()
            );

            $exists_unpay_order_num = Db::name('cross_order')->where([
                'uid'              => $params['uid'],
                'package_id'       => $v['package_id'],
                'sub_order_status' => 0,
                'is_delete'        => 0,
            ])->count();
            if ($exists_unpay_order_num) $this->throwError('您已有该商品的待支付订单，请先处理。');

            $addSubOrder = Db::name('cross_order')->insert($subData);
            if (empty($addSubOrder)) $this->throwError('子订单写入失败');
            //对值得买订单进行进行记录
            $subData['periods_type'] = $v['periods_type'];
            (new \app\model\OrderZdmRecord())->addRecord($subData, $params);
            //暂存订单记录日志方便客服后续排查暂存情况
            if ($v['is_ts'] == 1) {
                Db::name('order_remarks')->insert(['sub_order_no' => $subData['sub_order_no'], 'admin_id' => -1, 'remarks' => $ts_remarks, 'created_time' => time()]);
            }
            $datas[] = $subData;
        }
        return $datas;
    }

    /**
     * Description:跨境订单修改
     * Author: zrc
     * Date: 2021/8/2
     * Time: 14:50
     * @param $requestparams
     */
    public function updateOrder($requestparams)
    {
        $params    = $requestparams;
        $orderInfo = Db::name('cross_order')->field('id,sub_order_status,main_order_id')->where(['sub_order_no' => $params['sub_order_no']])->find();
        if (empty($orderInfo)) {
            $this->throwError('未获取到订单信息');
        }
        if (isset($params['sub_order_status']) && is_numeric($params['sub_order_status'])) $updateData['sub_order_status'] = $params['sub_order_status'];
        if (isset($params['is_ts']) && is_numeric($params['is_ts'])) $updateData['is_ts'] = $params['is_ts'];
        if (isset($params['express_type']) && is_numeric($params['express_type'])) $updateData['express_type'] = $params['express_type'];
        if (!empty($params['consignee'])) {
            //用户信息加密处理
            $consignee                   = trim($params['consignee']);
            $encrypt                     = cryptionDeal(1, [$consignee], $params['operator'], '前端用户');
            $consignee                   = isset($encrypt[$consignee]) ? $encrypt[$consignee] : '';
            $updateMainData['consignee'] = $consignee;
        }
        if (!empty($params['consignee_phone'])) {
            //用户信息加密处理
            $phone                             = trim($params['consignee_phone']);
            $encrypt                           = cryptionDeal(1, [$phone], $params['operator'], '前端用户');
            $phone                             = isset($encrypt[$phone]) ? $encrypt[$phone] : '';
            $updateMainData['consignee_phone'] = $phone;
        }
        if (!empty($params['province_id'])) $updateMainData['province_id'] = $params['province_id'];
        if (!empty($params['city_id'])) $updateMainData['city_id'] = $params['city_id'];
        if (!empty($params['district_id'])) $updateMainData['district_id'] = $params['district_id'];
        if (!empty($params['address'])) $updateMainData['address'] = trim($params['address']);
        if (!empty($params['return_number'])) $updateData['return_number'] = trim($params['return_number']);
        if (!empty($params['invoice_progress'])) $updateData['invoice_progress'] = trim($params['invoice_progress']);
        if (isset($updateData)) {
            $result = Db::name('cross_order')->where(['id' => $orderInfo['id']])->update($updateData);
            if (empty($result)) $this->throwError('修改订单信息失败');
        }
        if (isset($updateMainData)) {
            $result = Db::name('order_main')->where(['id' => $orderInfo['main_order_id']])->update($updateMainData);
            if (empty($result)) $this->throwError('修改订单信息失败');
        }
        return true;
    }

    /**
     * Description:跨境订单列表
     * Author: zrc
     * Date: 2021/8/3
     * Time: 9:45
     * @param $requestpqrams
     * @return \think\Response
     */
    public function orderList($requestparams)
    {
        $page       = !empty($requestparams['page']) ? $requestparams['page'] : 1;
        $limit      = !empty($requestparams['limit']) ? $requestparams['limit'] : 10;
        $params     = $requestparams;
        $crossModel = new CrossModel();
        $orderLists = $crossModel->getOrderList($params, $page, $limit);
        return $orderLists;
    }

    /**
     * Description:跨境订单详情
     * Author: zrc
     * Date: 2021/8/3
     * Time: 9:57
     * @param $requestparams
     */
    public function orderDetail($requestparams)
    {
        $params     = $requestparams;
        $crossModel = new CrossModel();
        $result     = $crossModel->getOrderDetail($params);
        if (empty($result)) $this->throwError('未获取到订单详情');
        return $result;
    }

    /**
     * Description:钉钉审批跨境限额黑名单录入
     * Author: zrc
     * Date: 2021/11/10
     * Time: 16:50
     * @param $requestparams
     * @return bool|void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function inputBlackList($requestparams)
    {
        $params = $requestparams;
        if (!isset($params['sponsor'])) $params['sponsor'] = '';
        $toYear    = date('Y', time());
        $blacklist = Db::name('cross_quota_blacklist')->where(array('id_card_no' => $params['id_card_no']))->find();
        if ($params['type'] == 1) {
            if (empty($blacklist)) {
                Db::name('cross_quota_blacklist')->insert(array('id_card_no' => $params['id_card_no'], 'type' => 1, 'year' => $toYear, 'sponsor' => $params['sponsor'], 'note' => $params['note'], 'created_time' => time()));
            } else if ($blacklist['type'] == 1) {
                Db::name('cross_quota_blacklist')->where(array('id_card_no' => $params['id_card_no'], 'type' => 1))->update(array('year' => $toYear, 'sponsor' => $params['sponsor'], 'note' => $params['note'], 'created_time' => time()));
            }
        } else if ($params['type'] == 2) {
            if (empty($blacklist)) {
                Db::name('cross_quota_blacklist')->insert(array('id_card_no' => $params['id_card_no'], 'type' => 2, 'year' => $toYear, 'sponsor' => $params['sponsor'], 'note' => $params['note'], 'created_time' => time()));
            }
        }
        return true;
    }

    /**
     * Description:钉钉审批跨境退款(原因是限额的录入自然年黑名单，其他原因返还当前自然年消费金额）
     * Author: zrc
     * Date: 2021/11/10
     * Time: 16:50
     * @param $requestparams
     * @return bool|void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function dingTalkDealBlackList($requestparams)
    {
        $params    = $requestparams;
        $orderInfo = Db::name('cross_order')->field('id_card_no,created_time,payment_amount')->where(['sub_order_no' => $params['sub_order_no']])->find();
        //身份证解密处理
        $encrypt    = cryptionDeal(2, [$orderInfo['id_card_no']], '15736175219', '宗仁川');
        $id_card_no = isset($encrypt[$orderInfo['id_card_no']]) ? $encrypt[$orderInfo['id_card_no']] : '';
        if (empty($id_card_no)) {
            $this->throwError('未获取到订单身份证号码');
        }
        $blacklist = Db::name('cross_quota_blacklist')->where(array('id_card_no' => $id_card_no))->find();
        $toYear    = date('Y', time());
        $time      = strtotime($toYear . '-1-1');
        if (strpos($params['make'], '超额') !== false) {
            //黑名单不存在，过滤跨年退款订单后写入黑名单
            if (empty($blacklist) && $orderInfo['created_time'] > $time) {
                Db::name('cross_quota_blacklist')->insert(array('id_card_no' => $id_card_no, 'year' => $toYear, 'type' => 1, 'note' => '退款审批录入', 'created_time' => time()));
            }
            //黑名单存在，过滤永久类型黑名单、已录入当前自然年类型黑名单、跨年退款订单后更新黑名单年份
            if ($blacklist && $blacklist['year'] != $toYear && $blacklist['type'] == 1 && $orderInfo['created_time'] > $time) {
                Db::name('cross_quota_blacklist')->where(['id' => $blacklist['id']])->update(array('year' => $toYear, 'note' => '退款审批录入', 'created_time' => time()));
            }
        } else {
            $todayTime = strtotime(date('Y-m-d'));
            $record    = Db::name('cross_year_amount')->where(['id_card_no' => $id_card_no, 'year' => $toYear])->find();
            //过滤跨年退款订单，定时任务未定时任务未执行的订单后修改年消费金额
            if ($record && $orderInfo['created_time'] > $time && $orderInfo['created_time'] < $todayTime) {
                $newamount = $record['year_money'] - $orderInfo['payment_amount'];
                if ($newamount <= 0) $newamount = 0;
                Db::name('cross_year_amount')->where(['cardno' => $id_card_no])->update(['year_money' => $newamount, 'update_time' => time()]);
                //黑名单存在，判断新年消费额是否小于2.6W，是否是年消费拉黑，是，移除黑名单
                if ($blacklist && $blacklist['year'] == $toYear && $blacklist['type'] == 1 && $newamount < 26000 && $blacklist['note'] == '跨境自然年身份证消费金额满2.6W元定时任务录入') {
                    Db::name('quota_blacklist')->where(['id' => $blacklist['id']])->delete();
                }
            }
        }
        return true;
    }

    /**
     * Description:跨境订单推送池列表
     * Author: zrc
     * Date: 2022/4/24
     * Time: 13:36
     * @param $requestparams
     * @return mixed
     */
    public function pushPoolList($requestparams)
    {
        $page       = !empty($requestparams['page']) ? $requestparams['page'] : 1;
        $limit      = !empty($requestparams['limit']) ? $requestparams['limit'] : 10;
        $params     = $requestparams;
        $crossModel = new CrossModel();
        $orderLists = $crossModel->pushPoolList($params, $page, $limit);
        return $orderLists;
    }

    /**
     * Description:跨境商品备案信息列表
     * Author: zrc
     * Date: 2022/4/26
     * Time: 9:10
     * @param $requestparams
     * @return mixed
     */
    public function goodsRecordInformationList($requestparams)
    {
        $page       = !empty($requestparams['page']) ? $requestparams['page'] : 1;
        $limit      = !empty($requestparams['limit']) ? $requestparams['limit'] : 10;
        $params     = $requestparams;
        $crossModel = new CrossModel();
        $orderLists = $crossModel->goodsRecordInformationList($params, $page, $limit);
        return $orderLists;
    }

    /**
     * Description:跨境商品备案信息详情
     * Author: zrc
     * Date: 2022/4/27
     * Time: 17:49
     * @param $requestparams
     * @return array|mixed|Db|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function goodsRecordInformationDetail($requestparams)
    {
        $params = $requestparams;
        $result = Db::name('cross_record_information')->where(['id' => $params['id']])->find();
        if (empty($result)) $this->throwError('未获取到商品备案信息');
        return $result;
    }

    /**
     * Description:跨境商品备案信息excel批量录入
     * Author: zrc
     * Date: 2022/4/27
     * Time: 16:58
     * @param $excelData
     * @return int
     * @throws \Exception
     */
    public function importInformationList($excelData)
    {
        $information = array();
        foreach ($excelData as $key => $val) {
            $information[$key]['goods_item_name']     = trim($val[1]);
            $information[$key]['goods_record_name']   = trim($val[16]);
            $information[$key]['goods_itemno']        = trim($val[2]);
            $information[$key]['goods_barcode']       = trim($val[3]);
            $information[$key]['gross_weight']        = trim($val[17]);
            $information[$key]['net_weight']          = trim($val[18]);
            $information[$key]['unit']                = trim($val[23]);
            $information[$key]['unit1']               = trim($val[24]);
            $information[$key]['unit2']               = trim($val[34]);
            $information[$key]['qty']                 = trim($val[27]);
            $information[$key]['qty1']                = trim($val[25]);
            $information[$key]['qty2']                = trim($val[35]);
            $information[$key]['goods_item_recordno'] = trim($val[29]);
            $information[$key]['hs_code']             = trim($val[22]);
            $information[$key]['country_code']        = trim($val[31]);
            $information[$key]['gmodel']              = trim($val[39]);
            $information[$key]['created_time']        = time();
            //数据验证
            $validate = Validate::rule([
                'goods_item_name|企业商品中文名'   => 'require',
                'goods_record_name|商品备案名称'   => 'require',
                'goods_itemno|商品货号'            => 'require|number',
                'goods_barcode|商品国际条码'       => 'require|number',
                'gross_weight|毛重'                => 'require|float',
                'net_weight|净重'                  => 'require|float',
                'unit|计量单位'                    => 'require',
                'unit1|法定计量单位'               => 'require',
                'unit2|第二计量单位'               => 'require',
                'qty|数量'                         => 'require|number',
                'qty1|法定数量'                    => 'require|float',
                'qty2|第二数量'                    => 'require|float',
                'goods_item_recordno|账册备案料号' => 'require',
                'hs_code|hs编码'                   => 'require|number',
                'country_code|原产国'              => 'require',
                'gmodel|商品规格'                  => 'require',
            ]);
            if (!$validate->check($information[$key])) {
                $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
            }
        }
        $result = Db::name('cross_record_information')->insertAll($information);
        if (empty($result)) $this->throwError('商品备案信息录入失败');
        return $result;
    }

    /**
     * Description:跨境商品备案信息修改
     * Author: zrc
     * Date: 2022/4/27
     * Time: 17:18
     * @param $requestparams
     * @return int
     * @throws \think\db\exception\DbException
     */
    public function updateInformation($requestparams)
    {
        $params = $requestparams;
        $data   = ['update_time' => time()];
        if (!empty($params['goods_item_name'])) $data['goods_item_name'] = trim($params['goods_item_name']);
        if (!empty($params['goods_record_name'])) $data['goods_record_name'] = trim($params['goods_record_name']);
        if (!empty($params['goods_itemno'])) $data['goods_itemno'] = trim($params['goods_itemno']);
        if (!empty($params['goods_barcode'])) $data['goods_barcode'] = trim($params['goods_barcode']);
        if (!empty($params['gross_weight'])) $data['gross_weight'] = trim($params['gross_weight']);
        if (!empty($params['net_weight'])) $data['net_weight'] = trim($params['net_weight']);
        if (!empty($params['unit'])) $data['unit'] = trim($params['unit']);
        if (!empty($params['unit1'])) $data['unit1'] = trim($params['unit1']);
        if (!empty($params['unit2'])) $data['unit2'] = trim($params['unit2']);
        if (!empty($params['qty'])) $data['qty'] = trim($params['qty']);
        if (!empty($params['qty1'])) $data['qty1'] = trim($params['qty1']);
        if (!empty($params['qty2'])) $data['qty2'] = trim($params['qty2']);
        if (!empty($params['goods_item_recordno'])) $data['goods_item_recordno'] = trim($params['goods_item_recordno']);
        if (!empty($params['hs_code'])) $data['hs_code'] = trim($params['hs_code']);
        if (!empty($params['country_code'])) $data['country_code'] = trim($params['country_code']);
        if (!empty($params['gmodel'])) $data['gmodel'] = trim($params['gmodel']);
        $result = Db::name('cross_record_information')->where(['id' => $params['id']])->update($data);
        if (empty($result)) $this->throwError('修改失败');
        return $result;
    }

    /**
     * Description:跨境订单推送代发仓记录列表
     * Author: zrc
     * Date: 2022/4/29
     * Time: 13:35
     * @param $requestparams
     * @return mixed
     */
    public function pushWarehouseLogList($requestparams)
    {
        $page       = !empty($requestparams['page']) ? $requestparams['page'] : 1;
        $limit      = !empty($requestparams['limit']) ? $requestparams['limit'] : 10;
        $params     = $requestparams;
        $crossModel = new CrossModel();
        $orderLists = $crossModel->pushWarehouseLogList($params, $page, $limit);
        return $orderLists;
    }

    /**
     * Description:海关申报异常记录列表
     * Author: zrc
     * Date: 2022/4/29
     * Time: 15:20
     * @param $requestparams
     * @return mixed
     */
    public function declareRecordList($requestparams)
    {
        $page       = !empty($requestparams['page']) ? $requestparams['page'] : 1;
        $limit      = !empty($requestparams['limit']) ? $requestparams['limit'] : 10;
        $params     = $requestparams;
        $crossModel = new CrossModel();
        $orderLists = $crossModel->declareRecordList($params, $page, $limit);
        return $orderLists;
    }

    /**
     * Description:获取海关申报异常明细
     * Author: zrc
     * Date: 2022/4/30
     * Time: 15:36
     * @param $requestparams
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getExceptionDetails($requestparams)
    {
        $params  = $requestparams;
        $details = [];
        //获取代发仓异步日志
        $callbackLog = Db::name('cross_callback_log')->where(['main_order_no' => $params['main_order_no']])->order('id desc')->select()->toArray();
        if (count($callbackLog) > 0) {
            foreach ($callbackLog as &$val) {
                $callBackData = json_decode($val['call_back_data'], true);
                if ($val['store_type'] == 1 && isset($callBackData['msg'])) {
                    $details[] = array(
                        'describe'     => $callBackData['msg'],
                        'receive_time' => $val['created_time'],
                    );
                } else if ($val['store_type'] == 2 && isset($callBackData['notes'])) {
                    $details[] = array(
                        'describe'     => $callBackData['notes'],
                        'receive_time' => $val['created_time'],
                    );
                }
            }
        }
        //获取推送代发仓日志
        $warehouse_log = Db::name('cross_push_warehouse_log')->where(['main_order_no' => $params['main_order_no']])->order('id desc')->select()->toArray();
        if (count($warehouse_log) > 0) {
            foreach ($warehouse_log as &$v) {
                $details[] = array(
                    'describe'     => $v['result_msg'],
                    'receive_time' => $v['created_time'],
                );
            }
        }
        //获取推送支付单日志
        $declare_log = Db::name('cross_declare_log')->where(['main_order_no' => $params['main_order_no']])->order('id desc')->select()->toArray();
        if (count($declare_log) > 0) {
            foreach ($declare_log as &$vv) {
                $details[] = array(
                    'describe'     => !empty($vv['notify_msg']) ? $vv['notify_msg'] : $vv['request_msg'],
                    'receive_time' => $vv['created_time'],
                );
            }
        }
        return $details;
    }

    /**
     * Description:支付单记录列表
     * Author: zrc
     * Date: 2022/5/9
     * Time: 13:27
     * @param $requestparams
     * @return mixed
     */
    public function declareLogList($requestparams)
    {
        $page       = !empty($requestparams['page']) ? $requestparams['page'] : 1;
        $limit      = !empty($requestparams['limit']) ? $requestparams['limit'] : 10;
        $params     = $requestparams;
        $crossModel = new CrossModel();
        $orderLists = $crossModel->declareLogList($params, $page, $limit);
        return $orderLists;
    }

    /**
     * Description:支付单推送查询
     * Author: zrc
     * Date: 2022/5/16
     * Time: 16:30
     * @param $requestparams
     * @return array|mixed
     * @throws \Think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function declareQuery($requestparams)
    {
        $params = $requestparams;
        // 获取订单信息
        $order_info = Db::name('order_main')
            ->alias('om')
            ->field('om.uid,om.payment_amount,om.tradeno,om.payment_subject,co.sub_order_no,co.store_type,co.realname,co.id_card_no,co.payment_time')
            ->leftJoin('cross_order co', 'co.main_order_id=om.id')
            ->where(['om.main_order_no' => $params['main_order_no']])
            ->find();
        if (empty($order_info)) $this->throwError('未获取到订单信息');
        $queryResult = [];
        switch ($params['type']) {
            case 1://微信
                if ($order_info['payment_subject'] == 4) {
                    require_once(app()->getRootPath() . 'lib/wechat.acquire.customs/function.php');
                    $queryResultXml = (string)custom_declare_query($params['main_order_no']);
                } else {
                    require_once(app()->getRootPath() . 'lib/wechat_customs/function.php');
                    $queryResultXml = (string)custom_declare_query($params['main_order_no']);
                }
                $queryResult = json_decode(json_encode(simplexml_load_string($queryResultXml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
                return $queryResult;
                break;
            case 2://支付宝
                $out_request_no = Db::name('cross_declare_log')->where(['main_order_no' => $params['main_order_no']])->value('out_request_no');
                if (empty($out_request_no)) $this->throwError('未获取到报关流水号');
                if ($order_info['payment_subject'] == 4) {
                    require_once(app()->getRootPath() . 'lib/alipay.acquire.customs/function.php');
                    $queryResultXml = (string)custom_declare_query($out_request_no);
                } else {
                    require_once(app()->getRootPath() . 'lib/alipay_customs/function.php');
                    $queryResultXml = (string)custom_declare_query($out_request_no);
                }
                $queryResult = json_decode(json_encode(simplexml_load_string($queryResultXml)), true);
                if ($queryResult['is_success'] == 'T') {
                    $array                           = array(
                        '_input_charset'  => $queryResult['request']['param'][1],
                        'service'         => $queryResult['request']['param'][2],
                        'sign'            => $queryResult['request']['param'][3],
                        'out_request_nos' => $queryResult['request']['param'][4],
                        'sign_type'       => $queryResult['request']['param'][5],
                    );
                    $queryResult['request']['param'] = $array;
                }
                return $queryResult;
                break;
            case 3://银联
                // 加载支付单申报class
                require_once(root_path() . "extend/umsSecss/SecssUtil.php");
                $secssUtil        = new \SecssUtil();
                $securityPropFile = root_path() . "/extend/umsSecss/security.properties";
                $init_status      = $secssUtil->init($securityPropFile); //初始化安全控件
                if (!$init_status) $this->throwError('初始化异常');
                $data = array(
                    'Version'        => env('ORDERS.Version'), //版本号
                    'MerId'          => env('ORDERS.MerId'), //商户号
                    'MerOrderNo'     => $params['main_order_no'], //商户订单号
                    'TranDate'       => date('Ymd', $order_info['payment_time']), //商户交易日期
                    'TranType'       => "0502", //交易类型
                    'BusiType'       => env('ORDERS.BusiType'), //业务类型
                    'CustomsOrderNo' => $params['main_order_no'], //电商平台订单号
                );
                // 签名
                $secssUtil->sign($data);
                if ("00" !== $secssUtil->getErrCode()) $this->throwError($secssUtil->getErrMsg());
                // 获取签名
                $data['Signature'] = $secssUtil->getSign();
                $result_not_format = httpPostString(env('ORDERS.QueryUrl'), http_build_query($data), ['Content-Type: application/x-www-form-urlencoded;charset=utf-8']); //x-www-form-urlencoded form-data
                parse_str($result_not_format, $queryResult);
                break;
        }
        return $queryResult;
    }

    /**
     * Description:嘉创异步回执
     * Author: zrc
     * Date: 2022/6/18
     * Time: 14:50
     * @param $params
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function jiaChuangNotify($params)
    {
        $method        = $params['method'];//接口名称
        $bizcontent    = $params['bizcontent'];//业务请求参数的集合
        $aes           = new Aes();
        $jsonData      = $aes->decrypt($bizcontent, $this->secret, $this->iv);
        $data          = json_decode($jsonData, true);
        $main_order_no = $data['sellerOrderNo'];
        //日志记录
        $log = array(
            'main_order_no'  => $main_order_no,
            'store_type'     => 1,
            'call_back_data' => $jsonData,
            'created_time'   => time(),
        );
        Db::name('cross_callback_log')->insert($log);
        $es            = new ElasticSearchService();
        $arr           = array(
            'index' => ['orders'],
            'match' => [['main_order_no' => $main_order_no]],
            'range' => [['created_time' => ['gte' => date('Y-m-d H:i:s', 1602777600)]]],
            'limit' => 1,
        );
        $esData        = $es->getDocumentList($arr);
        $orderInfo     = $esData['data'][0];
        $main_order_id = Db::name('order_main')->where(['main_order_no' => $main_order_no])->value('id');
        if (empty($main_order_id)) $this->throwError('未获取到订单信息');
        if ($method == 'com.umatou.order.deliver') {//订单发货异步通知
            $updateData = array(
                'sub_order_status' => 2,
                'express_number'   => $data['logisticsNo'],
                'is_ts'            => 0,
                'delivery_time'    => time(),
                'update_time'      => time(),
            );
            $result     = Db::name('cross_order')->where(['main_order_id' => $main_order_id])->update($updateData);
            if (empty($result)) $this->throwError('修改子订单信息失败');
            //订单推送T+
            $sub_order_no = Db::name('cross_order')->where(['main_order_id' => $main_order_id])->value('sub_order_no');
            if ($sub_order_no) {
                $pushService = new PushService();
                $pushService->pushTplus(['sub_order_no' => $sub_order_no, 'order_type' => 2]);
            }
            //小程序订阅消息推送+app消息推送
            if (isset($orderInfo['uid']) && !empty($orderInfo['uid']) && empty($orderInfo['express_number'])) {
                $userInfo = httpGet(env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo', ['uid' => $orderInfo['uid'], 'field' => 'nickname,applet_openid']);
                if ($userInfo['error_code'] == 0 && isset($userInfo['data']['list'][0])) {
                    //access_token获取
                    $getToken = $this->httpGet(env('ITEM.WECHART_URL') . '/wechat/v3/minapp/accesstoken');
                    if ($getToken['error_code'] == 0 && isset($userInfo['data']['list'][0]['applet_openid'])) {
                        $pushData = array(
                            'touser'            => $userInfo['data']['list'][0]['applet_openid'],
                            'template_id'       => '0tiG9uQEuyT4OqvLl5Z6_tJ2P58dyxin_zyOzQBLhuY',
                            'page'              => 'packageB/pages/order-detail/order-detail?orderNo=' . $orderInfo['sub_order_no'],
                            'miniprogram_state' => env('ORDERS.MINIPROGRAM_STATE'),
                            "topcolor"          => "#FF0000",
                            'data'              => [
                                'amount3'           => [
                                    'value' => $orderInfo['payment_amount'] . '元',
                                    'color' => '#173177'
                                ],
                                'thing2'            => [
                                    'value' => truncate_utf8_string($orderInfo['title'], 15),
                                    'color' => '#173177'
                                ],
                                'character_string1' => [
                                    'value' => $orderInfo['sub_order_no'],
                                    'color' => '#173177'
                                ]
                            ]
                        );
                        curlRequest('https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=' . $getToken['access_token'], json_encode($pushData));
                    }
                    //app推送消息
                    $single = array(
                        'is_push'      => 1,
                        'uid'          => $orderInfo['uid'],
                        'title'        => "物流通知",
                        'content'      => "物流通知 您的" . isset($orderInfo['title']) ? $orderInfo['title'] : '商品' . "已经发货，物流单号是：" . $data['logisticsNo'] . "，点击查看物流信息",
                        'data_type'    => 20,
                        'data'         => [
                            'title'        => "物流通知",
                            'content'      => "物流通知 您的" . isset($orderInfo['title']) ? $orderInfo['title'] : '商品' . "已经发货，物流单号是：" . $data['logisticsNo'] . "，点击查看物流信息",
                            'cover'        => isset($orderInfo['banner_img']) ? $orderInfo['banner_img'] : '',
                            'logisticCode' => $data['logisticsNo'],
                            'expressType'  => 2
                        ],
                        'label'        => "LogisticsDetails",
                        'custom_param' => ['cover' => isset($orderInfo['banner_img']) ? $orderInfo['banner_img'] : '', 'logisticCode' => $data['logisticsNo'], 'expressType' => 2]
                    );
                    httpPostString(env('ITEM.APPPUSH_URL') . '/apppush/v3/push/single', json_encode($single, JSON_UNESCAPED_UNICODE));
                }
            }
        } else if ($method == 'com.umatou.order.custom') {//订单申报状态异步通知
            $declareRecord = Db::name('cross_customs_declare_record')->where(['main_order_no' => $main_order_no])->find();
            if (empty($declareRecord)) $this->throwError('跨境海关申报记录');
            $updateData['update_time'] = time();
            //报关异常处理
            if ($data['type'] == 'fail' && $declareRecord['customs_status'] != 2) {
                $updateData['abnormal_node']  = 3;
                $updateData['error_des']      = $data['msg'];
                $updateData['customs_status'] = 1;
                if ($data['hsStatus'] == '100' || $data['hsStatus'] == '0') {
                    //添加订单备注
                    $main_order_id = Db::name('order_main')->where(['main_order_no' => $main_order_no])->value('id');
                    $subOrderInfo  = Db::name('cross_order')->field('uid,sub_order_no,id_card_no')->where(['main_order_id' => $main_order_id])->find();
                    if ($main_order_id && $subOrderInfo) {
                        $orderService = new OrderService();
                        $remark       = array(
                            'sub_order_no' => $subOrderInfo['sub_order_no'],
                            'order_type'   => 2,
                            'content'      => $data['msg'],
                            'admin_id'     => 0
                        );
                        $orderService->createRemarks($remark);
                    }
                    //黑名单处理
                    if ($data['hsStatus'] == '100' && strpos($data['msg'], '订购人购买超过年度限额') !== false) {
                        // 超额;
                        $pushData   = array(
                            'namespace' => 'excess_auto_refund',
                            'key'       => $subOrderInfo['sub_order_no'],
                            'data'      => base64_encode(json_encode(['sub_order_no' => $subOrderInfo['sub_order_no']])),
                            'callback'  => env('ITEM.ORDERS_URL') . '/orders/v3/cross/excessAutoRefund',
                            'timeout'   => '5s',
                        );
                        $timing_res = httpPostString(env('ITEM.TIMEING_SERVICE_URL') . '/services/v3/timing/add', json_encode($pushData));
                        Log::write("添加海关超额自动退款任务: " . json_encode($pushData) . '   ' . json_encode($timing_res));
                        $encrypt                    = cryptionDeal(2, [$subOrderInfo['id_card_no']], $subOrderInfo['uid'], '前端用户');
                        $subOrderInfo['id_card_no'] = isset($encrypt[$subOrderInfo['id_card_no']]) ? $encrypt[$subOrderInfo['id_card_no']] : '';
                        $this->inputBlackList(['id_card_no' => $subOrderInfo['id_card_no'], 'type' => 1, 'note' => $data['msg']]);
                    }
                    //自动推单统计处理
                    $autoPushLog = Db::name('cross_auto_push_log')->where(['main_order_no' => $main_order_no])->order('id desc')->find();
                    if ($autoPushLog) {
                        $updateLogData = array(
                            'status'      => 2,
                            'error_msg'   => $data['msg'],
                            'update_time' => time(),
                        );
                        Db::name('cross_auto_push_log')->where(['main_order_no' => $main_order_no])->update($updateLogData);
                    }
                    //异常提示
                    $store      = '古斯缇';
                    $short_code = isset($autoPushLog['short_code']) ? $autoPushLog['short_code'] : '';
                    $content    = "# 跨境自动推单异常提示\n";
                    $content    .= "-主订单号：" . $params['orderNo'] . "\n";
                    $content    .= "-订单仓库：" . $store . "\n";
                    $content    .= "-商品条码：" . $short_code . "\n";
                    $content    .= "-异常信息：" . $data['msg'] . "\n";
                    $queueData  = array(
                        'access_token' => env('ORDERS.cross_token'),
                        'type'         => 'text',
                        'at'           => '***********,***********,***********',
                        'content'      => base64_encode($content),
                    );
                    $data       = base64_encode(json_encode($queueData));
                    $pushData   = array(
                        'exchange_name' => 'dingtalk',
                        'routing_key'   => 'dingtalk_sender',
                        'data'          => $data,
                    );
                    httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
                }
            }
            //海关放行处理
            if ($data['type'] == 'success') {
                $updateData['abnormal_node']  = 0;
                $updateData['customs_status'] = 2;
                //添加订单备注
                $main_order_id = Db::name('order_main')->where(['main_order_no' => $main_order_no])->value('id');
                $subOrderInfo  = Db::name('cross_order')->field('uid,sub_order_no,id_card_no')->where(['main_order_id' => $main_order_id])->find();
                if ($main_order_id && $subOrderInfo) {
                    $orderService = new OrderService();
                    $remark       = array(
                        'sub_order_no' => $subOrderInfo['sub_order_no'],
                        'order_type'   => 2,
                        'content'      => $data['msg'],
                        'admin_id'     => 0
                    );
                    $orderService->createRemarks($remark);
                }
            }
            $updateRecord = Db::name('cross_customs_declare_record')->where(['main_order_no' => $main_order_no])->update($updateData);
            if (empty($updateRecord)) $this->throwError('修改跨境海关申报记录失败');
        } else if ($method == 'com.umatou.goods.stock') {//库存异步通知
            switch ($data['stockType']) {
                case 1://良品转不良品
                    foreach ($data['goodsList'] as &$val) {
                        $inventoryRecordId = Db::name('cross_inventory')
                            ->where(['goods_barcode' => $val['goodsSn'], 'store_type' => 1, 'is_delete' => 0])
                            ->order('id desc')
                            ->value('id');
                        if ($inventoryRecordId) {
                            //减可售
                            $items[] = array(
                                'id'             => $inventoryRecordId,
                                'nums'           => $val['goodsNumber'],
                                'inventory_type' => 'available',
                                'is_add'         => false,
                            );
                            //加残次
                            $items[]       = array(
                                'id'             => $inventoryRecordId,
                                'nums'           => $val['goodsNumber'],
                                'inventory_type' => 'defective',
                                'is_add'         => true,
                            );
                            $inventoryData = array('items' => $items);
                            $this->crossInventoryDeal($inventoryData, 4, 0, '', '古斯缇回执');
                        }
                    }
                    break;
                case 2://不良品转良品
                    foreach ($data['goodsList'] as &$val) {
                        $inventoryRecordId = Db::name('cross_inventory')
                            ->where(['goods_barcode' => $val['goodsSn'], 'store_type' => 1, 'is_delete' => 0])
                            ->order('id desc')
                            ->value('id');
                        if ($inventoryRecordId) {
                            //加可售
                            $items[] = array(
                                'id'             => $inventoryRecordId,
                                'nums'           => $val['goodsNumber'],
                                'inventory_type' => 'available',
                                'is_add'         => true,
                            );
                            //减残次
                            $items[]       = array(
                                'id'             => $inventoryRecordId,
                                'nums'           => $val['goodsNumber'],
                                'inventory_type' => 'defective',
                                'is_add'         => false,
                            );
                            $inventoryData = array('items' => $items);
                            $this->crossInventoryDeal($inventoryData, 4, 0, '', '古斯缇回执');
                        }
                    }
                    break;
            }
        }
        return true;
    }

    /**
     * Description:库存管理列表
     * Author: zrc
     * Date: 2022/9/19
     * Time: 13:19
     * @param $requestparams
     * @return mixed
     */
    public function stockManagementList($requestparams)
    {
        $page       = !empty($requestparams['page']) ? $requestparams['page'] : 1;
        $limit      = !empty($requestparams['limit']) ? $requestparams['limit'] : 10;
        $params     = $requestparams;
        $crossModel = new CrossModel();
        $orderLists = $crossModel->stockManagementList($params, $page, $limit);
        return $orderLists;
    }

    /**
     * Description:库存管理编辑
     * Author: zrc
     * Date: 2022/9/21
     * Time: 18:34
     * @param $params
     * @return bool
     * @throws \Exception
     */
    public function stockManagementUpdate($params)
    {
        $goods_barcode = Db::name('cross_inventory')->where(['id' => $params['id'], 'is_delete' => 0])->value('goods_barcode');
        if (empty($goods_barcode)) $this->throwError('库存记录信息不存在或已删除');
        $updateData = [];
        if (!empty($params['goods_name'])) $updateData['goods_name'] = trim($params['goods_name']);
        if (!empty($params['year'])) $updateData['year'] = trim($params['year']);
        if (!empty($params['country'])) $updateData['country'] = trim($params['country']);
        if (!empty($params['area'])) $updateData['area'] = trim($params['area']);
        if (!empty($params['category'])) $updateData['category'] = trim($params['category']);
        if (!empty($params['capacity'])) $updateData['capacity'] = trim($params['capacity']);
        if (!empty($params['supplier_name'])) $updateData['supplier_name'] = trim($params['supplier_name']);
        if (!empty($params['purchase_time'])) $updateData['purchase_time'] = strtotime($params['purchase_time']);
        if (!empty($params['entry_time'])) $updateData['entry_time'] = strtotime($params['entry_time']);
        if (isset($params['is_pay']) && in_array($params['is_pay'], [0, 1])) $updateData['is_pay'] = $params['is_pay'];
        if (isset($params['store_type']) && in_array($params['store_type'], [1, 2])) $updateData['store_type'] = $params['store_type'];
        if (!empty($params['pickup_warehouse'])) $updateData['pickup_warehouse'] = trim($params['pickup_warehouse']);
        if (!empty($params['purchase_price'])) $updateData['purchase_price'] = $params['purchase_price'];
        if (!empty($params['currency'])) $updateData['currency'] = $params['currency'];
        if (!empty($params['actual_purchase_price'])) $updateData['actual_purchase_price'] = $params['actual_purchase_price'];
        if (isset($params['goods_status']) && in_array($params['goods_status'], [0, 1, 2, 3, 4])) $updateData['goods_status'] = $params['goods_status'];
        $updateData['operator']    = $params['operator'];
        $updateData['update_time'] = time();

        if (!empty($params['inventory_type']) && isset($params['nums']) && $params['nums'] > 0 && !empty($params['operate_type'])) {
            if (!in_array($params['inventory_type'], ['enter', 'purchase', 'available', 'defective', 'sold', 'ts', 'real'])) $this->throwError('数量调整类型异常', ErrorCode::PARAM_ERROR);
            if (!in_array($params['operate_type'], ['inc', 'dec'])) $this->throwError('操作类型异常', ErrorCode::PARAM_ERROR);
            if ($params['operate_type'] == 'inc') {
                $is_add = true;
            } else {
                $is_add = false;
            }
            //enter-入库数量，purchase-采购数量，available-可售库存 defective-残次品数量 sold-已售数量 ts-暂存数量 real-实物库存
            $inventoryData = array(
                'items' => array(
                    array(
                        'id'             => $params['id'],
                        'nums'           => $params['nums'],
                        'inventory_type' => $params['inventory_type'],
                        'is_add'         => $is_add
                    )
                )
            );
            if ($params['inventory_type'] == 'defective' && isset($params['is_affect_available']) && ($params['is_affect_available'] == 1)) {
                $inventoryData['items'][] = [
                    'id'             => $params['id'],
                    'nums'           => $params['nums'],
                    'inventory_type' => 'available',
                    'is_add'         => !$is_add
                ];
            }
        }
        try {
            $result = Db::name('cross_inventory')->where(['id' => $params['id'], 'is_delete' => 0])->update($updateData);
            if (empty($result)) $this->throwError('修改库存记录信息失败');
            if (isset($inventoryData)) {
                $this->crossInventoryDeal($inventoryData, 2, $params['operator'], '', '后台编辑');
            }
        } catch (\Exception $e) {
            $this->throwError($e->getMessage());
        }
        //实物库存增加触发订单自动推单
        if (!empty($params['inventory_type']) && $params['inventory_type'] == 'real' && $params['operate_type'] == 'inc') {
            $crossPushData = array(
                'exchange_name' => 'orders',
                'routing_key'   => 'cross_auto_push',
                'data'          => base64_encode(json_encode(['short_code' => $goods_barcode]))
            );
            httpPostString(env('ITEM.QUEUE_URL'), json_encode($crossPushData));
        }
        //残次品减少触发自动推单
        if (!empty($params['inventory_type']) && $params['inventory_type'] == 'defective' && $params['operate_type'] == 'dec') {
            $crossPushData = array(
                'exchange_name' => 'orders',
                'routing_key'   => 'cross_auto_push',
                'data'          => base64_encode(json_encode(['short_code' => $goods_barcode]))
            );
            httpPostString(env('ITEM.QUEUE_URL'), json_encode($crossPushData));
        }
        return true;
    }

    /**
     * Description:添加库存管理备注
     * Author: zrc
     * Date: 2022/9/20
     * Time: 14:13
     * @param $params
     * @return bool
     * @throws \Exception
     */
    public function addRemarks($params)
    {
        $isset = Db::name('cross_inventory')->where(['id' => $params['cross_inventory_id'], 'is_delete' => 0])->count();
        if ($isset == 0) $this->throwError('库存记录信息不存在或已删除');
        $remarks = array(
            'cross_inventory_id' => $params['cross_inventory_id'],
            'admin_id'           => $params['admin_id'],
            'content'            => $params['content'],
            'created_time'       => time()
        );
        $result  = Db::name('cross_inventory_remarks')->insert($remarks);
        if (empty($result)) $this->throwError('添加备注失败');
        return true;
    }

    /**
     * Description:库存管理备注列表
     * Author: zrc
     * Date: 2022/9/20
     * Time: 14:36
     * @param $params
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function remarksList($params)
    {
        $result = Db::name('cross_inventory_remarks')->where(['cross_inventory_id' => $params['cross_inventory_id']])->order('id desc')->select()->toArray();
        if (count($result) > 0) {
            $admin_id = array_unique(array_column($result, 'admin_id'));
            foreach ($admin_id as $key => $val) {
                if ($val == 0) unset($admin_id[$key]);
            }
            $adminInfo = $this->httpGet(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info', ['admin_id' => implode(',', $admin_id), 'field' => 'realname']);
            foreach ($result as &$val) {
                $val['created_time'] = date('Y-m-d H:i:s', $val['created_time']);
                if ($val['admin_id'] == 0) $val['admin_id'] = '系统';
                if (isset($adminInfo['data'][$val['admin_id']])) $val['admin_id'] = $adminInfo['data'][$val['admin_id']];
            }
        }
        return ['list' => $result];
    }

    /**
     * Description:库存记录导入
     * Author: zrc
     * Date: 2022/9/21
     * Time: 10:45
     * @param $excelData
     * @param $params
     * @return bool
     * @throws \Exception
     */
    public function importInventory($excelData, $params)
    {
        $es = new ElasticSearchService();
        Db::startTrans();
        try {
            foreach ($excelData as $key => $val) {
                if (count($val) < 20) $this->throwError('表格数据有误，请下载模板操作');
                $information                          = array();
                $information['goods_barcode']         = trim($val[1]);
                $information['goods_name']            = trim($val[2]);
                $information['year']                  = trim($val[3]);
                $information['country']               = trim($val[4]);
                $information['area']                  = trim($val[5]);
                $information['category']              = trim($val[6]);
                $information['capacity']              = trim($val[7]);
                $information['purchase_nums']         = trim($val[8]);
                $information['supplier_name']         = trim($val[9]);
                $information['purchase_time']         = strtotime($val[10]);
                $information['is_pay']                = trim($val[11]) == 'Y' ? 1 : 0;
                $information['store_type']            = trim($val[12]) == '南沙' ? 2 : 1;
                $information['enter_nums']            = trim($val[13]);
                $information['pickup_warehouse']      = trim($val[14]);
                $information['purchase_price']        = trim($val[15]);
                $information['currency']              = trim($val[16]);
                $information['actual_purchase_price'] = trim($val[17]);
                $information['defective_nums']        = trim($val[18]);
//                $information['entry_time']            = trim($val[20]);
                //数据验证
                $validate = Validate::rule([
                    'goods_barcode|跨境ID'                  => 'require',
                    'goods_name|品名'                       => 'require',
                    'year|年份'                             => 'require',
                    'country|国家'                          => 'require',
                    'area|产区'                             => 'require',
                    'category|品类'                         => 'require',
                    'capacity|容量'                         => 'require',
                    'purchase_nums|采购数量'                => 'require|number',
                    'supplier_name|供货商名称'              => 'require',
                    'purchase_time|采购日期'                => 'require',
                    'is_pay|是否付款'                       => 'require',
                    'enter_nums|入库数量'                   => 'require|number',
                    'pickup_warehouse|提货仓库'             => 'require',
                    'purchase_price|采购价（外币）'           => 'require|float',
                    'currency|币种'                         => 'require',
                    'actual_purchase_price|实际采购价格RMB' => 'require|float',
                    'defective_nums|残次品'                 => 'require|number',
                    'entry_time|残次品'                     => 'date',
                ]);
                if (!$validate->check($information)) {
                    $this->throwError(trim($val[1]) . $validate->getError(), ErrorCode::PARAM_ERROR);
                }
//                $information['entry_time'] = empty($information['entry_time']) ? 0 : strtotime($information['entry_time']);
                //商品状态处理 商品状态：0-未知 1-已安排文案 2-在售中 3-已下架未售完 4-已售完
                $arr                         = array(
                    'index'  => ['periods'],
                    'match'  => [['short_code' => $information['goods_barcode']], ['periods_type' => 2]],
                    'source' => ['id', 'onsale_status', 'periods_type', 'sold_out_time', 'predict_shipment_time'],
                    'sort'   => [['id' => 'desc']],
                    'limit'  => 1
                );
                $esPeriodsData               = $es->getDocumentList($arr);
                $information['goods_status'] = 0;
                if (isset($esPeriodsData['data'][0]['onsale_status'])) {
                    //onsale_status上架状态（0：待上架，1：待售中，2：在售中，3：已下架，4：已售馨）
                    switch ($esPeriodsData['data'][0]['onsale_status']) {
                        case 0:
                        case 1:
                            $information['goods_status'] = 1;
                            break;
                        case 2:
                            $information['goods_status'] = 2;
                            break;
                        case 3:
                            $information['goods_status']       = 3;
                            $information['last_takedown_time'] = strtotime($esPeriodsData['data'][0]['sold_out_time']);
                            break;
                        case 4:
                            $information['goods_status']       = 4;
                            $information['last_takedown_time'] = strtotime($esPeriodsData['data'][0]['sold_out_time']);
                            break;
                    }
                }
                //预计发货时间处理
                $information['predict_time']   = isset($esPeriodsData['data'][0]['predict_shipment_time']) ? strtotime($esPeriodsData['data'][0]['predict_shipment_time']) : 0;
                $information['sold_nums']      = 0;
                $information['ts_nums']        = 0;
                $information['available_nums'] = $information['purchase_nums'] - $information['defective_nums'];
                $information['real_nums']      = $information['enter_nums'];
                $information['operator']       = $params['admin_id'];
                $information['update_time']    = time();
                $information['created_time']   = time();
                $cross_inventory_id            = Db::name('cross_inventory')->insertGetId($information);
                if (empty($cross_inventory_id)) $this->throwError(trim($val[1]) . '库存记录写入失败');
                if (!empty(trim($val[19]))) {
                    //备注写入
                    $remarks = array(
                        'cross_inventory_id' => $cross_inventory_id,
                        'admin_id'           => $params['admin_id'],
                        'content'            => trim($val[19]),
                        'created_time'       => time()
                    );
                    $result  = Db::name('cross_inventory_remarks')->insert($remarks);
                    if (empty($result)) $this->throwError(trim($val[1]) . '添加备注失败');
                }
                //日志记录
                $log = array(
                    'cross_inventory_id' => $cross_inventory_id,
                    'submit_data'        => json_encode($val, JSON_UNESCAPED_UNICODE),
                    'type'               => 1,
                    'operator'           => $params['admin_id'],
                    'created_time'       => time(),
                );
                Db::name('cross_inventory_change_log')->insert($log);
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->throwError($e->getMessage());
        }
        return true;
    }

    /**
     * Description:入库数量导入
     * Author: zrc
     * Date: 2022/9/21
     * Time: 17:39
     * @param $excelData
     * @param $params
     * @return bool
     * @throws \Exception
     */
    public function importInventoryNums($excelData, $params)
    {
        Db::startTrans();
        try {
            $updateItems    = [];
            $short_code_arr = [];
            foreach ($excelData as $key => $val) {
                if (count($val) < 7) $this->throwError('表格数据有误，请下载模板操作');
                $goods_barcode = trim($val[1]);
                $supplier_name = trim($val[3]);
                $store         = trim($val[4]);
                $nums          = trim($val[5]);
//                $entry_time    = trim($val[7]);
                $store_type = 0;
                if ($store == '古斯缇') $store_type = 1;
                if ($store == '南沙') $store_type = 2;
                $cross_inventory_id = Db::name('cross_inventory')->where(['goods_barcode' => $goods_barcode, 'supplier_name' => $supplier_name, 'store_type' => $store_type, 'is_delete' => 0])->order('id desc')->value('id');
                if (empty($cross_inventory_id)) $this->throwError($goods_barcode . '库存记录信息不存在或已删除');
                if ($nums > 0) {
                    $updateItems[]    = array('id' => $cross_inventory_id, 'nums' => intval($nums), 'inventory_type' => 'enter', 'is_add' => true);
                    $updateItems[]    = array('id' => $cross_inventory_id, 'nums' => intval($nums), 'inventory_type' => 'real', 'is_add' => true);
                    $short_code_arr[] = $goods_barcode;
                }
                if ($nums < 0) {
                    $updateItems[] = array('id' => $cross_inventory_id, 'nums' => intval(abs($nums)), 'inventory_type' => 'enter', 'is_add' => false);
                    $updateItems[] = array('id' => $cross_inventory_id, 'nums' => intval(abs($nums)), 'inventory_type' => 'real', 'is_add' => false);
                }
//                if (!empty($entry_time)) {
//                    $entry_time = strtotime($entry_time);
//                    if ($entry_time) {
//                        Db::name('cross_inventory')->where('id', $cross_inventory_id)->update(compact('entry_time'));
//                    }
//                }
                if (!empty(trim($val[4]))) {
                    //添加备注
                    $remarks = array(
                        'cross_inventory_id' => $cross_inventory_id,
                        'admin_id'           => $params['admin_id'],
                        'content'            => trim($val[6]),
                        'created_time'       => time()
                    );
                    $result  = Db::name('cross_inventory_remarks')->insert($remarks);
                    if (empty($result)) $this->throwError(trim($val[1]) . '添加备注失败');
                }
            }
            if (!empty($updateItems)) {
                $inventoryData = array(
                    'items' => $updateItems
                );
                $this->crossInventoryDeal($inventoryData, 2, $params['admin_id'], '', '批量导入');
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->throwError($e->getMessage());
        }
        //实物库存增加触发订单自动推单
        if (!empty($short_code_arr)) {
            $crossPushData = array(
                'exchange_name' => 'orders',
                'routing_key'   => 'cross_auto_push',
                'data'          => base64_encode(json_encode(['short_code' => implode(',', $short_code_arr)]))
            );
            httpPostString(env('ITEM.QUEUE_URL'), json_encode($crossPushData));
        }
        return true;
    }

    /**
     * Description:删除库存记录
     * Author: zrc
     * Date: 2022/11/17
     * Time: 9:43
     * @param $params
     * @return bool
     * @throws \think\db\exception\DbException
     */
    public function delInventory($params)
    {
        $result = Db::name('cross_inventory')->where(['id' => $params['cross_inventory_id']])->update(['is_delete' => 1, 'operator' => $params['admin_id'], 'update_time' => time()]);
        if (empty($result)) $this->throwError('删除失败');
        return true;
    }

    /**
     * Description:批量修改库存记录信息
     * Author: zrc
     * Date: 2022/9/22
     * Time: 10:44
     * @param $params
     * @return bool
     * @throws \Exception
     */
    public function batchUpdate($params)
    {
        $cross_inventory_id_arr = explode(',', $params['cross_inventory_ids']);
        $updateData             = array(
            $params['field'] => $params['content'],
            'operator'       => $params['admin_id'],
            'update_time'    => time(),
        );
        Db::startTrans();
        try {
            $result = Db::name('cross_inventory')->where([['id', 'in', $cross_inventory_id_arr]])->update($updateData);
            if (empty($result)) $this->throwError('批量更新失败');
            foreach ($cross_inventory_id_arr as &$val) {
                //日志记录
                $log = array(
                    'cross_inventory_id' => $val,
                    'submit_data'        => json_encode($params, JSON_UNESCAPED_UNICODE),
                    'type'               => 2,
                    'operator'           => $params['admin_id'],
                    'created_time'       => time(),
                );
                Db::name('cross_inventory_change_log')->insert($log);
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->throwError($e->getMessage());
        }
        return true;
    }

    /**
     * Description:导出库存信息记录
     * Author: zrc
     * Date: 2022/9/22
     * Time: 17:19
     * @param $params
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function exportInventory($params)
    {
        //创建导出队列
        $pushData = array(
            'exchange_name' => 'orders',
            'routing_key'   => 'cross_inventory_export',
            'data'          => base64_encode(json_encode($params))
        );
        $result   = httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
        if (!isset($result['error_code']) || $result['error_code'] != 0) $this->throwError('推送导出队列失败');
        return true;
    }

    /**
     * Description:导出库存信息记录队列处理
     * Author: zrc
     * Date: 2022/9/22
     * Time: 17:25
     * @param $params
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function exportInventoryMqDeal($params)
    {
        // 记录开始时间
        $start_time = microtime(true);

        $params = json_decode($params, true);

        $p_is_analyze_order = $params['is_analyze_order'] ?? 0;

        $where  = [];
        if (!empty($params['goods_barcode'])) {
            $where[] = ["goods_barcode", "=", $params['goods_barcode']];
        }
        if (!empty($params['goods_name'])) {
            $where[] = ["goods_name", "like", "%{$params['goods_name']}%"];
        }
        if (!empty($params['supplier_name'])) {
            $where[] = ["supplier_name", "like", "%{$params['supplier_name']}%"];
        }
        if (!empty($params['purchase_time_s'])) {
            $where[] = ["purchase_time", ">=", strtotime($params['purchase_time_s'])];
        }
        if (!empty($params['purchase_time_e'])) {
            $where[] = ["purchase_time", "<", strtotime($params['purchase_time_e'])];
        }
        if (!empty($params['predict_time_s'])) {
            $where[] = ["predict_time", ">=", strtotime($params['predict_time_s'])];
        }
        if (!empty($params['predict_time_e'])) {
            $where[] = ["predict_time", "<", strtotime($params['predict_time_e'])];
        }
        if (!empty($params['last_takedown_time_s'])) {
            $where[] = ["last_takedown_time", ">=", strtotime($params['last_takedown_time_s'])];
        }
        if (!empty($params['last_takedown_time_e'])) {
            $where[] = ["last_takedown_time", "<", strtotime($params['last_takedown_time_e'])];
        }
        if (isset($params['goods_status']) && is_numeric($params['goods_status'])) {
            $where[] = ["goods_status", "=", $params['goods_status']];
        }
        if (isset($params['store_type']) && is_numeric($params['store_type'])) {
            $where[] = ["store_type", "=", $params['store_type']];
        }
        if (!empty($params['pickup_warehouse'])) {
            $where[] = ["pickup_warehouse", "like", "%{$params['pickup_warehouse']}%"];
        }
        $where[] = ["is_delete", "=", 0];
        $lists   = Db::name('cross_inventory')->where($where)->order('created_time desc')->select()->toArray();
        // 库存记录ID
        $inventory_id = array_unique(array_column($lists, 'id'));

        // 查询备注
        $inventory_remarks = Db::name('cross_inventory_remarks')
            ->whereIn('cross_inventory_id', $inventory_id)
            ->order('id asc')
            ->column('cross_inventory_id,content');
        $remarks           = [];
        foreach ($inventory_remarks as $v) {
            if (empty($remarks[$v['cross_inventory_id']])) {
                $remarks[$v['cross_inventory_id']] = $v['content'] . PHP_EOL;
            } else {
                $remarks[$v['cross_inventory_id']] .= $v['content'] . PHP_EOL;
            }
        }

        if($p_is_analyze_order == 1){
        $goods_barcodes           = array_values(array_unique(array_column($lists, 'goods_barcode')));
        $has_short_periods        = Es::name(Es::PERIODS)->where([
            ['short_code', 'in', $goods_barcodes]
        ])->field('id,short_code')->order(['id' => 'desc'])->select()->toArray();
        $goods_barcode_period_ids = [];
        foreach ($has_short_periods as $has_short_period) {
            foreach ($has_short_period['short_code'] as $goods_barcode) {
                $goods_barcode_period_ids[$goods_barcode][] = $has_short_period['id'];
            }
        }

        $pids                  = Db::table('vh_wiki.vh_products')->where('short_code', 'in', $goods_barcodes)->column('short_code', 'id');
        $has_short_pkgs        = Db::table('vh_commodities.vh_periods_cross_set')
            ->where('period_id', 'in', array_column($has_short_periods, 'id'))
            ->where(function ($query) use ($pids) {
                foreach ($pids as $pid => $sc) {
                    $query->whereOr('associated_products', 'LIKE', "%:{$pid},%");
                }
            })->column('associated_products', 'id');
        #region 优化
        $pkg_pords    = [];
        $all_gpkg_ids = [];
        foreach ($has_short_pkgs as $pkg_id => $associated_products) {
            $associated_products = json_decode($associated_products, true);
            foreach ($associated_products as $ap_info) {
                if (!is_array($ap_info['product_id']) && !empty($pids[$ap_info['product_id']])) {
                    if (!empty($ap_info['sub_package_id'])) {
                        $pkg_pords[$ap_info['sub_package_id']][$pids[$ap_info['product_id']]] = $ap_info['nums'];
                        $all_gpkg_ids[]                                                       = $ap_info['sub_package_id'];
                    }
                    $pkg_pords[$pkg_id][$pids[$ap_info['product_id']]] = $ap_info['nums'];
                    $all_gpkg_ids[]                                    = $pkg_id;
                }
            }
        }
        $all_gpkg_ids = array_values(array_unique($all_gpkg_ids));

        $temp_quantitys = [];
        $wh_list        = range(1, 2);

        $ts_where   = $wd_where = [
            ['package_id', 'in', $all_gpkg_ids], //简码包含的全部套餐ID
            ['is_delete', '=', 0], //是否删除 0-正常 1-已删除
            ['order_type', '=', 2], //是否删除 2-跨境
            ['store_type', 'in', $wh_list],
            ['sub_order_status', '=', 1], //订单状态=1 订单状态：0-待支付 1-已支付 2-已发货 3-已完成 4-已取消
            ['refund_status', '=', 0], //退款状态=0  0-未退款 1-退款中 2-退款成功 3-退款失败
        ];
        $ts_where[] = ['is_ts', '=', 1]; //是否暂存：0否 1是
        $ts_where[] = ['push_store_status', '!=', 1]; //代发仓推送状态：0-未推送 1-推送成功 2-推送失败 3-不推送
        $wd_where[] = ['push_store_status', '=', 1]; //代发仓推送状态：0-未推送 1-推送成功 2-推送失败 3-不推送

        $temp_orders  = Es::name(Es::ORDERS)->where($ts_where)->field('id,store_type,order_qty,package_id')->select()->toArray();
        $wait_deliver = Es::name(Es::ORDERS)->where($wd_where)->field('id,store_type,order_qty,package_id')->select()->toArray();

        $temp_orders_g  = [];
        $wait_deliver_g = [];
        foreach ($temp_orders as $tmp_order) {
            $temp_orders_g[$tmp_order['package_id']][$tmp_order['store_type']] = bcadd(($temp_orders_g[$tmp_order['package_id']][$tmp_order['store_type']] ?? 0), $tmp_order['order_qty']);
        }
        foreach ($wait_deliver as $wait_order) {
            $wait_deliver_g[$wait_order['package_id']][$wait_order['store_type']] = bcadd(($wait_deliver_g[$wait_order['package_id']][$wait_order['store_type']] ?? 0), $wait_order['order_qty']);
        }

        foreach ($wait_deliver_g as $top_pkg_id => $tog) {
            $tog_pkg_prod = $pkg_pords[$top_pkg_id];
            foreach ($tog as $tog_type => $tog_nums) {
                foreach ($tog_pkg_prod as $tog_sc => $tog_prod_num) {
                    $temp_quantitys[$tog_sc][$tog_type][0] = bcadd(($temp_quantitys[$tog_sc][$tog_type][0] ?? 0), bcmul($tog_prod_num, $tog_nums)); //已支付未发货
                }
            }
        }
        foreach ($temp_orders_g as $top_pkg_id => $tog) {
            $tog_pkg_prod = $pkg_pords[$top_pkg_id];
            foreach ($tog as $tog_type => $tog_nums) {
                foreach ($tog_pkg_prod as $tog_sc => $tog_prod_num) {
                    $temp_quantitys[$tog_sc][$tog_type][1] = bcadd(($temp_quantitys[$tog_sc][$tog_type][1] ?? 0), bcmul($tog_prod_num, $tog_nums)); //暂存
                }
            }
        }
        #endregion 优化

        #推单时需要【实物库存-次品数量-暂存数量-已推送未发货数量】
        $entry_nums             = [];
        $temp_cross_inventorys  = Db::name('cross_inventory')->where([
            ['goods_barcode', 'in', $goods_barcodes],
            ['store_type', 'in', $wh_list],
            ['is_delete', '=', 0]
        ])->column('id,goods_barcode,store_type,real_nums,defective_nums');
        $group_cross_inventorys = array_group($temp_cross_inventorys, 'goods_barcode');
        foreach ($group_cross_inventorys as &$group_cross_inventory_format) {
            $group_cross_inventory_format = array_group($group_cross_inventory_format, 'store_type');
        }

        foreach ($group_cross_inventorys as $b_code => $group_cross_inventory) {
            foreach ($wh_list as $wh_id) {
                $wh_gci                      = $group_cross_inventory[$wh_id] ?? [];
                $entry_nums[$b_code][$wh_id] = bcsub(array_sum(array_column($wh_gci, 'real_nums')), array_sum(array_column($wh_gci, 'defective_nums'))); //实物库存 - 次品库存之和
            }
        }
        }

        foreach ($lists as &$value) {
            $i_temp_quantity = $value['ts_nums'];
            if ($p_is_analyze_order == 1) {
                //是否暂存：0否 1是
                $i_temp_quantity = $temp_quantitys[$value['goods_barcode']][$value['store_type']][1] ?? 0; //暂存数
                $i_normal_temp_quantity = $temp_quantitys[$value['goods_barcode']][$value['store_type']][0] ?? 0; //已推送未发货数量
                $i_quantity = bcadd(strval($i_temp_quantity), strval($i_normal_temp_quantity));

                $value['temp_quantity'] = $i_temp_quantity;
                $value['normal_temp_quantity'] = $i_normal_temp_quantity;
                $value['pushable'] = bcsub(strval($entry_nums[$value['goods_barcode']][$value['store_type']] ?? 0), strval($i_quantity)); //可推送数量
            }

            switch ($value['goods_status']) {
                case 0:
                    $value['goods_status'] = '未知';
                    break;
                case 1:
                    $value['goods_status'] = '已安排文案';
                    break;
                case 2:
                    $value['goods_status'] = '在售中';
                    break;
                case 3:
                    $value['goods_status'] = '已下架未售完';
                    break;
                case 4:
                    $value['goods_status'] = '已售完';
                    break;
            }
            switch ($value['is_pay']) {
                case 0:
                    $value['is_pay'] = '否';
                    break;
                case 1:
                    $value['is_pay'] = '是';
                    break;
            }
            switch ($value['store_type']) {
                case 1:
                    $value['store_type'] = '古斯缇';
                    break;
                case 2:
                    $value['store_type'] = '南沙仓';
                    break;
            }
            $oneData = [
                "goods_barcode"         => $value['goods_barcode'],
                "goods_name"            => $value['goods_name'],
                "year"                  => $value['year'],
                "country"               => $value['country'],
                "area"                  => $value['area'],
                "category"              => $value['category'],
                "capacity"              => $value['capacity'],
                "goods_status"          => $value['goods_status'],
                "purchase_nums"         => $value['purchase_nums'],
                "supplier_name"         => $value['supplier_name'],
                "purchase_time"         => $value['purchase_time'] == 0 ? '-' : date('Y-m-d', $value['purchase_time']),
                "is_pay"                => $value['is_pay'],
                "last_takedown_time"    => $value['last_takedown_time'] == 0 ? '-' : date('Y-m-d H:i:s', $value['last_takedown_time']),
                "predict_time"          => $value['predict_time'] == 0 ? '-' : date('Y-m-d', $value['predict_time']),
                "store_type"            => $value['store_type'],
                "enter_nums"            => $value['enter_nums'],
                "pickup_warehouse"      => $value['pickup_warehouse'],
                "purchase_price"        => $value['purchase_price'],
                "currency"              => $value['currency'],
                "actual_purchase_price" => $value['actual_purchase_price'],
                "sold_nums"             => $value['sold_nums'],
                "ts_nums"               => $i_temp_quantity, //$value['ts_nums'],
                "available_nums"        => $value['available_nums'],
                "defective_nums"        => $value['defective_nums'],
                "real_nums"             => $value['real_nums'],
                "update_time"           => $value['update_time'] == 0 ? '-' : date('Y-m-d H:i:s', $value['update_time']),
                "created_time"          => $value['created_time'] == 0 ? '-' : date('Y-m-d H:i:s', $value['created_time']),
                "remarks"               => $remarks[$value['id']] ?? '',
            ];
            if ($p_is_analyze_order == 1) {
                $oneData['pushable'] = $value['pushable'];
            }

            $data[] = $oneData;
        }
        // 导出类型：0-明显，1-汇总
        if (!empty($params['export_type']) && $params['export_type'] == 1) {
            $summary = [];
            foreach ($data as $v) {
                $key = $v['goods_barcode'] . '_' . $v['store_type'];
                if (empty($summary[$key])) {
                    $summary[$key] = $v;
                    continue;
                }
                // 汇总数据
                $summary[$key]['purchase_nums']  += $v['purchase_nums'];
                $summary[$key]['enter_nums']     += $v['enter_nums'];
                $summary[$key]['sold_nums']      += $v['sold_nums'];
                $summary[$key]['ts_nums']        = $v['ts_nums'];
                $summary[$key]['available_nums'] += $v['available_nums'];
                $summary[$key]['defective_nums'] += $v['defective_nums'];
                $summary[$key]['real_nums']      += $v['real_nums'];
                $summary[$key]['remarks']        .= $v['remarks'];
            }
            $data = array_merge($summary);
        }
        $filename = "crossInventoryRecord";
        $header   = array(
            array('column' => 'goods_barcode', 'name' => '跨境ID', 'width' => 15),
            array('column' => 'goods_name', 'name' => '品名', 'width' => 30),
            array('column' => 'year', 'name' => '年份', 'width' => 15),
            array('column' => 'country', 'name' => '国家', 'width' => 15),
            array('column' => 'area', 'name' => '产区', 'width' => 15),
            array('column' => 'category', 'name' => '品类', 'width' => 15),
            array('column' => 'capacity', 'name' => '容量', 'width' => 15),
            array('column' => 'goods_status', 'name' => '商品状态', 'width' => 15),
            array('column' => 'purchase_nums', 'name' => '采购数量', 'width' => 15),
            array('column' => 'supplier_name', 'name' => '供货商名称', 'width' => 15),
            array('column' => 'purchase_time', 'name' => '采购日期', 'width' => 15),
            array('column' => 'is_pay', 'name' => '是否付款', 'width' => 15),
            array('column' => 'last_takedown_time', 'name' => '上次下架时间', 'width' => 15),
            array('column' => 'predict_time', 'name' => '承诺发货日期', 'width' => 15),
            array('column' => 'store_type', 'name' => '目的仓库', 'width' => 15),
            array('column' => 'enter_nums', 'name' => '入库数量', 'width' => 15),
            array('column' => 'pickup_warehouse', 'name' => '提货仓库', 'width' => 15),
            array('column' => 'purchase_price', 'name' => '采购价(外币)', 'width' => 15),
            array('column' => 'currency', 'name' => '币种', 'width' => 15),
            array('column' => 'actual_purchase_price', 'name' => '实际采购价(RMB)', 'width' => 15),
            array('column' => 'sold_nums', 'name' => '已售数量', 'width' => 15),
            array('column' => 'ts_nums', 'name' => '暂存数量', 'width' => 15),
            array('column' => 'available_nums', 'name' => '可售库存', 'width' => 15),
            array('column' => 'defective_nums', 'name' => '残次品数量', 'width' => 15),
            array('column' => 'real_nums', 'name' => '实物库存', 'width' => 15),
            array('column' => 'update_time', 'name' => '数据更新时间', 'width' => 15),
            array('column' => 'created_time', 'name' => '数据创建时间', 'width' => 15),
            array('column' => 'remarks', 'name' => '备注', 'width' => 15),
        );

        if ($p_is_analyze_order == 1) {
            $header[] = array('column' => 'pushable', 'name' => '可推送库存', 'width' => 15);
        }
        try {
            $uploadUrl = exportSheelExcel($data, $header, $filename);
            if (empty($uploadUrl)) $this->throwError('生成excel文件异常');
            $file     = app()->getRootPath() . "public/storage/" . $uploadUrl;
            $media_id = weixinUpload($file, '跨境库存记录表.xlsx');
            if (empty($media_id)) $this->throwError('上传企业微信临时文件失败');
            unlink($file);
            //获取发起人企业微信信息
            $userInfo = $this->httpGet(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info', ['admin_id' => $params['admin_id'], 'field' => 'userid,dept_id']);
            if ($userInfo['error_code'] != 0 || !isset($userInfo['data'][$params['admin_id']])) $this->throwError('未获取到企业微信用户信息');
            $userid  = $userInfo['data'][$params['admin_id']]['userid'];
            $msgData = array(
                'content' => $media_id,
                'userid'  => $userid,
                'msgtype' => 'file',
                'agentid' => 0,
            );
            $result  = httpPostString(env('ITEM.WECHART_URL') . '/wechat/v3/wecom/app/send', json_encode($msgData));
            if ($result['error_code'] != 0) $this->throwError('发送文件到企业微信失败');
        } catch (\Exception $e) {
            $this->throwError("导出失败：" . $e->getMessage());
        }
        return true;
    }

    /**
     * Description:跨境订单监听处理
     * Author: zrc
     * Date: 2022/9/23
     * Time: 16:40
     * @param $requestparams
     * @return bool
     * @throws \Exception
     */
    public function crossOrderMonitorDeal($requestparams)
    {
        $params = json_decode($requestparams, true);
        $es     = new ElasticSearchService();
        if (
            $params['table'] == 'vh_cross_order'
            && ((isset($params['old']['sub_order_status']) && ($params['sub_order_status'] == 1 || $params['sub_order_status'] == 2 || $params['sub_order_status'] == 3))
                || (isset($params['old']['refund_status']) && in_array($params['refund_status'], [2])))
        ) {
            try {
                //查询库存记录
                $arr           = array(
                    'index'  => ['periods'],
                    'match'  => [['id' => $params['period']]],
                    'source' => ['id', 'short_code'],
                );
                $esPeriodsData = $es->getDocumentList($arr);
                if (!isset($esPeriodsData['data'][0]['short_code'])) $this->throwError('未获取到商品信息');
                $esPeriodsSetData = $es->getDocumentList(['index' => ['periods_set'], 'match' => [['id' => $params['package_id']]], 'source' => ['associated_products'], 'limit' => 1]);
                if (!isset($esPeriodsSetData['data'][0])) $this->throwError('未获取到商品套餐信息');
                $associated_products                    = json_decode($esPeriodsSetData['data'][0]['associated_products'], true);
                $pids                                   = array_column($associated_products, 'product_id');
                $short_codes                            = Db::table('vh_wiki.vh_products')->where('id', 'in', $pids)->column('id', 'short_code');
                $esPeriodsData['data'][0]['short_code'] = array_keys($short_codes);
                $inventory_ids                          = Db::name('cross_inventory')->where([
                    ['goods_barcode', 'in', array_keys($short_codes)],
                    ['store_type', '=', $params['store_type']],
                    ['is_delete', '=', 0]
                ])->order('id desc')->column('id');

                //是否瑕疵品：0-否，1-是
                $p_period           = Db::table('vh_commodities.vh_periods_cross')->where('id', $params['period'])->find();
                $is_defective_goods = $p_period['is_defective_goods'] ?? 0;

                //订单支付处理
                if ($params['oper_type'] == 'UPDATE' && $params['sub_order_status'] == 1 && isset($params['old']['sub_order_status'])) {
                    //查询库存记录
//                    $arr           = array(
//                        'index'  => ['periods'],
//                        'match'  => [['id' => $params['period']]],
//                        'source' => ['id', 'short_code'],
//                    );
//                    $esPeriodsData = $es->getDocumentList($arr);
//                    if (!isset($esPeriodsData['data'][0]['short_code'])) $this->throwError('未获取到商品信息');
//                    $esPeriodsSetData = $es->getDocumentList(['index' => ['periods_set'], 'match' => [['id' => $params['package_id']]], 'source' => ['associated_products'], 'limit' => 1]);
//                    if (!isset($esPeriodsSetData['data'][0])) $this->throwError('未获取到商品套餐信息');
//                    $associated_products = json_decode($esPeriodsSetData['data'][0]['associated_products'], true);
//                    $pids = array_column($associated_products,'product_id');
//                    $short_codes = Db::table('vh_wiki.vh_products')->where('id','in',$pids)->column( 'id','short_code');
//                    $esPeriodsData['data'][0]['short_code'] = array_keys($short_codes);
                    foreach ($esPeriodsData['data'][0]['short_code'] as &$val) {
                        $num = 1;
                        foreach ($associated_products as &$v) {
                            if (($short_codes[$val] ?? $esPeriodsData['data'][0]['id']) == $v['product_id']) $num = $v['nums'];
                        }
                        $where             = array(
                            ['goods_barcode', '=', $val],
                            ['store_type', '=', $params['store_type']],
                            ['is_delete', '=', 0],
                            ['available_nums', '>=', $num * $params['order_qty']]
                        );
                        $inventoryRecordId = Db::name('cross_inventory')->where($where)->order('id asc')->value('id');
                        if (empty($inventoryRecordId)) {
                            //异常提示
                            $store     = $params['store_type'] == 1 ? '古斯缇' : '南沙';
                            $content   = "# 跨境库存监听变动异常\n";
                            $content   .= "-子订单号：" . $params['sub_order_no'] . "\n";
                            $content   .= "-订单仓库：" . $store . "\n";
                            $content   .= "-商品条码：" . $val . "\n";
                            $content   .= "-商品数量：" . $num * $params['order_qty'] . "\n";
                            $content   .= "-异常信息：未获取到产品库存记录信息或可售库存不足\n";
                            $queueData = array(
                                'access_token' => env('ORDERS.cross_token'),
                                'type'         => 'text',
                                'at'           => '***********,***********,***********',
                                'content'      => base64_encode($content),
                            );
                            $data      = base64_encode(json_encode($queueData));
                            $pushData  = array(
                                'exchange_name' => 'dingtalk',
                                'routing_key'   => 'dingtalk_sender',
                                'data'          => $data,
                            );
                            httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
                            $this->throwError($val . '未获取到产品库存记录信息');
                        }
                        $is_channel = Db::table('vh_commodities.vh_periods_cross')->where('id', $params['period'])->value('is_channel');
                        $excess_num = $excess_id = 0;
                        if ($is_channel == 1) {
                            //锁定数量
                            $excess = Db::name('cross_excess')
                                ->where([
                                    ['goods_barcode', '=', $val],
                                    ['store_type', '=', $params['store_type']],
                                    ['status', '=', 1],
                                ])->column('id,num');

                            $excess_id = $excess[0]['id'] ?? 0;
                            if ($excess_id) {
                                Db::name('cross_excess')->where('id', $excess_id)->update([
                                    'status'      => 2,
                                    'update_time' => time(),
                                    'remark'      => $params['sub_order_no'] ?? '',
                                ]);
                            }
                            $excess_num = array_sum(array_column($excess, 'num'));
                        }
                        $nums  = $num * $params['order_qty'];
                        $items = [];
                        //加已售
                        $items[] = array(
                            'id'             => $inventoryRecordId,
                            'nums'           => $nums,
                            'inventory_type' => 'sold',
                            'is_add'         => true,
                        );
                        //加已售未发货
                        $items[] = array(
                            'id'             => $inventoryRecordId,
                            'nums'           => $nums,
                            'inventory_type' => 'ts',
                            'is_add'         => true,
                        );
                        //减可售
                        $items[]       = array(
                            'id'             => $inventoryRecordId,
                            'nums'           => $nums,
                            'inventory_type' => 'available',
                            'is_add'         => false,
                        );
                        $inventoryData = array('items' => $items);
                        $this->crossInventoryDeal($inventoryData, 3, 0, $params['sub_order_no'], '订单支付');
                    }
                }
                //订单发货处理
                if ($params['oper_type'] == 'UPDATE' && $params['sub_order_status'] == 2 && isset($params['old']['sub_order_status'])) {
                    $log = Db::name('cross_inventory_change_log')->field('cross_inventory_id,submit_data')->where('cross_inventory_id', 'in', $inventory_ids)->where(['sub_order_no' => $params['sub_order_no']])->order('id asc')->find();
                    if ($log) {
                        $inventoryRecordId = intval($log['cross_inventory_id']);
                        $submit_data       = json_decode($log['submit_data'], true);
                        $nums              = $submit_data['items'][0]['nums'] ?? 1;
                        //减已售未发货
                        $items[] = array(
                            'id'             => $inventoryRecordId,
                            'nums'           => $nums,
                            'inventory_type' => 'ts',
                            'is_add'         => false,
                        );
                        if($is_defective_goods == 1){
                            //减次品
                            $items[]       = array(
                                'id'             => $inventoryRecordId,
                                'nums'           => $nums,
                                'inventory_type' => 'defective',
                                'is_add'         => false,
                            );
                        }
                        //减实物
                        $items[]       = array(
                            'id'             => $inventoryRecordId,
                            'nums'           => $nums,
                            'inventory_type' => 'real',
                            'is_add'         => false,
                        );
                        $inventoryData = array('items' => $items);
                        $this->crossInventoryDeal($inventoryData, 3, 0, $params['sub_order_no'], '订单发货');
                    } else {
                        //查询库存记录
//                        $arr           = array(
//                            'index'  => ['periods'],
//                            'match'  => [['id' => $params['period']]],
//                            'source' => ['id', 'short_code'],
//                        );
//                        $esPeriodsData = $es->getDocumentList($arr);
//                        if (!isset($esPeriodsData['data'][0]['short_code'])) $this->throwError('未获取到商品信息');
//                        $esPeriodsSetData = $es->getDocumentList(['index' => ['periods_set'], 'match' => [['id' => $params['package_id']]], 'source' => ['associated_products'], 'limit' => 1]);
//                        if (!isset($esPeriodsSetData['data'][0])) $this->throwError('未获取到商品套餐信息');
//                        $associated_products = json_decode($esPeriodsSetData['data'][0]['associated_products'], true);
                        foreach ($esPeriodsData['data'][0]['short_code'] as &$val) {
                            $num = 1;
                            foreach ($associated_products as &$v) {
                                if (($short_codes[$val] ?? $esPeriodsData['data'][0]['id']) == $v['product_id']) $num = $v['nums'];
                            }
                            $where             = array(
                                ['goods_barcode', '=', $val],
                                ['store_type', '=', $params['store_type']],
                                ['is_delete', '=', 0]
                            );
                            $inventoryRecordId = Db::name('cross_inventory')->where($where)->order('id desc')->value('id');
                            if (empty($inventoryRecordId)) $this->throwError($val . '未获取到产品库存记录信息');
                            $nums  = $num * $params['order_qty'];
                            $items = [];
                            //减已售未发货
                            $items[] = array(
                                'id'             => $inventoryRecordId,
                                'nums'           => $nums,
                                'inventory_type' => 'ts',
                                'is_add'         => false,
                            );
                            if ($is_defective_goods == 1) {
                                //减次品
                                $items[] = array(
                                    'id'             => $inventoryRecordId,
                                    'nums'           => $nums,
                                    'inventory_type' => 'defective',
                                    'is_add'         => false,
                                );
                            }
                            //减实物
                            $items[]       = array(
                                'id'             => $inventoryRecordId,
                                'nums'           => $nums,
                                'inventory_type' => 'real',
                                'is_add'         => false,
                            );
                            $inventoryData = array('items' => $items);
                            $this->crossInventoryDeal($inventoryData, 3, 0, $params['sub_order_no'], '订单发货');
                        }
                    }
                    if ($inventoryRecordId) {
                        Db::name('cross_inventory')->where('id', $inventoryRecordId)->update(['last_out_time' => time()]);
                    }
                }

                //订单收货处理 判断物流是否异常
                if ($params['oper_type'] == 'UPDATE' && $params['sub_order_status'] == 3 && isset($params['old']['sub_order_status'])) {
                    try {
                        $order = Es::name(Es::ORDERS)->where([
                            ["_id",'==',$params['sub_order_no']]
                        ])->find();
                        $waybill_info = \Curl::kuaidi100MapTrack([
                            'logisticCode' => $order['express_number'],
                            'expressType'  => $order['express_type'],
                        ]);
                        if (!empty($waybill_info)) {
                            if ($waybill_info['state'] == 7 || strpos(json_encode($waybill_info['traces'], JSON_UNESCAPED_UNICODE), '转投') !== false) {
                                //异常三次推送 自然年
                                $year = date('Y');
                                Db::name('cross_logistics')->insert([
                                    'year'         => $year,
                                    'uid'          => $order['uid'],
                                    'sub_order_no' => $order['sub_order_no'],
                                    'traces'       => json_encode($waybill_info['traces'], JSON_UNESCAPED_UNICODE),
                                    'remark'       => '',
                                    'created_time' => time(),
                                ]);
                                $clcount = Db::name('cross_logistics')->where([
                                    'year' => $year,
                                    'uid'  => $order['uid'],
                                ])->group('sub_order_no')->count();

                                if ($clcount >= 3) {

                                    $user = [];
                                    if (!empty($order['uid'])) {
                                        $user = Db::table('vh_user.vh_user')->where('uid', $order['uid'])->field('nickname,telephone')->find();
                                    }
                                    $enc = [];
                                    if (!empty($user['telephone'])) $enc[] = $user['telephone'];
                                    if (!empty($order['consignee_phone'])) $enc[] = $order['consignee_phone'];
                                    if (!empty($order['consignee'])) $enc[] = $order['consignee'];
                                    $dec = \Curl::cryptionDeal(array_values(array_unique($enc)));

                                    $order_address = ($order['province_name'] ?? '').($order['city_name'] ?? '').($order['district_name'] ?? '').($order['address'] ?? '');
                                    $notice_msg = "# 跨境异常用户提示\n";
//                                    $notice_msg .= "-用户昵称：" . ($user['nickname'] ?? '') . "\n";
//                                    $notice_msg .= "-用户手机号码：" . ($dec[$user['telephone'] ?? ''] ?? '') . "\n";
                                    $notice_msg .= "-用户订单号码：" . ($order['sub_order_no'] ?? '') . "\n";
//                                    $notice_msg .= "-收件人手机号：" . ($dec[$order['consignee_phone'] ?? ''] ?? '') . "\n";
//                                    $notice_msg .= "-收件人姓名：" . ($dec[$order['consignee'] ?? ''] ?? '') . "\n";
//                                    $notice_msg .= "-收件地址：" . ($order_address ?? '') . "\n";
                                    $notice_msg .= "-异常原因：" . "用户订单自然年内物流转投达到{$clcount}次,单号: {$order['express_number']}" . "\n";
                                    Log::write("异常原因 inspectionCheck $notice_msg");
                                    \Curl::sendWechatSender([
                                        'msg'          => $notice_msg,
                                        'at'           => '',
                                        'access_token' => (env("APP_DEBUG") === true) ? '19bb5a7a-0cff-40db-8116-f7908e5b5522' : '556cf0e5-**************-8220514bd270',
                                    ]);

                                    $monitor = [
                                        'year'            => $year,
                                        "id_card_no"      => $order['id_card_no'] ?? '',
                                        "sub_order_no"    => $order['sub_order_no'] ?? '',
                                        "uid"             => $order['uid'] ?? '',
                                        "nickname"        => $user['nickname'] ?? '',
                                        "phone"           => $user['telephone'] ?? '',
                                        "consignee"       => $order['consignee'] ?? '',
                                        "consignee_phone" => $order['consignee_phone'] ?? '',
                                        "address"         => $order_address ?? '',
                                        "type"            => 6,
                                        "num"             => $clcount,
                                        "created_time"    => time(),
                                        "update_time"     => time(),
                                    ];
                                    $monitor_id = Db::name('cross_monitor')->insert($monitor);
                                    if ($monitor_id) {
                                        try {
                                            $timeoutParam = [
                                                'namespace' => 'orders_cross_monitor', //命名空间。可根据项目自定义名称。
                                                'key'       => $order['sub_order_no'], //唯一键名，在同一个命名空间下，如果有相同键名存在会覆盖已有数据。
                                                'data'      => base64_encode(json_encode(compact('monitor_id'))), //超时触发时，提交的数据。建议使用Base64编码（注：回调接口传递的数据会自动解码）。
                                                'callback'  => env('ITEM.ORDERS_URL') . '/orders/v3/cross/monitorAnomaly', //超时触发时，调用的接口回调地址。
                                                'timeout'   => '5s', //超时时间。支持s,m,h单位进行描述。比如：1s=1秒，1分钟=1m，1小时=1h
                                            ];
                                            \Curl::timingAdd($timeoutParam);
                                        } catch (\Exception $ex) {
                                            Log::write('跨境异常超时任务创建失败：' . json_encode($timeoutParam ?? ['key' => $order['sub_order_no']]) . ' ' . $ex->getMessage());
                                        }
                                    }
                                }
                            }
                        }
                    } catch (\Exception $e) {
                    }
                }

                //退款修改处理
                if ($params['oper_type'] == 'UPDATE' && isset($params['old']['refund_status']) && $params['refund_status'] == 2) {
                    $excess = Db::name('cross_excess')->where('sub_order_no', $params['sub_order_no'])->where('status', 0)->find();
                    $now    = time();
                    $log    = Db::name('cross_inventory_change_log')->field('cross_inventory_id,submit_data')->where('cross_inventory_id', 'in', $inventory_ids)->where(['sub_order_no' => $params['sub_order_no']])->order('id asc')->find();
                    Db::name('cross_lock_order')->where('sub_order_no', $params['sub_order_no'])->update([
                        'status' => 0,
                        'remark' => '订单退款自动解除锁定'
                    ]);
                    if ($log) {
                        $inventoryRecordId = intval($log['cross_inventory_id']);
                        $submit_data       = json_decode($log['submit_data'], true);
                        $nums              = $submit_data['items'][0]['nums'] ?? 1;
                        //减已售
                        $items[] = array(
                            'id'             => $inventoryRecordId,
                            'nums'           => $nums,
                            'inventory_type' => 'sold',
                            'is_add'         => false,
                        );
                        //未发货退款订单退还可售库存
                        if (empty($params['express_number'])) {
                            //减已售未发货
                            $items[] = array(
                                'id'             => $inventoryRecordId,
                                'nums'           => $nums,
                                'inventory_type' => 'ts',
                                'is_add'         => false,
                            );
                            //加可售
                            $items[]       = array(
                                'id'             => $inventoryRecordId,
                                'nums'           => $nums,
                                'inventory_type' => 'available',
                                'is_add'         => true,
                            );
                            $inventoryData = array('items' => $items);
                            $this->crossInventoryDeal($inventoryData, 3, 0, $params['sub_order_no'], '订单退款');
                            //已删除记录恢复
                            $is_delete = Db::name('cross_inventory')->where(['id' => $inventoryRecordId])->value('is_delete');
                            if ($is_delete == 1) {
                                Db::name('cross_inventory')->where(['id' => $inventoryRecordId])->update(['is_delete' => 0, 'update_time' => time()]);
                            }
                        }
                    } else {
                        //查询库存记录
//                        $arr           = array(
//                            'index'  => ['periods'],
//                            'match'  => [['id' => $params['period']]],
//                            'source' => ['id', 'short_code'],
//                        );
//                        $esPeriodsData = $es->getDocumentList($arr);
//                        if (!isset($esPeriodsData['data'][0]['short_code'])) $this->throwError('未获取到商品信息');
//                        $esPeriodsSetData = $es->getDocumentList(['index' => ['periods_set'], 'match' => [['id' => $params['package_id']]], 'source' => ['associated_products'], 'limit' => 1]);
//                        if (!isset($esPeriodsSetData['data'][0])) $this->throwError('未获取到商品套餐信息');
//                        $associated_products = json_decode($esPeriodsSetData['data'][0]['associated_products'], true);
                        foreach ($esPeriodsData['data'][0]['short_code'] as &$val) {
                            $num = 1;
                            foreach ($associated_products as &$v) {
                                if (($short_codes[$val] ?? $esPeriodsData['data'][0]['id']) == $v['product_id']) $num = $v['nums'];
                            }
                            $where             = array(
                                ['goods_barcode', '=', $val],
                                ['store_type', '=', $params['store_type']],
                                ['is_delete', '=', 0]
                            );
                            $inventoryRecordId = Db::name('cross_inventory')->where($where)->order('id desc')->value('id');
                            if (empty($inventoryRecordId)) $this->throwError($val . '未获取到产品库存记录信息');
                            $nums  = $num * $params['order_qty'];
                            $items = [];
                            //减已售
                            $items[] = array(
                                'id'             => $inventoryRecordId,
                                'nums'           => $nums,
                                'inventory_type' => 'sold',
                                'is_add'         => false,
                            );
                            //未发货退款订单退还可售库存
                            if (empty($params['express_number'])) {
                                //减已售未发货
                                $items[] = array(
                                    'id'             => $inventoryRecordId,
                                    'nums'           => $nums,
                                    'inventory_type' => 'ts',
                                    'is_add'         => false,
                                );
                                //加可售
                                $items[]       = array(
                                    'id'             => $inventoryRecordId,
                                    'nums'           => $nums,
                                    'inventory_type' => 'available',
                                    'is_add'         => true,
                                );
                                $inventoryData = array('items' => $items);
                                $this->crossInventoryDeal($inventoryData, 3, 0, $params['sub_order_no'], '订单退款');
                                //已删除记录恢复
                                $is_delete = Db::name('cross_inventory')->where(['id' => $inventoryRecordId])->value('is_delete');
                                if ($is_delete == 1) {
                                    Db::name('cross_inventory')->where(['id' => $inventoryRecordId])->update(['is_delete' => 0, 'update_time' => time()]);
                                }
                            }
                        }
                    }
                    if (!empty($excess)) {
                        $excess['goods_barcode'] = array_keys($short_codes)[0];
                        $excess['store_type']    = $params['store_type'];
                        $excess['num']           = $params['order_qty'];
                        $excess['status']        = 1;
                        $excess['log_id']        = $inventoryRecordId;
                        $excess['unlock_time']   = $now + (7 * 24 * 3600);
                        $excess['update_time']   = $now;
                        Db::name('cross_excess')->where('id', $excess['id'])->update($excess);

                        //添加拼团超时任务
                        $pushData   = array(
                            'namespace' => 'cross_excess',
                            'key'       => $excess['sub_order_no'],
                            'data'      => base64_encode(json_encode(['id' => $excess['id']])),
                            'callback'  => env('ITEM.ORDERS_URL') . '/orders/v3/cross/excessPastDue',
                            'timeout'   => env('ORDERS.CROSS_EXCESS_LOCK_TIMING', '168h'),
                        );
                        $timing_res = httpPostString(env('ITEM.TIMEING_SERVICE_URL') . '/services/v3/timing/add', json_encode($pushData));
                        Log::write("添加拼团超时任务 " . json_encode($pushData) . '   ' . json_encode($timing_res));
                    }
                }
                $msg = '处理成功';
            } catch (\Exception $e) {
                $msg = $e->getMessage();
                $this->throwError($e->getMessage());
            } finally {
                //日志记录
                file_put_contents(root_path() . "/runtime/log/" . date('Ym') . "/" . date('Ymd') . 'monitor' . '.log', $requestparams . '处理结果：' . $msg . PHP_EOL, FILE_APPEND);
            }
        }
        return true;
    }

    /**
     * Description:跨境库存服务调用
     * Author: zrc
     * Date: 2022/9/26
     * Time: 13:19
     * @param $inventoryData
     * @param $type
     * @param int $operator
     * @param string $sub_order_no
     * @return bool
     * @throws \Exception
     */
    public function crossInventoryDeal($inventoryData, $type, $operator = 0, $sub_order_no = '', $note = '')
    {
        $result = httpPostString(env('ITEM.VINEHOO_INVENTORY_INOUT_URL') . '/inventory_service/v3/inventory/cross/update', json_encode($inventoryData));
        if (!isset($result['error_code']) || $result['error_code'] != 0) $this->throwError('库存数量调整失败：' . isset($result['error_msg']) ? $result['error_msg'] : '库存扣减服务异常');
        //记录日志
        $cross_inventory_id = implode(',', array_unique(array_column($inventoryData['items'], 'id')));
        $log                = array(
            'cross_inventory_id' => $cross_inventory_id,
            'submit_data'        => json_encode($inventoryData),
            'type'               => $type,
            'sub_order_no'       => $sub_order_no,
            'note'               => $note,
            'operator'           => $operator,
            'created_time'       => time(),
        );
        Db::name('cross_inventory_change_log')->insert($log);
        //推送企业微信群
        if (in_array($type, [3, 4])) {
            $cross_inventory = Db::name('cross_inventory')->where(['id' => $cross_inventory_id])->find();
            if ($cross_inventory && ($cross_inventory['available_nums'] < 0 || $cross_inventory['real_nums'] < 0)) {
                $msg = '';
                foreach ($inventoryData['items'] as &$v) {
                    switch ($v['inventory_type']) {
                        case 'enter':
                            $inventory_type = '入库数量';
                            break;
                        case 'purchase':
                            $inventory_type = '采购数量';
                            break;
                        case 'available':
                            $inventory_type = '可售库存';
                            break;
                        case 'defective':
                            $inventory_type = '残次品数量';
                            break;
                        case 'sold':
                            $inventory_type = '已售数量';
                            break;
                        case 'ts':
                            $inventory_type = '已支付暂存数量';
                            break;
                        case 'real':
                            $inventory_type = '实物库存';
                            break;
                    }
                    if ($v['is_add'] == false) $nums = '-' . $v['nums'];
                    if ($v['is_add'] == true) $nums = '+' . $v['nums'];
                    $msg .= $inventory_type . '【' . $nums . '】 ';
                }
                $store     = $cross_inventory['store_type'] == 1 ? '古斯缇' : '南沙';
                $content   = "## 跨境库存监听变动提示\n";
                $content   .= "-品名：" . $cross_inventory['goods_name'] . "\n";
                $content   .= "-国际条码：" . $cross_inventory['goods_barcode'] . "\n";
                $content   .= "-供货商名称：" . $cross_inventory['supplier_name'] . "\n";
                $content   .= "-仓库：" . $store . "\n";
                $content   .= "-子订单号：" . $sub_order_no . "\n";
                $content   .= "-变动类型：" . $note . "\n";
                $content   .= "-变动信息：" . $msg . "\n";
                $content   .= "-现有库存信息：采购数量【" . $cross_inventory['purchase_nums'] . "】 入库数量【" . $cross_inventory['enter_nums'] . "】 已售数量【" . $cross_inventory['sold_nums'] . "】 已支付暂存数量【" . $cross_inventory['ts_nums'] . "】 可售库存【" . $cross_inventory['available_nums'] . "】 残次品数量【" . $cross_inventory['defective_nums'] . "】 实物库存【" . $cross_inventory['real_nums'] . "】\n";
                $data      = array(
                    'title' => '跨境库存变动通知',
                    'text'  => $content
                );
                $queueData = array(
                    'access_token' => env('ORDERS.cross_token'),
                    'type'         => 'markdown',
                    'at'           => '',
                    'content'      => base64_encode(json_encode($data)),
                );
                $data      = base64_encode(json_encode($queueData));
                $pushData  = array(
                    'exchange_name' => 'dingtalk',
                    'routing_key'   => 'dingtalk_sender',
                    'data'          => $data,
                );
                httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
            }
        }
        return true;
    }

    /**
     * Description:跨境商品监听处理
     * Author: zrc
     * Date: 2022/9/26
     * Time: 14:17
     * @param $requestparams
     * @return bool
     * @throws \think\db\exception\DbException
     */
    public function crossPeriodMonitorDeal($requestparams)
    {
        $params = json_decode($requestparams, true);
        if ($params['table'] == 'vh_periods_cross' && (isset($params['old']['onsale_status']) || isset($params['old']['sold_out_time']))) {
            //查询库存记录
            $es            = new ElasticSearchService();
            $arr           = array(
                'index'  => ['periods'],
                'match'  => [['id' => $params['id']]],
                'source' => ['id', 'short_code'],
            );
            $esPeriodsData = $es->getDocumentList($arr);
            if (!isset($esPeriodsData['data'][0]['short_code'])) $this->throwError('未获取到商品信息');
            foreach ($esPeriodsData['data'][0]['short_code'] as &$val) {
                $inventoryRecordId = Db::name('cross_inventory')->where(['goods_barcode' => $val, 'is_delete' => 0])->order('id desc')->value('id');
                if (empty($inventoryRecordId)) $this->throwError($val . '未获取到产品库存记录信息');
                $updateData = [];
                if (isset($params['old']['onsale_status'])) {
                    switch ($params['onsale_status']) {
                        case 0:
                        case 1:
                            $updateData['goods_status'] = 1;
                            break;
                        case 2:
                            $updateData['goods_status'] = 2;
                            break;
                        case 3:
                            $updateData['goods_status'] = 3;
                            break;
                        case 4:
                            $updateData['goods_status'] = 4;
                            break;
                    }
                }
                if (isset($params['old']['sold_out_time'])) $updateData['last_takedown_time'] = $params['sold_out_time'];
                $updateData['update_time'] = time();
                $updateData['operator']    = 0;
                $result                    = Db::name('cross_inventory')->where(['id' => $inventoryRecordId])->update($updateData);
                if (empty($result)) $this->throwError('跨境商品监听处理失败');
            }
        }
        return true;
    }

    /**
     * Description:订单代付详情
     * Author: zrc
     * Date: 2022/9/29
     * Time: 13:59
     * @param $params
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function orderOnBehalfDetail($params)
    {
        $orderInfo = Db::name('order_main')
            ->alias('om')
            ->field('om.uid,om.payment_amount,om.created_time,co.sub_order_status,co.created_time,co.is_replace_pay,co.period,co.package_id,co.order_qty,co.order_type')
            ->leftJoin('cross_order co', 'co.main_order_id = om.id')
            ->where(['main_order_no' => $params['order_no']])
            ->find();
        if (empty($orderInfo)) $this->throwError('未获取到订单信息');
        $searchData  = array(
            'items' => array(
                array(
                    'db_name'    => 'vh_user',
                    'table_name' => 'vh_user',
                    'fields'     => ['avatar_image', 'nickname'],
                    'conditions' => 'uid=' . $orderInfo['uid']
                ),
                array(
                    'db_name'    => 'vh_commodities',
                    'table_name' => 'vh_periods_cross',
                    'fields'     => ['banner_img', 'title'],
                    'conditions' => 'id=' . $orderInfo['period']
                ),
                array(
                    'db_name'    => 'vh_commodities',
                    'table_name' => 'vh_periods_cross_set',
                    'fields'     => ['package_name', 'price'],
                    'conditions' => 'id=' . $orderInfo['package_id']
                ),
            )
        );
        $combineInfo = httpPostString(env('ITEM.MYSQL_BATCH_SEARCH') . '/services/v3/batchsearch/combine', json_encode($searchData));
        if (!isset($combineInfo['error_code']) || $combineInfo['error_code'] != 0) $this->throwError('未获取到订单相关信息');
        $nickname      = $combineInfo['data']['vh_user.nickname'] ?? '';
        $avatar_image  = $combineInfo['data']['vh_user.avatar_image'] ?? '';
        $banner_img    = $combineInfo['data']['vh_periods_cross.banner_img'] ?? '';
        $title         = $combineInfo['data']['vh_periods_cross.title'] ?? '';
        $package_name  = $combineInfo['data']['vh_periods_cross_set.package_name'] ?? '';
        $package_price = $combineInfo['data']['vh_periods_cross_set.price'] ?? '';
        if ($orderInfo['order_type'] != 2 || $orderInfo['is_replace_pay'] != 1) $this->throwError('订单不是代付订单');
        $countdown = env('ORDERS.replace_pay_time_out') - (time() - $orderInfo['created_time']);
        $result    = array(
            'order_no'       => $params['order_no'],
            'status'         => $orderInfo['sub_order_status'],
            'payment_amount' => $orderInfo['payment_amount'],
            'created_time'   => date('Y-m-d H:i:s', $orderInfo['created_time']),
            'countdown'      => $countdown <= 0 ? 0 : $countdown,
            'order_type'     => $orderInfo['order_type'],
            'is_replace_pay' => $orderInfo['is_replace_pay'],
            'nickname'       => $nickname,
            'avatar_image'   => imagePrefix($avatar_image),
            'is_own'         => 0,
            'goodsInfo'      => array(
                array(
                    'period'        => $orderInfo['period'],
                    'package_id'    => $orderInfo['package_id'],
                    'goods_img'     => imagePrefix($banner_img),
                    'goods_title'   => $title,
                    'package_name'  => $package_name,
                    'order_qty'     => $orderInfo['order_qty'],
                    'package_price' => $package_price,
                    'periods_type'  => $orderInfo['order_type'],
                    'label'         => 0
                )
            )
        );
        if ($orderInfo['uid'] == $params['uid']) $result['is_own'] = 1;
        return $result;
    }

    /**
     * Description:订单代付提交
     * Author: zrc
     * Date: 2022/9/29
     * Time: 14:37
     * @param $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function orderOnBehalfSubmit($params)
    {
        $orderMain = Db::name('order_main')
            ->alias('om')
            ->field('om.id,om.uid,om.main_order_no,om.main_order_status,om.payment_amount,co.order_type,co.is_replace_pay,co.created_time')
            ->leftJoin('cross_order co', 'co.main_order_id=om.id')
            ->where(['om.main_order_no' => $params['order_no']])
            ->find();
        if (empty($orderMain)) $this->throwError('未获取到订单信息');
        if ($orderMain['main_order_status'] == 1) $this->throwError('订单已支付，无需代付');
        if ($orderMain['main_order_status'] == 4) $this->throwError('订单已超时取消');
        if ($orderMain['order_type'] != 2 || $orderMain['is_replace_pay'] != 1) $this->throwError('订单不是代付订单');
        $behalfInfo = Db::name('cross_pay_on_behalf')->where(['main_order_no' => $orderMain['main_order_no']])->find();
        if (!empty($behalfInfo) && $behalfInfo['behalf_uid'] != $params['uid']) $this->throwError('该订单已被他人发起支付，请勿重复支付');
        //少数名族真实姓名含有全角符号的点替换成半角符号的点
        $params['realname'] = str_replace("．", "·", $params['realname']);
        $params['realname'] = str_replace(".", "·", $params['realname']);
        $params['realname'] = str_replace("。", "·", $params['realname']);
        $params['realname'] = str_replace("•", "·", $params['realname']);
        //用户信息加密处理
        $realname   = trim($params['realname']);
        $id_card_no = trim(strtoupper($params['id_card_no']));
        $encrypt    = cryptionDeal(1, [$realname, $id_card_no], $params['uid'], '前端用户');
        $realname   = isset($encrypt[$realname]) ? $encrypt[$realname] : '';
        $id_card_no = isset($encrypt[$id_card_no]) ? $encrypt[$id_card_no] : '';
        $behalfData = array(
            'uid'            => $orderMain['uid'],
            'main_order_no'  => $orderMain['main_order_no'],
            'status'         => 0,
            'behalf_uid'     => $params['uid'],
            'realname'       => $realname,
            'id_card_no'     => $id_card_no,
            'payment_amount' => $orderMain['payment_amount'],
        );
        $id         = Db::name('cross_pay_on_behalf')->where(['main_order_no' => $orderMain['main_order_no'], 'behalf_uid' => $params['uid']])->value('id');
        if ($id) {
            $behalfData['update_time'] = time();
            $deal_behalf               = Db::name('cross_pay_on_behalf')->where(['main_order_no' => $orderMain['main_order_no'], 'behalf_uid' => $params['uid']])->update($behalfData);
        } else {
            $behalfData['created_time'] = time();
            $deal_behalf                = Db::name('cross_pay_on_behalf')->insert($behalfData);
        }
        if (empty($deal_behalf)) $this->throwError('创建代付信息失败');
        $countdown = env('ORDERS.replace_pay_time_out') - (time() - $orderMain['created_time']);
        //返回前端数据
        $backParams = array(
            'main_order_no' => $orderMain['main_order_no'],
            'countdown'     => $countdown <= 0 ? 0 : $countdown,
            'created_time'  => $orderMain['created_time']
        );
        return $backParams;
    }

    /**
     * Description:跨境黑名单验证
     * Author: zrc
     * Date: 2022/10/28
     * Time: 11:24
     * @param $params
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function blackListCheck($params)
    {
        $toYear    = date('Y', time());
        $blacklist = Db::name('cross_quota_blacklist')->field('type,year')->where(array('id_card_no' => $params['id_card_no']))->find();

        $order = Db::name('order_main')->alias('m')
            ->join('cross_order o', 'm.id=o.main_order_id')
            ->where('m.main_order_no', $params['main_order_no'])
            ->field('o.id,o.predict_time,o.payment_amount')->find();

        if (!$order) $this->throwError('未找到预计发货时间！');
        $cross_predict_year = date('Y', $order['predict_time']);
        $is_intercept       = false; //是否拦截创建订单


        if ($blacklist['type'] == 2) $is_intercept = true;
        if ($toYear == $cross_predict_year) {
            if ($blacklist['type'] == 1 && $blacklist['year'] == $toYear) $is_intercept = true;
        } else {
            //查询该年份是否超额 该年份以往额度 + 这个订单额度是否 超过 26000
            $year_start             = "{$cross_predict_year}-01-01 00:00:00";
            $year_end               = date('Y-m-d H:i:s', strtotime($year_start . " +1year -1 second"));
            $enc_id_card_no         = cryptionDeal(1, [$params['id_card_no']], 1, '前端用户')[$params['id_card_no']];
            $history_payment_amount = Db::name('cross_order')
                ->where([
                    ['refund_status', '<>', 2],
                    ['sub_order_status', 'in', [1, 2, 3]],
                    ['id_card_no', '=', $enc_id_card_no],
                ])->whereBetweenTime('predict_time', $year_start, $year_end)
                ->sum('payment_amount');
            $total_year_amount      = bcadd($order['payment_amount'], $history_payment_amount, 2);
            if ($total_year_amount > 26000) $is_intercept = true;
        }

        if ($is_intercept) $this->throwError('跨境电子商务年度个人额度不足');

        return true;
    }

    /**
     * Description:跨境库存变动日志
     * Author: zrc
     * Date: 2022/11/3
     * Time: 9:29
     * @param $params
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function inventoryChangeLog($params)
    {
        $offset   = ($params['page'] - 1) * $params['limit'];
        $totalNum = Db::name('cross_inventory_change_log')
            ->where(['cross_inventory_id' => $params['cross_inventory_id']])
            ->count();
        $lists    = Db::name('cross_inventory_change_log')
            ->where(['cross_inventory_id' => $params['cross_inventory_id']])
            ->limit($offset, $params['limit'])
            ->order('id desc')
            ->select()->toArray();
        $admin_id = array_unique(array_column($lists, 'operator'));
        foreach ($admin_id as $key => $val) {
            if ($val == 0) unset($admin_id[$key]);
        }
        $adminInfo = $this->httpGet(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info', ['admin_id' => implode(',', $admin_id), 'field' => 'realname']);
        foreach ($lists as $key => $val) {
            $submit_data = json_decode($val['submit_data'], true);
            if (isset($submit_data['items'])) {
                $msg = '';
                foreach ($submit_data['items'] as &$v) {
                    switch ($v['inventory_type']) {
                        case 'enter':
                            $inventory_type = '入库数量';
                            break;
                        case 'purchase':
                            $inventory_type = '采购数量';
                            break;
                        case 'available':
                            $inventory_type = '可售库存';
                            break;
                        case 'defective':
                            $inventory_type = '残次品数量';
                            break;
                        case 'sold':
                            $inventory_type = '已售数量';
                            break;
                        case 'ts':
                            $inventory_type = '已支付暂存数量';
                            break;
                        case 'real':
                            $inventory_type = '实物库存';
                            break;
                    }
                    if ($v['is_add'] == false) $nums = '-' . $v['nums'];
                    if ($v['is_add'] == true) $nums = '+' . $v['nums'];
                    $msg .= $inventory_type . '：' . $nums . '；';
                }
                $lists[$key]['submit_data'] = isset($val['note']) ? '【' . $val['note'] . '】' . $msg : $msg;
                switch ($val['type']) {
                    case 1:
                        $lists[$key]['type'] = '后台添加';
                        break;
                    case 2:
                        $lists[$key]['type'] = '后台修改';
                        break;
                    case 3:
                        $lists[$key]['type'] = '监听服务修改';
                        break;
                    case 4:
                        $lists[$key]['type'] = '代发仓回执修改';
                        break;
                }
                $lists[$key]['created_time'] = date('Y-m-d H:i:s', $val['created_time']);
                if ($val['operator'] == 0) $lists[$key]['operator'] = '系统';
                if (isset($adminInfo['data'][$val['operator']])) $lists[$key]['operator'] = $adminInfo['data'][$val['operator']];
            } else {
                unset($lists[$key]);
            }
        }
        $result['list']  = array_values($lists);
        $result['total'] = $totalNum;
        return $result;
    }

    /**
     * Description:179跨境订单原始支付信息获取
     * Author: zrc
     * Date: 2022/12/6
     * Time: 16:37
     * @param $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function checkCustoms($params)
    {
        $orderData            = Db::name('order_main')
            ->alias('om')
            ->field('co.guid,co.ver_dept,co.payment_request_raw_data,co.payment_response_raw_data,om.payment_amount,om.payment_time,co.remarks,om.main_order_no,co.period,om.payment_subject,om.payment_method,om.tradeno')
            ->leftJoin('cross_order co', 'co.main_order_id=om.id')
            ->where(['om.main_order_no' => $params['orderno']])
            ->find();
        $result               = [];
        $exchangeSignatureStr = '';
        $payExchangeInfoHead  = [];
        $payExchangeInfoLists = [];
        if (!empty($orderData)) {
            $es                         = new ElasticSearchService();
            $arr                        = array(
                'index'  => ['periods'],
                'match'  => [['id' => $orderData['period']]],
                'source' => ['title'],
                'limit'  => 1,
            );
            $periods                    = $es->getDocumentList($arr);
            $title                      = $periods['data'][0]['title'] ?? '';
            $verify_department_trade_id = Db::name('cross_declare_log')->where(['main_order_no' => $orderData['main_order_no']])->order('id desc')->value('verify_department_trade_id');
            if (in_array($orderData['payment_subject'], [1, 2])) {
                $payType     = '312228036U';
                $recpAccount = '040102029003081388';
            } else if (in_array($orderData['payment_subject'], [3, 4])) {
                if (in_array($orderData['payment_method'], [3, 4, 5, 7, 8])) {
                    $payType     = '4403169D3W';
                    $recpAccount = '**********';
                }
                if (in_array($orderData['payment_method'], [0, 1, 2, 6])) {
                    $payType     = '31222699S7';
                    $recpAccount = '<EMAIL>';
                }
            }
            $time                 = time() . '000';
            $payExchangeInfoHead  = array(
                'guid'             => $orderData['guid'],
                'initalRequest'    => $orderData['payment_request_raw_data'],
                'initalResponse'   => "ok",
                'ebpCode'          => '50039609JR',
                'payCode'          => $payType,
                'payTransactionId' => !empty($verify_department_trade_id) ? $verify_department_trade_id : $orderData['tradeno'],
                'totalAmount'      => $orderData['payment_amount'],
                'currency'         => '142',
                'verDept'          => $orderData['ver_dept'],
                'payType'          => $orderData['payment_method'],
                'tradingTime'      => $orderData['payment_time'],
                'note'             => ''
            );
            $payExchangeInfoLists = array(
                array(
                    'orderNo'     => $orderData['main_order_no'],
                    'goodsInfo'   => array(
                        array(
                            'gname'    => $title,
                            'itemLink' => 'https://wineyun.com/pages/goods-detail/goods-detail?id=' . $orderData['period']
                        )
                    ),
                    'recpAccount' => $recpAccount,
                    'recpCode'    => '91500103MA60T4DH9B',
                    'recpName'    => '佰酿云酒(重庆)科技有限公司'
                )
            );
            $exchangeSignatureStr = base64_encode('"sessionID":"' . $params['sessionID'] . '"||"payExchangeInfoHead":"' . json_encode($payExchangeInfoHead, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES) . '"||"payExchangeInfoLists":"' . json_encode($payExchangeInfoLists, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES) . '"||"serviceTime":"' . $time . '"');
        }
        $result = array(
            'exchangeSignatureStr' => $exchangeSignatureStr,
            'payExchangeInfoHead'  => $payExchangeInfoHead,
            'payExchangeInfoLists' => $payExchangeInfoLists,
        );
        return $result;
    }

    /**
     * Description:验证跨境库存信息
     * Author: zrc
     * Date: 2023/3/2
     * Time: 14:20
     * @param $params
     * @return bool
     * @throws \Exception
     */
    public function verifyCrossStock($params)
    {
        $params['store_type'] = 1;
        if ($params['warehouse_code'] == '028') $params['store_type'] = 2;
        $where          = array(
            'goods_barcode' => $params['goods_barcode'],
            'store_type'    => $params['store_type'],
            'is_delete'     => 0
        );
        $available_nums = Db::name('cross_inventory')->where($where)->sum('available_nums');
        $defective_num = Db::name('cross_inventory')->where($where)->sum('defective_nums');
        $p_period = $params['period'] ?? false;
        if ($p_period) {
            $is_defective_goods = Es::name(Es::PERIODS)->where([
                ['_id', '==', $p_period],
            ])->value('is_defective_goods');
            if ($is_defective_goods == 1) {
                //次品
                if ($defective_num < $params['inventory_nums']) $this->throwError($params['goods_barcode'] . '次品库存不足！');
            } else {
                $available_nums -= $defective_num;
            }
        } else {
            $available_nums -= $defective_num;
        }
        if ($available_nums < $params['inventory_nums']) $this->throwError($params['goods_barcode'] . '可售库存不足！');
        return true;
    }

    /**
     * Description:钉钉审批跨境限额黑名单录入
     * Author: zrc
     * Date: 2023/3/21
     * Time: 15:21
     * @param $requestparams
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function inputBlackListCallBack($requestparams)
    {
        $params = $requestparams['process_instance'];
        foreach ($params['form_component_values'] as &$val) {
            if (isset($val['name'])) {
                if ($val['name'] == '身份证号') $id_card_no = trim($val['value']);
                if ($val['name'] == '录入原因') $note = trim($val['value']);
            }
        }
        $weChatService = new WeChatService();
        if (empty($id_card_no) || empty($note)) {
            $weChatService->weChatSendText($params['originator_userid'], '未获取到身份证号码和拉黑原因,黑名单录入审批流处理失败!');
            $this->throwError('未获取到身份证号码和拉黑原因,黑名单录入审批流处理失败!');
        }
        //审批通过处理
        if ($params['result'] == 'agree') {
            $data = array(
                'type'       => 1,
                'id_card_no' => $id_card_no,
                'note'       => $note,
                'sponsor'    => $params['originator_userid']
            );
            $this->inputBlackList($data);
        }
        return true;
    }

    /**
     * Description:黑名单列表
     * Author: zrc
     * Date: 2023/3/22
     * Time: 11:16
     * @param $params
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function blackList($params)
    {
        $offset  = ($params['page'] - 1) * $params['limit'];
        $where   = [];
        $toYear  = date('Y', time());
        $where[] = ['year', '=', $toYear];
        if (!empty($params['id_card_no'])) {
            $where[] = ['id_card_no', '=', $params['id_card_no']];
        }
        $totalNum = Db::name('cross_quota_blacklist')->where($where)->count();
        $lists    = Db::name('cross_quota_blacklist')->where($where)->limit($offset, $params['limit'])->order('created_time desc')->select()->toArray();
        foreach ($lists as $key => $val) {
            $lists[$key]['created_time'] = date('Y-m-d H:i:s', $val['created_time']);
            $lists[$key]['sponsor']      = empty($val['sponsor']) ? '' : $val['sponsor'];
        }
        $result['list']  = $lists;
        $result['total'] = $totalNum;
        return $result;
    }


    public function ldleStock()
    {
        $goods_barcodes = Db::name('cross_inventory')
            ->where('real_nums', '>', 0)
            ->where('is_delete', 0)
            ->group('goods_barcode')
            ->column('goods_barcode');

        $has_short_periods        = Es::name(Es::PERIODS)->where([
            ['short_code', 'in', $goods_barcodes]
        ])->field('id,short_code')->order(['id' => 'desc'])->select()->toArray();
        $goods_barcode_period_ids = [];
        foreach ($has_short_periods as $has_short_period) {
            foreach ($has_short_period['short_code'] as $goods_barcode) {
                $goods_barcode_period_ids[$goods_barcode][] = $has_short_period['id'];
            }
        }
        $pids                  = Db::table('vh_wiki.vh_products')->where('short_code', 'in', $goods_barcodes)->column('short_code', 'id');
        $has_short_pkgs        = Db::table('vh_commodities.vh_periods_cross_set')
            ->where('period_id', 'in', array_column($has_short_periods, 'id'))
            ->where(function ($query) use ($pids) {
                foreach ($pids as $pid => $sc) {
                    $query->whereOr('associated_products', 'LIKE', "%:{$pid},%");
                }
            })->column('associated_products', 'id');
        $goods_barcode_pkg_ids = [];
        foreach ($has_short_pkgs as $pkg_id => $associated_products) {
            $associated_products = json_decode($associated_products, true);
            foreach ($associated_products as $ap_info) {
                if (!is_array($ap_info['product_id']) && !empty($pids[$ap_info['product_id']])) {
                    if (!empty($ap_info['sub_package_id']))
                        $goods_barcode_pkg_ids[$pids[$ap_info['product_id']]][] = $ap_info['sub_package_id'];
                    $goods_barcode_pkg_ids[$pids[$ap_info['product_id']]][] = $pkg_id;
                }
            }
        }

        $temp_quantitys = [];

        $wh_list = range(1, 2);
        foreach ($goods_barcode_period_ids as $i_goods_barcode => $i_goods_barcode_period_id) {
            if (in_array($i_goods_barcode, $goods_barcodes)) {

                $ts_where = $wd_where = [
                    ['period', 'in', $i_goods_barcode_period_id], //简码包含的全部期数ID
                    ['package_id', 'in', $goods_barcode_pkg_ids[$i_goods_barcode] ?? []], //简码包含的全部套餐ID
                    ['is_delete', '=', 0], //是否删除 0-正常 1-已删除
                    ['order_type', '=', 2], //是否删除 2-跨境
                    ['store_type', 'in', $wh_list],
                    ['sub_order_status', '=', 1], //订单状态=1 订单状态：0-待支付 1-已支付 2-已发货 3-已完成 4-已取消
                    ['refund_status', '<>', 2], //退款状态=0  0-未退款 1-退款中 2-退款成功 3-退款失败
                ];

//                $ts_where[] = ['is_ts', '=', 1]; //是否暂存：0否 1是
//                $ts_where[] = ['push_store_status', '!=', 1]; //代发仓推送状态：0-未推送 1-推送成功 2-推送失败 3-不推送
//                $wd_where[] = ['push_store_status', '=', 1]; //代发仓推送状态：0-未推送 1-推送成功 2-推送失败 3-不推送

                $temp_orders = Es::name(Es::ORDERS)->where($ts_where)->field('id,store_type,order_qty')->select()->toArray();
//                $wait_deliver = Es::name(Es::ORDERS)->where($wd_where)->field('id,store_type,order_qty')->select()->toArray();
                $wait_deliver = [];

                $temp_order_group   = array_group($temp_orders, 'store_type');
                $wait_deliver_group = array_group($wait_deliver, 'store_type');

                foreach ($wh_list as $wh_id) {
                    //代发仓：1-古斯缇 2-南沙仓
                    $temp_quantitys[$i_goods_barcode][$wh_id][0] = array_sum(array_column($wait_deliver_group[$wh_id] ?? [], 'order_qty')); //已支付未发货
                    $temp_quantitys[$i_goods_barcode][$wh_id][1] = array_sum(array_column($temp_order_group[$wh_id] ?? [], 'order_qty')); //暂存
                }
            }
        }

        $excess       = Db::name('cross_excess')
            ->where('status', 1)
            ->where('unlock_time', '>', time())
            ->column('id,goods_barcode,store_type,num');
        $excess_locks = [];
        foreach ($excess as $exces) {
            $excess_locks[$exces['goods_barcode']][$exces['store_type']] = bcadd(($excess_locks[$exces['goods_barcode']][$exces['store_type']] ?? 0), $exces['num']);
        }

        #推单时需要【实物库存-次品数量-暂存数量-已推送未发货数量 - 超额锁定库存】
        $entry_nums             = [];
        $defective_nums             = [];
        $temp_cross_inventorys  = Db::name('cross_inventory')->where([
            ['goods_barcode', 'in', $goods_barcodes],
            ['store_type', 'in', $wh_list],
            ['is_delete', '=', 0]
        ])->column('id,goods_barcode,store_type,real_nums,defective_nums');
        $group_cross_inventorys = array_group($temp_cross_inventorys, 'goods_barcode');
        foreach ($group_cross_inventorys as &$group_cross_inventory_format) {
            $group_cross_inventory_format = array_group($group_cross_inventory_format, 'store_type');
        }
        foreach ($group_cross_inventorys as $b_code => $group_cross_inventory) {
            foreach ($wh_list as $wh_id) {
                $wh_gci                      = $group_cross_inventory[$wh_id] ?? [];
//                $entry_nums[$b_code][$wh_id] = bcsub(array_sum(array_column($wh_gci, 'real_nums')), array_sum(array_column($wh_gci, 'defective_nums'))); //实物库存 - 次品库存之和
                $entry_nums[$b_code][$wh_id] = array_sum(array_column($wh_gci, 'real_nums')); //实物库存 - 次品库存之和
                $entry_nums[$b_code][$wh_id] -= ($excess_locks[$b_code][$wh_id] ?? 0); // 实物库存 - 超额锁定库存数量
                $defective_nums[$b_code][$wh_id] = array_sum(array_column($wh_gci, 'defective_nums')); //实物库存 - 次品库存之和
            }
        }

        $has_sales_short_code = [];
        foreach ($entry_nums as $en_short_code => $en_arr) {
            $i_temp_quantity = bcadd(array_sum(($temp_quantitys[$en_short_code][2] ?? [])), array_sum(($temp_quantitys[$en_short_code][1] ?? [])));
            $en_arr_sum      = bcsub(array_sum($en_arr), $i_temp_quantity);
            if ($en_arr_sum > 0) {
                $has_sales_short_code[$en_short_code] = $en_arr_sum;
            }
        }

        $en_short_periods = Es::name(Es::PERIODS)->where([
            ['short_code', 'in', array_keys($has_sales_short_code)], //简码
            ['periods_type', 'in', [2]],
            ['onsale_status', 'in', [1, 2]], //上架状态（0：待上架，1：待售中，2：在售中，3：已下架，4：已售罄）
        ])->field('id,short_code')->order(['id' => 'desc'])->select()->toArray();

        foreach ($en_short_periods as $en_short_period) {
            foreach (($en_short_period['short_code'] ?? []) as $en_short_period_code) {
                unset($has_sales_short_code[$en_short_period_code]);
            }
        }

        $products = Es::name(Es::PRODUCTS)->where([['short_code', 'in', array_keys($has_sales_short_code)]])->field('short_code,en_product_name')->select()->toArray();
        $products = array_column($products, null, 'short_code');

        $out_data[] = ['商品简码', '英文名', '闲置类型', '闲置库存数', '采购人', '下架时间', '支付时间', '残次品数量'];
        foreach ($has_sales_short_code as $send_short_code => $send_nums) {
            $last_period       = Db::table('vh_commodities.vh_periods_cross')
                ->whereFindInSet('short_code', $send_short_code)
                ->where('onsale_verify_status', '1')
                ->order('sold_out_time', "DESC")
                ->field('id,sold_out_time,buyer_name')
                ->find();
            $last_period_time  = empty($last_period['sold_out_time']) ? '' : date('Y-m-d H:i:s', $last_period['sold_out_time']);
            $last_payment_time = '';
            if (!empty($last_period['id'])) {
                $last_payment_time = Db::name('cross_order')
                    ->where('period', $last_period['id'])
                    ->where('refund_status', 'in', [0, 1, 3])
                    ->where('payment_time', '>', 0)
                    ->order('payment_time', 'DESC')
                    ->value('payment_time');
                $last_payment_time = empty($last_payment_time) ? '' : date('Y-m-d H:i:s', $last_payment_time);
            }

            $out_data[] = [
                strval($send_short_code),
                ($products[$send_short_code]['en_product_name'] ?? ''),
                '现货',
                $send_nums,
                $last_period['buyer_name'] ?? '',
                $last_period_time,
                $last_payment_time,
                bcadd(($defective_nums[strval($send_short_code)][1] ?? 0), ($defective_nums[strval($send_short_code)][2] ?? 0)),
            ];
        }

        #region 预售
        $gbodes = Db::name('cross_inventory')
            ->where('goods_barcode', 'in', $goods_barcodes)
            ->where('real_nums', '=', 0)
            ->where('available_nums', '>', 0)
            ->where('is_delete', 0)
            ->column('goods_barcode,available_nums');
        $gbodes = array_group($gbodes, 'goods_barcode');

        $es_periods = Es::name(Es::PERIODS)->where([
            ['short_code', 'in', array_keys($gbodes)], //简码
            ['periods_type', 'in', [2]],
            ['onsale_status', 'in', [1, 2]], //上架状态（0：待上架，1：待售中，2：在售中，3：已下架，4：已售罄）
        ])->field('id,short_code')->order(['id' => 'desc'])->select()->toArray();

        foreach ($es_periods as $es_period) {
            foreach (($es_period['short_code'] ?? []) as $es_period_code) {
                unset($gbodes[$es_period_code]);
            }
        }

        $products = Es::name(Es::PRODUCTS)->where([['short_code', 'in', array_keys($gbodes)]])->field('short_code,en_product_name')->select()->toArray();
        $products = array_column($products, null, 'short_code');

        foreach ($gbodes as $send_short_code => $send_nums) {
            $send_nums   = array_sum(array_column($send_nums, 'available_nums'));
            $last_period = Db::table('vh_commodities.vh_periods_cross')
                ->whereFindInSet('short_code', $send_short_code)
                ->where('onsale_verify_status', '1')
                ->order('sold_out_time', "DESC")
                ->field('id,sold_out_time,buyer_name')
                ->find();

            $last_period_time  = empty($last_period['sold_out_time']) ? '' : date('Y-m-d H:i:s', $last_period['sold_out_time']);
            $last_payment_time = '';
            if (!empty($last_period['id'])) {
                $last_payment_time = Db::name('cross_order')
                    ->where('period', $last_period['id'])
                    ->where('refund_status', 'in', [0, 1, 3])
                    ->where('payment_time', '>', 0)
                    ->order('payment_time', 'DESC')
                    ->value('payment_time');
                $last_payment_time = empty($last_payment_time) ? '' : date('Y-m-d H:i:s', $last_payment_time);
            }

            $out_data[] = [
                strval($send_short_code),
                ($products[$send_short_code]['en_product_name'] ?? ''),
                '预售',
                $send_nums,
                $last_period['buyer_name'] ?? '',
                $last_period_time,
                $last_payment_time,
                bcadd(($defective_nums[strval($send_short_code)][1] ?? 0), ($defective_nums[strval($send_short_code)][2] ?? 0)),
            ];
        }
        #endregion 预售

        $title    = date('Y-m-d') . "跨境闲置存货";
        $filename = "{$title}.xlsx";
        $path     = app()->getRuntimePath() . 'storage' . DIRECTORY_SEPARATOR;
        if (!is_dir($path)) {
            mkdir($path, 0755, true);
        }
        $excel      = new Excel(compact('path'));
        $file_path  = $excel->fileName($filename, 'sheet')->data($out_data)->output();
        $send_param = [
            'to'         => [
                [
                    'address' => '<EMAIL>',
                    'name'    => '跨境采购组'
                ],
            ],
            'attachment' => [
                $file_path,
            ],
            'title'      => $title,
            'content'    => $title
        ];
        $res        = \Mailer::send($send_param);
        unlink($file_path);
        return $res;
    }

    public function lockedOrder()
    {
        //跨境订单已退款但还出于锁定状态,  需要将锁定状态清除
        try {
            $sub_nos    = CrossLockOrder::where('status', 1)->column('id,sub_order_no,remark', 'sub_order_no');
            $refund_nos = Db::name('cross_order')->where('sub_order_no', 'in', array_keys($sub_nos))
                ->where('refund_status', '=', 2) //退款状态 0-未退款 1-退款中 2-退款成功 3-退款失败
                ->column('sub_order_no');
            foreach ($refund_nos as $refund_no) {
                $refund_up                = $sub_nos[$refund_no];
                $refund_up['status']      = 0;
                $refund_up['update_time'] = time();
                $refund_up['remark']      .= ' 订单已退款, 自动解除锁定';
                CrossLockOrder::where('id', $refund_up['id'])->update($refund_up);
            }
        } catch (\Exception $e) {
            Log::write('跨境订单已退款但还出于锁定状态,  需要将锁定状态清除 错误: ' . $e->getMessage() . ' ' . $e->getLine());
        }

        $list = CrossLockOrder::where('status', 1)
            ->whereTime('lock_time', '<=', date('Y-m-d H:i:s', strtotime('-1 week')))
            ->column('*');

        $main_order_ids = array_column($list, 'order_main_id');
        $sub_orders     = Es::name(Es::ORDERS)->where([
            ['main_order_id', 'in', $main_order_ids],
            ['sub_order_status', 'in', [1, 2, 3,]],
            ['refund_status', 'in', [0, 1, 3,]]
        ])->field('main_order_id,period')->select()->toArray();
        $sub_orders     = array_column($sub_orders, null, 'main_order_id');
        $periods        = array_column($sub_orders, 'period');

        $period_infos = Es::name(Es::PERIODS)->where([['id', 'in', $periods]])->field('short_code,id')->select()->toArray();
        $period_infos = array_column($period_infos, null, 'id');

        $short_codes = [];
        foreach ($period_infos as $info) {
            foreach ($info['short_code'] as $sc_item) {
                $short_codes[] = $sc_item;
            }
        }
        $short_codes = array_values(array_unique($short_codes));
        $products    = Es::name(Es::PRODUCTS)->where([['short_code', 'in', $short_codes]])->field('short_code,en_product_name')->select()->toArray();
        $products    = array_column($products, null, 'short_code');


        $out_data[] = ['主订单号', '子订单号', '英文名', '简码', '备注'];
        foreach ($list as $lock_info) {
            $sub_order_item = $sub_orders[$lock_info['order_main_id']] ?? [];
            if (!empty($sub_order_item)) {
                $order_period           = $sub_order_item['period'] ?? 0;
                $order_periodinfo       = $period_infos[$order_period] ?? [];
                $order_short_codes      = $order_periodinfo['short_code'] ?? [];
                $order_product_en_names = [];
                foreach ($order_short_codes as $shoret_code) {
                    $order_product_en_names[] = $products[$shoret_code]['en_product_name'] ?? '';
                }

                $out_data[] = [
                    $lock_info['main_order_no'],
                    $lock_info['sub_order_no'],
                    implode(',', $order_product_en_names),
                    implode(',', $order_short_codes),
                    $lock_info['remark'],
                ];
            }
        }

        $title    = date('Y-m-d') . "锁定时长超过7天订单";
        $filename = "{$title}.xlsx";
        $path     = app()->getRuntimePath() . 'storage' . DIRECTORY_SEPARATOR;
        if (!is_dir($path)) {
            mkdir($path, 0755, true);
        }
        $excel      = new Excel(compact('path'));
        $file_path  = $excel->fileName($filename, 'sheet')->data($out_data)->output();
        $send_param = [
            'to'         => [
                [
                    'address' => '<EMAIL>',
                    'name'    => '跨境采购组'
                ],
                [
                    'address' => '<EMAIL>',
                    'name'    => '客服组'
                ],
            ],
            'attachment' => [
                $file_path,
            ],
            'title'      => $title,
            'content'    => $title
        ];
        $res        = \Mailer::send($send_param);
        unlink($file_path);
        return $res;
    }

    //发货异常
    public function deliveryException_BAK()
    {
        //发货异常
        $list = Db::name('cross_order')->alias('o')
            ->join('order_main m', 'm.id=o.main_order_id')
            ->join('vh_commodities.vh_periods_cross_set p', 'p.id=o.package_id')
            ->where('o.sub_order_no', 'like', 'VHS%')
//            ->where('o.push_store_status', '<>', 1)
            ->where('o.sub_order_status', 1) // 已支付
//            ->where('o.is_ts', 0) // 已支付
            ->whereTime('o.predict_time', '<', date('Y-m-d H:i:s'))
            ->column('o.sub_order_no,o.is_ts,o.remarks,m.main_order_no,o.store_type,o.predict_time,p.associated_products');

        $pids = [];
        foreach ($list as &$item) {
            $i_pids                      = [];
            $item['associated_products'] = json_decode($item['associated_products'], true) ?? [];
            foreach ($item['associated_products'] as $p_info) {
                $i_pids[] = $p_info['product_id'];
            }
            $item['pids'] = $i_pids;
            $pids         = array_values(array_unique(array_merge($pids, $i_pids)));
        }
        $bar_codes = Db::table('vh_wiki.vh_products')->where('id', 'in', $pids)->column('bar_code', 'id');

        #region 可推送数量计算
        $goods_barcodes    = array_values(array_unique(array_values($bar_codes)));
        $has_short_periods = Es::name(Es::PERIODS)->where([
            ['short_code', 'in', $goods_barcodes]
        ])->field('id,short_code')->order(['id' => 'desc'])->select()->toArray();

        $goods_barcode_period_ids = [];
        foreach ($has_short_periods as $has_short_period) {
            foreach ($has_short_period['short_code'] as $goods_barcode) {
                $goods_barcode_period_ids[$goods_barcode][] = $has_short_period['id'];
            }
        }
        $pids                  = Db::table('vh_wiki.vh_products')->where('short_code', 'in', $goods_barcodes)->column('short_code', 'id');
        $has_short_pkgs        = Db::table('vh_commodities.vh_periods_cross_set')
            ->where('period_id', 'in', array_column($has_short_periods, 'id'))
            ->where(function ($query) use ($pids) {
                foreach ($pids as $pid => $sc) {
                    $query->whereOr('associated_products', 'LIKE', "%:{$pid},%");
                }
            })->column('associated_products', 'id');
        $goods_barcode_pkg_ids = [];
        foreach ($has_short_pkgs as $pkg_id => $associated_products) {
            $associated_products = json_decode($associated_products, true);
            foreach ($associated_products as $ap_info) {
                if (!is_array($ap_info['product_id']) && !empty($pids[$ap_info['product_id']])) {
                    if (!empty($ap_info['sub_package_id']))
                        $goods_barcode_pkg_ids[$pids[$ap_info['product_id']]][] = $ap_info['sub_package_id'];
                    $goods_barcode_pkg_ids[$pids[$ap_info['product_id']]][] = $pkg_id;
                }
            }
        }

        $temp_quantitys = [];
        $wh_list        = range(1, 2);
        foreach ($goods_barcode_period_ids as $i_goods_barcode => $i_goods_barcode_period_id) {
            if (in_array($i_goods_barcode, $goods_barcodes)) {

                $ts_where = $wd_where = [
                    ['period', 'in', $i_goods_barcode_period_id], //简码包含的全部期数ID
                    ['package_id', 'in', $goods_barcode_pkg_ids[$i_goods_barcode] ?? []], //简码包含的全部套餐ID
                    ['is_delete', '=', 0], //是否删除 0-正常 1-已删除
                    ['order_type', '=', 2], //是否删除 2-跨境
                    ['store_type', 'in', $wh_list],
                    ['sub_order_status', '=', 1], //订单状态=1 订单状态：0-待支付 1-已支付 2-已发货 3-已完成 4-已取消
                    ['refund_status', '=', 0], //退款状态=0  0-未退款 1-退款中 2-退款成功 3-退款失败
                ];

                $ts_where[] = ['is_ts', '=', 1]; //是否暂存：0否 1是
                $ts_where[] = ['push_store_status', '!=', 1]; //代发仓推送状态：0-未推送 1-推送成功 2-推送失败 3-不推送
                $wd_where[] = ['push_store_status', '=', 1]; //代发仓推送状态：0-未推送 1-推送成功 2-推送失败 3-不推送

                $temp_orders  = Es::name(Es::ORDERS)->where($ts_where)->field('id,store_type,order_qty')->select()->toArray();
                $wait_deliver = Es::name(Es::ORDERS)->where($wd_where)->field('id,store_type,order_qty')->select()->toArray();

                $temp_order_group   = array_group($temp_orders, 'store_type');
                $wait_deliver_group = array_group($wait_deliver, 'store_type');

                foreach ($wh_list as $wh_id) {
                    //代发仓：1-古斯缇 2-南沙仓
                    $temp_quantitys[$i_goods_barcode][$wh_id][0] = array_sum(array_column($wait_deliver_group[$wh_id] ?? [], 'order_qty')); //已支付未发货
                    $temp_quantitys[$i_goods_barcode][$wh_id][1] = array_sum(array_column($temp_order_group[$wh_id] ?? [], 'order_qty')); //暂存
                }
            }
        }

        #推单时需要【实物库存-次品数量-暂存数量-已推送未发货数量】
        $entry_nums             = [];
        $temp_cross_inventorys  = Db::name('cross_inventory')->where([
            ['goods_barcode', 'in', $goods_barcodes],
            ['store_type', 'in', $wh_list],
            ['is_delete', '=', 0]
        ])->column('id,goods_barcode,store_type,real_nums,defective_nums');
        $group_cross_inventorys = array_group($temp_cross_inventorys, 'goods_barcode');
        foreach ($group_cross_inventorys as &$group_cross_inventory_format) {
            $group_cross_inventory_format = array_group($group_cross_inventory_format, 'store_type');
        }

        foreach ($group_cross_inventorys as $b_code => $group_cross_inventory) {
            foreach ($wh_list as $wh_id) {
                $wh_gci                      = $group_cross_inventory[$wh_id] ?? [];
//                $entry_nums[$b_code][$wh_id] = bcsub(array_sum(array_column($wh_gci, 'real_nums')), array_sum(array_column($wh_gci, 'defective_nums'))); //实物库存 - 次品库存之和
                $entry_nums[$b_code][$wh_id] = bcsub(array_sum(array_column($wh_gci, 'real_nums')),0); //实物库存 - 次品库存之和
            }
        }

        $pushable = [];
        foreach ($goods_barcodes as $barcode) {
            foreach (range(1, 2) as $bc_type) {
                //是否暂存：0否 1是
                $i_temp_quantity              = $temp_quantitys[$barcode][$bc_type][1] ?? 0; //暂存数
                $i_normal_temp_quantity       = $temp_quantitys[$barcode][$bc_type][0] ?? 0; //已推送未发货数量
                $i_quantity                   = bcadd(strval($i_temp_quantity), strval($i_normal_temp_quantity));
                $pushable[$barcode][$bc_type] = bcsub(strval($entry_nums[$barcode][$bc_type] ?? 0), strval($i_quantity)); //可推送数量
            }
        }
        #endregion 可推送数量计算

        $lock_orders = CrossLockOrder::where('sub_order_no', 'in', array_column($list, 'sub_order_no'))
            ->where('status', 1)
            ->column('sub_order_no');

        $out_data[] = ['主订单号', '状态', '备注', '子订单号', '代发仓', '简码', '预计发货时间'];
        $store_type = [1 => '古斯缇', 2 => '南沙仓'];

        foreach ($list as $order) {
            $o_barcodes = [];
            $push       = true;
            foreach ($order['pids'] as $opid) {
                $op_barcode = $bar_codes[$opid] ?? $opid;
                if (($pushable[$op_barcode][$order['store_type']] ?? 0) <= 0) {
                    $push = false;
                    break;
                }
                $o_barcodes[] = $op_barcode;
            }

            $order_status = [$order['is_ts'] == 1 ? '暂存': '非暂存'];
            if(in_array($order['sub_order_no'], $lock_orders)){
                $order_status[] = '锁定';
            }
            if ($push) {

                $out_data[] = [
                    $order['main_order_no'],
                    implode('/',$order_status),
                    $order['remarks'],
                    $order['sub_order_no'],
                    $store_type[$order['store_type']] ?? $order['store_type'],
                    implode(',', $o_barcodes),
                    ($order['predict_time'] > 0) ? date('Y-m-d H:i:s', $order['predict_time']) : '',
                ];
            }
        }

        $title    = date('Y-m-d') . "有可推送库存, 超时未发货订单";
        $filename = "{$title}.xlsx";
        $path     = app()->getRuntimePath() . 'storage' . DIRECTORY_SEPARATOR;
        if (!is_dir($path)) {
            mkdir($path, 0755, true);
        }
        $excel      = new Excel(compact('path'));
        $file_path  = $excel->fileName($filename, 'sheet')->data($out_data)->output();
        $send_param = [
            'to'         => [
                [
                    'address' => '<EMAIL>',
                    'name'    => '跨境采购组'
                ],
                [
                    'address' => '<EMAIL>',
                    'name'    => '客服组'
                ],
            ],
            'attachment' => [
                $file_path,
            ],
            'title'      => $title,
            'content'    => $title
        ];
        $res        = \Mailer::send($send_param);
        unlink($file_path);
        return $res;
    }

    public function deliveryException()
    {
        //发货异常
        $list = Db::name('cross_order')->alias('o')
            ->join('order_main m', 'm.id=o.main_order_id')
            ->join('vh_commodities.vh_periods_cross_set p', 'p.id=o.package_id')
            ->where('o.sub_order_no', 'like', 'VHS%')
//            ->where('o.push_store_status', '<>', 1)
            ->where('o.sub_order_status', 1) // 已支付
//            ->where('o.is_ts', 0) // 已支付
            ->whereTime('o.predict_time', '<', date('Y-m-d H:i:s', strtotime('+1 day')))
            ->column('o.sub_order_no,o.push_store_status,o.is_ts,o.remarks,m.main_order_no,o.store_type,o.predict_time,p.associated_products');


        $pids = [];
        foreach ($list as &$item) {
            $i_pids                      = [];
            $item['associated_products'] = json_decode($item['associated_products'], true) ?? [];
            foreach ($item['associated_products'] as $p_info) {
                $i_pids[] = $p_info['product_id'];
            }
            $item['pids'] = $i_pids;
            $pids         = array_values(array_unique(array_merge($pids, $i_pids)));
        }
        $bar_codes = Db::table('vh_wiki.vh_products')->where('id', 'in', $pids)->column('bar_code', 'id');

        $lock_orders = CrossLockOrder::where('sub_order_no', 'in', array_column($list, 'sub_order_no'))
            ->where('status', 1)
            ->column('sub_order_no');

        $out_data[]        = ['主订单号', '状态', '备注', '子订单号', '代发仓', '简码', '预计发货时间', '仓库推送状态'];
        $store_type        = [1 => '古斯缇', 2 => '南沙仓'];
        $push_store_status = [0 => "未推送", 1 => "推送成功", 2 => "推送失败", 3 => "不推送",];

        foreach ($list as $order) {
            $o_barcodes = [];
            $push       = true;
            foreach ($order['pids'] as $opid) {
                $op_barcode   = $bar_codes[$opid] ?? $opid;
                $o_barcodes[] = $op_barcode;
            }

            $order_status = [$order['is_ts'] == 1 ? '暂存' : '非暂存'];
            if (in_array($order['sub_order_no'], $lock_orders)) {
                $order_status[] = '锁定';
            }
            if ($push) {

                $out_data[] = [
                    $order['main_order_no'],
                    implode('/', $order_status),
                    $order['remarks'],
                    $order['sub_order_no'],
                    $store_type[$order['store_type']] ?? $order['store_type'],
                    implode(',', $o_barcodes),
                    ($order['predict_time'] > 0) ? date('Y-m-d H:i:s', $order['predict_time']) : '',
                    $push_store_status[$order['push_store_status']] ?? $order['push_store_status'],
                ];
            }
        }

        $title    = date('Y-m-d') . "超时未发货订单";
        $filename = "{$title}.xlsx";
        $path     = app()->getRuntimePath() . 'storage' . DIRECTORY_SEPARATOR;
        if (!is_dir($path)) {
            mkdir($path, 0755, true);
        }
        $excel      = new Excel(compact('path'));
        $file_path  = $excel->fileName($filename, 'sheet')->data($out_data)->output();

        $send_param = [
            'to'         => [
                [
                    'address' => '<EMAIL>',
                    'name'    => '跨境采购组'
                ],
                [
                    'address' => '<EMAIL>',
                    'name'    => '客服组'
                ],
            ],
            'attachment' => [
                $file_path,
            ],
            'title'      => $title,
            'content'    => $title
        ];
        $res        = \Mailer::send($send_param);
        unlink($file_path);
        return $res;
    }

    public function inspectionCheck($params)
    {
        define('MONITORING', 3);
        $status = 0;
        $exec_num = 0;
        $msg    = 'success';
        $at = ""; //18696616422

        try {
            $order = Db::name('cross_order')->alias('o')
                ->join("order_main m", 'o.main_order_id=m.id')
                ->where('o.payment_time', '>', 0)
                ->where('o.sub_order_no|m.main_order_no', '=', $params['order_no'])
                ->field('o.uid,o.payment_amount,o.period,o.sub_order_no,m.main_order_no,o.id_card_no,o.created_time,m.consignee,m.consignee_phone,m.province_id,m.city_id,m.district_id,m.address')
                ->find();
            if (!$order) throw new \Exception('未找到订单!');

            $stime = date('Y-01-01 00:00:00', $order['created_time']);
            $etime = date('Y-m-d H:i:s', strtotime($stime . '+1year -1second'));

            $user_orders = Db::name('cross_order')->alias('o')
                ->join("order_main m", 'o.main_order_id=m.id')
                ->where('o.uid', '=', $order['uid'])
                ->where('o.payment_time', '>', 0)
                ->whereBetweenTime('o.payment_time', $stime, $etime)
                ->field('o.uid,o.sub_order_no,m.main_order_no,o.id_card_no,o.created_time,m.province_id,m.consignee,m.province_id,m.city_id,m.district_id,m.address')
                ->select()->toArray();

            $region_ids = array_values(array_unique(array_merge(array_column($user_orders, 'province_id'), array_column($user_orders, 'city_id'), array_column($user_orders, 'district_id'))));
            $regional   = Db::table('vh_user.vh_regional')->where('id', 'in', $region_ids)->column('name', 'id');

            $order_address = trim(implode('', [
                $regional[$order['province_id']] ?? '',
                $regional[$order['city_id']] ?? '',
                $regional[$order['district_id']] ?? '',
                $order['address'] ?? '',
            ]));
            $id_cards      = $address = [];
            $is_exists     = false;
            foreach ($user_orders as $user_order) {
                $item_address = trim(implode('', [
                    $regional[$user_order['province_id']] ?? '',
                    $regional[$user_order['city_id']] ?? '',
                    $regional[$user_order['district_id']] ?? '',
                    $user_order['address'] ?? '',
                ]));
                if (($order_address == $item_address) && ($user_order['main_order_no'] != $order['main_order_no'])) {
                    $is_exists = true;
                }

                $id_cards[$user_order['id_card_no']] = $user_order['id_card_no'];
                $address[$item_address]              = $item_address;
            }

            if ($is_exists === false) {
                \Curl::similarInsert([
                    "dataset" => 'cross_addr',
                    'records' => [
                        [
                            'name' => $order_address,
                            'memo' => implode(',', [
                                $order['uid'] ?? '',
                                date('Y', $order['created_time'] ?? 0),
                                $order['sub_order_no'] ?? '',
                            ]),
                        ]
                    ]
                ]);
            }
            if (count($id_cards) >= MONITORING) {
                $status = 2;
                $exec_num = count($id_cards);
                throw new \Exception('用户账号使用' . count($id_cards) . '个身份证号下单!');
            }

            if (count($address) >= MONITORING) {
                $temp_item   = array_values($address);
                $valiad_addr = [];

                while (!empty($temp_item)) {
                    $p_addr        = array_shift($temp_item);
                    $valiad_addr[] = $p_addr;
                    $repetition    = [];

                    $url    = env('ITEM.SIMILAR_URL') . '/services/v3/similar/find?' . http_build_query([
                            "dataset" => 'cross_addr',
                            "content" => $p_addr,
                            "limit"   => 15,
                        ]);
                    $code   = httpCurl($url, 'get', json_encode([]), 10);
                    $result = json_decode($code, true);

                    if (!empty($result['data']) && isset($result['error_code']) && ($result['error_code'] == 0)) {
                        foreach ($result['data'] as $sfi) {
                            if (($sfi['rate'] > 85) && in_array($sfi['name'], $temp_item)) {
                                $repetition[] = $sfi['name'];
                            }
                        }

                        if (!empty($repetition)) {
                            foreach ($temp_item as $tik => $tiv) {
                                if (in_array($tiv, $repetition)) {
                                    unset($temp_item[$tik], $address[$tiv]);
                                }
                            }
                        }
                    }
                    $temp_item = array_values($temp_item);
                }

                if (count($address) >= 4) {
                    $status = 1;
                    $exec_num = count($address);
                    throw new \Exception('用户使用' . count($address) . '个收货地址下单!');
                }
            }

            $phone_count = Db::name('cross_order')->alias('o')
                ->join("order_main m", 'o.main_order_id=m.id')
                ->where('m.consignee_phone', '=', $order['consignee_phone'])
                ->where('o.payment_time', '>', 0)
                ->whereBetweenTime('o.payment_time', $stime, $etime)
                ->group('o.uid')
                ->count();
            if ($phone_count >= 4) {
                $status = 3;
                $exec_num = $phone_count;
                throw new \Exception("收货手机存在{$phone_count}个用户下单!");
            }

            //验证用户的订单数量,
            $monitoring = MONITORING;
            if ($order['payment_amount'] < 1000) {
                $monitoring = 6;
            }
            $buy_count = Db::name('cross_order')->alias('o')
                ->join("order_main m", 'o.main_order_id=m.id')
                ->where('o.sub_order_status', 'in', [1, 2, 3])
                ->where('o.uid', $order['uid'])
                ->where('o.period', $order['period'])
                ->count();
            if ($buy_count >= $monitoring) {
                if ($order['payment_amount'] < 1000) {
                    $status = 5;
                } else {
                    $status = 4;
                }
                $exec_num = $phone_count;
                throw new \Exception("用户高频复购同一期数: {$order['period']},支付金额: {$order['payment_amount']},复购次数: {$buy_count} !");
            }
        } catch (\Exception $e) {
            $msg = $e->getMessage();
            if (in_array($status, [1, 2, 3, 4, 5])) {
                $year = date('Y');
                $user = [];
                if (!empty($order['uid'])) {
                    $user = Db::table('vh_user.vh_user')->where('uid', $order['uid'])->field('nickname,telephone')->find();
                }
                $enc = [];
                if (!empty($user['telephone'])) $enc[] = $user['telephone'];
                if (!empty($order['consignee_phone'])) $enc[] = $order['consignee_phone'];
                if (!empty($order['consignee'])) $enc[] = $order['consignee'];
                if (!empty($order['id_card_no'])) $enc[] = $order['id_card_no'];
                $dec = \Curl::cryptionDeal(array_values(array_unique($enc)));

                $notice_msg = "# 跨境异常用户提示\n";
//                $notice_msg .= "-用户昵称：" . ($user['nickname'] ?? '') . "\n";
//                $notice_msg .= "-用户手机号码：" . ($dec[$user['telephone'] ?? ''] ?? '') . "\n";
                $notice_msg .= "-用户订单号码：" . ($order['sub_order_no'] ?? '') . "\n";
//                $notice_msg .= "-收件人手机号：" . ($dec[$order['consignee_phone'] ?? ''] ?? '') . "\n";
//                $notice_msg .= "-收件人姓名：" . ($dec[$order['consignee'] ?? ''] ?? '') . "\n";
//                $notice_msg .= "-收件地址：" . ($order_address ?? '') . "\n";
                $notice_msg .= "-异常原因：" . ($msg ?? '') . "\n";
                Log::write("inspectionCheck $notice_msg");
                \Curl::sendWechatSender([
                    'msg'          => $notice_msg,
                    'at'           => $at,
                    "access_token" => (env("APP_DEBUG") === true) ? '19bb5a7a-0cff-40db-8116-f7908e5b5522' : '556cf0e5-**************-8220514bd270',
                ]);

                $monitor = [
                    'year'            => $year,
                    "sub_order_no"    => $order['sub_order_no'] ?? '',
                    "id_card_no"      => $order['id_card_no'] ?? '',
                    "uid"             => $order['uid'] ?? '',
                    "nickname"        => $user['nickname'] ?? '',
                    "phone"           => $user['telephone'] ?? '',
                    "consignee"       => $order['consignee'] ?? '',
                    "consignee_phone" => $order['consignee_phone'] ?? '',
                    "address"         => $order_address ?? '',
                    "type"            => $status,
                    "num"             => $exec_num,
                    "created_time"    => time(),
                    "update_time"     => time(),
                ];
                $monitor_id = Db::name('cross_monitor')->insert($monitor);
                if ($monitor_id) {
                    try {
                        $timeoutParam = [
                            'namespace' => 'orders_cross_monitor', //命名空间。可根据项目自定义名称。
                            'key'       => $order['sub_order_no'], //唯一键名，在同一个命名空间下，如果有相同键名存在会覆盖已有数据。
                            'data'      => base64_encode(json_encode(compact('monitor_id'))), //超时触发时，提交的数据。建议使用Base64编码（注：回调接口传递的数据会自动解码）。
                            'callback'  => env('ITEM.ORDERS_URL') . '/orders/v3/cross/monitorAnomaly', //超时触发时，调用的接口回调地址。
                            'timeout'   => '5s', //超时时间。支持s,m,h单位进行描述。比如：1s=1秒，1分钟=1m，1小时=1h
                        ];
                        \Curl::timingAdd($timeoutParam);
                    } catch (\Exception $ex) {
                        Log::write('跨境异常超时任务创建失败：' . json_encode($timeoutParam ?? ['key' => $order['sub_order_no']]) . ' ' . $ex->getMessage());
                    }
                }
            } else {
                $status = 6;
            }
        }

        return compact('status', 'msg');
    }

}