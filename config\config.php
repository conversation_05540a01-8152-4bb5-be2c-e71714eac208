<?php

// +----------------------------------------------------------------------
// | 基本配置
// +----------------------------------------------------------------------

return [
    //子订单状态
    'sub_order_status'               => [
        [
            'value' => 0,
            'label' => '待支付'
        ],
        [
            'value' => 1,
            'label' => '已支付'
        ],
        [
            'value' => 2,
            'label' => '已发货'
        ],
        [
            'value' => 3,
            'label' => '已完成'
        ],
        [
            'value' => 4,
            'label' => '已取消'
        ],
        [
            'value' => 10,
            'label' => '已锁定'
        ]
    ],
    //订单来源
    'order_from'                     => [
        [
            'value' => 0,
            'label' => 'IOS/苹果APP'
        ],
        [
            'value' => 1,
            'label' => 'Android/安卓APP'
        ],
        [
            'value' => 2,
            'label' => 'H5/移动端'
        ],
        [
            'value' => 3,
            'label' => 'PC/桌面端'
        ],
        [
            'value' => 4,
            'label' => 'Wxapp/微信小程序'
        ]
    ],
    //开票进度
    'invoice_progress'               => [
        [
            'value' => 0,
            'label' => '不开票'
        ],
        [
            'value' => 1,
            'label' => '开票中'
        ],
        [
            'value' => 2,
            'label' => '开票成功'
        ],
        [
            'value' => 3,
            'label' => '开票失败'
        ]
    ],
    //退款状态
    'refund_status'                  => [
        [
            'value' => 0,
            'label' => '未退款'
        ],
        [
            'value' => 1,
            'label' => '退款中'
        ],
        [
            'value' => 2,
            'label' => '退款成功'
        ],
        [
            'value' => 3,
            'label' => '退款失败'
        ]
    ],
    //拼团状态
    'group_status'                   => [
        [
            'value' => 1,
            'label' => '拼团中'
        ],
        [
            'value' => 2,
            'label' => '拼团成功'
        ]
    ],
    //快递方式
    'express_type'                   => [
        [
            'value' => 31,
            'label' => '顺丰温控包裹'
        ],
        [
            'value' => 2,
            'label' => '顺丰快递'
        ],
        [
            'value' => 3,
            'label' => '顺丰冷链'
        ],
        [
            'value' => 4,
            'label' => '京东快递(不保价)'
        ],
        [
            'value' => 5,
            'label' => '京东快递(保价)'
        ],
        [
            'value' => 6,
            'label' => '客户仓库自提'
        ],
        [
            'value' => 7,
            'label' => '菜鸟顺丰'
        ],
        [
            'value' => 8,
            'label' => '抖店京东'
        ],
        [
            'value' => 9,
            'label' => '抖店顺丰'
        ],
        [
            'value' => 10,
            'label' => '京东快运'
        ],
        [
            'value' => 11,
            'label' => '顺丰标快'
        ],
        [
            'value' => 12,
            'label' => '菜鸟德邦'
        ],
        [
            'value' => 13,
            'label' => '顺丰快递保价'
        ],
        [
            'value' => 14,
            'label' => '德邦快递'
        ],
        [
            'value' => 15,
            'label' => '松鸽京东不保价'
        ],
        [
            'value' => 16,
            'label' => '松鸽顺丰'
        ],
        [
            'value' => 21,
            'label' => '欣运物流自提'
        ],
        [
            'value' => 22,
            'label' => '欣运物流上门'
        ],
        [
            'value' => 23,
            'label' => '京东TC'
        ],
        [
            'value' => 51,
            'label' => '韵达快递'
        ],
        [
            'value' => 52,
            'label' => '圆通速递'
        ],
        [
            'value' => 53,
            'label' => '联邦快递'
        ],
        [
            'value' => 54,
            'label' => '中通快递'
        ],
        [
            'value' => 55,
            'label' => '申通快递'
        ],
        [
            'value' => 56,
            'label' => 'EMS'
        ],
        [
            'value' => 57,
            'label' => '德邦快递'
        ],
        [
            'value' => 58,
            'label' => '天天快递'
        ],
        [
            'value' => 59,
            'label' => '优速快递'
        ],
        [
            'value' => 60,
            'label' => '百世快运'
        ],
        [
            'value' => 61,
            'label' => '顺心捷达'
        ],
        [
            'value' => 62,
            'label' => '九曳供应链'
        ],
        [
            'value' => 63,
            'label' => '同城快寄'
        ],
        [
            'value' => 64,
            'label' => '跨越速运'
        ],
        [
            'value' => 65,
            'label' => '本地物流'
        ],
        [
            'value' => 66,
            'label' => '极兔快递'
        ],
        [
            'value' => 67,
            'label' => '壹米滴答'
        ],
        [
            'value' => 95,
            'label' => '商家配送'
        ],
        [
            'value' => 100,
            'label' => '闪送'
        ]
    ],
    //是否删除
    'is_delete'                      => [
        [
            'value' => 0,
            'label' => '否'
        ],
        [
            'value' => 1,
            'label' => '是'
        ]
    ],
    //是否赠品
    'is_gift'                        => [
        [
            'value' => 0,
            'label' => '否'
        ],
        [
            'value' => 1,
            'label' => '是'
        ]
    ],
    //是否暂存
    'is_ts'                          => [
        [
            'value' => 0,
            'label' => '否'
        ],
        [
            'value' => 1,
            'label' => '是'
        ]
    ],
    //是否虚拟
    'is_virtual'                     => [
        [
            'value' => 0,
            'label' => '否'
        ],
        [
            'value' => 1,
            'label' => '是'
        ]
    ],
    //T+推送状态
    'push_t_status'                  => [
        [
            'value' => 0,
            'label' => '未推送'
        ],
        [
            'value' => 1,
            'label' => '推送成功'
        ],
        [
            'value' => 2,
            'label' => '推送失败'
        ],
        [
            'value' => 3,
            'label' => '不推送'
        ]
    ],
    //萌芽推送状态
    'push_wms_status'                => [
        [
            'value' => 0,
            'label' => '未推送'
        ],
        [
            'value' => 1,
            'label' => '推送成功'
        ],
        [
            'value' => 2,
            'label' => '推送失败'
        ],
        [
            'value' => 3,
            'label' => '不推送'
        ]
    ],
    //订单频道类型
    'order_type'                     => [
        [
            'value' => 0,
            'table' => 'flash_order',
            'label' => '闪购'
        ],
        [
            'value' => 1,
            'table' => 'second_order',
            'label' => '秒发'
        ],
        [
            'value' => 2,
            'table' => 'cross_order',
            'label' => '跨境'
        ],
        [
            'value' => 3,
            'table' => 'tail_order',
            'label' => '尾货'
        ],
        [
            'value' => 4,
            'table' => 'rabbit_order',
            'label' => '兔头实物'
        ],
        [
            'value' => 5,
            'table' => 'wine_party_order',
            'label' => '酒会(酒历)'
        ],
        [
            'value' => 6,
            'table' => 'wine_academy_order',
            'label' => '课程'
        ],
        [
            'value' => 7,
            'table' => 'tripartite_order',
            'label' => '三方'
        ],
        [
            'value' => 8,
            'table' => 'offline_order',
            'label' => '线下'
        ],
        [
            'value' => 9,
            'table' => 'merchant_second_order',
            'label' => '商家秒发'
        ],
        [
            'value' => 11,
            'table' => 'orders',
            'label' => '拍卖'
        ],
//        [
//            'value' => 60,
//            'table' => 'orders',
//            'label' => '礼品卡'
//        ],
    ],
    //支付方式
    'payment_method'                 => [
        [
            'value' => -1,
            'label' => '未知'
        ],
        [
            'value' => 0,
            'label' => '支付宝APP'
        ],
        [
            'value' => 1,
            'label' => '支付宝H5'
        ],
        [
            'value' => 2,
            'label' => 'PC扫码'
        ],
        [
            'value' => 3,
            'label' => '微信APP'
        ],
        [
            'value' => 4,
            'label' => '微信小程序'
        ],
        [
            'value' => 5,
            'label' => '微信H5'
        ],
        [
            'value' => 6,
            'label' => '抖音支付宝'
        ],
        [
            'value' => 7,
            'label' => '微信JSAPI(公众号支付)'
        ],
        [
            'value' => 8,
            'label' => '抖音微信'
        ],
        [
            'value' => 9,
            'label' => '微信扫码'
        ],
        [
            'value' => 10,
            'label' => '线上电汇'
        ],
        [
            'value' => 11,
            'label' => '线下转账'
        ],
        [
            'value' => 12,
            'label' => '华为支付'
        ],
        [
            'value' => 13,
            'label' => '余额支付'
        ],
        [
            'value' => 201,
            'label' => '兔头'
        ],
        [
            'value' => 202,
            'label' => '礼品卡'
        ]
    ],
    //支付主体
    'payment_subject'                => [
        [
            'value' => 0,
            'label' => '未知'
        ],
        [
            'value' => 1,
            'label' => '重庆云酒佰酿电子商务有限公司（银联）'
        ],
        [
            'value' => 2,
            'label' => '佰酿云酒（重庆）科技有限公司（银联）'
        ],
        [
            'value' => 3,
            'label' => '重庆云酒佰酿电子商务有限公司'
        ],
        [
            'value' => 4,
            'label' => '佰酿云酒（重庆）科技有限公司'
        ]
    ],
    //跨境支付单推送状态
    'payment_doc'                    => [
        [
            'value' => 0,
            'label' => '未推送'
        ],
        [
            'value' => 1,
            'label' => '推送成功'
        ],
        [
            'value' => 2,
            'label' => '推送失败'
        ],
        [
            'value' => 3,
            'label' => '不推送'
        ]
    ],
    //跨境代发仓推送状态
    'push_store_status'              => [
        [
            'value' => 0,
            'label' => '未推送'
        ],
        [
            'value' => 1,
            'label' => '推送成功'
        ],
        [
            'value' => 2,
            'label' => '推送失败'
        ],
        [
            'value' => 3,
            'label' => '不推送'
        ]
    ],
    //特殊类型：0-普通 1-拼团 2-新人 3-加购 4-膨胀金 34-代发
    'special_type'=>[
        [
            'value' => 0,
            'label' => '普通'
        ],
        [
            'value' => 1,
            'label' => '拼团'
        ],
        [
            'value' => 2,
            'label' => '新人'
        ],
        [
            'value' => 3,
            'label' => '加购'
        ],
        [
            'value' => 4,
            'label' => '订金'
        ],
        [
            'value' => 34,
            'label' => '代发'
        ]
    ],
    //现有仓库
    'warehouse'                      => [
        [
            'value' => 1,
            'label' => '南通萌牙',
            'code'  => '002,004,013,316,034,314,315,309,318,319'
        ],
        [
            'value' => 2,
            'label' => '京东昆山',
            'code'  => '501,502,701,702,201,202,320,322'
        ],
    ],
    //仓库编码
    'warehouse_code'                 => [
        [
            'value'       => '002',
            'label'       => '佰酿云酒（南通常货仓）',
            'physical_id' => 6
        ],
        [
            'value'       => '004',
            'label'       => '佰酿云酒（南通闪购仓）',
            'physical_id' => 6
        ],
        [
            'value'       => '013',
            'label'       => '佰酿云酒（南通秒发仓）',
            'physical_id' => 6
        ],
        [
            'value'       => '201',
            'label'       => '佰酿云酒（昆山闪购仓）',
            'physical_id' => 7
        ],
        [
            'value'       => '202',
            'label'       => '佰酿云酒（昆山秒发仓）',
            'physical_id' => 7
        ],
        [
            'value'       => '316',
            'label'       => '跨境退货仓',
            'physical_id' => 6
        ],
        [
            'value'       => '501',
            'label'       => '佰酿云酒（重庆闪购仓）',
            'physical_id' => 8
        ],
        [
            'value'       => '502',
            'label'       => '佰酿云酒（重庆秒发仓）',
            'physical_id' => 8
        ],
        [
            'value'       => '701',
            'label'       => '佰酿云酒（武汉闪购仓）',
            'physical_id' => 8
        ],
        [
            'value'       => '702',
            'label'       => '佰酿云酒（武汉秒发仓）',
            'physical_id' => 8
        ],
        [
            'value'       => '034',
            'label'       => '佰酿云酒（代发仓）',
            'physical_id' => 6
        ],
        [
            'value'       => '272',
            'label'       => '酒云微醺代发仓',
            'physical_id' => 6
        ],
        [
            'value'       => '314',
            'label'       => '佰酿云酒（南通食品仓）',
            'physical_id' => 6
        ],
        [
            'value'       => '315',
            'label'       => '南通尾货仓',
            'physical_id' => 6
        ],
        [
            'value'       => '309',
            'label'       => '南通次品仓',
            'physical_id' => 6
        ],
        [
            'value'       => '021',
            'label'       => '重庆古丝缇跨境仓',
            'physical_id' => 6
        ],
        [
            'value'       => '028',
            'label'       => '广州南沙跨境仓',
            'physical_id' => 6
        ],

        [
            'value'       => '310',
            'label'       => '兔头商店南通仓',
            'physical_id' => 6
        ],
        [
            'value'       => '008',
            'label'       => '视频电商仓',
            'physical_id' => 6
        ],
        [
            'value'       => '27',
            'label'       => '新媒体-南通仓',
            'physical_id' => 6
        ],
        [
            'value'       => '888',
            'label'       => '供应商赠品仓',
            'physical_id' => 6
        ],
        [
            'value'       => '338',
            'label'       => '微醺酒业南通仓',
            'physical_id' => 6
        ],
        [
            'value'       => '362',
            'label'       => '佰酿科技（BD仓）',
            'physical_id' => 6
        ],
        [
            'value'       => '364',
            'label'       => '重庆解放碑店',
            'physical_id' => 6
        ],
        [
            'value'       => '043',
            'label'       => '酒云研酒所（重庆解放碑店）',
            'physical_id' => 6
        ],

    ],
    //商家秒发-发货方式
    'delivery_method'                => [
        [
            'value' => 1,
            'label' => '物流配送'
        ],
        [
            'value' => 2,
            'label' => '同城配送'
        ],
        [
            'value' => 3,
            'label' => '自提订单'
        ],
    ],
    //币种
    'currency'                       => [
        [
            'value' => 0,
            'name'  => '人民币',
            'label' => 'RMB'
        ],
        [
            'value' => 1,
            'name'  => '美元',
            'label' => 'USD'
        ],
        [
            'value' => 2,
            'name'  => '欧元',
            'label' => 'EUR'
        ],
        [
            'value' => 3,
            'name'  => '港币',
            'label' => 'HKD'
        ],
        [
            'value' => 4,
            'name'  => '日元',
            'label' => 'JPY'
        ],
        [
            'value' => 5,
            'name'  => '英镑',
            'label' => 'GBP'
        ],
        [
            'value' => 6,
            'name'  => '德国马克',
            'label' => 'DEM'
        ],
        [
            'value' => 7,
            'name'  => '瑞士法郎',
            'label' => 'CHF'
        ],
        [
            'value' => 8,
            'name'  => '法国法郎',
            'label' => 'FRF'
        ],
        [
            'value' => 9,
            'name'  => '加拿大元',
            'label' => 'CAD'
        ],
        [
            'value' => 10,
            'name'  => '澳大利亚元',
            'label' => 'AUD'
        ],
        [
            'value' => 11,
            'name'  => '奥地利先令',
            'label' => 'ATS'
        ],
        [
            'value' => 12,
            'name'  => '芬兰马克',
            'label' => 'FIM'
        ],
        [
            'value' => 13,
            'name'  => '比利时法郎',
            'label' => 'BEF'
        ],
        [
            'value' => 14,
            'name'  => '爱尔兰镑',
            'label' => 'IEP'
        ],
        [
            'value' => 15,
            'name'  => '意大利里拉',
            'label' => 'ITL'
        ],
        [
            'value' => 16,
            'name'  => '卢森堡法郎',
            'label' => 'LUF'
        ],
        [
            'value' => 17,
            'name'  => '荷兰盾',
            'label' => 'NLG'
        ],
        [
            'value' => 18,
            'name'  => '葡萄牙埃斯库多',
            'label' => 'PTE'
        ],
        [
            'value' => 19,
            'name'  => '西班牙比塞塔',
            'label' => 'ESP'
        ],
        [
            'value' => 20,
            'name'  => '印尼盾',
            'label' => 'IDR'
        ],
        [
            'value' => 21,
            'name'  => '马来西亚林吉特',
            'label' => 'MYR'
        ],
        [
            'value' => 22,
            'name'  => '新西兰元',
            'label' => 'NZD'
        ],
        [
            'value' => 23,
            'name'  => '菲律宾比索',
            'label' => 'PHP'
        ],
        [
            'value' => 24,
            'name'  => '俄罗斯卢布',
            'label' => 'SUR'
        ],
        [
            'value' => 25,
            'name'  => '新加坡元',
            'label' => 'SGD'
        ],
        [
            'value' => 26,
            'name'  => '韩国元',
            'label' => 'KRW'
        ],
        [
            'value' => 27,
            'name'  => '泰铢',
            'label' => 'THB'
        ],
    ],
    //erp运费方式对应萌牙快递类型
    'delivery_express'               => [
        [
            'delivery_mode' => '顺丰垫付',
            'express_type'  => 2
        ],
        [
            'delivery_mode' => '顺丰冷链',
            'express_type'  => 3
        ],
        [
            'delivery_mode' => '京东垫付',
            'express_type'  => 4
        ],
        [
            'delivery_mode' => '京东快递到付',
            'express_type'  => 5
        ],
        [
            'delivery_mode' => '客户仓库自提',
            'express_type'  => 6
        ],
        [
            'delivery_mode' => '京东TC',
            'express_type'  => 23
        ],
        [
            'delivery_mode' => '德邦快递垫付',
            'express_type'  => 14
        ],
        [
            'delivery_mode' => '跨越速运',
            'express_type'  => 25
        ],
        [
            'delivery_mode' => '京东物流到付',
            'express_type'  => 10
        ],
        [
            'delivery_mode' => '京东物流垫付',
            'express_type'  => 10
        ],
        [
            'delivery_mode' => '德邦快运',
            'express_type'  => 19
        ],
        [
            'delivery_mode' => '壹米滴答',
            'express_type'  => 24
        ],
    ],
    //三方订单店铺
    'tripartite_store'               => [
        [
            'value' => '419938814',
            'label' => '佰酿科技（天猫旗舰店）'
        ],
        [
            'value' => '492964257',
            'label' => '酒云网拍卖店'
        ],
        [
            'value' => '558695549',
            'label' => '天猫（法国南部葡萄酒官方旗舰店）'
        ],
        [
            'value' => '566768114',
            'label' => '桃公子淘宝店'
        ],
        [
            'value' => '961036630',
            'label' => '拼多多（酒云网官方旗舰店）'
        ],
        [
            'value' => '662831451',
            'label' => '拼多多（桃氏物语酒类专营店）'
        ],
        [
            'value' => '19912474163',
            'label' => '木兰朵天猫旗舰店'
        ],
        [
            'value' => '163005133',
            'label' => '木兰朵-拼多多旗舰店'
        ],
        [
            'value' => '320552154',
            'label' => '木兰朵-天猫旗舰店'
        ],
        [
            'value' => '827871079',
            'label' => '佰酿云酒酒类专营店（拼多多店）'
        ],
        [
            'value' => '68119621',
            'label' => '酒云网抖店-卖场旗舰店'
        ],
        [
            'value' => '8911798',
            'label' => '抖音小商店-云酒佰酿酒类专营店'
        ],
        [
            'value' => '208798183',
            'label' => '木兰朵-抖音零售店'
        ],
        [
            'value' => 'wx91084bb0a5b66d53',
            'label' => '微信小商店-酒云网官方旗舰店'
        ],
        [
            'value' => 'wx7ab502be70d73aa5',
            'label' => '微信小商店-佰酿云酒酒类专营店'
        ],

        [
            'value' => 'wx0fe6132e6a98d48a',
            'label' => '微信小商店-木兰朵微信小店'
        ],
        [
            'value' => '452890329',
            'label' => '酒遇喵掌柜'
        ],
        [
            'value' => '2807304908',
            'label' => '天猫国际进口超市-云酒'
        ],
        [
            'value' => '2807304908',
            'label' => '佰酿云酒（天猫国际）'
        ],
        [
            'value' => '28073049081',
            'label' => '天猫国际(科技)'
        ],
        [
            'value' => '28073049081',
            'label' => '天猫国际小酒馆-科技'
        ],
        [
            'value' => '227734657',
            'label' => '佰酿美酒天猫'
        ],
        [
            'value' => '10136705',
            'label' => '云酒京东店铺'
        ],
        [
            'value' => '1000398259',
            'label' => '佰酿科技（京东自营）'
        ],
        [
            'value' => '10232541',
            'label' => '法国南部葡萄酒官方产区馆'
        ],
        [
            'value' => '5946418',
            'label' => '佰酿科技（得物APP）'
        ],
        [
            'value' => '686511',
            'label' => '老外买酒'
        ],
        [
            'value' => 'store00013',
            'label' => '佰酿美酒重庆光环店'
        ],
        [
            'value' => 'store00014',
            'label' => '上海酒云研酒所'
        ],
        [
            'value' => 'store00016',
            'label' => ' 酒云研酒所（重庆解放碑店）'
        ],
        [
            'value' => '62b98b750d601800010dc853',
            'label' => '行吟信息科技（武汉）有限公司(云酒网小红书店）'
        ],
        [
            'value' => '6426461c6fdda100018a7b76',
            'label' => '【小红书】Laowine的店'
        ],
        [
            'value' => '646b6293ebbcb20001b9ee01',
            'label' => '【小红书】元喜Motoki的店'
        ],
        [
            'value' => '650a60e17fa15200013acf16',
            'label' => '木兰朵-小红书'
        ],
//        [
//            'value' => 'wx0fe6132e6a98d48a',
//            'label' => '木兰朵微信小店'
//        ],
        [
            'value' => '6542368b368edf00013dc6f5',
            'label' => '【小红书】兔总葡萄酒买手店的店'
        ],
        [
            'value' => '65113b63effd830001ca90e0',
            'label' => '小红书-威哥蒸馏所'
        ],
        [
            'value' => '653b599dbffe730001559bd6',
            'label' => '小红书-Brown Brothers布琅兄弟'
        ],
        [
            'value' => 'tianmaochaoshi20230518',
            'label' => '天猫超市'
        ],
        [
            'value' => '100620515',
            'label' => '木兰朵-抖音旗舰店'
        ],
        [
            'value' => '381499438',
            'label' => '抖音-木兰朵全国总仓'
        ],
        [
            'value' => '68029232',
            'label' => '抖音布琅兄弟葡萄酒旗舰店'
        ],
        [
            'value' => '381499438',
            'label' => '拼多多（布琅兄弟旗舰店）'
        ],
        [
            'value' => 'ldd_1',
            'label' => '链多多'
        ],
        [
            'value' => '20318455683',
            'label' => '杨树禄-快团团'
        ],
        [
            'value' => '848265289',
            'label' => '开瓶有益 - 老陈的酒馆'
        ],
        [
            'value' => '20121190650',
            'label' => '快团团[兔子星球-意神]'
        ],
        [
            'value' => '216379813',
            'label' => '快团团-兔子福利社'
        ],
        [
            'value' => '20959411495',
            'label' => '[快团团]公域-酒云网VINEHOO'
        ],
//        [
//            'value' => '20369140079',
//            'label' => '[快团团]松鸽的Soul List'
//        ],
//        [
//            'value' => '517978682',
//            'label' => '[快团团]惺忪的Soul List'
//        ],
        [
            'value' => '541276454',
            'label' => '美尼多天猫旗舰店'
        ],
        [
            'value' => '258788035',
            'label' => '蒙大菲酒类旗舰店'
        ],
        [
            'value' => '114968496',
            'label' => '【天猫】一大包零食'
        ],
        [
            'value' => '311029595',
            'label' => '松鸽-新西兰葡萄酒多产区旗舰店'
        ],
        [
            'value' => '18565487',
            'label' => '木兰朵-京东旗舰店'
        ],
        [
            'value' => '19051305',
            'label' => '京东-酒云网官方旗舰店'
        ],
        [
            'value' => '02223',
            'label' => '渝中区微醺酒业商行 （兔子）'
        ],
        [
            'value' => 'mldmydm',
            'label' => '木兰朵·命蕴大美'
        ],
    ],
    //三方订单店铺(松鸽)
    'tripartite_sg_store' => [
        [
            'value' => '20369140079',
            'label' => '松鸽的Soul List'
        ],
        [
            'value' => '517978682',
            'label' => '快团团惺松的Soul List'
        ],
        //三方订单来源 1-京东 2-淘宝 3-拼多多 4-天猫 13-天猫国际 15-淘宝代发 16-兔总天猫代发 17-小红书 18-抖店直播 19-微店直播 20-得物APP 21-老外买酒 22-门店 23-天猫超市 24-木兰朵天猫旗舰店 25-木兰朵酒类旗舰店 26-渝欧
    ],
    'tripartite_order_from'          => [
        [
            'value' => 1,
            'label' => '京东'
        ],
        [
            'value' => 2,
            'label' => '淘宝'
        ],
        [
            'value' => 3,
            'label' => '拼多多'
        ],
        [
            'value' => 4,
            'label' => '天猫'
        ],
        [
            'value' => 13,
            'label' => '天猫国际(云酒)'
        ],
        [
            'value' => 15,
            'label' => '淘宝代发'
        ],
        [
            'value' => 16,
            'label' => '兔总天猫代发'
        ],
        [
            'value' => 17,
            'label' => '小红书'
        ],
        [
            'value' => 18,
            'label' => '抖店直播'
        ],
        [
            'value' => 19,
            'label' => '微店直播'
        ],
        [
            'value' => 20,
            'label' => '得物APP'
        ],
        [
            'value' => 21,
            'label' => '老外买酒'
        ],
        [
            'value' => 22,
            'label' => '门店'
        ],
        [
            'value' => 23,
            'label' => '天猫超市'
        ],
        [
            'value' => 25,
            'label' => '木兰朵酒类旗舰店'
        ],
        [
            'value' => 26,
            'label' => '渝欧'
        ],
        [
            'value' => 27,
            'label' => '快团团'
        ],
        [
            'value' => 28,
            'label' => '天猫国际(科技)'
        ],
        [
            'value' => 29,
            'label' => '天猫一大包零食'
        ],
        [
            'value' => 30,
            'label' => '兔子星球'
        ],
        [
            'value' => 31,
            'label' => '木兰朵·命蕴大美'
        ],
    ],
    //萌牙推送前需查询三方订单状态的店铺ID集合
    'tripartite_order_status_search' => [
        "227734657", "419938814", "452890329", "474092239", "492964257", "558695549", "566768114", "1000398259", "10136705", "10232541", "wx91084bb0a5b66d53", "8911798", "68119621", "662831451", "961036630", "6426461c6fdda100018a7b76", "62b98b750d601800010dc853","320552154","163005133","100620515","381499438","827871079","646b6293ebbcb20001b9ee01","ldd_1","20318455683","216379813","20959411495","541276454","650a60e17fa15200013acf16","wx7ab502be70d73aa5","258788035","6542368b368edf00013dc6f5","65113b63effd830001ca90e0","653b599dbffe730001559bd6","114968496","311029595","20121190650","18565487","19051305","848265289","68029232","381499438"
    ]
];
