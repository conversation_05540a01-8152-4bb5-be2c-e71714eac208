<?php


namespace app\validate;


class CourseCreateOrder extends BaseValidate
{
    /**
     * 定义验证规则
     * 格式：'字段名'    =>    ['规则1','规则2'...]
     *
     * @var array
     */
    protected $rule = [
        'uid'            => 'require|number',
        'course_id'      => 'require',
        'order_from'     => 'require|in:0,1,2,3,4',
        'realname'       => 'require',
        'phone'          => 'require|number',
        'wechat_account' => 'require'
    ];

    /**
     * 定义错误信息
     * 格式：'字段名.规则名'    =>    '错误信息'
     *
     * @var array
     */
    protected $message = [
        'uid.require'            => '用户ID必传',
        'course_id.require'      => '课程ID必传',
        'order_from.require'     => '来源必传',
        'realname.require'       => '真实姓名必传',
        'phone.require'          => '手机号必传',
        'wechat_account.require' => '微信号必传'
    ];
}