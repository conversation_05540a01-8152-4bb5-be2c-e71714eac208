<?php
declare (strict_types = 1);

namespace app\service;
use app\BaseService;
use app\ErrorCode;
use app\model\Express as ExpressModel;
use think\cache\driver\Redis;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\Log;
use app\service\RedisExpand as RedisExpandService;

class Express extends BaseService
{

    /**
     * Description：快递方式列表
     * Author: zjl
     * Date: 2022/5/6
     * @param $params
     * @return mixed
     */
    public function getExpressList($params)
    {
        $page = !empty($params['page']) ? $params['page'] : 1;
        $limit = !empty($params['limit']) ? $params['limit'] : 10;
        $result= (new ExpressModel())->expressList($params,$page,$limit);
        foreach ($result['list'] as &$value){
            $express_rules = json_decode($value['express_rules']);
            foreach ($express_rules as &$v){
                $warehouse_name='';
                $warehouse = $v->warehouse;
                $warehouse = implode(',',$warehouse);
             //   $warehouse = '002,004,013';
                $res = httpGet(env("ITEM.COMMODITIES_URL").'/commodities/v3/warehouse/getVirtualByVirtualId',['erp_id'=>$warehouse]);
                if ($res['error_code'] == 0 && !empty($res['data'])) {

                   foreach ($res['data'] as $temp){
                       $warehouse_name.=$temp['virtual_name'];
                   }
                }
                $v->warehouse_name=$warehouse_name;
            }
            $value['express_rules'] = $express_rules;
            $value['created_time'] = $value['created_time']? date("Y-m-d H:i:s",$value['created_time']):'';
            $value['update_time'] = $value['update_time']? date("Y-m-d H:i:s",$value['update_time']):'';
        }
        return $result;

    }
    public function webList($params)
    {
        $page = !empty($params['page']) ? $params['page'] : 1;
        $limit = !empty($params['limit']) ? $params['limit'] : 10;
        $result= (new ExpressModel())->webList($params,$page,$limit);
        return $result;

    }
    /**
     * Description：快递方式添加
     * Author: zjl
     * Date: 2022/5/6
     * @param $params
     * @return array|bool|string
     * @throws \Exception
     */
    public function createExpress($params)
    {
        if (!isset($params['name']) || empty($params['name'])) {
            throw new ValidateException('模板名称不能为空');
        }
        # 数据验证 验证仓库编码不能重复出现在不同规则 每个规则必须有默认的快递方式
        $warehouseIds=[];
        foreach ($params['express'] as $express){
            if (!isset($express['default_express']) || empty($express['default_express'])) {
                throw new ValidateException('请指定对应规则的默认快递方式');
            }
            foreach ($express['warehouse'] as $v){
                if (in_array($v,$warehouseIds)) {
                    throw new ValidateException('同一个仓库只能在一个规则里,不可重复');
                }
                $warehouseIds[]=$v;
            }
            foreach ($express['appoint'] as $a){
                if (!isset($a['appoint_express']) || empty($a['appoint_express'])){
                    throw new ValidateException('请选择指定快递方式');
                }
                if (empty($a['number']) && empty($a['price']) && empty($a['goods_attribute'])){
                    throw new ValidateException('请设置规则');
                }
            }
        }
        $expressData=$params['express'];
        $express_json=json_encode($params['express']);
        $warehouse_ids=json_encode(array_values($warehouseIds));
        $data=[
            'name'=>$params['name'],
            'express_rules'=>$express_json,
            'warehouse_ids'=>$warehouse_ids,
        ];
        #规则存入redis 仓库id=>默认快递方式 指定快速方式 瓶数 价格 商品属性

        #有id就是修改
        if (isset($params['id']) && !empty($params['id'])) {
            $list = (new ExpressModel())->where('id','=',$params['id'])->find();
            if (empty($list)) {
                throw new ValidateException('未检测到相关模板');
            }
            # 已经启用中的修改 redis 重新记录
//            #规则存入redis 仓库id=>默认快递方式 指定快速方式 瓶数 价格 商品属性
//            $redis = new  RedisExpandService(Config('cache')['stores']['redis']);
//            foreach ($expressData as $value){
//                foreach ($value['warehouse'] as $warehouseId){
//                    $redis->del("vinehoo.express.".$params['id'].'.'.$warehouseId);
//                    $insert1 =  $redis->rPush("vinehoo.express.".$params['id'].'.'.$warehouseId,json_encode([$value['default_express'],0,0,0]));
//                    if ($insert1 == null){
//                        Log::error('redis 写入失败');
//                        $this->throwError("redis 写入失败");
//                    }
//                    foreach ($value['appoint'] as $a){
//                        $insert=[$a['appoint_express'],intval($a['number']),floatval($a['price']),$a['goods_attribute']];
//                        $insert2= $redis->rPush("vinehoo.express.".$params['id'].'.'.$warehouseId,json_encode($insert));
//                        if ($insert2 == null){
//                            Log::error('redis 写入指定快递方式失败');
//                            $this->throwError("redis 写入指定快递方式失败");
//                        }
//                    }
//                }
//            }
            $data['update_time']=time();
            Db::name('express')->where('id','=',$params['id'])->update($data);

        }else{
            # 没有id就是添加
            $data['status']=1;
            $data['created_time']=time();
            $data['update_time']=time();
            $id = Db::name('express')->insertGetId($data);
            if ($id<0) {
                Log::error('快递方式添加失败：'.$id);
                $this->throwError('快递方式添加失败：'.$id);
            }
            $params['id']=$id;
        }
        #规则存入redis 仓库id=>默认快递方式 指定快速方式 瓶数 价格 商品属性
        $redis = new  RedisExpandService(Config('cache')['stores']['redis']);
        foreach ($expressData as $value){
            foreach ($value['warehouse'] as $warehouseId){
                $redis->del("vinehoo.express.".$params['id'].'.'.$warehouseId);
                $insert1 =  $redis->rPush("vinehoo.express.".$params['id'].'.'.$warehouseId,json_encode([$value['default_express'],0,0,0]));
                if ($insert1 == null){
                    Log::error('redis 写入失败');
                    $this->throwError("redis 写入失败");
                }
                foreach ($value['appoint'] as $a){
                    $insert=[$a['appoint_express'],intval($a['number']),floatval($a['price']),$a['goods_attribute']];
                    $insert2= $redis->rPush("vinehoo.express.".$params['id'].'.'.$warehouseId,json_encode($insert));
                    if ($insert2 == null){
                        Log::error('redis 写入指定快递方式失败');
                        $this->throwError("redis 写入指定快递方式失败");
                    }
                }
            }
        }
        return true;
    }

    /**
     * 修改
     * @param $params
     * @return bool|string
     * @throws \Exception
     */
    public function updateExpress($params)
    {
        if (!isset($params['name']) || empty($params['name'])) {
            throw new ValidateException('模板名称不能为空');
        }
        $appointData=!empty($params['appoint'])?$params['appoint']:[];
        if (empty($appointData)) {
            throw new ValidateException("请对所属仓库进行设置");
        }
        $expressIds = array_column($params['appoint'],'id');
        $status=1;
        if (!empty($expressIds)){
            $res=  $res = Db::name('express')->field("id,status")->where('id','in',$expressIds)->where('status','<>',3)->select()->toArray();
            if (empty($res)) {
                throw new ValidateException("修改的指定快递方式未检测到数据");
            }
            $status=$res[0]['status'];
        }

        foreach ($appointData as &$temp){
            if (!isset($temp['warehouse'], $temp['express'])) {
                throw new \Exception('appoint 字段不完整');
            }
            $temp['name']=$params['name'];
            $temp['update_time']=time();
            if (isset($temp['id'])){
                $res = Db::name('express')->where('id','=',$temp['id'])->find();
                if (empty($res)) {
                    $this->throwError("未检测到快递方式数据");
                }
                $edit = Db::name('express')->where('id','=',$temp['id'])->update($temp);
                if (!$edit){
                    Db::rollback();
                    $this->throwError("修改失败");
                }
            }else{
                $temp['status']=$status;
                $temp['created_time']=time();
                $temp['update_time']=time();
                $insert = Db::name('express')->insert($temp);
                if (!$insert){
                    Db::rollback();
                    $this->throwError("修改失败");
                }
            }

        }
        return true;
    }

    /**状态设置
     * @param $params
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */

    public function setStatus($params)
    {
        if (empty($params['id'])) {
            $this->throwError("模板id不能为空");
         }
        $list =  Db::name('express')->where('id','=',$params['id'])->find();
        if (empty($list)) {
            $this->throwError("未检测到数据");
        }
        #redis 数据更新
        Db::startTrans();
        try {
            $expressData=json_decode($list['express_rules'],true);
            #规则存入redis 仓库id=>默认快递方式 指定快速方式 瓶数 价格 商品属性
            $redis = new  RedisExpandService(Config('cache')['stores']['redis']);
            foreach ($expressData as $value){
                foreach ($value['warehouse'] as $warehouseId){
                    $redis->del("vinehoo.express.".$params['id'].'.'.$warehouseId);
                    $insert1 =  $redis->rPush("vinehoo.express.".$params['id'].'.'.$warehouseId,json_encode([$value['default_express'],0,0,0]));
                    if ($insert1 == null){
                        Db::rollback();
                        Log::error('redis 写入失败');
                        $this->throwError("redis 写入失败");
                    }
                    foreach ($value['appoint'] as $a){
                        $insert=[$a['appoint_express'],intval($a['number']),floatval($a['price']),$a['goods_attribute']];
                        $insert2= $redis->rPush("vinehoo.express.".$params['id'].'.'.$warehouseId,json_encode($insert));
                        if ($insert2 == null){
                            Db::rollback();
                            Log::error('redis 写入指定快递方式失败');
                            $this->throwError("redis 写入指定快递方式失败");
                        }
                    }
                }
            }
          $result=  (new ExpressModel())->setStatus($params);
            if (!$result){
                Db::rollback();
                $this->throwError("默认设置失败");
            }
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Log::error('默认设置失败：' . $e->getMessage());
            throw new ValidateException("默认设置失败");
        }

    }
}
