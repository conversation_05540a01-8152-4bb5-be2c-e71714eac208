<?php
declare (strict_types = 1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;

/**
 * 收款统计数据同步定时任务
 * 每日凌晨执行，将前一天的Redis数据同步到MySQL
 */
class PaymentStatisticsSyncCommand extends Command
{
    protected $service;

    protected function configure()
    {
        // 指令配置
        $this->setName('PaymentStatisticsSyncCommand')
            ->setDescription('收款统计数据同步定时任务');
    }

    protected function execute(Input $input, Output $output)
    {
        // 指令输出
        $this->init();
        $result = $this->service->exec();
        
        if ($result) {
            $output->writeln('收款统计数据同步成功');
        } else {
            $output->writeln('收款统计数据同步失败');
        }
    }

    protected function init()
    {
        $this->service = new \app\service\command\PaymentStatisticsSyncCommand();
    }
}
