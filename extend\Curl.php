<?php


use think\Exception;

class Curl
{


    /**
     * @方法描述: 获取T+业务员
     * <AUTHOR>
     * @Date 2022/9/27 16:04
     * @param $param
     * $param[name]    否    string    业务员名称
     * $param[account_no]    否    string    账套 默认002
     * @return mixed
     * @throws Exception
     */
    static public function getTPlusClerk($param = [])
    {
        $method = "/pushtplus/v3/clerk";
        $query  = self::array_to_url_prarm($param);
        $query  = $query ? '?' . $query : '';
        $url    = env('ITEM.PUSH_T_PLUS_URL') . $method . $query;

        $code   = httpCurl($url, 'get', [], 3);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0) {
            throw new Exception('获取T+业务员失败: ' . $result['error_msg']);
        }

        return $result['data'];
    }

    /**
     * @方法描述: 获取T+仓库
     * <AUTHOR>
     * @Date 2022/9/27 16:04
     * @param $param
     * $param[name]    否    string    仓库名称
     * $param[code]    否    string    仓库编码
     * $param[account_no]    否    string    账套 默认002
     * @return mixed
     * @throws Exception
     */
    static public function getTPlusWarehouse($param = [])
    {
        $method = "/pushtplus/v3/warehouse";
        $query  = self::array_to_url_prarm($param);
        $query  = $query ? '?' . $query : '';
        $url    = env('ITEM.PUSH_T_PLUS_URL') . $method . $query;

        $code   = httpCurl($url, 'get', [], 3);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0) {
            throw new Exception('获取T+业务员失败: ' . $result['error_msg']);
        }

        return $result['data'];
    }

    /**
     * @方法描述: 获取管理员信息
     * <AUTHOR>
     * @Date 2022/9/27 14:20
     * @param $param
     * $param[admin_id]    是    string    管理员ID(多个id用英文半角逗号分隔)
     * $param[field]    否    string    指定字段不传默认(id,realname,title,userid)
     * @return mixed
     * @throws Exception
     */
    static public function adminInfo($param)
    {
        $method = "/authority/v3/admin/info";
        $url    = env('ITEM.AUTHORITY_URL') . $method . '?' . self::array_to_url_prarm($param);

        $code   = httpCurl($url, 'get', [], 3);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0) {
            throw new Exception('查询管理员列表错误: ' . $result['error_msg']);
        }

        return $result['data'];
    }

    /**
     * @方法描述: 获取管理员列表
     * <AUTHOR>
     * @Date 2022/9/27 14:20
     * @param $param
     * $param[role_id]    否    int    角色ID
     * $param[telephone]    否    int    手机号
     * $param[realname]    否    string    真实姓名
     * $param[limit]    是    int    返回条数
     * $param[page]    是    int    当前页
     * @return mixed
     * @throws Exception
     */
    static public function adminList($param)
    {
        $method = "/authority/v3/admin/list";
        $url    = env('ITEM.AUTHORITY_URL') . $method . '?' . self::array_to_url_prarm($param);

        $code   = httpCurl($url, 'get', [], 3);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0) {
            throw new Exception('查询管理员列表错误: ' . $result['error_msg']);
        }

        return $result['data'];
    }

    /**
     * @方法描述: 获取指定管理员列表
     * <AUTHOR>
     * @Date 2022/9/16 16:59
     * @param $type int 列表类型：2采购组，3文案组，4客服组，20用户查询, 35订单查询机器人
     * @return mixed
     * @throws Exception
     */
    static public function getSpecifyList($type)
    {
        $method = "/authority/v3/admin/specifyList";
        $url    = env('ITEM.AUTHORITY_URL') . $method . '?type=' . $type;

        $code   = httpCurl($url, 'get', [], 3);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0) {
            throw new Exception('查询管理员列表错误: ' . $result['error_msg']);
        }

        return $result['data']['list'];
    }

    /**
     * @方法描述: 取出不为空结果
     * <AUTHOR>
     * @Date 2022/9/27 14:27
     * @param $method
     * @param $args
     * @return mixed
     * @throws \think\Exception
     */
    static public function notEmptyResult($method, $args)
    {
        $result = self::$method(...$args);
        if (empty($result)) {
            throw new \think\Exception(" {$method} 查询结果为空");
        }
        return $result;
    }

    /**
     * @方法描述:数组转 Query url
     * <AUTHOR>
     * @Date 2022/9/27 13:26
     * @param $array
     * @return string
     */
    static public function array_to_url_prarm($array)
    {
        $prarms = [];

        foreach ($array as $key => $val) {
            $prarms[] = $key . '=' . str_replace(' ', '+', $val);
        }

        return implode('&', $prarms);
    }

    /**
     * @方法描述: 获取用户信息
     * <AUTHOR>
     * @Date 2022/8/24 17:39
     * @param $uid
     * @param string $fields
     * @return mixed
     * @throws Exception
     */
    static public function getUserInfo($uid, $fields = 'uid,telephone,nickname,avatar_image,user_level,type')
    {
        return self::getUserInfoList($uid, $fields)[0];
    }

    /**
     * @方法描述: 获取用户信息列表
     * <AUTHOR>
     * @Date 2022/8/24 17:39
     * @param $uid
     * @param string $fields
     * @return mixed
     * @throws Exception
     */
    static public function getUserInfoList($uid, $fields = 'uid,telephone,nickname,avatar_image,user_level,type')
    {
        $method = '/user/v3/profile/getUserInfo';
        $url    = env('ITEM.USER_URL') . $method . '?uid=' . $uid . "&field=$fields";

        $code   = httpCurl($url, 'get', [], 3);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0 || !isset($result['data']['list'][0])) {
            throw new Exception('未找到用户');
        }

        return $result['data']['list'];
    }

    /**
     * @方法描述: 数据加解密
     * <AUTHOR>
     * @Date 2022/10/11 10:37
     * @param array $param 参数
     * @param string $type 类型：D解密，E加密
     * @param int $i 请求次数
     * @return array|\think\Response
     */
    static public function cryptionDeal($orig_data, $type = 'D', $i = 1)
    {
        $types = ["D" => 2, 'E' => 1];
        return cryptionDeal($types[$type], $orig_data, '8888888888', 'tp6-orders');
    }

    /**
     * @方法描述: 获取企业微信accesstoken
     * <AUTHOR>
     * @Date 2022/10/11 11:35
     * @param array $param
     * $param[corpid]    string    否    -
     * $param[agentid]    string    否    -
     * 不需要传递corpid和agentid代表获取vinehoo中台应用的accesstoken，大多数情况下是获取vinehoo中台应用
     * @return mixed
     * @throws Exception
     */
    static public function getWechatAccessToken($param = [])
    {
        $method = "/wechat/v3/wecom/accesstoken";
        $url    = env('ITEM.WECHART_URL') . $method;
        $query  = self::array_to_url_prarm($param);
        $query  = $query ? '?' . $query : $query;
        $url    .= $query;

        $code   = httpCurl($url, 'get', [], 3);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0) {
            throw new Exception('查询管理员列表错误: ' . $result['error_msg']);
        }

        return $result['access_token'];
    }

    public static function downloadGet($url, $file)
    {
        return file_put_contents($file, file_get_contents($url));
    }

    /**
     * @方法描述: 上传临时文件到微信
     * <AUTHOR>
     * @Date 2022/10/11 20:58
     * @param $filePath
     * @param string $type
     * @return bool
     * @throws Exception
     */
    static public function upTempFileToWechat($filePath, $type = 'file')
    {
        $accessToken = self::getWechatAccessToken();
        $url         = "https://qyapi.weixin.qq.com/cgi-bin/media/upload?access_token={$accessToken}&type={$type}";
        $info        = new \CURLFile(realpath($filePath));
        $info->setPostFilename(date('Y-m-d H:i:s') . basename($filePath));
        $data   = [
            'media' => $info,
        ];
        $header = [];

        $result = self::curl_post($url, $data, $header);
        $data   = json_decode($result, true);

        if (!$data || $data['errcode'] != 0) {
            throw new Exception('上传文件到微信失败! result: ' . $result);
        }

        return $data;
    }

    /**
     * @方法描述:
     * <AUTHOR>
     * @Date 2025/6/11 15:40
     * @param $media_id 临时素材
     * @param $path 保存路径
     * @return string
     * @throws Exception
     */
    static public function downloadTempFileToWechat($media_id, $path)
    {
        $accessToken = self::getWechatAccessToken();
        $url         = "https://qyapi.weixin.qq.com/cgi-bin/media/get?access_token={$accessToken}&media_id={$media_id}";

        // 创建目录
        if (!is_dir($path)) {
            mkdir($path, 0755, true);
        }

        // 第一步：获取header，解析文件名
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HEADER, true);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        $header = curl_exec($ch);
        if ($header === false) {
            throw new \Exception('获取header失败: ' . curl_error($ch));
        }
        $fileSize = curl_getinfo($ch, CURLINFO_CONTENT_LENGTH_DOWNLOAD);
        // 解析文件名

        $filename = null;
        if (preg_match('/filename="([^"]+)"/', $header, $matches)) {
            $filename = $matches[1];
        } else {
            $err_msg = "获取文件名称失败 ";
            if (preg_match('/error-msg:\\s*(.+)/i', $header, $matches)) {
                $err_msg .= isset($matches[1]) ? trim($matches[1]) : '';
                throw new Exception($err_msg);
            }
        }

        $fullPath = rtrim($path, DIRECTORY_SEPARATOR) . DIRECTORY_SEPARATOR . $filename;
        curl_setopt($ch, CURLOPT_NOBODY, false);

        // 第二步：下载文件
        $chunkSize = 20 * 1024 * 1024; // 20MB
        $fp        = fopen($fullPath, 'wb');
        if ($fileSize > $chunkSize && $fileSize > 0) {
            // 分块下载
            for ($start = 0; $start < $fileSize; $start += $chunkSize) {
                $end = min($start + $chunkSize - 1, $fileSize - 1);
                curl_setopt($ch, CURLOPT_RANGE, "bytes={$start}-{$end}");
                $response = curl_exec($ch);
                if ($response === false) {
                    fclose($fp);
                    unlink($fullPath);
                    throw new \Exception('下载文件失败: ' . curl_error($ch));
                }
                $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
                $body       = substr($response, $headerSize);
                fwrite($fp, $body);
            }
        } else {
            // 直接下载
            curl_setopt($ch, CURLOPT_RANGE, null); // 清除range
            $response = curl_exec($ch);
            if ($response === false) {
                fclose($fp);
                unlink($fullPath);
                throw new \Exception('下载文件失败: ' . curl_error($ch));
            }
            $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
            $body       = substr($response, $headerSize);
            fwrite($fp, $body);
        }
        fclose($fp);
        curl_close($ch);
        return $fullPath;
    }

    /**
     * @方法描述: 发起 POST 请求
     * <AUTHOR>
     * @Date 2022/10/11 20:53
     * @param $url
     * @param null $data
     * @return bool|string
     */
    static public function curl_post($url, $data = null, $header = [])
    {
        //创建一个新cURL资源
        $curl = curl_init();
        //设置URL和相应的选项
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);

        if (!empty($data)) {
            curl_setopt($curl, CURLOPT_POST, 1);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        }

        if (!empty($header)) {
            curl_setopt($curl, CURLOPT_HTTPHEADER, $header); //设置头信息
        }

        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        //执行curl，抓取URL并把它传递给浏览器
        $output = curl_exec($curl);
        //关闭cURL资源，并且释放系统资源
        curl_close($curl);
        return $output;
    }

    /**
     * 发送应用消息
     */
    static public function wecomSend($content, $userid, $msgtype, $agentid = 0)
    {
        $method = '/wechat/v3/wecom/app/send';
        $url    = env('ITEM.WECHART_URL') . $method;

        $code   = httpCurl($url, 'post', json_encode(compact('content', 'userid', 'msgtype', 'agentid')), 3);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0) {
            throw new Exception('发送应用消息失败: ' . $result['error_msg']);
        }

        return true;
    }

    /**
     * @方法描述: 获取用户信息列表
     * <AUTHOR>
     * @Date 2022/8/24 17:39
     * @param $param ['ips'] array
     * @param string $fields
     * @return mixed
     * @throws Exception
     */
    static public function naliIp($param)
    {
        $method = '/services/v3/ip/check';
        $url    = env('ITEM.NALI_IP_URL') . $method;

        $code   = httpCurl($url, 'post', json_encode($param), 3);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0 || !isset($result['data'])) {
            \think\facade\Log::error("naliIp 查找IP地址错误 " . json_encode([$url, 'post', $param]));
            throw new Exception('查找IP地址错误');
        }

        $data = [];
        foreach ($result['data'] as $item) {
            foreach ($item as $ip => $addr) {
                $data[$ip] = explode('|', $addr);
            }
        }

        return $data;
    }

    /**
     * @方法描述: 拍卖 添加消息通知
     * <AUTHOR>
     * @Date 2022/8/24 17:39
     * @param $param ['notice_type'] int 类型1:拍卖，2:通知，3:订单，4:评论
     * @param $param ['title'] string 标题
     * @param $param ['content'] string 内容
     * @param $param ['uid'] string 发送给那个用户
     * @param $param ['notice_data'] 对应的参数 例如：订单的信息 评论的信息。具体内容请看设计图的消息详情
     * @param string $fields
     * @return mixed
     * @throws Exception
     */
    static public function message($param, $headers = [])
    {
        if (isset($param['uid'])) {
            $param['uid'] = strval($param['uid']);
        }
        $method = '/message/v3/notice/noticeAdd';
        $url    = env('ITEM.MESSAGE_URL') . $method;

        $code   = httpCurl($url, 'post', json_encode($param), 3, $headers);
        $result = json_decode($code, true);


        if (!$result || $result['error_code'] != 0 || !isset($result['data'])) {
            \think\facade\Log::error("message 添加消息通知错误: " . json_encode([$url, 'post', $param]) . ' 请求结果: ' . $code);
//            throw new Exception('添加消息通知错误, ' . ($result['error_msg'] ?? ''));
        }

        return true;
    }

    /**
     * @方法描述: 拍卖 APP 批量（单个）用户透传推送
     * <AUTHOR>
     * @Date 2022/8/24 17:39
     * @文档地址 https://showdoc.wineyun.com//web/#/89/3899
     * @param $param ['transmission'] string json字符串（营销推送配置的模版数据 文档地址：https://showdoc.wineyun.com/web/#/24/2716）
     * @param $param ['title'] string 标题
     * @param $param ['content'] string 内容
     * @param $param ['notice_type'] int 通知类型(101:拍卖系统通知)
     * @param $param ['uids'] int array 推送人的用户id
     * @param string $fields
     * @return mixed
     * @throws Exception
     */
    static public function appPushAliasBatch($param, $headers = [])
    {
        $uids   = [];
        $p_uids = $param['uids'] ?? [];
        foreach ($p_uids as $uid) {
            $uids[] = intval($uid);
        }
        $param['uids'] = $uids;

        $method = '/message/v3/push/appPushAliasBatch';
        $url    = env('ITEM.MESSAGE_URL') . $method;

        $code   = httpCurl($url, 'post', json_encode($param), 3, $headers);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0 || !isset($result['data'])) {
            \think\facade\Log::error("appPushAliasBatch 用户透传推送错误: " . json_encode([$url, 'post', $param]) . ' 请求结果: ' . $code);
            //throw new Exception('用户透传推送错误, ' . ($result['error_msg'] ?? ''));
        }

        return true;
    }


    /**
     * @方法描述: 客户端路径配置-查询通过唯一标识
     * <AUTHOR>
     * @Date 2022/8/24 17:39
     * @文档地址 https://showdoc.wineyun.com/web/#/24/2716
     * @param $param ['code'] 必选 string 唯一标识
     * @param $param ['rep_type'] 可选 string  "1"=自定义配置路径
     * @param $param ['goods_id'] 可选 string  rep_type=3 必选. 拍品ID
     * @param string $fields
     * @return mixed
     * @throws Exception
     */
    static public function getClientpathByCode($param)
    {
        $method = '/marketing-conf/v3/clientpath/codefilterlist?' . self::array_to_url_prarm($param);
        $url    = env('ITEM.MARKET_CONF_URL') . $method;

        $code   = httpCurl($url, 'get', [], 3);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0 || !isset($result['data'])) {
            \think\facade\Log::error("getClientpathByCode 获取客户端路径配置错误: " . json_encode([$url, 'post', $param]) . ' 请求结果: ' . $code);
            throw new Exception('获取客户端路径配置错误, ' . $result['error_msg'] ?? '');
        }

        if (!empty($param ['rep_type']) && in_array($param ['rep_type'], ["1"])) {
            $rep_urls = [
                '1' => "xxxx", //自定义路径
            ];
            $url      = $rep_urls[$param ['rep_type']] ?? null;
            if ($url) {
                $result['data']['client_path_param'][0]['ios_val']     = $url;
                $result['data']['client_path_param'][0]['android_val'] = $url;
            }
        }

        return $result['data'];
    }

    /**
     * @方法描述:添加秒级任务
     * <AUTHOR>
     * @Date 2022/12/19 22:18
     * @param $param
     * @param $param ['task_id'] String 必传 任务ID，请使用GUID方式生成唯一值
     * @param $param ['task_trigger_time'] TimeStamp 必传 任务触发时间，10位时间戳
     * @param $param ['task_url'] String 必传 任务触发时调用的接口地址，必须带上HTTP或HTTPS
     * @param $param ['task_data'] String 必传 调用接口时使用POST方式传递给接口的数据（RAW DATA方式）
     * @return bool
     * @throws Exception
     */
    static public function secondLevelSchedulerAdd($param)
    {
        $method = '/services/v3/task/add';
        $url    = env('ITEM.SLS_URL') . $method;

        $data_string  = json_encode($param);
        $url_header   = [];
        $url_header[] = 'vinehoo-client: tp6-auction';
        $url_header[] = 'vinehoo-client-version: v3';
        $url_header[] = 'Content-Length: ' . strlen($data_string);

        $code   = httpCurl($url, 'post', $data_string, 3, $url_header);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0) {
            $desc = '添加秒级任务';
            \think\facade\Log::error(__FUNCTION__ . " {$desc}错误: " . json_encode([$url, 'post', $param]) . ' 请求结果: ' . $code);
            throw new Exception("{$desc}错误, " . ($result['error_msg'] ?? ($result['msg'] ?? '')));
        }

        return true;
    }

    /**
     * @方法描述:更新秒级任务时间、接口地址或参数
     * <AUTHOR>
     * @Date 2022/12/19 22:18
     * @param $param
     * @param $param ['old_task_id'] String 必传 被更新的任务ID
     * @param $param ['new_task_id'] String 必传 新的任务ID，请使用GUID方式生成唯一值
     * @param $param ['task_trigger_time'] TimeStamp 必传 任务触发时间，10位时间戳
     * @param $param ['task_url'] String 必传 任务触发时调用的接口地址，必须带上HTTP或HTTPS
     * @param $param ['task_data'] String 必传 调用接口时使用POST方式传递给接口的数据（RAW DATA方式）
     * @return bool
     * @throws Exception
     */
    static public function secondLevelSchedulerUpdate($param)
    {
        $method = '/services/v3/task/update';
        $url    = env('ITEM.SLS_URL') . $method;

        $data_string  = json_encode($param);
        $url_header   = [];
        $url_header[] = 'vinehoo-client: tp6-auction';
        $url_header[] = 'vinehoo-client-version: v3';
        $url_header[] = 'Content-Length: ' . strlen($data_string);

        $code   = httpCurl($url, 'post', $data_string, 3, $url_header);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0 || !isset($result['data'])) {
            $desc = '更新任务时间、接口地址或参数';
            \think\facade\Log::error(__FUNCTION__ . " {$desc}错误: " . json_encode([$url, 'post', $param]) . ' 请求结果: ' . $code);
            throw new Exception("{$desc}错误, " . $result['error_msg'] ?? '');
        }

        return true;
    }

    /**
     * @方法描述: 删除秒级任务
     * <AUTHOR>
     * @Date 2023/8/21 12:01
     * @param $param
     * @return true
     * @throws Exception
     */
    static public function secondLevelSchedulerDelete($param)
    {
        $method = '/services/v3/task/delete';
        $url    = env('ITEM.SLS_URL') . $method;

        $data_string  = json_encode($param);
        $url_header   = [];
        $url_header[] = 'vinehoo-client: tp6-auction';
        $url_header[] = 'vinehoo-client-version: v3';
        $url_header[] = 'Content-Length: ' . strlen($data_string);

        $code   = httpCurl($url, 'post', $data_string, 3, $url_header);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0) {
            $desc = '根据任务ID删除任务';
            \think\facade\Log::error(__FUNCTION__ . " {$desc}错误: " . json_encode([$url, 'post', $param]) . ' 请求结果: ' . $code);
            throw new Exception("{$desc}错误, " . ($result['error_msg'] ?? ($result['msg'] ?? '')));
        }

        return true;
    }

    /**
     * @方法描述: 拍卖信用分更新
     * <AUTHOR>
     * @Date 2022/12/21 11:36
     * @param $param
     * @param $param ['uid'] 必传 int 用户ID
     * @param $param ['operation'] 必传 int 操作：0:增加,1:减少
     * @param $param ['reason'] 必传 int 原因
     * @param $param ['score'] 必传 int 分数
     * @return bool
     * @throws Exception
     */
    static public function creditScoreUpdate($param)
    {
        $method = '/user/v3/auction/CreditScoreUpdate';
        $url    = env('ITEM.USER_URL') . $method;

        $code   = httpCurl($url, 'post', json_encode($param), 3);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0) {
            \think\facade\Log::error("creditScoreUpdate 拍卖信用分更新 : " . json_encode($param) . '; result: ' . $code);
            return $result['error_msg'] ?? ($result['msg'] ?? '更新失败');
        }

        return true;
    }

    /**
     * @方法描述: 身份证实名校验
     * <AUTHOR>
     * @Date 2022/12/21 11:36
     * 文档地址 https://showdoc.wineyun.com//web/#/37/3942
     * @param $param
     * @param $param ['idcard'] 必传 string 身份证号码
     * @param $param ['name'] 必传 string 姓名
     * @return bool
     * @throws Exception
     */
    static public function eidCheck($param)
    {
        $method = '/user/v3/user/eidCheck';
        $url    = env('ITEM.USER_URL') . $method;

        $code   = httpCurl($url, 'post', json_encode($param), 3);
        $result = json_decode($code, true);

        if ((!$result) || ($result['error_code'] != 0)) {
            \think\facade\Log::write('eidCheck 身份证验证错误: ' . $code);
            throw new Exception(($result['error_msg'] ?? ($result['msg'] ?? '验证失败')));
        }

        if (empty($result['data'])) {
            throw new Exception("身份证号错误!");
        }

        return $result['data'];
    }

    /**
     * @方法描述: 三方订单推送
     * @文档地址: https://showdoc.wineyun.com//web/#/42/2058
     * <AUTHOR>
     * @Date 2023/2/21 15:23
     * @param $param
     * @return array
     */
    static public function tripartiteOrderPush($param): array
    {
        $method = '/orders/v3/tripartite/create';
        $url    = env('ITEM.ORDERS_URL') . $method;

        $code   = httpCurl($url, 'post', json_encode($param), 3);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0) {
            $desc = '三方订单推送';
            \think\facade\Log::error(__FUNCTION__ . " {$desc}错误: " . json_encode([$url, 'post', $param]) . ' 请求结果: ' . $code);
            return [false, $code];
        }

        return [true, $code];
    }

    /**
     * @方法描述: 发送机器人消息
     * <AUTHOR>
     * @Date 2023/2/21 13:37
     * @param $param
     * @return bool
     * @throws Exception
     */
    static public function sendWechatSender($param)
    {
        $header[] = 'vinehoo-client:tp6-orders';
        $url      = env('ITEM.QUEUE_URL');

        $basedata = [
            'access_token' => $param['access_token'] ?? '9dd56154-a216-4f16-886e-e865fe106652',
            'type'         => 'text',
            'content'      => base64_encode($param['msg']),
            'at'           => $param['at'] ?? '', // 中台用户手机号
        ];

        $code   = httpCurl($url, 'post', json_encode([
            'exchange_name' => 'dingtalk',//  dingtalk
            'routing_key'   => 'dingtalk_sender', //  dingtalk_sender
            'data'          => base64_encode(json_encode($basedata)),
        ]), 5, $header);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0) {
            \think\facade\Log::error(__FUNCTION__ . " 发送机器人消息 加入队列错误: " . json_encode([$url, 'post', $param]) . ' 请求结果: ' . $code);
            throw new Exception('发送机器人消息 加入队列错误');
        }
        return true;

    }

    /**
     * @方法描述: 手机号查询中台UID
     * https://showdoc.wineyun.com/web/#/37/3334
     * <AUTHOR>
     * @Date 2023/6/6 10:05
     * @param $telephone 手机号（多个手机号英文半角逗号分隔）
     * @return mixed
     * @throws Exception
     */
    static public function telephoneMatchUser($telephone)
    {
        $method = '/user/v3/user/telephoneMatchUser';
        $url    = env('ITEM.USER_URL') . $method . '?telephone=' . $telephone;

        $code   = httpCurl($url, 'get', [], 5);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0) {
            throw new Exception('未找到用户');
        }

        return $result['data']['list'];
    }


    /**
     * @方法描述: 专题活动优惠券 列表
     * https://showdoc.wineyun.com/web/#/24/3332
     * <AUTHOR>
     * @Date 2023/6/6 11:24
     * @param $param
     * @return mixed
     * @throws Exception
     */
    static public function specialActivityCouponList($param)
    {
        $method = '/invite/v3/specialActivityCoupon/list';
        $url    = env('ITEM.MARKETING_INVITE_NEWUSERS_URL') . $method;
        $query  = self::array_to_url_prarm($param);
        $query  = $query ? '?' . $query : $query;
        $url    .= $query;

        $code   = httpCurl($url, 'get', [], 5);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0) {
            throw new Exception('查询活动列表错误: ' . ($result['error_code'] ?? ''));
        }

        return $result['data']['list'];
    }

    /**
     * @方法描述: 专题活动领取优惠券(包)
     * https://showdoc.wineyun.com/web/#/24/3333
     * <AUTHOR>
     * @Date 2023/6/6 11:24
     * @param $param
     * @return mixed
     * @throws Exception
     */
    static public function specialActivityCouponSend($param)
    {
        $method = '/invite/v3/specialActivityCoupon/send';
        $url    = env('ITEM.MARKETING_INVITE_NEWUSERS_URL') . $method;

        $code   = httpCurl($url, 'post', json_encode($param), 5);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0) {
            $desc = '专题活动领取优惠券(包)';
            \think\facade\Log::error(__FUNCTION__ . " {$desc}错误: " . json_encode([$url, 'post', $param]) . ' 请求结果: ' . $code);
            throw new Exception(" {$desc}错误: " . ($result['error_msg'] ?? ''));
        }

        return $result['data'];
    }

    /**
     * @方法描述: 计算最低价
     * 计算最低价(金额 - 新人价 - 优惠劵价格)
     * <AUTHOR>
     * @Date 2023/6/29 19:18
     * @param $param
     * @return false|mixed
     */
    static public function calcPrice($param)
    {
        $method = '/go-recommend/v3/calc/calcPrice';
        $url    = env('ITEM.RECOMMEND_URL') . $method;

        $code   = httpCurl($url, 'post', json_encode($param));
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0) {
            return false;
        }
        return $result['data'];
    }


    /**发起企微审批
     * @方法描述:
     * <AUTHOR>
     * @Date 2022/11/7 16:51
     * @param $param
     * @return bool
     * @throws Exception
     */
    static public function approval($param)
    {

        $method = "/wechat/v3/wecom/approval/create";
        $url    = env('ITEM.WECHART_URL') . $method;

        $data   = json_encode($param);
        $code   = httpCurl($url, 'post', $data, [], false, '10');
        $result = json_decode($code, true);

        \think\facade\Log::write("发起审批请求 {$url} post {$data}    {$code}");

        if (!$result || $result['error_code'] != 0) {
            throw new Exception('发起审批失败: ' . $result['error_msg']);
        }

        return true;
    }


    static protected function request($param)
    {
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $timeout      = $param['timeout'] ?? 10;    //超时时间
        $desc         = $param['desc'] ?? '未定义请求';    //请求描述
        $header       = $param['header'] ?? [];    //header
        #region 发起请求
        $request_body = [];
        if (!empty($param['param'])) {
            if ($param['request_type'] == 'get') {
                $param['url'] = $param['url'] . '?' . http_build_query($param['param']);
            } elseif ($param['request_type'] == 'post') {
                $request_body = $param['param'];
            }
        }
        $request_body = json_encode($request_body);
        $code         = httpCurl($param['url'], $param['request_type'], $request_body, $timeout, $header);
        $result       = json_decode($code, true);
        #endregion 发起请求

        \think\facade\Log::write("CURL发起请求：url:{$param['url']}, method:{$param['request_type']}，body:{$request_body}, code:{$code}");
        if (!$result || $result[$code_field] != $success_code) {
            throw new Exception("{$desc}错误: " . ($result[$msg_field] ?? '未知错误'));
        }

        return $result;
    }

    /**
     * @方法描述: 发送短信
     * <AUTHOR>
     * @Date 2023/9/1 16:19
     * @param $param
     * @return mixed
     * @throws Exception
     */
    static public function sendSms($param)
    {
        $desc         = '短信发送';
        $method       = '/sms/v3/group/sendSms';
        $url          = env('ITEM.SMS_URL') . $method;
        $request_type = "post"; //get post

        $data   = compact('desc', 'param', 'url', 'request_type');
        $result = self::request($data);
        return $result['data'];
    }

    /**
     * @方法描述: 快递100查询物流轨迹
     * <AUTHOR>
     * @Date 2023/6/26 15:18
     * @param $param
     * @return array|mixed
     */
    static public function kuaidi100MapTrack($param)
    {
        $url    = 'https://callback.vinehoo.com/logistics/logistics/mapTrack/v3/track?' . self::array_to_url_prarm($param);
        $code   = httpCurl($url, 'get', '', 3);
        $result = json_decode($code, true);
        return $result['data'] ?? [];
    }

    //https://showdoc.wineyun.com/web/#/18/792
    static public function receiveOrderSync($param)
    {
        $desc         = '萌芽撤单(退货)';
        $method       = '/sync/receiveOrderSync';
        $url          = env('ITEM.DISTRIBUTE_URL') . $method; //outbound
        $request_type = "post"; //get post
        $code_field   = $param['code_field'] ?? 'errorCode'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码

        $param['store_code']    = $param['store_code'] ?? 'xGpS0mPbNqo5FMQcdTk4nR1DJr9LUEfa';//固定值
        $param['refund_status'] = $param['refund_status'] ?? 1; // 1=撤单

        $data = compact('desc', 'param', 'url', 'request_type', 'code_field', 'msg_field', 'success_code');

        $result = self::request($data);
        return $result['data'];
    }

    //param['goods_id'] = 1
    static public function wmsCancelOrder($param)
    {
        $desc         = '取消拍卖临时订单(撤单)';
        $method       = '/auction-order/v3/order/wmsCancelOrder';
        $url          = env('ITEM.AUCTION_ORDERS_URL') . $method;
        $request_type = "post"; //get post

        $data   = compact('desc', 'param', 'url', 'request_type');
        $result = self::request($data);
        return $result['data'];
    }

    /**
     * @方法描述: 获取酒云网微信公众号AccessToken
     * <AUTHOR>
     * @Date 2022/10/11 11:35
     * @param array $param
     * $param[appid]    string    否    -
     * @return mixed
     * @throws Exception
     */
    static public function getGzhAccessToken($param = [])
    {
        $desc         = '获取酒云网微信公众号AccessToken';
        $method       = '/wechat/v3/gzh/accesstoken';
        $url          = env('ITEM.WECHART_URL') . $method;
        $request_type = "get"; //get post

        $data   = compact('desc', 'param', 'url', 'request_type');
        $result = self::request($data);

        return $result['access_token'];
    }

    /**
     * @方法描述: 获取小程序miniapp accesstoken
     * https://showdoc.wineyun.com/web/#/32/1847
     * <AUTHOR>
     * @Date 2023/12/5 9:54
     * @param $param
     * @return mixed
     * @throws Exception
     */
    static public function getMinappAccessToken($param = [])
    {
        $desc         = '获取酒云网微信公众号StableAccessToken';
        $method       = '/wechat/v3/minapp/accesstoken';
        $url          = env('ITEM.WECHART_URL') . $method;
        $request_type = "get"; //get post

        $data   = compact('desc', 'param', 'url', 'request_type');
        $result = self::request($data);

        return $result['access_token'];
    }

    static public function sendTemplateMsg($param)
    {
        $code_field   = $param['code_field'] ?? 'errcode'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'errmsg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $desc         = $param['desc'] ?? '微信公众号发送模板消息';    //请求描述
        $access_token = self::getGzhStableAccessToken();

        $url          = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token={$access_token}";
        $request_type = "post"; //get post

        $data   = compact('desc', 'param', 'url', 'request_type', 'code_field', 'msg_field', 'success_code');
        $result = self::request($data);

        return $result;
    }

    static public function getGzhStableAccessToken($param = [])
    {
        $desc         = '获取酒云网微信公众号StableAccessToken';
        $method       = '/wechat/v3/gzh/stable_accesstoken';
        $url          = env('ITEM.WECHART_URL') . $method;
        $request_type = "get"; //get post

        $data   = compact('desc', 'param', 'url', 'request_type');
        $result = self::request($data);

        return $result['access_token'];
    }

    static public function addrecord($param = [])
    {
        $desc         = '添加表格记录';
        $method       = '/wechat/v3/wecom/smartdoc/addrecord';
        $url          = env('ITEM.WECHART_URL') . $method;
        $request_type = "post"; //get post

        $param['access_token'] = $param['access_token'] ?? self::getWechatAccessToken();

        $data   = compact('desc', 'param', 'url', 'request_type');
        $result = self::request($data);

        return $result;
    }

    //同步修改发货单信息
    static public function receiptInfo($param)
    {
        $desc         = '同步修改发货单信息';
        $method       = '/sync/shiporder/receiptInfo';
        $url          = env('ITEM.DISTRIBUTE_URL') . $method; //outbound
        $request_type = "post"; //get post
        $code_field   = $param['code_field'] ?? 'errorCode'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $timeout      = 30;    //成功状态码

        $param['store_code'] = $param['store_code'] ?? 'xGpS0mPbNqo5FMQcdTk4nR1DJr9LUEfa';//固定值

        $data = compact('desc', 'param', 'url', 'request_type', 'code_field', 'msg_field', 'success_code', 'timeout');

        $result = self::request($data);
        return $result['data'];
    }

    //跨境套餐根据SKU拆分为不同的子套餐
    //https://showdoc.wineyun.com/web/#/25/4856
    static public function packageSplit($param)
    {
        $desc         = '跨境套餐根据SKU拆分为不同的子套餐';
        $method       = '/commodities/v3/package/split';
        $url          = env('ITEM.COMMODITIES_URL') . $method;
        $request_type = "get"; //get post
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码

        $data = compact('desc', 'param', 'url', 'request_type', 'code_field', 'msg_field', 'success_code');

        $result = self::request($data);
        return $result['data'];
    }


    //https://showdoc.wineyun.com/web/#/99/4214
    static public function similarInsert($param)
    {
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $desc         = '相似度查询新增数据到数据集中';
        $method       = '/services/v3/similar/insert';
        $url          = env('ITEM.SIMILAR_URL') . $method;
        $request_type = "post"; //get post

        $data   = compact('code_field', 'msg_field', 'success_code', 'desc', 'param', 'url', 'request_type');
        $result = self::request($data);
        return true;
    }

    //  销售订单u8c删除
    //  https://showdoc.wineyun.com/web/#/94/4940
    static public function saleOrderDelete($param)
    {
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $timeout      = $param['timeout'] ?? 30;    //超时时间
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $desc         = '销售订单u8c删除';
        $method       = '/erp/v3/saleOrder/deleteHandle';
        $url          = env('ITEM.ERP_URL') . $method;
        $request_type = "post"; //get post

        $data   = compact('timeout', 'code_field', 'msg_field', 'success_code', 'desc', 'param', 'url', 'request_type');
        $result = self::request($data);
        return true;
    }

    //  其他出库单(样品订单)u8c删除
    //  https://showdoc.wineyun.com/web/#/94/4941
    static public function icOtherOutDelete($param)
    {
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $timeout      = $param['timeout'] ?? 30;    //超时时间
        $desc         = '其他出库单(样品订单)u8c删除';
        $method       = '/erp/v3/icOtherOut/delete';
        $url          = env('ITEM.ERP_URL') . $method;
        $request_type = "post"; //get post

        $data   = compact('timeout', 'code_field', 'msg_field', 'success_code', 'desc', 'param', 'url', 'request_type');
        $result = self::request($data);
        return true;
    }


    static public function addressAiMatch($param)
    {
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $timeout      = $param['timeout'] ?? 30;    //超时时间
        $desc         = '收货地址智能匹配';
        $method       = '/user/v3/address/AiMatch';
        $url          = env('ITEM.USER_URL') . $method;
        $request_type = "get"; //get post

        $data   = compact('timeout', 'code_field', 'msg_field', 'success_code', 'desc', 'param', 'url', 'request_type');
        $result = self::request($data);
        return $result['data'];
    }


    //查询退货入库单详情
    static public function queryWmsSalesOrder($param)
    {
        $code_field   = $param['code_field'] ?? 'errorCode'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $timeout      = $param['timeout'] ?? 30;    //超时时间
        $desc         = '查询退货入库单详情';
        $method       = '/query/returns/details';
        $url          = env('ITEM.DISTRIBUTE_URL') . $method;
        $request_type = "post"; //get post

        $param['store_code'] = $param['store_code'] ?? 'xGpS0mPbNqo5FMQcdTk4nR1DJr9LUEfa';//固定值

        $data   = compact('timeout', 'code_field', 'msg_field', 'success_code', 'desc', 'param', 'url', 'request_type');
        $result = self::request($data);
        return $result['data'];
    }

    //确认退货入库单
    static public function syncReturnsConfirm($param)
    {
        $code_field   = $param['code_field'] ?? 'errorCode'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $timeout      = $param['timeout'] ?? 30;    //超时时间
        $desc         = '确认退货入库单';
        $method       = '/sync/returns/confirm';
        $url          = env('ITEM.DISTRIBUTE_URL') . $method;
        $request_type = "post"; //get post

        $param['store_code'] = $param['store_code'] ?? 'xGpS0mPbNqo5FMQcdTk4nR1DJr9LUEfa';//固定值

        $data   = compact('timeout', 'code_field', 'msg_field', 'success_code', 'desc', 'param', 'url', 'request_type');
        $result = self::request($data);
        return $result['data'];
    }

    //获取萌芽库存
    static public function getWmsStock($param)
    {
        $code_field   = $param['code_field'] ?? 'errorCode'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $timeout      = $param['timeout'] ?? 30;    //超时时间
        $desc         = '获取萌芽库存';
        $method       = '/query/shortCodeGetCount';
        $url          = env('ITEM.DISTRIBUTE_URL') . $method;
        $request_type = "post"; //get post

        $param['store_code'] = $param['store_code'] ?? 'xGpS0mPbNqo5FMQcdTk4nR1DJr9LUEfa';//固定值

        $data   = compact('timeout', 'code_field', 'msg_field', 'success_code', 'desc', 'param', 'url', 'request_type');
        $result = self::request($data);
        return $result['data'];
    }

    //
    static public function wmsGoodsGetFictitiousCount($param)
    {
        $code_field   = $param['code_field'] ?? 'errorCode'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $timeout      = $param['timeout'] ?? 30;    //超时时间
        $desc         = '根据商品条码/简码获取虚拟仓库库存';
        $method       = '/query/goodsGetFictitiousCount';
        $url          = env('ITEM.DISTRIBUTE_URL') . $method;
        $request_type = "post"; //get post

        $param['store_code'] = $param['store_code'] ?? 'xGpS0mPbNqo5FMQcdTk4nR1DJr9LUEfa';//固定值

        $data   = compact('timeout', 'code_field', 'msg_field', 'success_code', 'desc', 'param', 'url', 'request_type');
        $result = self::request($data);
        return $result['data'];
    }

    //撤销退货单（旧）
    static public function revokeReturns($param)
    {
        $code_field   = $param['code_field'] ?? 'errorCode'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $timeout      = $param['timeout'] ?? 30;    //超时时间
        $desc         = '萌芽撤销退货单';
        $method       = '/sync/revokeReturns';
        $url          = env('ITEM.DISTRIBUTE_URL') . $method;
        $request_type = "post"; //get post

        $param['store_code'] = $param['store_code'] ?? 'xGpS0mPbNqo5FMQcdTk4nR1DJr9LUEfa';//固定值

        $data   = compact('timeout', 'code_field', 'msg_field', 'success_code', 'desc', 'param', 'url', 'request_type');
        $result = self::request($data);
        return $result['data'];
    }

    static public function timingAdd($param)
    {
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $timeout      = $param['timeout'] ?? 30;    //超时时间
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $desc         = '添加超时任务';
        $method       = '/services/v3/timing/add';
        $url          = env('ITEM.TIMEING_SERVICE_URL') . $method;
        $request_type = "post"; //get post

        $data   = compact('timeout', 'code_field', 'msg_field', 'success_code', 'desc', 'param', 'url', 'request_type');
        $result = self::request($data);
        return true;
    }

    static public function erpGetCostPrice($param)
    {
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $timeout      = $param['timeout'] ?? 30;    //超时时间
        $desc         = '获取存货最新成本价';
        $method       = '/erp/v3/ic/getCostPrice';
        $url          = env('ITEM.ERP_URL') . $method;
        $request_type = "post"; //get post

        $data   = compact('timeout', 'code_field', 'msg_field', 'success_code', 'desc', 'param', 'url', 'request_type');
        $result = self::request($data);
        return $result['data'];
    }

    static public function kitQuery($param)
    {
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $timeout      = $param['timeout'] ?? 30;    //超时时间
        $desc         = '查询支付订单';
        $method       = '/payment/v3/kit/query';
        $url          = env('ITEM.PAYMENT_URL') . $method;
        $request_type = "post"; //get post

        $data   = compact('timeout', 'code_field', 'msg_field', 'success_code', 'desc', 'param', 'url', 'request_type');
        $result = self::request($data);
        return $result['data'];
    }

    static public function kitSubRefund($param)
    {
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $timeout      = $param['timeout'] ?? 30;    //超时时间
        $desc         = '发起子订单退款（暂不支持全额退款）';
        $method       = '/payment/v3/kit/subRefund';
        $url          = env('ITEM.PAYMENT_URL') . $method;
        $request_type = "post"; //get post
        $data         = compact('timeout', 'code_field', 'msg_field', 'success_code', 'desc', 'param', 'url', 'request_type');
        $result       = self::request($data);
        return $result['data'];
    }


    static public function refundQuery($param)
    {
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $timeout      = $param['timeout'] ?? 30;    //超时时间
        $desc         = '退款查询';
        $method       = '/payment/v3/kit/refundQuery';
        $url          = env('ITEM.PAYMENT_URL') . $method;
        $request_type = "post"; //get post
        $data         = compact('timeout', 'code_field', 'msg_field', 'success_code', 'desc', 'param', 'url', 'request_type');
        $result       = self::request($data);
        return $result['data'];
    }


    static public function queryOrderStatus($param)
    {
        //https://showdoc.wineyun.com/web/#/18/784
        $code_field   = $param['code_field'] ?? 'errorCode'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $timeout      = $param['timeout'] ?? 30;    //超时时间
        $desc         = '查询订单详情';
        $method       = '/query/orderStatus';
        $url          = env('ITEM.DISTRIBUTE_URL') . $method;
        $request_type = "post"; //get post

        $param['store_code'] = $param['store_code'] ?? 'xGpS0mPbNqo5FMQcdTk4nR1DJr9LUEfa';//固定值

        $data   = compact('timeout', 'code_field', 'msg_field', 'success_code', 'desc', 'param', 'url', 'request_type');
        $result = self::request($data);
        return $result['data'];
    }

    //https://showdoc.wineyun.com/web/#/94/5371
    //新增单据
    static public function arapOrder($param)
    {
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $timeout      = $param['timeout'] ?? 30;    //超时时间
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $desc         = '应收收款管理-新增单据';
        $method       = '/erp/v3/ArapOrder/create';
        $url          = env('ITEM.ERP_URL') . $method;
        $request_type = "post"; //get post
        $header       = [
            'vinehoo-uid:' . request()->header('vinehoo-uid'),
            'vinehoo-vos-name:' . request()->header('vinehoo-vos-name'),
        ];

        $data   = compact('header', 'timeout', 'code_field', 'msg_field', 'success_code', 'desc', 'param', 'url', 'request_type');
        $result = self::request($data);
        return $result['data'];
    }

    static public function vmProductReport($param)
    {
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $timeout      = $param['timeout'] ?? 30;    //超时时间
        $desc         = 'vm商品上报';
        $method       = '/maidian/v3/report/vmProductReport';
        $url          = env('ITEM.MAIDIAN_URL') . $method;
        $request_type = "post"; //get post
        $data         = compact('timeout', 'code_field', 'msg_field', 'success_code', 'desc', 'param', 'url', 'request_type');
        $result       = self::request($data);
        return $result['data'];
    }

    static public function erpGetAmounts($param)
    {
        $desc         = '获取销售单金额';
        $method       = '/erp/v3/saleOrder/getAmounts';
        $url          = env('ITEM.ERP_URL') . $method;
        $request_type = "post"; //get post
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $timeout      = 600;

        $data = compact('timeout', 'desc', 'param', 'url', 'request_type', 'code_field', 'msg_field', 'success_code');

        $result = self::request($data);
        return $result['data'];
    }


    //https://showdoc.wineyun.com/web/#/37/5445
    //余额扣除(按比例自动拆分)
    static public function splitBalanceByProportion($param)
    {
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $timeout      = $param['timeout'] ?? 30;    //超时时间
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $desc         = '余额扣除(按比例自动拆分)';
        $method       = '/internal/balance/splitBalanceByProportion';
        $url          = env('ITEM.GO_INTERNAL') . $method;
        $request_type = "post"; //get post
        $header = [];

        $data   = compact('header', 'timeout', 'code_field', 'msg_field', 'success_code', 'desc', 'param', 'url', 'request_type');
        $result = self::request($data);
        return $result['data'];
    }


    //https://showdoc.wineyun.com/web/#/37/5445
    //余额扣除(按比例自动拆分)
    static public function balanceChange($param)
    {
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $timeout      = $param['timeout'] ?? 30;    //超时时间
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $desc         = '余额变动';
        $method       = '/internal/balance/balanceChange';
        $url          = env('ITEM.GO_INTERNAL') . $method;
        $request_type = "post"; //get post
        $header = [];

        $data   = compact('header', 'timeout', 'code_field', 'msg_field', 'success_code', 'desc', 'param', 'url', 'request_type');
        $result = self::request($data);
        return $result;
    }

    static public function queuePush($param)
    {
        $desc         = '推送到队列';
        $url          = env('ITEM.QUEUE_URL');
        $request_type = "post"; //get post
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $header[]     = 'vinehoo-client:tp6-orders';

        $data = compact('header', 'desc', 'param', 'url', 'request_type', 'code_field', 'msg_field', 'success_code');

        $result = self::request($data);
        return true;
    }

}