# 收款退款统计接口使用示例

## 场景说明

### 场景1：单频道订单收款
用户购买了一个闪购商品，生成主订单 `VHM202412040001`，包含1个闪购子订单。

**调用示例：**
```bash
curl -X POST http://your-domain.com/orders/v3/payment-statistics/write \
  -H "Content-Type: application/json" \
  -d '{
    "main_order_no": "VHM202412040001",
    "amount": 158.00
  }'
```

**处理流程：**
1. 查询主订单，获取 `order_type = "0"` (闪购)
2. 查询 `vh_flash_order` 表获取子订单
3. 根据子订单期数查询ES获取收款商户ID
4. 将158.00元分配给对应的收款商户

### 场景2：多频道混合订单收款
用户购物车下单，包含闪购+秒发商品，生成主订单 `VHM202412040002`。

**订单结构：**
- 主订单：`VHM202412040002`，`order_type = "0,1"`
- 闪购子订单：期数123，现付金额100.00，收款商户ID=1
- 秒发子订单：期数456，现付金额200.00，收款商户ID=2

**调用示例：**
```bash
curl -X POST http://your-domain.com/orders/v3/payment-statistics/write \
  -H "Content-Type: application/json" \
  -d '{
    "main_order_no": "VHM202412040002", 
    "amount": 300.00
  }'
```

**处理流程：**
1. 查询主订单，获取 `order_type = "0,1"`
2. 查询 `vh_flash_order` 和 `vh_second_order` 表
3. 按现付金额比例分配：
   - 收款商户1：300.00 × (100.00/300.00) = 100.00元
   - 收款商户2：300.00 × (200.00/300.00) = 200.00元

### 场景3：工单退款
用户申请售后，生成工单 `GD202412040001`，退款50.00元。

**调用示例：**
```bash
curl -X POST http://your-domain.com/orders/v3/payment-statistics/write \
  -H "Content-Type: application/json" \
  -d '{
    "refund_no": "GD202412040001",
    "amount": 50.00
  }'
```

**处理流程：**
1. 识别为工单退款（GD开头）
2. 查询 `vh_customer_service.vh_work_order` 表获取期数
3. 根据期数查询ES获取收款商户ID
4. 将50.00元退款记录到对应商户

### 场景4：普通退款
订单超时自动取消，生成退款单 `RF202412040001`，退款80.00元。

**调用示例：**
```bash
curl -X POST http://your-domain.com/orders/v3/payment-statistics/write \
  -H "Content-Type: application/json" \
  -d '{
    "refund_no": "RF202412040001",
    "amount": 80.00
  }'
```

**处理流程：**
1. 识别为普通退款（非GD开头）
2. 查询 `vh_refund_order` 表获取子订单号
3. 通过ES查询子订单获取期数
4. 根据期数查询ES获取收款商户ID
5. 将80.00元退款记录到对应商户

## 防重复机制示例

### 重复收款调用
```bash
# 第一次调用 - 成功
curl -X POST http://your-domain.com/orders/v3/payment-statistics/write \
  -H "Content-Type: application/json" \
  -d '{"main_order_no": "VHM202412040001", "amount": 158.00}'

# 第二次调用 - 被拒绝（已处理过）
curl -X POST http://your-domain.com/orders/v3/payment-statistics/write \
  -H "Content-Type: application/json" \
  -d '{"main_order_no": "VHM202412040001", "amount": 158.00}'
```

**响应：**
```json
{
  "code": 400,
  "msg": "没有有效的收款商户需要处理",
  "data": null
}
```

### 重复退款调用
```bash
# 第一次调用 - 成功
curl -X POST http://your-domain.com/orders/v3/payment-statistics/write \
  -H "Content-Type: application/json" \
  -d '{"refund_no": "GD202412040001", "amount": 50.00}'

# 第二次调用 - 被拒绝
curl -X POST http://your-domain.com/orders/v3/payment-statistics/write \
  -H "Content-Type: application/json" \
  -d '{"refund_no": "GD202412040001", "amount": 50.00}'
```

**响应：**
```json
{
  "code": 400,
  "msg": "该退款单已经处理过，请勿重复提交",
  "data": null
}
```

## 错误处理示例

### 主订单不存在
```bash
curl -X POST http://your-domain.com/orders/v3/payment-statistics/write \
  -H "Content-Type: application/json" \
  -d '{"main_order_no": "VHM999999999", "amount": 100.00}'
```

**响应：**
```json
{
  "code": 400,
  "msg": "主订单不存在",
  "data": null
}
```

### 订单未支付
```bash
curl -X POST http://your-domain.com/orders/v3/payment-statistics/write \
  -H "Content-Type: application/json" \
  -d '{"main_order_no": "VHM202412040003", "amount": 100.00}'
```

**响应：**
```json
{
  "code": 400,
  "msg": "子订单 VHS202412040003001 状态错误，订单未支付",
  "data": null
}
```

## 兼容性示例

### 旧版本接口调用
```bash
curl -X POST http://your-domain.com/orders/v3/payment-statistics/write \
  -H "Content-Type: application/json" \
  -d '{
    "company_code": "001",
    "operation_type": 1,
    "amount": 200.00
  }'
```

**响应：**
```json
{
  "code": 200,
  "msg": "数据写入成功",
  "data": true
}
```

系统会自动识别这是旧版本调用，使用原有的处理逻辑。
