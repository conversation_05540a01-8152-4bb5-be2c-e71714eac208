<?php

namespace app;


use GuzzleHttp\Exception\RequestException;
use think\facade\Log;

abstract class BaseService
{
    use ApiResponse;
    /**
     * Description:GET请求
     * Author: zrc
     * Date: 2021/10/18
     * Time: 13:51
     * @param string $url
     * @param array $params
     * @param array $configs
     * @return array|mixed|string
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function httpGet(string $url, array $params = [], array $configs = [])
    {
        $timeout = env('TIMEOUT',5);
        $configs['timeout'] = $timeout;
        $client = new \GuzzleHttp\Client($configs);
        $params = ['query' => $params];
        try {
            $request = $client->request('GET', $url, $params);
            $return = $request->getBody()->getContents();
        } catch (RequestException $e) {
            $message = $e->getMessage();
            $return = [
                'error_code' =>'10002',
                'error_msg'=>$message,
                'data'=> []
            ];
            Log::error($url . '调用失败：' . $message.'调用参数：'.json_encode($params));
        }
        if(!is_array($return)){
            $response = json_decode($return, true);
        }else{
            $response = $return;
        }
        return $response;
    }

    /**
     * Description:POST请求
     * Author: zrc
     * Date: 2021/10/18
     * Time: 13:51
     * @param string $url
     * @param array $params
     * @param array $configs
     * @param string $contentType
     * @return array|mixed|string
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function httpPost(string $url, array $params = [], array $configs = [], string $contentType = 'form_params')
    {
        $timeout = env('TIMEOUT',5);
        $configs['timeout'] = $timeout;
        $client = new \GuzzleHttp\Client($configs);
        $params = [$contentType => $params];
        try {
            $request = $client->request('POST', $url, $params);
            $return = $request->getBody()->getContents();
        } catch (RequestException $e) {
            $message = $e->getMessage();
            $return = [
                'error_code' =>'10002',
                'error_msg'=>$message,
                'data'=> []
            ];
            Log::error($url . '调用失败：' . $message.'调用参数：'.json_encode($params));
        }
        if(!is_array($return)){
            $response = json_decode($return, true);
        }else{
            $response = $return;

        }
        return $response;
    }

    /**
     * Description:curl请求
     * Author: zrc
     * Date: 2022/9/23
     * Time: 17:38
     * @param $url
     * @param array $data
     * @param array $haeder
     * @param string $method
     * @param int $timeout
     * @param bool $sync
     * @return bool|string
     */
    function curl_request($url, $data = [], $haeder = [], $method = 'POST', $timeout = 30, $sync = True)
    {
        if (empty($header)) {
            $haeder = array(
                "Content-Type: application/json",
                "vinehoo-client: orders",
            );
        }
        $ch = curl_init();
        if (is_array($data)) {
            $data = http_build_query($data);
        }
        if ($method == 'POST') {
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, True);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        } else {
            if (!empty($data)) curl_setopt($ch, CURLOPT_URL, "{$url}?{$data}");
            else curl_setopt($ch, CURLOPT_URL, $url);
        }
        if (strlen($url) > 5 && strtolower(substr($url, 0, 5)) == "https") {
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        }
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        if ($haeder) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $haeder);
        }
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, $sync);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_NOBODY, !$sync);
        $return = curl_exec($ch);
        curl_close($ch);
        #记录请求日志
        $param = is_array($data) ? json_encode($data) : $data;
        Log::info("请求URL：{$url}，请求参数：{$param}，响应参数：" . $return);
        return $return;
    }
}