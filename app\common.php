<?php
// 应用公共文件

use app\ElasticSearchConnection;
use GuzzleHttp\Exception\RequestException;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use think\facade\Db;
use think\facade\Log;
use think\Response;

/**
 * Description:创建订单号
 * Author: zrc
 * Date: 2021/7/30
 * Time: 15:20
 * @param string $prex
 */
function creatOrderNo($prex = '', $uid = '')
{
    if ($uid) {
        $orderSn = $prex . substr(date('Ymd'), 2) . str_pad($uid, 8, '0', STR_PAD_LEFT) . str_pad(rand(0, 4999) + rand(0, 5000), 4, '0', STR_PAD_LEFT);
        $where[] = ['bool' => ['should' => [['match_phrase' => ['sub_order_no.keyword' => $orderSn]], ['match_phrase' => ['main_order_no' => $orderSn]]]]];
        $data    = esGetList('vinehoo.orders', $where, [], 0, 1);
        if (isset($data['hits']['hits']) && count($data['hits']['hits']) > 0) {
            $orderSn = creatOrderNo($prex, $uid);
        }
    } else {
        $orderSn = $prex . substr(date('Ymd'), 2) . substr(time(), -5) . substr(microtime(), 2, 5) . str_pad(rand(0, 99), 2, '0', STR_PAD_LEFT);
    }
    return $orderSn;
}

/**
 * Description:
 * Author: zrc
 * Date: 2021/7/24
 * Time: 12:23
 * @param $type 1加密 2解密
 * @param $orig_data /加解密数据数组
 * @param $uid /用户ID/手机号
 * @param $operator /操作人
 */
function cryptionDeal($type, $orig_data, $uid, $operator)
{
    if ($type == 1) {
        $url = env('ITEM.CRYPTION_ADDRESS') . '/v1/encrypt';
    } else {
        $url = env('ITEM.CRYPTION_ADDRESS') . '/v1/decrypt';
    }
    $crypt_data = array(
        'orig_data' => array_values($orig_data),
        'from'      => 'php-mall',
        'uid'       => (string)$uid,
        'operator'  => (string)$operator
    );
    $crypt      = (new \app\CommonHttpRequest())->httpPost($url, $crypt_data, [], 'json');
    if (isset($crypt['data'])) {
        return $crypt['data'];
    }
    return [];
}

/**
 * Description:部分字符串隐藏
 * Author: zrc
 * Date: 2021/7/24
 * Time: 10:29
 * @param $string
 * @param int $start
 * @param int $length
 * @param string $re
 * @return bool|string
 */
function hidestr($string, $start = 0, $length = 0, $re = '*')
{
    if (empty($string)) return false;
    $strarr    = array();
    $mb_strlen = mb_strlen($string);
    while ($mb_strlen) {//循环把字符串变为数组
        $strarr[]  = mb_substr($string, 0, 1, 'utf8');
        $string    = mb_substr($string, 1, $mb_strlen, 'utf8');
        $mb_strlen = mb_strlen($string);
    }
    $strlen = count($strarr);
    $begin  = $start >= 0 ? $start : ($strlen - abs($start));
    $end    = $last = $strlen - 1;
    if ($length > 0) {
        $end = $begin + $length - 1;
    } elseif ($length < 0) {
        $end -= abs($length);
    }
    for ($i = $begin; $i <= $end; $i++) {
        $strarr[$i] = $re;
    }
    if ($begin > $end || $begin > $last || $end > $last) return '*';
    return implode('', $strarr);
}

/**
 * http post请求，参数为json字符串
 * @param $url
 * @param $data_string
 * @return bool|string
 */
function httpPostString(string $url, string $data_string, $header = '', $timeOut = 5)
{
    if (empty($header)) {
        $header = array(
            'Content-Type: application/json',
            "vinehoo-client: orders",
        );
    }
    $curl = curl_init();
    curl_setopt_array($curl, array(
        CURLOPT_URL            => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING       => "",
        CURLOPT_MAXREDIRS      => 10,
        CURLOPT_TIMEOUT        => $timeOut,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION   => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST  => "POST",
        CURLOPT_POSTFIELDS     => $data_string,
        CURLOPT_HTTPHEADER     => $header
    ));
    $response = curl_exec($curl);
    $err      = curl_error($curl);
    curl_close($curl);
    if ($err) {
        Log::error($url . '调用失败：' . $err . '调用参数：' . $data_string);
        return ['error_code' => 10002, 'error_msg' => $err, 'data' => []];
    } else {
        Log::write($url . '调用成功：调用参数：' . $data_string);
        if (is_null(json_decode($response))) return $response;
        return json_decode($response, true);
    }
}

/**
 * Description:GET请求
 * Author: zrc
 * Date: 2021/10/18
 * Time: 14:45
 * @param string $url
 * @param array $params
 * @param array $configs
 * @return array|mixed|string
 * @throws \GuzzleHttp\Exception\GuzzleException
 */
function httpGet(string $url, array $params = [], array $configs = [])
{
    $timeout            = env('TIMEOUT', 5);
    $configs['timeout'] = $timeout;
    $client             = new \GuzzleHttp\Client($configs);
    $params             = ['query' => $params];
    try {
        $request = $client->request('GET', $url, $params);
        $return  = $request->getBody()->getContents();
    } catch (RequestException $e) {
        $message = $e->getMessage();
        $return  = [
            'error_code' => 10002,
            'error_msg'  => $message,
            'data'       => []
        ];
        Log::error($url . '调用失败：' . $message . '调用参数：' . json_encode($params));
    }
    if (!is_array($return)) {
        $response = json_decode($return, true);
    } else {
        $response = $return;
    }
    return $response;
}

/**
 * Description:curl请求
 * Author: zrc
 * Date: 2023/2/14
 * Time: 17:07
 * @param $url
 * @param array $data
 * @param array $haeder
 * @param string $method
 * @param int $timeout
 * @param bool $sync
 * @return bool|string
 */
function curlRequest($url, $data = [], $haeder = [], $method = 'POST', $timeout = 30, $sync = True)
{
    if ($method == 'POST' && !is_array($data) && !in_array('Content-Type:application/json', $haeder)) {
        $haeder[] = 'Content-Type:application/json';
        $haeder[] = 'vinehoo-client:orders';
    }
    $ch = curl_init();
    if (is_array($data)) {
        $data = http_build_query($data);
    }
    if ($method == 'POST') {
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, True);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    } else {
        if (!empty($data)) curl_setopt($ch, CURLOPT_URL, "{$url}?{$data}");
        else curl_setopt($ch, CURLOPT_URL, $url);
    }
    if (strlen($url) > 5 && strtolower(substr($url, 0, 5)) == "https") {
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    }
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    if ($haeder) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $haeder);
    }
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, $sync);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_NOBODY, !$sync);
    $return = curl_exec($ch);
    curl_close($ch);
    #记录请求日志
    $param = is_array($data) ? json_encode($data) : $data;
    Log::info("请求URL：{$url}，请求参数：{$param}，响应参数：" . $return);

    return json_decode($return, true);
}

if (!function_exists('httpCurl')) {
    function httpCurl($url, $http = 'get', $data = [], $timeout = 10, $headers = [])
    {
        $headers[] = 'Content-Type: application/json;charset=utf-8';

        $curl = curl_init(); //初始化
        curl_setopt($curl, CURLOPT_URL, $url); //设置抓取的url
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); // 跳过证书检查
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false); // 从证书中检查SSL加密算法是否存在
        curl_setopt($curl, CURLOPT_HEADER, false); //设置头文件的信息作为数据流输出
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers); //设置头信息
        if ($http == 'post') {
            curl_setopt($curl, CURLOPT_POST, true); //设置post方式提交
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data); //设置post数据
        }
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true); //设置获取的信息以文件流的形式返回，而不是直接输出。
        curl_setopt($curl, CURLOPT_TIMEOUT, $timeout); //设置超时时间
        $response = curl_exec($curl); //执行命令
        curl_close($curl); //关闭URL请求
        // print_r($response); //显示获得的数据
        return $response;
    }
}


/**
 * Description:ES获取一条数据
 * Author: zrc
 * Date: 2021/8/12
 * Time: 18:33
 * @param $id
 * @param $index
 * @return Response
 */
function esGetOne($id, $index)
{
    try {
        $es     = new ElasticSearchConnection();
        $result = $es->connection()->get(['id' => $id, 'index' => $index, 'type' => '_doc']);
        return $result['_source'];
    } catch (Exception $e) {
        return '';
    }
}

/**
 * Description:ES获取列表数据
 * Author: zrc
 * Date: 2021/8/13
 * Time: 12:02
 * @param $index
 * @param $where
 * @param int $page
 * @param int $limit
 * @return array|callable|Response
 */
function esGetList($index, $where, $order = [], $offset = 0, $limit = 100)
{
    try {
        $es     = new ElasticSearchConnection();
        $params = [
            'index' => $index
        ];
        if (is_numeric($offset) && is_numeric($limit)) {
            $params['from'] = $offset;
            $params['size'] = $limit;
        }
        if ($where) {
            $params['body']['query']['bool'] = ['must' => $where];
        }
        if ($order) {
            $params['body']['sort'] = $order;
        }
        $result = $es->connection()->search($params);
        return $result;
    } catch (Exception $e) {
        return '';
    }
}

/**
 * Description:ES添加数据
 * Author: zrc
 * Date: 2021/8/13
 * Time: 12:11
 * @param $id
 * @param $index
 * @param $body
 * @return array|callable|Response
 */
function esAddData($id, $index, $body)
{
    try {
        $es     = new ElasticSearchConnection();
        $params = [
            'id'    => $id,
            'index' => $index,
            'type'  => '_doc',
            'body'  => $body
        ];
        $result = $es->connection()->create($params);
        return $result;
    } catch (Exception $e) {
        return '';
    }
}

/**
 * Description:ES更新数据
 * Author: zrc
 * Date: 2021/8/13
 * Time: 12:11
 * @param $id
 * @param $index
 * @param $body
 * @return array|callable|Response
 */
function esUpdateData($id, $index, $body)
{
    try {
        $es     = new ElasticSearchConnection();
        $params = [
            'id'    => $id,
            'index' => $index,
            'type'  => '_doc',
            'body'  => ['doc' => $body]
        ];
        $result = $es->connection()->update($params);
        return $result;
    } catch (Exception $e) {
        return '';
    }
}

/**
 * 支付签名
 */
if (!function_exists('sign')) {
    function sign($param = [], $key = 'tp6')
    {
        //按字典序排序数组参数
        ksort($param);
        //将数组参数拼接为url: key=value&key=value
        $string = '';
        if (!empty($param)) {
            $array = [];
            foreach ($param as $k => $v) {
                if (strlen($v) > 0) {
                    $array[] = $k . '=' . $v;
                }
            }
            $string = implode("&", $array);
        }
        // 在字符串后追加KEY
        $string .= $key;
        return strtoupper(hash("sha256", $string));
    }
}
/**
 * Description:冷链快递费计算
 * Author: zrc
 * Date: 2021/8/23
 * Time: 14:56
 * city_id 省市区ID(市ID)
 * capacity 容量
 */
function calcFreight($city_id, $capacity)
{
    $freight      = 0;
    $regionalRule = Db::name('regional_fee')->where(['rid' => $city_id, 'type' => 31])->value('fees');
    $pinfo        = json_decode($regionalRule, true);
    $volume_pip   = 750;
    $total        = ceil($capacity / $volume_pip);
    $x            = floor($total / 6);
    $y            = $total % 6;
    if (!empty($x)) {
        $freight += $x * ($pinfo[6]);
    }
    if (!empty($y)) {
        $freight += $pinfo[$y];
    }
    $freight = round($freight, 1);
    return $freight;
}

/**
 * Description:图片地址补全
 * Author: zrc
 * Date: 2021/9/7
 * Time: 16:07
 * @param $imgurl
 * @return string
 */
function imagePrefix($imgurl)
{
    if (strpos($imgurl, 'vinehoo/goods-imageshttp://img.vinehoo.com')) {
        $imgurl = str_replace('vinehoo/goods-imageshttp://img.vinehoo.com', '', $imgurl);
    }
    if (strpos($imgurl, 'http') === false) {
        if (substr($imgurl, 0, 1) == '/') {
            $imgurl = env('ALIURL') . $imgurl;
        } else {
            $imgurl = env('ALIURL') . '/' . $imgurl;
        }
    }
    return $imgurl;
}

/**
 * Description:参数验证
 * Author: zrc
 * Date: 2022/1/14
 * Time: 17:46
 * @param array $data
 * @param array $rule
 * @param array $message
 * @return bool
 */
function checkParam(array $data, array $rule, array $message)
{
    $validate = \think\facade\Validate::rule($rule);
    $validate->message($message);
    if ($validate->check($data)) {
        return true;
    } else {
        return $validate->getError();
    }
}

/**
 * Description:预计发货时间处理
 * Author: zrc
 * Date: 2022/3/14
 * Time: 11:37
 * @param $predictTime
 * @param $periods_type
 * @return false|string
 * 跨境：当前时间+72小时：
 *闪购、尾货：当前时间+24小时；
 *闪购-代发：18点之前下单的用户，预计发货为当前时间+24小时，18点之后下单的用户，预计发货时间为当前时间+48小时
 *秒发-代发：18点之前下单的用户，预计发货为当前时间+24小时，18点之后下单的用户，预计发货时间为当前时间+48小时
 *秒发：当前时间+4小时小于16点显示当天，大于16点显示第二天
 * 冷链（不包含跨境）：冷链的订单预计发货时间需要固定到周三或者周六
 * $periods_type 商品频道（0：闪购，1：秒发，2：跨境，3：尾货 9：商家秒发）
 */
function predictTimeDeal($predictTime, $periods_type, $express_type = 0, $warehouse_code = '')
{
    if ($predictTime < time()) {
        if (in_array($periods_type, [0, 1, 3]) && in_array($express_type, [3, 31])) {
            $week = date('w');
            switch ($week) {
                case 1:
                    $newPredictTime = date('Y-m-d 23:59:59', time() + 86400 * 2);
                    break;
                case 2:
                    $newPredictTime = date('Y-m-d 23:59:59', time() + 86400 * 1);
                    break;
                case 3:
                    $newPredictTime = date('Y-m-d 23:59:59', time() + 86400 * 3);
                    break;
                case 4:
                    $newPredictTime = date('Y-m-d 23:59:59', time() + 86400 * 2);
                    break;
                case 5:
                    $newPredictTime = date('Y-m-d 23:59:59', time() + 86400 * 1);
                    break;
                case 6:
                    $newPredictTime = date('Y-m-d 23:59:59', time() + 86400 * 4);
                    break;
                case 0:
                    $newPredictTime = date('Y-m-d 23:59:59', time() + 86400 * 3);
                    break;
                default:
                    $newPredictTime = date('Y-m-d 23:59:59', time());
            }
        } else {
            if ($periods_type == 2) {
                $newPredictTime = date('Y-m-d 23:59:59', time() + 3 * 86400);
            } else if ($periods_type == 0 || $periods_type == 3) {
                if ($periods_type == 0 && in_array($warehouse_code, ['034', '272'])) {
                    $time = strtotime(date('Y-m-d', time())) + 18 * 60 * 60;
                    if (time() < $time) {
                        $newPredictTime = date('Y-m-d 23:59:59', time() + 86400);
                    } else {
                        $newPredictTime = date('Y-m-d 23:59:59', time() + 172800);
                    }
                } else {
                    $newPredictTime = date('Y-m-d 23:59:59', time() + 86400);
                }
            } else if ($periods_type == 1) {
                if (in_array($warehouse_code, ['034', '272'])) {
                    $time = strtotime(date('Y-m-d', time())) + 18 * 60 * 60;
                    if (time() < $time) {
                        $newPredictTime = date('Y-m-d 23:59:59', time() + 86400);
                    } else {
                        $newPredictTime = date('Y-m-d 23:59:59', time() + 172800);
                    }
                } else {
                    $time = strtotime(date('Y-m-d', time())) + 16 * 60 * 60;
                    if (time() + 4 * 60 * 60 < $time) {
                        $newPredictTime = date('Y-m-d 23:59:59', time());
                    } else {
                        $newPredictTime = date('Y-m-d 23:59:59', time() + 86400);
                    }
                }
            } else {
                $newPredictTime = date('Y-m-d 23:59:59', time() + 86400);
            }
        }
    } else {
        $newPredictTime = date('Y-m-d 23:59:59', $predictTime);
    }
    return $newPredictTime;
}

/**
 * Description:根据省市区ID获取中文名
 * Author: zrc
 * Date: 2022/4/11
 * Time: 15:01
 * @param $id
 * @return mixed|string
 * @throws \GuzzleHttp\Exception\GuzzleException
 */
function getRegionalInfo($id)
{
    $data = httpGet(env('ITEM.USER_URL') . '/user/v3/regional/getInfo', ['id' => $id]);
    if (!isset($data['data']['name'])) return '';
    return $data['data']['name'];
}

/**
 * Description:解析excel-xls文件
 * Author: zrc
 * Date: 2022/4/27
 * Time: 10:34
 * @param $path
 * @param $startI
 * @return array
 */
function getExcelData($path, $startI)
{
    $extension = pathinfo($path, PATHINFO_EXTENSION);
    if ($extension == 'xlsx') {

        $filename = basename($path);
        $path     = dirname($path);

        $config    = ['path' => $path];
        $excel     = new \Vtiful\Kernel\Excel($config);
        $sheetList = $excel->openFile($filename)->sheetList();

        $data = [
            'path'      => $path,
            'file_name' => $filename,
        ];

        $i = 1;
        foreach ($sheetList as $sheetName) {

            $sheetData = $excel
                ->openSheet($sheetName)
                ->getSheetData();

            $data['sheet'][$i] = [
                'name' => $sheetName,
                'list' => $sheetData,
            ];

            $data['sheet' . $i] = [
                'name' => $sheetName,
                'list' => $sheetData,
            ];
            $i++;
        }

        return [
            'error_code' => 0,
            'error_msg'  => '',
            'data'       => $data['sheet1']['list']
        ];
    } else {
        $reader = new Xls();
        try {
            // 设置读取选项，强制第一列为文本
            $reader->setReadDataOnly(true);
            $spreadsheet = $reader->load($path);
        } catch (\Exception $e) {
            $return = [
                'error_code' => 10002,
                'error_msg'  => $e->getMessage(),
                'data'       => []
            ];
            return $return;
        }
        $sheet     = $spreadsheet->getActiveSheet();
        $excelData = array();
        foreach ($sheet->getRowIterator($startI) as $row) {
            $tmp = array();
            $colIndex = 0;
            foreach ($row->getCellIterator() as $cell) {
                if ($colIndex == 1) {
                    // 第一列特殊处理，强制为字符串
                    $value = $cell->getValue();
                    if (is_numeric($value)) {
                        // 使用格式化值确保保留前导零
                        $tmp[] = (string)$cell->getFormattedValue();
                    } else {
                        $tmp[] = $cell->getFormattedValue();
                    }
                } else {
                    $tmp[] = $cell->getFormattedValue();
                }
                $colIndex++;
            }
            $excelData[$row->getRowIndex()] = $tmp;
        }
        return [
            'error_code' => 0,
            'error_msg'  => '',
            'data'       => $excelData
        ];
    }
}

/**
 * Description:解析csv文件
 * Author: zrc
 * Date: 2022/4/26
 * Time: 14:03
 * @param $path
 * @return array
 */
function getCsvData($path)
{
    //读取文件内容
    $file      = fopen($path, "r");
    $excelData = [];
    //输出文本中所有的行，直到文件结束为止。
    while (!feof($file)) {
        $str = fgets($file);
        if (!empty($str)) {
            #转换编码
            $encode = mb_detect_encoding($str, array("ASCII", 'UTF-8', "GB2312", "GBK", 'BIG5'));
            if ($encode != 'UTF-8') {
                $str = mb_convert_encoding($str, 'UTF-8', $encode);
            }
            $str         = trim($str, ',');
            $excelData[] = explode(',', $str);//fgets()函数从文件指针中读取一行
        }
    }
    fclose($file);
    return $excelData;
}

/** 拉去远端文件 */
function download_image($url, $format = 'xlsx')
{
    $file           = file_get_contents($url);
    $time           = time();
    $localPath      = root_path() . 'public/storage/file';
    $pic_local_path = $localPath . '/' . date("Ymd", time()) . '/' . $time . '.' . $format;
    $pic_local_url  = $localPath . '/' . date("Ymd", time());
    if (!file_exists($localPath)) {
        mkdir($localPath, 0777);
    }
    if (!file_exists($pic_local_url)) {
        mkdir($pic_local_url, 0777);
    }
    file_put_contents($pic_local_path, $file);
    return $pic_local_path;
}

function getRegion($id)
{
    $region_name = '';
    $regionData  = httpGet(env("ITEM.USER_URL") . "/user/v3/regional/getBatchInfo", ['id' => $id]);
    if (isset($regionData['data']) && isset($regionData['data']['list'])) {
        $region      = array_column($regionData['data']['list'], 'name');
        $region_name = implode(',', $region);
    }
    return $region_name;
}

/*
*异步请求
*$param 数组发送post请求，非数组直接发送
*/
function syncRequest($url, $param = '', $timeout = 10)
{
    if (strpos($url, 'http') === false) {
        $url = 'http://' . $url;
    }

    $urlParmas = parse_url($url);
    $host      = $urlParmas['host'];
    $path      = $urlParmas['path'];
    $port      = isset($urlParmas['port']) ? $urlParmas['port'] : 80;
    $errno     = 0;
    $errstr    = '';

    $fp = fsockopen($host, $port, $errno, $errstr, $timeout);
    if (is_array($param)) {
        $query = isset($param) ? http_build_query($param) : '';
    } else {
        $query = isset($param) ? $param : '';
    }
    $out = "POST " . $path . " HTTP/1.1\r\n";
    $out .= "host:" . $host . "\r\n";
    $out .= "content-length:" . strlen($query) . "\r\n";
    $out .= "content-type:application/x-www-form-urlencoded\r\n";
    $out .= "connection:close\r\n\r\n";
    $out .= $query;

    fputs($fp, $out);
    fclose($fp);
}

/**
 * @param $uids
 * @param string $files
 * @return array
 * @throws \GuzzleHttp\Exception\GuzzleException
 */
function getUserInfoByUids($uids, $files = '')
{
    //请求地址
    $base = env('ITEM.USER_URL');
    $url  = $base . '/user/v3/profile/getUserInfo';
    //数据组装
    $body      = [
        'uid'   => $uids,
        'field' => $files
    ];
    $userInfo  = httpGet($url, $body);
    $userInfos = [];
    if ((isset($userInfo['data']) && !empty($userInfo['data']))) {
        $data = $userInfo['data']['list'];
        foreach ($data as $v) {
            if (isset($v['avatar_image']) && !empty($v['avatar_image'])) {
                $v['avatar_image'] = imagePrefix($v['avatar_image']);
            }
            if (isset($v['created_time']) && !empty($v['created_time'])) {
                $v['created_time'] = date("Y-m-d H:i:s", $v['created_time']);
            }
            $userInfos[$v['uid']] = $v;
        }
        return $userInfos;
    }
    Log::error("用户信息：" . json_encode($userInfo));
    return $userInfos;
}

/**
 * @param array $uids
 * @param string $files
 * @return array
 * @throws \GuzzleHttp\Exception\GuzzleException
 */
 function getUserInfoByUidsBatches($uids, $files = '')
 {
    $result = [];

    $s_users = [];
    foreach ($uids as $u) {
        $s_users[] = $u;
        if (count($s_users) >= 500) {
            $uidStr = implode(',', $s_users);
            $s_users = [];
            $user_info = getUserInfoByUids($uidStr, $files);
            foreach ($user_info as $k => $v) {
                $result[$k] = $v;
            }
        }
    }
    if (!empty($s_users)) {
        $uidStr = implode(',', $s_users);
        $user_info = getUserInfoByUids($uidStr, $files);
        foreach ($user_info as $k => $v) {
            $result[$k] = $v;
        }
    }
    return $result;
 }

/**
 * *代替 除第一个字以外
 * @param $name
 * @return string
 */
function string_cut($name)
{
    $strlen   = mb_strlen($name, 'utf-8'); //获取字符长度
    $firstStr = mb_substr($name, 0, 1, 'utf-8');  //查找字符第一个
    $str      = $firstStr . str_repeat('*', $strlen - 1);  //拼接第一个+把字符串 "* " 重复 $strlen - 1 次：
    return $str;
}

/** oss上传 */
function OssUpload($path, $savename)
{
    // 阿里云主账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM账号进行API访问或日常运维，请登录RAM控制台创建RAM账号。
    $accessKeyId     = env("OSS.ACCESSKEYID");
    $accessKeySecret = env("OSS.ACCESSKEYSECRET");
// Endpoint以杭州为例，其它Region请按实际情况填写。
    $endpoint = env("OSS.ENDPOINT");
// 设置存储空间名称。
    $bucket = env("OSS.BUCKET");

    //exportExcel
// 设置文件名称。
    $object = $savename;

    $path   = str_replace('\\', '/', $path);
    $object = str_replace('\\', '/', $object);
    try {

        $ossClient = new \OSS\OssClient($accessKeyId, $accessKeySecret, $endpoint);
        $res       = $ossClient->uploadFile($bucket, $object, $path);
        $OssUrl    = $res['info']['url'];
        if (!empty($OssUrl)) {
            $OssUrl = env('OSS.ALIURL') . '/' . $object;
        }
        return $OssUrl;
    } catch (\OSS\Core\OssException $e) {
        //  printf(__FUNCTION__ . ": FAILED\n");
        printf($e->getMessage() . "\n");
        return $e->getMessage();
    }

}

/**
 *
 * @param $datas
 * @param array $options
 * @param string $fileName
 * @param string $format
 * @param int $type
 * @return bool|string
 * @throws \PhpOffice\PhpSpreadsheet\Exception
 * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
 */
function exportSheelExcel($datas, $options = [], $fileName = '', $format = 'Xlsx', $type = 0)
{
    set_time_limit(0);
    //初始化
    $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
    // $fileName    = iconv('utf-8', 'gb2312', $fileName);//文件名称
    //设置标题
    $spreadsheet->getActiveSheet()->setTitle($fileName);
    $filename = $fileName . '_' . date('YmdHis');
    $cellNum  = count($options);

    /* 设置默认文字居中 */
    $styleArray = [
        'alignment' => [
            'horizontal' => 'left',
            'vertical'   => 'left',
        ],
    ];
    $spreadsheet->getDefaultStyle()->applyFromArray($styleArray);
    /* 设置Excel Sheet */
    $spreadsheet->setActiveSheetIndex(0);
    $cellName = [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z',
        'AA',
        'AB',
        'AC',
        'AD',
        'AE',
        'AF',
        'AG',
        'AH',
        'AI',
        'AJ',
        'AK',
        'AL',
        'AM',
        'AN',
        'AO',
        'AP',
        'AQ',
        'AR',
        'AS',
        'AT',
        'AU',
        'AV',
        'AW',
        'AX',
        'AY',
        'AZ'
    ];

    $spreadsheet->setActiveSheetIndex(0)->setCellValue('A1', $fileName);
    //设置行高
    $spreadsheet->getActiveSheet()->getRowDimension(1)->setRowHeight(30);
    $spreadsheet->getActiveSheet()->getStyle('A1')->getFont()->setBold(true)->setName('Arial')->setSize(20);;
    //设置行高
    //$spreadsheet->getActiveSheet()->getRowDimension('A1')->setRowHeight(30);
    //合并单元格
    $spreadsheet->getActiveSheet()->mergeCells('A1:' . $cellName[$cellNum - 1] . '1');
    //默认水平居中
    $styleArray = [
        'alignment' => [
            'horizontal' => 'center',
            'vertical'   => 'center',
        ],
    ];
    $spreadsheet->getActiveSheet()->getStyle('A1')->applyFromArray($styleArray);
    $color = [
        'Black'   => 'FF000000',
        'White'   => 'FFFFFFFF',
        'Red'     => 'FFFF0000',
        'Red1'    => 'FF800000',//COLOR_DARKRED
        'Green'   => 'FF00FF00',
        'Green1'  => 'FF008000',//COLOR_DARKGREEN
        'Blue'    => 'FF0000FF',
        'Blue1'   => 'FF000080',//COLOR_DARKBLUE
        'Yellow'  => 'FFFFFF00',
        'Yellow1' => 'FF808000',//COLOR_DARKYELLOW
    ];

    //设置excel第2行数据
    foreach ($options as $key => $val) {
        $column = $cellName[$key] . '2';
        //设置表头
        $spreadsheet->setActiveSheetIndex(0)
            ->setCellValue($column, $val['name']);
        //设置列宽
        if (isset($val['width']) && !empty($val['width'])) {
            $spreadsheet->getActiveSheet()->getColumnDimension($cellName[$key])->setWidth($val['width']);
        } else {
            $spreadsheet->getActiveSheet()->getDefaultColumnDimension()->setWidth(15);//设置默认列宽为
        }
        //设置字体 粗体
        $spreadsheet->getActiveSheet()->getStyle($column)->getFont()->setBold(true);
        //设置行高
        if (!empty($val['height'])) {
            $spreadsheet->getActiveSheet()->getRowDimension($column)->setRowHeight($val['height']);
            //设置默认行高 $spreadsheet->getActiveSheet()->getDefaultRowDimension()->setRowHeight(15);
        }
        //设置颜色
        if (!empty($val['color']) && isset($color[$val['color']])) {
            $spreadsheet->getActiveSheet()->getStyle($column)->getFont()->getColor()->setARGB($color[$val['color']]);
        } else {
            $spreadsheet->getActiveSheet()->getStyle($column)->getFont()->getColor()->setARGB('FF000000');
        }
    }
    $yieldData = yieldData($datas);
    $i         = 0;
    foreach ($yieldData as $val) {
        for ($j = 0; $j < $cellNum; $j++) {
            //$spreadsheet->setActiveSheetIndex(0)->setCellValue($cellName[$j].($i+3),' '.$val[$options[$j]['column']].' ');

            //数据类型
            $dataType = isset($options[$j]['dataType']) ? $options[$j]['dataType'] : 's';
            switch ($dataType) {
                case 'n'://数字
                    $spreadsheet->setActiveSheetIndex(0)->setCellValueExplicit($cellName[$j] . ($i + 3), $val[$options[$j]['column']], $dataType);
                    break;
                case 'str2num':
                    $spreadsheet->setActiveSheetIndex(0)->setCellValueExplicit($cellName[$j] . ($i + 3), $val[$options[$j]['column']], $dataType);
                    break;
                case 'str':
                    $spreadsheet->setActiveSheetIndex(0)->setCellValueExplicit($cellName[$j] . ($i + 3), $val[$options[$j]['column']], $dataType);
                    break;
                case 's':
                    $spreadsheet->setActiveSheetIndex(0)->setCellValueExplicit($cellName[$j] . ($i + 3), $val[$options[$j]['column']], $dataType);
                    break;
                case 'inlineStr':
                    $spreadsheet->setActiveSheetIndex(0)->setCellValueExplicit($cellName[$j] . ($i + 3), $val[$options[$j]['column']], $dataType);
                    break;
                case 'null':
                    $spreadsheet->setActiveSheetIndex(0)->setCellValueExplicit($cellName[$j] . ($i + 3), $val[$options[$j]['column']], $dataType);
                    break;
                case 'f':
                    $spreadsheet->setActiveSheetIndex(0)->setCellValueExplicit($cellName[$j] . ($i + 3), $val[$options[$j]['column']], $dataType);
                    break;
                default:
                    $spreadsheet->setActiveSheetIndex(0)->setCellValueExplicit($cellName[$j] . ($i + 3), $val[$options[$j]['column']], $dataType);
                    break;
            }
        }
        $i++;
    }
    header('pragma:public');
    if ($format == 'Xlsx') {
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    } elseif ($format == 'Xls') {
        header('Content-Type: application/vnd.ms-excel');
    }
    // type等于1直接下载
    if ($type) {
        $objWriter = new \PhpOffice\PhpSpreadsheet\Reader\Xlsx($spreadsheet);
        $objWriter->setPreCalculateFormulas(false);
        header("Content-Type: application/force-download");
        header("Content-Type: application/octet-stream");
        header("Content-Type: application/download");
        header("Content-Disposition: attachment;filename=" . $filename . '.' . strtolower($format));
        header('Cache-Control: max-age=0');//禁止缓存
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header("Content-Transfer-Encoding:binary");
        header("Expires: 0");
        ob_clean();
        ob_start();
        $objWriter->save('php://output');
        /* 释放内存 */
        $spreadsheet->disconnectWorksheets();
        unset($spreadsheet);
        ob_end_flush();
        return true;
    } else {
        $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, $format);
        $rootPath  = app()->getRootPath();

        $savePath   = '/storage/exportExcel/' . $filename . '.' . strtolower($format);
        $resavePath = 'exportExcel/' . $filename . '.' . strtolower($format);
        $a          = $rootPath . 'public' . $savePath;
        if (!is_dir($rootPath . 'public' . '/storage/exportExcel/')) {
            $res = mkdir($rootPath . 'public' . '/storage/exportExcel/', 0755, true);
        }

        $objWriter->save($a);
        /* 释放内存 */
        $spreadsheet->disconnectWorksheets();
        unset($spreadsheet);
        @ob_end_flush();
        return $resavePath;
    }
}

/**
 *
 * @param $datas
 * @param array $options
 * @param string $fileName
 * @param string $format
 * @param int $type
 * @return bool|string
 * @throws \PhpOffice\PhpSpreadsheet\Exception
 * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
 */
function exportSheelExcels($datas, $options = [], $fileName = '', $format = 'Xls')
{
    set_time_limit(0);
    //初始化
    $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
    $cellNum     = count($options);
    $filename    = $fileName;
    /* 设置Excel Sheet */
    $spreadsheet->setActiveSheetIndex(0);
    $cellName = [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z',
        'AA',
        'AB',
        'AC',
        'AD',
        'AE',
        'AF',
        'AG',
        'AH',
        'AI',
        'AJ',
        'AK',
        'AL',
        'AM',
        'AN',
        'AO',
        'AP',
        'AQ',
        'AR',
        'AS',
        'AT',
        'AU',
        'AV',
        'AW',
        'AX',
        'AY',
        'AZ'
    ];
    $color    = [
        'Black'   => 'FF000000',
        'White'   => 'FFFFFFFF',
        'Red'     => 'FFFF0000',
        'Red1'    => 'FF800000',//COLOR_DARKRED
        'Green'   => 'FF00FF00',
        'Green1'  => 'FF008000',//COLOR_DARKGREEN
        'Blue'    => 'FF0000FF',
        'Blue1'   => 'FF000080',//COLOR_DARKBLUE
        'Yellow'  => 'FFFFFF00',
        'Yellow1' => 'FF808000',//COLOR_DARKYELLOW
    ];
    // 设置下拉选项列表
    $dataValidation = $spreadsheet->getActiveSheet()->getCell('D2')->getDataValidation();
    $dataValidation->setType('list');
    $dataValidation->setErrorStyle('stop');
    $dataValidation->setShowDropDown(true);
    $dataValidation->setFormula1('"顺丰快递,顺丰冷链,京东快递（不保价）,京东快递（保价）,京东快运,京东TC,韵达快递,圆通速递,联邦快递,中通快递,申通快递,EMS,德邦快递,同城快寄,跨越速运,极兔快递,壹米滴答"'); // 下拉选项的列表
    //设置excel第2行数据
    foreach ($options as $key => $val) {
        $column = $cellName[$key] . '1';
        //设置表头
        $spreadsheet->setActiveSheetIndex(0)
            ->setCellValue($column, $val['name']);
        //设置列宽
        if (isset($val['width']) && !empty($val['width'])) {
            $spreadsheet->getActiveSheet()->getColumnDimension($cellName[$key])->setWidth($val['width']);
        } else {
            $spreadsheet->getActiveSheet()->getDefaultColumnDimension()->setWidth(15);//设置默认列宽为
        }
        //设置字体 粗体
        $spreadsheet->getActiveSheet()->getStyle($column)->getFont()->setBold(true);
        //设置行高
        if (!empty($val['height'])) {
            $spreadsheet->getActiveSheet()->getRowDimension($column)->setRowHeight($val['height']);
            //设置默认行高 $spreadsheet->getActiveSheet()->getDefaultRowDimension()->setRowHeight(15);
        }
        //设置颜色
        if (!empty($val['color']) && isset($color[$val['color']])) {
            $spreadsheet->getActiveSheet()->getStyle($column)->getFont()->getColor()->setARGB($color[$val['color']]);
        } else {
            $spreadsheet->getActiveSheet()->getStyle($column)->getFont()->getColor()->setARGB('FF000000');
        }
    }
    $yieldData = yieldData($datas);
    $i         = 0;
    foreach ($yieldData as $val) {
        for ($j = 0; $j < $cellNum; $j++) {
            //$spreadsheet->setActiveSheetIndex(0)->setCellValue($cellName[$j].($i+3),' '.$val[$options[$j]['column']].' ');

            //数据类型
            $dataType = isset($options[$j]['dataType']) ? $options[$j]['dataType'] : 's';
            switch ($dataType) {
                case 'n'://数字
                    $spreadsheet->setActiveSheetIndex(0)->setCellValueExplicit($cellName[$j] . ($i + 2), $val[$options[$j]['column']], $dataType);
                    break;
                case 'str2num':
                    $spreadsheet->setActiveSheetIndex(0)->setCellValueExplicit($cellName[$j] . ($i + 2), $val[$options[$j]['column']], $dataType);
                    break;
                case 'str':
                    $spreadsheet->setActiveSheetIndex(0)->setCellValueExplicit($cellName[$j] . ($i + 2), $val[$options[$j]['column']], $dataType);
                    break;
                case 's':
                    $spreadsheet->setActiveSheetIndex(0)->setCellValueExplicit($cellName[$j] . ($i + 2), $val[$options[$j]['column']], $dataType);
                    break;
                case 'inlineStr':
                    $spreadsheet->setActiveSheetIndex(0)->setCellValueExplicit($cellName[$j] . ($i + 2), $val[$options[$j]['column']], $dataType);
                    break;
                case 'null':
                    $spreadsheet->setActiveSheetIndex(0)->setCellValueExplicit($cellName[$j] . ($i + 2), $val[$options[$j]['column']], $dataType);
                    break;
                case 'f':
                    $spreadsheet->setActiveSheetIndex(0)->setCellValueExplicit($cellName[$j] . ($i + 2), $val[$options[$j]['column']], $dataType);
                    break;
                default:
                    $spreadsheet->setActiveSheetIndex(0)->setCellValueExplicit($cellName[$j] . ($i + 2), $val[$options[$j]['column']], $dataType);
                    break;
            }
        }
        $i++;
    }
    header('pragma:public');
    header('Content-Type: application/vnd.ms-excel');
    $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, $format);
    $rootPath  = app()->getRootPath();

    $savePath   = '/exportExcels/' . $filename . '.' . strtolower($format);
    $resavePath = 'exportExcels/' . $filename . '.' . strtolower($format);
    $a          = $rootPath . 'public' . $savePath;
    if (!is_dir($rootPath . 'public' . '/exportExcels/')) {
        $res = mkdir($rootPath . 'public' . '/exportExcels/', 0755, true);
    }

    $objWriter->save($a);
    /* 释放内存 */
    $spreadsheet->disconnectWorksheets();
    unset($spreadsheet);
    @ob_end_flush();
    return $resavePath;
}

function yieldData($data)
{
    foreach ($data as $val) {
        yield $val;
    }
}

/**
 * Description:企业微信上传临时素材
 * Author: zrc
 * Date: 2022/9/22
 * Time: 14:16
 * @param $data
 */
function weixinUpload($file, $name)
{
    $getToken = httpGet(env('ITEM.WECHART_URL') . '/wechat/v3/wecom/accesstoken');
    if (!isset($getToken['access_token'])) return false;
    $url    = 'https://qyapi.weixin.qq.com/cgi-bin/media/upload?access_token=' . $getToken['access_token'] . '&type=file';
    $data   = array(
        'media' => new \CURLFile($file, '', $name),
    );
    $result = wx_curl_post($url, $data);
    $data   = @json_decode($result, true);
    return isset($data['media_id']) ? $data['media_id'] : '';
}

function wx_curl_post($url, $data = null)
{
    //创建一个新cURL资源
    $curl = curl_init();
    //设置URL和相应的选项
    curl_setopt($curl, CURLOPT_URL, $url);
    if (!empty($data)) {
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
    }
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    //执行curl，抓取URL并把它传递给浏览器
    $output = curl_exec($curl);
    //关闭cURL资源，并且释放系统资源
    curl_close($curl);
    return $output;
}

/**
 * Description:
 * Author: zrc
 * Date: 2022/10/12
 * Time: 18:10
 * @param $uid
 * @return array
 */
function getUserOrderCommentInfo($uid)
{
    $redis = new \Redis();
    $redis->connect(env('CACHE.HOST'), env('CACHE.PORT'));
    $redis->auth(env('CACHE.PASSWORD'));
    $redis->select(8);
    $data   = $redis->lrange('vinehoo.wineEvaluation.' . $uid, 0, -1);
    $result = array_unique($data);
    return $result;
}

/**
 * Description:跨境唯一识别码创建
 * Author: zrc
 * Date: 2022/12/6
 * Time: 9:29
 * @return string
 */
function buildGuid()
{
    $str  = md5(uniqid(mt_rand(), true));
    $guid = substr($str, 0, 8) . '-';
    $guid .= substr($str, 8, 4) . '-';
    $guid .= substr($str, 12, 4) . '-';
    $guid .= substr($str, 16, 4) . '-';
    $guid .= substr($str, 20, 12);
    return $guid;
}

/**
 * Description:截取utf-8格式字符串
 * Author: zrc
 * Date: 2023/4/11
 * Time: 15:10
 * @param $string
 * @param $length
 * @param string $etc
 * @return string
 */
function truncate_utf8_string($string, $length, $etc = '...')
{
    $result = '';
    $string = html_entity_decode(trim(strip_tags($string)), ENT_QUOTES, 'UTF-8');
    $strlen = strlen($string);
    for ($i = 0; (($i < $strlen) && ($length > 0)); $i++) {
        if ($number = strpos(str_pad(decbin(ord(substr($string, $i, 1))), 8, '0', STR_PAD_LEFT), '0')) {
            if ($length < 1.0) {
                break;
            }
            $result .= substr($string, $i, $number);
            $length -= 1.0;
            $i      += $number - 1;
        } else {
            $result .= substr($string, $i, 1);
            $length -= 0.5;
        }
    }
    $result = htmlspecialchars($result, ENT_QUOTES, 'UTF-8');
    if ($i < $strlen) {
        $result .= $etc;
    }
    return $result;
}

/**
 * 获取excel数据
 * @param $path
 * @return array
 * @throws PHPExcel_Exception
 * @throws PHPExcel_Reader_Exception
 */
function readExcelData($path)
{
    $type = pathinfo($path);
    $type = strtolower($type["extension"]);
    if ($type == 'xlsx') {
        $type = 'Excel2007';
    } elseif ($type == 'xls') {
        $type = 'Excel5';
    } else { #非表格直接返回错误
        $return = [
            'error_code' => 10001,
            'error_msg'  => "文件格式有误",
            'data'       => []
        ];
        return $return;
    }
    try {
        $objReader   = PHPExcel_IOFactory::createReader($type);
        $objPHPExcel = $objReader->load($path);
    } catch (\Exception $e) {
        $return = [
            'error_code' => 10002,
            'error_msg'  => $e->getMessage(),
            'data'       => []
        ];
        return $return;
    }

    $sheetSelected = 0;
    $objPHPExcel->setActiveSheetIndex($sheetSelected);
    $rowCount    = $objPHPExcel->getActiveSheet()->getHighestRow(); //获取表格行数
    $columnCount = $objPHPExcel->getActiveSheet()->getHighestColumn();//获取表格列数
    $dataArr     = array();
    #数据最后都存储到了$dataArr中
    for ($row = 1; $row <= $rowCount; $row++) {
        #列数循环 , 列数是以A列开始 getFormattedValue
        for ($n = ord('A'); $n <= ord($columnCount); $n++) {
            $column          = chr($n);
            $dataArr[$row][] = (string)$objPHPExcel->getActiveSheet()->getCell($column . $row)->getValue();
        }
        if (implode($dataArr[$row], '') == '') {#整行数据为空 则删除这行数据
            unset($dataArr[$row]);
        }
    }
    return [
        'error_code' => 0,
        'error_msg'  => '',
        'data'       => array_values($dataArr)
    ];
}

function deleteAllFilesInDirectory($directory)
{
    // 打开指定目录
    if ($handle = opendir($directory)) {
        // 遍历目录中的文件
        while (false !== ($file = readdir($handle))) {
            if ($file != "." && $file != "..") {
                $filePath = $directory . DIRECTORY_SEPARATOR . $file;
                // 检查文件是否是普通文件
                if (is_file($filePath)) {
                    // 删除文件
                    unlink($filePath);
                }
            }
        }
        closedir($handle);
    }
}

/**
 * @方法描述:列表数据分组
 * <AUTHOR>
 * @Date 2023/4/19 14:04
 * @param $array
 * @param $field
 * @return array
 */
if (!function_exists('array_group')) {
    function array_group($array, $field)
    {
        $gropu_list = [];
        foreach ($array as $item) {
            $gropu_list[$item[$field]][] = $item;
        }
        return $gropu_list;
    }
}

function readExcelData2($path)
{
    $type = pathinfo($path);
    $type = strtolower($type["extension"]);
    if ($type == 'xlsx') {
        $type = 'Excel2007';
    } elseif ($type == 'xls') {
        $type = 'Excel5';
    } else { #非表格直接返回错误
        $return = [
            'error_code' => 10001,
            'error_msg'  => "文件格式有误",
            'data'       => []
        ];
        return $return;
    }
    try {
        $objReader   = PHPExcel_IOFactory::createReader($type);
        $objPHPExcel = $objReader->load($path);
    } catch (\Exception $e) {
        $return = [
            'error_code' => 10002,
            'error_msg'  => $e->getMessage(),
            'data'       => []
        ];
        return $return;
    }

    $sheetSelected = 0;
    $objPHPExcel->setActiveSheetIndex($sheetSelected);
    $rowCount    = $objPHPExcel->getActiveSheet()->getHighestRow(); //获取表格行数
    $columnCount = $objPHPExcel->getActiveSheet()->getHighestColumn();//获取表格列数
    $dataArr     = array();
    #数据最后都存储到了$dataArr中

    $cols     = range("A", "Z");
    $all_cols = $cols;
    foreach (range("A", "B") as $i_col) {
        foreach ($cols as $j_col) {
            $all_cols[] = "{$i_col}{$j_col}";
        }
    }
    for ($row = 1; $row <= $rowCount; $row++) {
        #列数循环 , 列数是以A列开始 getFormattedValue
        foreach ($all_cols as $column) {
            $dataArr[$row][] = (string)$objPHPExcel->getActiveSheet()->getCell($column . $row)->getValue();
            if ($column == $columnCount) {
                break;
            }
        }
        if (implode($dataArr[$row], '') == '') {#整行数据为空 则删除这行数据
            unset($dataArr[$row]);
        }
    }
    return [
        'error_code' => 0,
        'error_msg'  => '',
        'data'       => array_values($dataArr)
    ];
}

/**
 * @方法描述:获取代发仓库编码
 * <AUTHOR>
 * @Date 2023/12/28
 * @return array
 */
function GetDfWarehouseCode($field = 'erp_id')
{
    return Db::table('vh_commodities.vh_virtual_warehouse')
        ->where('is_supplier_delivery', 1)
        ->column($field);
}

/**
 * @方法描述:重写查找子字符串在字符串中第一次出现的位置
 * <AUTHOR>
 * @param array $keyword 字符串
 * @param array $key 关键字
 * @return int|false
 */
function strpos_str($keyword, $key)
{
    return strpos(strval($keyword), strval($key));
}

function getPackages($package_ids, $field = "*")
{
    $packages = \app\service\es\Es::name(\app\service\es\Es::PERIODS_PACKAGE)->where([['id', 'in', $package_ids]])->field($field)->select()->toArray();

    foreach ($packages as &$pkg_info) {
        if (!empty($pkg_info['associated_products'])) {
            $pkg_info['associated_products'] = json_decode($pkg_info['associated_products'], true);
        }
    }
    $packages = array_column($packages, null, 'id');

    return $packages;
}

function getProducts($product_ids, $field = "*", $key_field = 'id')
{
    $wiki_products = \app\service\es\Es::name(\app\service\es\Es::PRODUCTS)->where([['id', 'in', $product_ids]])->field($field)->select()->toArray();
    $wiki_products = array_column($wiki_products, null, $key_field);
    return $wiki_products;
}

function exportSheelExcels2($datas, $options = [], $fileName = '', $format = 'Xls')
{
    set_time_limit(0);
    //初始化
    $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
    $cellNum     = count($options);
    $filename    = $fileName;
    /* 设置Excel Sheet */
    $spreadsheet->setActiveSheetIndex(0);
    $cellName = [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z',
        'AA',
        'AB',
        'AC',
        'AD',
        'AE',
        'AF',
        'AG',
        'AH',
        'AI',
        'AJ',
        'AK',
        'AL',
        'AM',
        'AN',
        'AO',
        'AP',
        'AQ',
        'AR',
        'AS',
        'AT',
        'AU',
        'AV',
        'AW',
        'AX',
        'AY',
        'AZ'
    ];
    $color    = [
        'Black'   => 'FF000000',
        'White'   => 'FFFFFFFF',
        'Red'     => 'FFFF0000',
        'Red1'    => 'FF800000',//COLOR_DARKRED
        'Green'   => 'FF00FF00',
        'Green1'  => 'FF008000',//COLOR_DARKGREEN
        'Blue'    => 'FF0000FF',
        'Blue1'   => 'FF000080',//COLOR_DARKBLUE
        'Yellow'  => 'FFFFFF00',
        'Yellow1' => 'FF808000',//COLOR_DARKYELLOW
        'Yellow2' => 'CC7832',//COLOR_DARKYELLOW
    ];
    // 设置下拉选项列表
    $dataValidation = $spreadsheet->getActiveSheet()->getCell('D2')->getDataValidation();
    $dataValidation->setType('list');
    $dataValidation->setErrorStyle('stop');
    $dataValidation->setShowDropDown(true);
    $dataValidation->setFormula1('"顺丰快递,顺丰冷链,京东快递（不保价）,京东快递（保价）,京东快运,京东TC,韵达快递,圆通速递,联邦快递,中通快递,申通快递,EMS,德邦快递,同城快寄,跨越速运,极兔快递,壹米滴答"'); // 下拉选项的列表
    //设置excel第2行数据
    foreach ($options as $key => $val) {
        $column = $cellName[$key] . '1';
        //设置表头
        $spreadsheet->setActiveSheetIndex(0)
            ->setCellValue($column, $val['name']);
        //设置列宽
        if (isset($val['width']) && !empty($val['width'])) {
            $spreadsheet->getActiveSheet()->getColumnDimension($cellName[$key])->setWidth($val['width']);
        } else {
            $spreadsheet->getActiveSheet()->getDefaultColumnDimension()->setWidth(15);//设置默认列宽为
        }
        //设置字体 粗体
        $spreadsheet->getActiveSheet()->getStyle($column)->getFont()->setBold(true);
        //设置行高
        if (!empty($val['height'])) {
            $spreadsheet->getActiveSheet()->getRowDimension($column)->setRowHeight($val['height']);
            //设置默认行高 $spreadsheet->getActiveSheet()->getDefaultRowDimension()->setRowHeight(15);
        }
        //设置颜色
        if (!empty($val['color']) && isset($color[$val['color']])) {
            $spreadsheet->getActiveSheet()->getStyle($column)->getFont()->getColor()->setARGB($color[$val['color']]);
        } else {
            $spreadsheet->getActiveSheet()->getStyle($column)->getFont()->getColor()->setARGB('FF000000');
        }
    }
    $yieldData = yieldData($datas);
    $i         = 0;
    foreach ($yieldData as $val) {
        for ($j = 0; $j < $cellNum; $j++) {
            //$spreadsheet->setActiveSheetIndex(0)->setCellValue($cellName[$j].($i+3),' '.$val[$options[$j]['column']].' ');

            //数据类型
            $dataType = isset($options[$j]['dataType']) ? $options[$j]['dataType'] : 's';
            $rgb      = $color[($options[$j]['back_color'] ?? 'White')];
            switch ($dataType) {
                case 'n'://数字
                    $spreadsheet->setActiveSheetIndex(0)->setCellValueExplicit($cellName[$j] . ($i + 2), $val[$options[$j]['column']], $dataType);
                    break;
                case 'str2num':
                    $spreadsheet->setActiveSheetIndex(0)->setCellValueExplicit($cellName[$j] . ($i + 2), $val[$options[$j]['column']], $dataType);
                    break;
                case 'str':
                    $spreadsheet->setActiveSheetIndex(0)->setCellValueExplicit($cellName[$j] . ($i + 2), $val[$options[$j]['column']], $dataType);
                    break;
                case 's':
                    $spreadsheet->setActiveSheetIndex(0)->setCellValueExplicit($cellName[$j] . ($i + 2), $val[$options[$j]['column']], $dataType);
                    break;
                case 'inlineStr':
                    $spreadsheet->setActiveSheetIndex(0)->setCellValueExplicit($cellName[$j] . ($i + 2), $val[$options[$j]['column']], $dataType);
                    break;
                case 'null':
                    $spreadsheet->setActiveSheetIndex(0)->setCellValueExplicit($cellName[$j] . ($i + 2), $val[$options[$j]['column']], $dataType);
                    break;
                case 'f':
                    $spreadsheet->setActiveSheetIndex(0)->setCellValueExplicit($cellName[$j] . ($i + 2), $val[$options[$j]['column']], $dataType);
                    break;
                default:
                    $spreadsheet->setActiveSheetIndex(0)->setCellValueExplicit($cellName[$j] . ($i + 2), $val[$options[$j]['column']], $dataType);
                    break;
            }
//            $spreadsheet->getActiveSheet()->getStyle($cellName[$j] . ($i + 2))->getFont()->getColor()->setARGB($rgb);
            $spreadsheet->getActiveSheet()->getStyle($cellName[$j] . ($i + 2))->applyFromArray([
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => [
                        'rgb' => $rgb, // 红色背景，格式为 RRGGBB
                    ]
                ]
            ]);
        }
        $i++;
    }
    header('pragma:public');
    header('Content-Type: application/vnd.ms-excel');
    $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, $format);
    $rootPath  = app()->getRootPath();

    $savePath   = '/exportExcels/' . $filename . '.' . strtolower($format);
    $resavePath = 'exportExcels/' . $filename . '.' . strtolower($format);
    $a          = $rootPath . 'public' . $savePath;
    if (!is_dir($rootPath . 'public' . '/exportExcels/')) {
        $res = mkdir($rootPath . 'public' . '/exportExcels/', 0755, true);
    }

    $objWriter->save($a);
    /* 释放内存 */
    $spreadsheet->disconnectWorksheets();
    unset($spreadsheet);
    @ob_end_flush();
    return $resavePath;
}

function generateTaskNo($prefix = "GD")
{
    $task_no = $prefix . substr(date('Ymd'), 2) . substr(time(), -5) . substr(microtime(), 2, 5) . str_pad(rand(0, 99), 2, '0', STR_PAD_LEFT);

    return $task_no;
}


/**
 * 根据身份证获取年龄
 *
 * @param string $idcard 身份证号
 * @param bool $isExact true：周岁，false：虚岁
 * @return void
 */
function get_age_by_id_card($idcard, $isExact = true)
{
    if (empty($idcard)) return null;
    // 从身份证中获取出生日期
    $birthDate = strtotime(substr($idcard, 6, 8)); //截取日期并转为时间戳

    // 格式化[出生日期]
    $year  = date('Y', $birthDate); //yyyy
    $month = date('m', $birthDate); //mm
    $day   = date('d', $birthDate); //dd

    // 格式化[当前日期]
    $currentYear  = date('Y'); //yyyy
    $currentMonth = date('m'); //mm
    $currentDay   = date('d'); //dd

    // 计算年龄()
    $age = $currentYear - $year; //今年减去生日年
    if ($isExact) {
        // 如果出生月大于当前月或出生月等于当前月但出生日大于当前日则减一岁
        if ($month > $currentMonth || ($month == $currentMonth && $day > $currentDay)) { //深层判断(日)
            $age--;
        }
    }

    return $age;
}

if (!function_exists('image_full_path')) {
    /**
     * @方法描述: 单图返回全路径
     * <AUTHOR>
     * @Date 2022/12/8 12:41
     * @param $value
     * @return string
     */
    function image_full_path($value)
    {
        if (!$value) return '';
        $host = env('ALIURL');
        if (empty($host)) return $value;
        return (false === strpos($value, $host)) ? $host . $value : $value;
    }
}

/**
 * @方法描述: 更新代发期数发货时间
 * <AUTHOR>
 * @Date 2025/1/22
 * @return bool
 */
function updateDfDeliveryTime()
{
    $url  = env('ITEM.TIMEING_SERVICE_URL') . '/services/v3/timing/add';
    $body = json_encode([
        'namespace' => 'tp6-orders',
        'key'       => 'updateDfDeliveryTime',
        'data'      => '',
        'callback'  => env('ITEM.COMMODITIES_URL') . '/commodities/v3/periods/updateDfDeliveryTime',
        'timeout'   => '5s',
    ]);
    return curlRequest($url, $body);
}

/**
 * @方法描述:推送到队列
 * <AUTHOR>
 * @Date 2025/02/20
 * @param string $exchange_name
 * @param string $routing_key
 * @param array $param
 * @param int $data_type 数据类型：1:需要转base64 2:直接使用
 * @return string
 */
function pushQueue($exchange_name, $routing_key, $param, $data_type = 1)
{
    $data = [
        'exchange_name' => $exchange_name,
        'routing_key' => $routing_key,
        'data' => base64_encode(json_encode($param)),
    ];
    if ($data_type == 2) {
        $data['data'] = $param;
    }
    $res = curlRequest(env('ITEM.QUEUE_URL'), json_encode($data));

    return $res;
}