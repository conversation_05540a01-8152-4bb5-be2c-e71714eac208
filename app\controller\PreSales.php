<?php


namespace app\controller;


use app\BaseController;
use app\ErrorCode;
use app\Request;
use app\validate\ListPagination;
use app\service\PreSales as PreSalesService;
use think\facade\Validate;

class PreSales extends BaseController
{
    /**
     * Description:售前客户列表
     * Author: zrc
     * Date: 2023/7/26
     * Time: 17:15
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function adminList(Request $request)
    {
        $params   = $request->param();
        $validate = new ListPagination();
        if (!$validate->goCheck($params)) {//验证参数
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $PreSalesService = new PreSalesService();
        $result          = $PreSalesService->adminList($params);
        return $this->success($result);
    }

    /**
     * Description:添加/修改售前客户
     * Author: zrc
     * Date: 2023/7/27
     * Time: 9:13
     * @param Request $request
     * @return \think\response\Json
     */
    public function addUpdateAdmin(Request $request)
    {
        $params = $request->param();
        if (empty($params['id'])) {//添加
            //数据验证
            $validate = Validate::rule([
                'admin_id|后台用户ID' => 'require|number',
                'realname|真实姓名'   => 'require',
                'userid|企微urerid' => 'require'
            ]);
            if (!$validate->check($params)) {
                $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
            }
        }
        $PreSalesService = new PreSalesService();
        $result          = $PreSalesService->addUpdateAdmin($params);
        return $this->success($result);
    }

    /**
     * Description:售前数据统计
     * Author: zrc
     * Date: 2023/7/28
     * Time: 10:26
     * @param Request $request
     * @return \think\response\Json
     */
    public function preSalesDataStats(Request $request)
    {
        $params          = $request->param();
        $PreSalesService = new PreSalesService();
        $result          = $PreSalesService->preSalesDataStats($params);
        return $this->success(['list' => $result]);
    }

    /**
     * Description:记录售前分享标识
     * Author: zrc
     * Date: 2023/8/23
     * Time: 9:46
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function recordPreSalesShare(Request $request)
    {
        $params        = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        //数据验证
        $validate = Validate::rule([
            'uid|用户ID'           => 'require|number',
            'period|期数'          => 'require|number',
            'source_user|售前客服ID' => 'require|number'
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $PreSalesService = new PreSalesService();
        $result          = $PreSalesService->recordPreSalesShare($params);
        return $this->success($result);
    }

    /**
     * Description:点击数值显示统计具体信息
     * Author: zrc
     * Date: 2023/10/19
     * Time: 9:54
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function clickNumsGetInfo(Request $request)
    {
        $params = $request->param();
        //数据验证
        $validate = Validate::rule([
            'admin_id|售前客服ID' => 'require|number',
            'type|点击类型'       => 'require',
            'page|页码'    => 'require|number|>:0',
            'limit|每页条数' => 'require|number|>:0'
        ]);
        if (!in_array($params['type'], ['new_transform', 'new_first', 'new_second', 'new_third', 'silent_transform', 'silent_first', 'silent_second', 'silent_third'])) {
            $this->throwError('点击类型异常');
        }
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $PreSalesService = new PreSalesService();
        $result          = $PreSalesService->clickNumsGetInfo($params);
        return $this->success($result);
    }
}