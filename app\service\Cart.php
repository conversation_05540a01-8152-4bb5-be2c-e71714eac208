<?php


namespace app\service;


use app\BaseService;
use app\ElasticSearchConnection;
use app\ErrorCode;
use app\service\elasticsearch\ElasticSearchService;
use think\facade\Db;

class Cart extends BaseService
{
    /**
     * Description:添加到购物车
     * Author: zrc
     * Date: 2021/8/16
     * Time: 16:32
     * @param $requestparams
     */
    public function addShoppingCart($requestparams)
    {
        $params = $requestparams;
        //购物车限制条数
        $cartNums = Db::name('shopping_cart')->where([
            'uid' => $params['uid'],
            'activity_id' => $params['activity_id']
        ])->count();
        if ($cartNums >= 99) $this->throwError('您的购物车已超过99条数据，请先清理', 20109);
        $where = array(
            'uid'          => $params['uid'],
            'period'       => $params['period'],
            'package_id'   => $params['package_id'],
            'periods_type' => $params['periods_type'],
            'activity_id'  => $params['activity_id']
        );
        if (in_array($params['periods_type'], [0, 1])) {
            //查询可用库存进行验证
            $goodsInfo[]  = array(
                'period'       => $params['period'],
                'package_id'   => $params['package_id'],
                'periods_type' => $params['periods_type'],
            );
            $getInventory = $this->httpPost(env('ITEM.COMMODITIES_URL') . '/commodities/v3/package/getPeriodsPackageProductInventory', $goodsInfo);
            if ($getInventory['error_code'] == 0 && count($getInventory['data']) > 0) {
                $available_stock = 0;
                foreach ($goodsInfo as &$value) {
                    if (isset($getInventory['data'][$value['package_id']])) {
                        $inventory       = $getInventory['data'][$value['package_id']]['inventory'];
                        $unlimited       = $getInventory['data'][$value['package_id']]['unlimited'];
                        $package_product = $getInventory['data'][$value['package_id']]['package_product'];
                        $is_mystery_box  = $getInventory['data'][$value['package_id']]['is_mystery_box'];
                        $max_purchase    = $getInventory['data'][$value['package_id']]['limit_number'] - $getInventory['data'][$value['package_id']]['purchased'];
                        if ($max_purchase < 0) $max_purchase = 0;
                        if ($unlimited == 0) {
                            $available_stock = $inventory < $max_purchase ? $inventory : $max_purchase;
                        }
                        if ($is_mystery_box == 1) {
                            foreach ($package_product as $ks => $tt) {
                                $product_num = intval(max($tt['inventory']) / $tt['nums']);
                                if ($product_num > $available_stock) $available_stock = $product_num < $max_purchase ? $product_num : $max_purchase;
                            }
                        } else {
                            foreach ($package_product as $ks => $tt) {
                                $product_num = intval($tt['inventory'] / $tt['nums']);
                                if ($ks == 0) $available_stock = $product_num < $max_purchase ? $product_num : $max_purchase;
                                if ($product_num < $available_stock) $available_stock = $product_num < $max_purchase ? $product_num : $max_purchase;
                            }
                        }
                        if ($available_stock < $params['nums']) $this->throwError('商品库存不足!');
                        if ($available_stock == 0) {
                            Db::name('shopping_cart')->where($where)->update(['status' => 1]);
                            $value['status'] = 1;
                        }
                    }
                }
            }
        } else {
            //获取商家秒发商品店铺最大库存数
            $goodsInfo[]  = array(
                'period'     => $params['period'],
                'package_id' => $params['package_id'],
            );
            $getInventory = $this->httpPost(env('ITEM.VMALL_URL') . '/vmall/v3/common/getperiodshopstock', ['items_info' => $goodsInfo]);
            if (!isset($getInventory['error_code']) || $getInventory['error_code'] != 0) $this->throwError('获取商品库存失败');
            $available_stock = isset($getInventory['data'][0]['periodstock']) ? $getInventory['data'][0]['periodstock'] : 0;
            if ($available_stock < $params['nums']) $this->throwError('商品库存不足!');
        }
        $data = Db::name('shopping_cart')->where($where)->find();
        if ($data) {
            if ($data['status'] == 1) {
                $updateData = array(
                    'nums'        => $params['nums'],
                    'status'      => 0,
                    'update_time' => time()
                );
            } else {
                $newNums = $params['nums'] + $data['nums'];
                if (in_array($params['periods_type'], [0, 1])) {
                    $updateData = array(
                        'nums'        => $newNums > $available_stock ? $available_stock : $newNums,
                        'update_time' => time()
                    );
                } else {
                    $updateData = array(
                        'nums'        => $newNums,
                        'update_time' => time()
                    );
                }
            }
            $updateData['is_original_package'] = isset($params['is_original_package']) ? $params['is_original_package'] : 0;
            $result                            = Db::name('shopping_cart')->where($where)->update($updateData);
        } else {
            //原箱处理
            $is_original_package = isset($params['is_original_package']) ? $params['is_original_package'] : 0;
            if (!isset($params['is_original_package'])) {
                $es                  = new ElasticSearchService();
                $arr                 = array(
                    'index'  => ['periods_set'],
                    'match'  => [['id' => $params['package_id']]],
                    'source' => ['is_original_package'],
                    'limit'  => 1
                );
                $data                = $es->getDocumentList($arr);
                $is_original_package = isset($data['data'][0]['is_original_package']) ? $data['data'][0]['is_original_package'] : 0;
            }
            $insertData = array(
                'uid'                 => $params['uid'],
                'period'              => $params['period'],
                'package_id'          => $params['package_id'],
                'periods_type'        => $params['periods_type'],
                'nums'                => $params['nums'],
                'status'              => 0,
                'is_original_package' => $is_original_package,
                'activity_id'         => $params['activity_id'],
                'created_time'        => time()
            );
            $result     = Db::name('shopping_cart')->insert($insertData);
        }
        //秒发商品反馈
        if ($params['periods_type'] == 1) {
            $feedback = array(
                'items' => [[
                    'genre'         => 'second_goods',
                    'feedback_type' => 'cart',
                    'item_id'       => $params['period'],
                ]]
            );
            curlRequest(env('ITEM.COMMODITIES_URL') . '/commodities/v3/userPortrait/batchFeedback', json_encode($feedback, true), ['vinehoo-uid:' . $params['uid']], 'POST');
        }
        if ($result) return [];
        $this->throwError('添加失败，请重试');
    }

    /**
     * Description:购物车列表
     * Author: zrc
     * Date: 2021/8/17
     * Time: 10:06
     * @param $requestparams
     */
    public function shoppingCartList($requestparams)
    {
        $data = Db::name('shopping_cart')
            ->where([
                'uid' => $requestparams['uid'],
                'activity_id' => $requestparams['activity_id'] 
            ])
            ->order('id desc')
            ->select()
            ->toArray();
        $list = array('count' => 0, 'activity' => [], 'nomal' => [], 'local' => [], 'invalid' => [], 'full_discount' => []);
        if (count($data) > 0) {
            $goodsInfo  = [];
            $goodsInfos = [];
            //商品、套餐es数据查询
            $goodsIds     = array_unique(array_column($data, 'period'));
            $goodsWhere[] = ['terms' => ['id' => array_values($goodsIds)]];
            $goodsData    = esGetList('vinehoo.periods', $goodsWhere, [], 0, 1000);
            $goodsArr     = [];
            if (isset($goodsData['hits']['hits'])) {
                foreach ($goodsData['hits']['hits'] as &$gv) {
                    $goodsArr[$gv['_source']['id']] = $gv['_source'];
                }
            }
            $packageIds     = array_unique(array_column($data, 'package_id'));
            $packageWhere[] = ['terms' => ['id' => array_values($packageIds)]];
            $packageData    = esGetList('vinehoo.periods_set', $packageWhere, [], 0, 1000);
            $packageArr     = [];
            if (isset($packageData['hits']['hits'])) {
                foreach ($packageData['hits']['hits'] as &$pv) {
                    $packageArr[$pv['_source']['id']] = $pv['_source'];
                }
            }
            foreach ($data as $key => $val) {
                //未获取到es数据、套餐被隐藏，删除购物车数据处理
                if (!isset($goodsArr[$val['period']]) || !isset($packageArr[$val['package_id']]) || $packageArr[$val['package_id']]['is_hidden'] == 1) {
                    Db::name('shopping_cart')->where(['id' => $val['id']])->delete();
                    unset($data[$key]);
                    continue;
                }
                $goodsData      = $goodsArr[$val['period']];
                $warehouse_code = '';
                if ($val['periods_type'] == 0 && $goodsData['is_supplier_delivery'] == 1) $warehouse_code = '034';
                $packageData                         = $packageArr[$val['package_id']];
                $data[$key]['title']                 = $goodsData['title'];
                $data[$key]['banner_img']            = imagePrefix($goodsData['banner_img']);
                $data[$key]['predict_shipment_time'] = date('Y-m-d', strtotime(predictTimeDeal(strtotime($goodsData['predict_shipment_time']), $val['periods_type'], $warehouse_code)));
                $data[$key]['package_name']          = $packageData['package_name'];
                $data[$key]['price']                 = $packageData['price'];
                $data[$key]['total_price']           = $packageData['price'] * $val['nums'];
                $data[$key]['goods_is_ts']           = $goodsData['is_support_ts'];
                $data[$key]['is_cold_chain']         = $goodsData['is_cold_chain'];
                $data[$key]['is_support_reduction']  = $goodsData['is_support_reduction'];
                $data[$key]['is_hidden_price']       = $goodsData['is_hidden_price'];
                if ($val['status'] == 0 && $goodsData['onsale_status'] != 2) {
                    Db::name('shopping_cart')->where(['id' => $val['id']])->update(['status' => 1]);
                    $data[$key]['status'] = 1;
                }
                if ($val['status'] == 0 && in_array($val['periods_type'], [0, 1])) {
                    $goodsInfo[] = array(
                        'period'       => $val['period'],
                        'package_id'   => $val['package_id'],
                        'periods_type' => $val['periods_type'],
                    );
                }
                if ($val['status'] == 0 && $val['periods_type'] == 9) {
                    $goodsInfos[] = array(
                        'period'     => $val['period'],
                        'package_id' => $val['package_id'],
                    );
                }
                $data[$key]['available_stock'] = 0;
            }
            if (count($goodsInfos) > 0) {
                //获取商家秒发商品店铺最大库存数
                $getInventory = $this->httpPost(env('ITEM.VMALL_URL') . '/vmall/v3/common/getperiodshopstock', ['items_info' => $goodsInfos]);
                if (isset($getInventory['error_code']) || $getInventory['error_code'] == 0) {
                    foreach ($data as $kee => $vaa) {
                        foreach ($getInventory['data'] as &$vav) {
                            if ($vav['package_id'] == $vaa['package_id']) {
                                $data[$kee]['available_stock'] = $vav['periodstock'];
                            }
                        }
                    }
                }
            }
            if (count($goodsInfo) > 0) {
                //酒云商品可用库存查询
                $getInventory = $this->httpPost(env('ITEM.COMMODITIES_URL') . '/commodities/v3/package/getPeriodsPackageProductInventory', $goodsInfo);
                if ($getInventory['error_code'] == 0 && count($getInventory['data']) > 0) {
                    foreach ($data as &$value) {
                        if (isset($getInventory['data'][$value['package_id']])) {
                            $inventory       = $getInventory['data'][$value['package_id']]['inventory'];
                            $unlimited       = $getInventory['data'][$value['package_id']]['unlimited'];
                            $package_product = $getInventory['data'][$value['package_id']]['package_product'];
                            $is_mystery_box  = $getInventory['data'][$value['package_id']]['is_mystery_box'];
                            $max_purchase    = $getInventory['data'][$value['package_id']]['limit_number'] - $getInventory['data'][$value['package_id']]['purchased'];
                            if ($max_purchase < 0) $max_purchase = 0;
                            if ($unlimited == 0) {
                                $value['available_stock'] = $inventory < $max_purchase ? $inventory : $max_purchase;
                            } else {
                                if ($is_mystery_box == 1) {
                                    foreach ($package_product as $ks => $tt) {
                                        $product_num = intval(max($tt['inventory']) / $tt['nums']);
                                        if ($product_num > $value['available_stock']) $value['available_stock'] = $product_num < $max_purchase ? $product_num : $max_purchase;
                                    }
                                } else {
                                    foreach ($package_product as $ks => $tt) {
                                        $product_num = intval($tt['inventory'] / $tt['nums']);
                                        if ($ks == 0) $value['available_stock'] = $product_num < $max_purchase ? $product_num : $max_purchase;
                                        if ($product_num < $value['available_stock']) $value['available_stock'] = $product_num < $max_purchase ? $product_num : $max_purchase;
                                    }
                                }
                            }
                            if ($value['available_stock'] == 0) {
                                Db::name('shopping_cart')->where(['id' => $value['id']])->update(['status' => 1]);
                                $value['status'] = 1;
                            }
                        }
                    }
                }
            }
            //购物车数据组装处理
            if (count($data) > 0) {
                $noInvalid = [];
                foreach ($data as &$v) {
                    if ($v['status'] == 1) {
                        $list['invalid'][] = $v;
                    } else {
                        if ($v['periods_type'] == 9) {
                            $list['local'][] = $v;
                        } else {
                            $noInvalid[] = $v;
                        }
                    }
                }
                if (count($noInvalid) > 0) {
                    // 活动购物车不查询专题活动信息
                    if (empty($requestparams['activity_id'])) {
                        $goods_ids = implode(',', array_unique(array_column($noInvalid, 'period')));
                        //获取专题活动信息
                        $getGoodsByPeriods = $this->httpGet(env('ITEM.ACTIVITIES_MANAGEMENT_URL') . '/activity/v3/goods/getGoodsByPeriods', ['goods_ids' => $goods_ids]);
                    }

                    if (!empty($getGoodsByPeriods) && (isset($getGoodsByPeriods['error_code']) || $getGoodsByPeriods['error_code'] == 0)) {
                        //专题活动商品处理
                        if (isset($getGoodsByPeriods['data']['activity']) && count($getGoodsByPeriods['data']['activity']) > 0) {
                            foreach ($getGoodsByPeriods['data']['activity'] as &$val) {
                                $goods_info = array();
                                foreach ($noInvalid as &$vv) {
                                    if (in_array($vv['period'], $val['goods_info'])) {
                                        $goods_info[] = $vv;
                                    }
                                }
                                $reduction = [];
                                $fullgift  = [];
                                $body = ['activity_range' => '4'];
                                $activies  = $this->httpGet(env('ITEM.FULLGIFT_URL') . '/fullgift/v3/activity/get', $body);
                                if (isset($activies['error_code']) && $activies['error_code'] == 0 && count($activies['data']['list']) > 0) {
                                    foreach ($activies['data']['list'] as $va) {
                                        if (in_array($val['id'], explode(',', $va['special_topic_ids']))) {
                                            if ($va['type'] == 1) $reduction = json_decode($va['activity_rule']);
                                            //满赠数据暂时都不返回，因为无法确定赠品库存-lf
                                            //if ($va['type'] == 2) $fullgift = json_decode($va['activity_rule']);
                                        }
                                    }
                                }
                                $list['activity'][] = array(
                                    'activity_id'   => $val['id'],
                                    'activity_name' => $val['activity_name'],
                                    'activity_url'  => $val['activity_url'],
                                    'reduction'     => $reduction,
                                    'fullgift'      => $fullgift,
                                    'goods_info'    => $goods_info
                                );
                            }
                        }
                        //普通商品处理
                        if (isset($getGoodsByPeriods['data']['nomal']) && count($getGoodsByPeriods['data']['nomal']) > 0) {
                            $list['nomal'] = [];
                            foreach ($data as $vvv) {
                                if (in_array($vvv['period'], $getGoodsByPeriods['data']['nomal'])) {
                                    $list['nomal'][] = $vvv;
                                }
                            }
                        }
                    } else {
                        $list['invalid'] = $list['local'] = [];
                        foreach ($data as &$va) {
                            if ($va['status'] == 1) {
                                $list['invalid'][] = $va;
                            } else {
                                if ($va['periods_type'] == 9) {
                                    $list['local'][] = $va;
                                } else {
                                    $list['nomal'][] = $va;
                                }
                            }
                        }
                    }
                    $body = ['activity_range' => '1,2,3'];
                    if (!empty($requestparams['activity_id'])) {
                        $body = ['activity_range' => '4', 'activity_id' => intval($requestparams['activity_id'])];
                    }
                    //获取满减活动信息（非指定活动）
                    $activity = $this->httpGet(env('ITEM.FULLGIFT_URL') . '/fullgift/v3/activity/get', $body);
                    if (isset($activity['error_code']) && $activity['error_code'] == 0 && count($activity['data']['list']) > 0) {
                        foreach ($activity['data']['list'] as &$val) {
                            $rules       = json_decode($val['activity_rule'], true);
                            $val['fill'] = $rules[0]['fill'];
                            if ($val['type'] == 1) {
                                $val['decrease']         = $rules[0]['decrease'];
                                $list['full_discount'][] = array(
                                    'name'           => $val['name'],
                                    'activity_range' => $val['activity_range'],
                                    'information'    => '满' . $val['fill'] . '元减' . $val['decrease'] . '元'
                                );
                            }
                            //满赠数据暂时都不返回，因为无法确定赠品库存-lf
//                            if ($val['type'] == 2) {
//                                $val['goods_name'] = $rules[0]['goods_name'];
//                                $list['full_discount'][] = array(
//                                    'name'           => $val['name'],
//                                    'activity_range' => $val['activity_range'],
//                                    'information'    => '满' . $val['fill'] . '元赠送' . $val['goods_name']
//                                );
//                            }
                        }
                    }
                }
            }
        }
        $list['count'] = Db::name('shopping_cart')->where(['uid' => $requestparams['uid'], 'status' => 0, 'activity_id' => $requestparams['activity_id']])->sum('nums');
        return $list;
    }

    /**
     * Description:购物车商品数量变更
     * Author: zrc
     * Date: 2021/8/17
     * Time: 12:00
     * @param $requestparams
     */
    public function changeNums($requestparams)
    {
        $params   = $requestparams;
        $cartData = Db::name('shopping_cart')->field('status,package_id')->where(['id' => $params['id']])->find();
        if (empty($cartData) || $cartData['status'] == 1) {
            $this->throwError('购物车商品不存在或已失效');
        }
        $updateData = array(
            'nums'        => $params['nums'],
            'update_time' => time()
        );
        $result     = Db::name('shopping_cart')->where(['id' => $params['id']])->update($updateData);
        return $result;
    }

    /**
     * Description:购物车商品删除
     * Author: zrc
     * Date: 2021/8/17
     * Time: 14:01
     * @param $requestparams
     */
    public function delete($requestparams)
    {
        $idArr = explode(',', $requestparams['ids']);
        foreach ($idArr as &$val) {
            $result = Db::name('shopping_cart')->where(['id' => $val, 'uid' => $requestparams['uid']])->delete();
        }
        return [];
    }

    /**
     * Description:购物车商品计数
     * Author: zrc
     * Date: 2021/8/17
     * Time: 14:13
     * @param $requestparams
     */
    public function count($requestparams)
    {
        $where = [
            'uid' => $requestparams['uid'], 
            'status' => 0,
            'activity_id' => $requestparams['activity_id'] ?? 0
        ];
        $count = Db::name('shopping_cart')->where($where)->sum('nums');
        return $count;
    }

    /**
     * Description:购物车商品勾选金额计算
     * Author: zrc
     * Date: 2021/12/15
     * Time: 11:03
     * @param $requestparams
     * @return array
     * @throws \Exception
     */
    public function calculateGoodsMoney($requestparams)
    {
        $params             = $requestparams;
        $total_money        = '0';
        $total_money_fs     = '0';
        $total_money_flash  = '0';
        $total_money_second = '0';
        $items              = [];
        //无items_info默认购物车所有有效商品
        if (empty($params['items_info'])) {
            $params['items_info'] = Db::name('shopping_cart')
                ->field('period,package_id,nums')
                ->where([
                    'uid' => $params['uid'],
                    'status' => 0,
                    'activity_id' => $requestparams['activity_id'],
                ])
                ->select()->toArray();
            if (empty($params['items_info'])) {
                $result = array(
                    'total_money'     => round(0, 2),
                    'money_off_value' => round(0, 2),
                    'full_discount'   => [],
                );
                return $result;
            }
        }
        foreach ($params['items_info'] as &$v) {
            //套餐ID获取商品套餐信息
            $packageInfo = esGetOne($v['package_id'], 'vinehoo.periods_set');
            $periodInfo  = esGetOne($v['period'], 'vinehoo.periods');
            if (empty($packageInfo) || empty($periodInfo)) $this->throwError('未获取到产品信息');
            $v['goods_money'] = $packageInfo['price'] * $v['nums'];
            if ($packageInfo['periods_type'] == 0) {//产品类别：0-闪购 1-秒发
                $channel = 'flash';
            } else {
                $channel = 'seconds';
            }
            if (isset($periodInfo['is_support_reduction']) && $periodInfo['is_support_reduction'] == 1) {
                if ($packageInfo['periods_type'] == 9 && isset($periodInfo['join_period_id'])) {
                    $v['period'] = $periodInfo['join_period_id'] > 0 ? $periodInfo['join_period_id'] : $v['period'];
                }
                $items[]        = array(
                    'period_id' => intval($v['period']),
                    'set_id'    => intval($v['package_id']),
                    'price'     => floatval($packageInfo['price']),
                    'nums'      => intval($v['nums']),
                    'channel'   => $channel
                );
                $total_money_fs = bcadd($total_money_fs, strval($v['goods_money']), 2);//商品支持满减总价格
                if ($packageInfo['periods_type'] == 0) {//闪购商品支持满减总价格
                    $total_money_flash = bcadd($total_money_flash, strval($v['goods_money']), 2);
                } else {//秒发商品支持满减总价格
                    $total_money_second = bcadd($total_money_second, strval($v['goods_money']), 2);
                }
            }
            $total_money = bcadd($total_money, strval($v['goods_money']), 2);//商品价格
        }
        /**满减金额计算 start**/
        $money_off = httpPostString(env('ITEM.CALC-ORDERS_PRICE') . '/ordersPrice/v3/order/fullReduction', json_encode(['items' => $items, 'activity_id' => intval($requestparams['activity_id'])]));
        if (empty($money_off)) $this->throwError('未获取到满减金额');
        if ($money_off['error_code'] != 0) $this->throwError($money_off['error_msg']);
        $money_off_value = 0;
        if (count($money_off['data']['discount']) > 0) {
            foreach ($money_off['data']['discount'] as &$val) {
                if ($val['discount'] > $money_off_value) $money_off_value = $val['discount'];
            }
        }
        $full_discount = [];
        $body = ['activity_range' => '1,2,3'];
        if (!empty($requestparams['activity_id'])) {
            $body = ['activity_range' => '4', 'activity_id' => intval($requestparams['activity_id'])];
        }
        //获取满减满赠活动信息（非指定活动）
        $activies = $this->httpGet(env('ITEM.FULLGIFT_URL') . '/fullgift/v3/activity/get', $body);
        if ($activies['error_code'] == 0 && count($activies['data']['list']) > 0) {
            foreach ($activies['data']['list'] as &$val) {
                if ($val['type'] == 1) {
                    if ($val['activity_range'] == 1 || $val['activity_range'] == 4) {
                        $total_money_activies = floatval($total_money_fs);
                    }
                    if ($val['activity_range'] == 2) $total_money_activies = floatval($total_money_flash);
                    if ($val['activity_range'] == 3) $total_money_activies = floatval($total_money_second);
                    
                    $rules = json_decode($val['activity_rule'], true);
                    $str   = array_column($rules, 'fill');
                    $max   = max($str);
                    if ($total_money_activies >= $max) {
                        foreach ($rules as &$va) {
                            if ($va['fill'] == $max) {
                                if (isset($va['stacking_numb'])) {
                                    $integer   = intval(floor($total_money_activies / $va['fill']));
                                    // $remainder = round($total_money_activies * 10000 % ($va['fill'] * 100) / 100, 2);
                                    $remainder = round(fmod($total_money_activies, $va['fill']), 2);

                                    if ($va['stacking_numb'] == 0) {
                                        $information = '已减' . $va['decrease'] * $integer . '元（差' . round($va['fill'] - $remainder, 2) . '元再减' . $va['decrease'] . '元）';
                                    } else {
                                        if ($va['stacking_numb'] <= $integer) {
                                            $information = '已减' . $va['decrease'] * $va['stacking_numb'] . '元';
                                        } else {
                                            $information = '已减' . $va['decrease'] * $integer . '元（差' . round($va['fill'] - $remainder, 2) . '元再减' . $va['decrease'] . '元）';
                                        }
                                    }
                                } else {
                                    $information = '已减' . $va['decrease'] . '元';
                                }
                                $full_discount[] = array(
                                    'information'    => $information,
                                    'name'           => $val['name'],
                                    'activity_range' => $val['activity_range'],
                                );
                                break;
                            }
                        }
                    } else {
                        foreach ($rules as &$va) {
                            if ($total_money_activies < $va['fill']) {
                                $full_discount[] = array(
                                    'information'    => '满' . $va['fill'] . '元减' . $va['decrease'] . '元（差' . round($va['fill'] - $total_money_activies, 2) . '元减' . $va['decrease'] . '元）',
                                    'name'           => $val['name'],
                                    'activity_range' => $val['activity_range'],
                                );
                                break;
                            }
                        }
                    }
                }
            }
        }
        /**满减金额计算 end**/
        $result = array(
            'total_money'     => round(floatval($total_money), 2),
            'money_off_value' => round($money_off_value, 2),
            'full_discount'   => $full_discount,
        );
        return $result;
    }
}