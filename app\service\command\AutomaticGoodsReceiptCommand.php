<?php
declare (strict_types = 1);

namespace app\service\command;

use app\service\Order as OrderService;
use think\facade\Db;

class AutomaticGoodsReceiptCommand
{
    /**
     * Description:订单15天自动确认收货
     * Author: zrc
     * Date: 2022/7/27
     * Time: 16:31
     * @return bool
     */
    public function exec()
    {
        $dataStr     = [];
        $where       = [];
        $where[]     = ['sub_order_status', '=', 2];
        $where[]     = ['delivery_time', '>', 0];
        $where[]     = ['delivery_time', '<', time() - 7 * 86400];
        $flash_order = Db::name('flash_order')->field('sub_order_no')->where($where)->select()->toArray();
        foreach ($flash_order as &$v) {
            $dataStr[] = base64_encode(json_encode(['sub_order_no_str' => $v['sub_order_no'], 'operator' => 0]));
        }
        $second_order = Db::name('second_order')->field('sub_order_no')->where($where)->select()->toArray();
        foreach ($second_order as &$vv) {
            $dataStr[] = base64_encode(json_encode(['sub_order_no_str' => $vv['sub_order_no'], 'operator' => 0]));
        }
        $cross_order = Db::name('cross_order')->field('sub_order_no')->where($where)->select()->toArray();
        foreach ($cross_order as &$va) {
            $dataStr[] = base64_encode(json_encode(['sub_order_no_str' => $va['sub_order_no'], 'operator' => 0]));
        }
        $tail_order = Db::name('tail_order')->field('sub_order_no')->where($where)->select()->toArray();
        foreach ($tail_order as &$val) {
            $dataStr[] = base64_encode(json_encode(['sub_order_no_str' => $val['sub_order_no'], 'operator' => 0]));
        }
        $rabbit_order = Db::name('rabbit_order')->field('sub_order_no')->where($where)->select()->toArray();
        foreach ($rabbit_order as &$vl) {
            $dataStr[] = base64_encode(json_encode(['sub_order_no_str' => $vl['sub_order_no'], 'operator' => 0]));
        }
        $rabbit_order = Db::name('merchant_second_order')->field('sub_order_no')->where($where)->select()->toArray();
        foreach ($rabbit_order as &$ll) {
            $dataStr[] = base64_encode(json_encode(['sub_order_no_str' => $ll['sub_order_no'], 'operator' => 0]));
        }
        //推送处理队列
        if (!empty($dataStr)) {
            $pushData = array(
                'exchange_name' => 'orders',
                'routing_key'   => 'automatic_goods_receipt',
                'data'          => $dataStr,
            );
            httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
        }
        return true;
    }
}