<?php
declare (strict_types = 1);

namespace app\model;

use think\facade\Db;
use think\facade\Log;
use think\Model;

/**
 * @mixin \think\Model
 */
class Express extends Model
{
    protected $name = 'express';

    protected function getTableName()
    {
        return Db::name($this->name);
    }

    public function expressList($params, $page = 1, $limit = 10)
    {

        $offset = ($page - 1) * $limit;
        $where=[];
        if (isset($params['status']) && !empty($params['status'])) {
            $where[]=['status','=',$params['status']];
        }
        $count=Db::name('express')->where($where)->count();
        $list=Db::name('express')->where($where)->order('status desc,id desc')->limit($offset,intval($limit))->select()->toArray();
        $result['list']=$list;
        $result['total']=$count;
        return $result;
    }
    public function createExpress($params)
    {
        $data= self::insertAll($params);
        return $data;
        
    }

    public function getOne($id)
    {
        $res=  self::where('id','=',$id)->find();
        if (empty($res)){
            return  [];
        }
        $result =$res;
        $result['appoint']=Db::name("express_appoint")->field("id,warehouse,appoint_express,number,price,goods_attribute,sort")->where([['express_id','=',$id],['status','=',1]])->select()->toArray();
        return  $result;

    }

    public function setStatus($param)
    {
        Db::startTrans();
        try {
            //把之前的修改为禁用不默认
           Db::name('express')->where('status','<>',3)->save(['status'=>1]);

            $res =  Db::name('express')->where('id','=',$param['id'])->where('status','<>',3)->save(['status'=>2]);
            if ($res<0){
                Db::rollback();
                return false;
            }
            Db::commit();
            return true;
        }catch (\Exception $e) {
            #回滚事务
            Db::rollback();
            return  $e->getMessage();
        }

    }
    public function webList($params, $page = 1, $limit = 10)
    {

        $offset = ($page - 1) * $limit;
        $where=[];
        if (isset($params['status']) && !empty($params['status'])) {
            $where[]=['status','=',$params['status']];
        }
        $count=Db::name('express')->field('id,name')->where($where)->count();
        $list=Db::name('express')->field('id,name')->where($where)->order('status desc,id desc')->limit($offset,intval($limit))->select()->toArray();
        $result['list']=$list;
        $result['total']=$count;
        return $result;
    }
}
