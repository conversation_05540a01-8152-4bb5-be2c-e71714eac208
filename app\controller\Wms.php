<?php


namespace app\controller;


use app\BaseController;
use app\ErrorCode;
use app\Request;
use app\service\Wms as WmsService;

class Wms extends BaseController
{
    /**
     * Description:萌牙撤单
     * Author: zrc
     * Date: 2021/12/17
     * Time: 10:39
     * @param Request $request
     * @return \think\response\Json
     */
    public function withdrawOrder(Request $request)
    {
        $params = $request->param();
        if (empty($params['store_code'])) $this->throwError('未获取到实体仓库编号', ErrorCode::PARAM_ERROR);
        if (empty($params['sub_order_no'])) $this->throwError('未获取到子订单号', ErrorCode::PARAM_ERROR);
        $wmsService = new WmsService();
        $wmsService->withdrawOrder($params);
        return $this->success([]);
    }

    /**
     * Description:接收萌牙订单回调处理
     * Author: zrc
     * Date: 2021/12/17
     * Time: 11:17
     * @param Request $request
     * @return \think\response\Json
     */
    public function WmsOrderReceive(Request $request)
    {
        $params = $request->param();
        if (empty($params['type']) || !in_array($params['type'], [1, 2, 3, 4])) $this->throwError('未获取到回传类型', ErrorCode::PARAM_ERROR);
        if (empty($params['data'])) $this->throwError('未获取到回传数据', ErrorCode::PARAM_ERROR);
        $wmsService = new WmsService();
        $wmsService->WmsOrderReceive($params);
        return $this->success([]);
    }
}