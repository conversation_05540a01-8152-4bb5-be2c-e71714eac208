<?php


namespace app\service;


use app\BaseService;
use app\ErrorCode;
use app\model\CrossLockOrder;
use app\service\es\Es;
use app\service\Order as OrderService;
use app\service\Push as PushService;
use app\service\WeChat as WeChatService;
use think\facade\Db;

class CrossAutoPush extends BaseService
{
    /**
     * Description:配置修改
     * Author: zrc
     * Date: 2023/3/7
     * Time: 9:56
     * @param $params
     * @return bool|int
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function autoPushConfigUpdate($params)
    {
        $updateData = [];
        $config     = Db::name('cross_auto_push_config')->where(['id' => 1])->find();
        if (empty($config)) $this->throwError('未获取到配置信息');
        if (isset($params['is_auto_push']) && in_array($params['is_auto_push'], [0, 1])) {
            if ($params['is_auto_push'] == $config['is_auto_push']) return true;
            $updateData['is_auto_push'] = $params['is_auto_push'];
        }
        if (!empty($params['push_start_time'])) $updateData['push_start_time'] = $params['push_start_time'];
        if (!empty($params['push_end_time'])) $updateData['push_end_time'] = $params['push_end_time'];
        if (!empty($params['interval_time'])) $updateData['interval_time'] = $params['interval_time'];
        if (!empty($params['pay_order_after_time'])) $updateData['pay_order_after_time'] = $params['pay_order_after_time'];
        $result = Db::name('cross_auto_push_config')->where(['id' => 1])->update($updateData);
        if (empty($result)) $this->throwError('配置修改失败');
        return $result;
    }

    /**
     * Description:配置获取
     * Author: zrc
     * Date: 2023/3/7
     * Time: 9:59
     * @param $params
     * @return array|mixed|Db|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getAutoPushConfig($params)
    {
        $result = Db::name('cross_auto_push_config')->where(['id' => 1])->find();
        if (empty($result)) $this->throwError('未获取到配置信息');
        return $result;
    }

    /**
     * Description:自动推单处理
     * Author: zrc
     * Date: 2023/3/8
     * Time: 14:21
     * @param $params
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function crossAutoPushDeal($params)
    {
        try {
            $close_info = Db::name('cross_close_conf')->whereBetweenTimeField('start_time', 'end_time')->where('status', 1)->find();
            if (!empty($close_info)) {
                Db::name('cross_close_log')->insert([
                    'main_order_no' => $params['main_order_no'],
                    'param'         => json_encode($params),
                    'remark'        => $close_info['remark'],
                    'status'        => 0,
                    'created_time'  => time(),
                    'update_time'   => time(),
                ]);
                $this->throwError("推单临时已关闭: " . $close_info['remark']);
            }

            $lock_order = CrossLockOrder::where([
                'main_order_no' => $params['main_order_no'],
                'status'        => 1,
            ])->find();
            if ($lock_order) {
                $this->throwError('该订单已锁定,锁定原因:' . ($lock_order['remark'] ?? ''), ErrorCode::PARAM_ERROR);
            }
            $config = Db::name('cross_auto_push_config')->where(['id' => 1])->find();
            if (empty($config)) {
                $status = 0;
                $this->throwError('未获取到自动推单配置');
            }
            if ($config['is_auto_push'] != 1) {
                $status = 0;
                $this->throwError('自动推单已关闭');
            }
            $field     = 'co.is_auto_push,co.sub_order_status,co.period,co.package_id,co.refund_status,co.is_ts,co.store_type,pc.is_presell,pc.short_code,pc.title';
            $orderInfo = Db::name('order_main')
                ->alias('om')
                ->field($field)
                ->join('cross_order co', 'co.main_order_id=om.id')
                ->join('vh_commodities.vh_periods_cross pc', 'pc.id=co.period')
                ->where(['main_order_no' => $params['main_order_no']])
                ->find();
            $close_auto_push_periods = Db::name('cross_close_auto_push_period')->column('period');
            if (in_array($orderInfo['period'], $close_auto_push_periods)) {
                $status = 0;
                $this->throwError('期数:' . $orderInfo['period'] . '，暂不自动推送');
            }
            if ($orderInfo['store_type'] == 2) {
                $status = 0;
                $this->throwError('南沙仓期数暂不自动推送');
            }
            $associated_products = Db::table('vh_commodities.vh_periods_cross_set')->where('id', $orderInfo['package_id'])->value('associated_products');
            $products = getProducts(array_column(json_decode($associated_products, true), 'product_id'), 'id,short_code', 'id');
            $orderInfo['short_code'] = implode(',', array_unique(array_column($products, 'short_code')));

            $p_period = Db::table('vh_commodities.vh_periods_cross')->where('id', $orderInfo['period'])->find();
            $is_channel = $p_period['is_channel'] ?? 0;
            $is_defective_goods = $p_period['is_defective_goods'] ?? 0;

            $excess_num = $excess_id = 0;
            if ($is_channel == 1) {
                //锁定数量
                $excess = Db::name('cross_excess')
                    ->where([
                        ['goods_barcode', '=', $orderInfo['short_code']],
                        ['store_type', '=', $orderInfo['store_type']],
                        ['status', '=', 1],
                    ])->column('id,num');

                $excess_id  = $excess[0]['id'] ?? 0;
                $excess_num = array_sum(array_column($excess, 'num'));
            }

            //订单商品验证
            if (empty($orderInfo)) {
                $status = 0;
                $this->throwError('未获取到订单信息，暂不自动推送');
            }
            if ($orderInfo['is_auto_push'] != 1) {
                $status = 0;
                $this->throwError('订单已设置不自动推单，暂不自动推送');
            }
            if ($orderInfo['sub_order_status'] != 1) {
                $status = 0;
                $this->throwError('订单状态异常，暂不自动推送');
            }
            if (in_array($orderInfo['refund_status'], [1, 2])) {
                $status = 0;
                $this->throwError('订单退款状态异常，暂不自动推送');
            }
            if ($orderInfo['is_ts'] == 1) {
                $status = 0;
                $this->throwError('暂存订单，暂不自动推送');
            }
            //临时关闭南沙仓自动推单
//            if($orderInfo['store_type'] == 2){
//                $status = 0;
//                $this->throwError('南沙仓订单，暂不自动推送');
//            }
            //商品实物库存验证
            $where = array(
                ['goods_barcode', '=', $orderInfo['short_code']],
                ['store_type', '=', $orderInfo['store_type']],
                ['is_delete', '=', 0]
            );


            #推单时需要【实物库存-次品数量-暂存数量-已支付已推单未发货数量】
            $cross_inventorys = Db::name('cross_inventory')->where($where)->column('id,real_nums,defective_nums');
            if ($is_defective_goods == 1) {
                $entry_num = array_sum(array_column($cross_inventorys, 'real_nums')); //实物库存
            } else {
                $entry_num = bcsub(array_sum(array_column($cross_inventorys, 'real_nums')), array_sum(array_column($cross_inventorys, 'defective_nums'))); //实物库存 - 次品库存之和
            }
            $entry_num        = bcsub($entry_num, $excess_num); //实物库存 - 锁定库存
            if ($entry_num > 0) {
                #查询暂存数量
                #1.查询出简码包含的全部期数ID
                $has_short_periods    = Es::name(Es::PERIODS)->where([
                    ['short_code', 'in', [$orderInfo['short_code']]]
                ])->field('id')->order(['id' => 'desc'])->select()->toArray();
                $has_short_period_ids = array_column($has_short_periods, 'id');

                $pid            = Db::table('vh_wiki.vh_products')->where('short_code', 'in', $orderInfo['short_code'])->value('id');
                $has_short_pkgs = Db::table('vh_commodities.vh_periods_cross_set')
                    ->where('period_id', 'in', $has_short_period_ids)
                    ->where('associated_products', 'LIKE', "%:{$pid},%")
                    ->column('associated_products', 'id');

                $goods_barcode_pkg_ids = [];
                foreach ($has_short_pkgs as $pkg_id => $associated_products) {
                    $associated_products = json_decode($associated_products, true);
                    foreach ($associated_products as $ap_info) {
                        if (!is_array($ap_info['product_id']) && $ap_info['product_id'] == $pid) {
                            if (!empty($ap_info['sub_package_id']))
                                $goods_barcode_pkg_ids[] = $ap_info['sub_package_id'];
                            $goods_barcode_pkg_ids[] = $pkg_id;
                        }
                    }
                }

                $ts_where   = $wd_where = [
                    ['period', 'in', $has_short_period_ids], //简码包含的全部期数ID
                    ['package_id', 'in', array_values(array_unique($goods_barcode_pkg_ids))], //简码包含的全部套餐ID
                    ['is_delete', '=', 0], //是否删除 0-正常 1-已删除
                    ['store_type', '=', $orderInfo['store_type']],
                    ['sub_order_status', '=', 1], //订单状态=1 订单状态：0-待支付 1-已支付 2-已发货 3-已完成 4-已取消
                    ['refund_status', '=', 0], //退款状态=0  0-未退款 1-退款中 2-退款成功 3-退款失败
                ];
                $ts_where[] = ['is_ts', '=', 1]; //是否暂存：0否 1是
                $ts_where[] = ['push_store_status', '!=', 1]; //代发仓推送状态：0-未推送 1-推送成功 2-推送失败 3-不推送
                $wd_where[] = ['push_store_status', '=', 1]; //代发仓推送状态：0-未推送 1-推送成功 2-推送失败 3-不推送

                #2.查找期数对应的暂存订单数量
                $temp_storages = Es::name(Es::ORDERS)->where($ts_where)->field('id,order_qty')->select()->toArray();
                $wait_deliver  = Es::name(Es::ORDERS)->where($wd_where)->field('id,order_qty')->select()->toArray();

                $temp_storage_num = array_sum(array_column($temp_storages, 'order_qty')); //暂存数量
                $wait_deliver_num = array_sum(array_column($wait_deliver, 'order_qty')); //已推送未发货数量
                $entry_num        = bcsub($entry_num, bcadd($temp_storage_num, $wait_deliver_num)); //(实物库存-次品数量)-(暂存数量 + 已推送未发货数量)
            }

            if ($entry_num <= 0) {
                $status = 0;
                if ($orderInfo['is_presell'] == 1) {
                    $this->throwError('预售商品订单无实物库存，暂不自动推送');
                } else {
                    $this->throwError('商品实物库存不足，暂不自动推送');
                }
            }
            //推送支付单、代发仓
            $pushService = new PushService();
            $pushService->customsDirectPush(['main_order_no' => $params['main_order_no']]);
            $status = 1;
        } catch (\Exception $e) {
            if (!isset($status)) {
                $status = 2;
                //异常提示
                $store     = $orderInfo['store_type'] == 1 ? '古斯缇' : '南沙';
                $content   = "# 跨境自动推单异常提示\n";
                $content   .= "-主订单号：" . $params['main_order_no'] . "\n";
                $content   .= "-订单仓库：" . $store . "\n";
                $content   .= "-商品条码：" . $orderInfo['short_code'] . "\n";
                $content   .= "-异常信息：" . $e->getMessage() . "\n";
                $queueData = array(
                    'access_token' => env('ORDERS.cross_token'),
                    'type'         => 'text',
                    'at'           => '***********,***********,***********',
                    'content'      => base64_encode($content),
                );
                $data      = base64_encode(json_encode($queueData));
                $pushData  = array(
                    'exchange_name' => 'dingtalk',
                    'routing_key'   => 'dingtalk_sender',
                    'data'          => $data,
                );
                httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
            }
            $msg = '自动推单处理：' . $e->getMessage();
            $this->throwError('自动推单处理：' . $e->getMessage());
        } finally {
            //自动推单日志写入
            if (!empty($orderInfo)) {
                $logData   = array(
                    'main_order_no' => $params['main_order_no'],
                    'period'        => $orderInfo['period'],
                    'title'         => $orderInfo['title'],
                    'short_code'    => $orderInfo['short_code'],
                    'store_type'    => $orderInfo['store_type'],
                    'status'        => $status,
                    'error_msg'     => isset($msg) ? $msg : '',
                    'created_time'  => time()
                );
                $todaytime = strtotime(date('Y-m-d'));
                $logId     = Db::name('cross_auto_push_log')->where([['main_order_no', '=', $params['main_order_no']], ['created_time', '>=', $todaytime]])->value('id');
                if (empty($logId)) {
                    Db::name('cross_auto_push_log')->insert($logData);
                } else {
                    Db::name('cross_auto_push_log')->where(['id' => $logId])->update($logData);
                }
            }
        }
        return true;
    }

    /**
     * Description:简码查询可推送订单自动推单
     * Author: zrc
     * Date: 2023/3/9
     * Time: 11:52
     * @param $params
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPushOrderNoByShortCode($params)
    {
        $data           = [];
        $short_code_arr = explode(',', $params['short_code']);
        $orderInfo      = Db::name('order_main')
            ->alias('om')
            ->field('om.main_order_no')
            ->leftJoin('cross_order co', 'co.main_order_id=om.id')
            ->leftJoin('vh_commodities.vh_periods_cross pc', 'pc.id=co.period')
            ->leftJoin('vh_commodities.vh_periods_cross_set pcs', 'pcs.id=co.package_id')
            ->where(function ($query) use ($short_code_arr) {
                $pids = Db::table('vh_wiki.vh_products')->where('bar_code|short_code','in', $short_code_arr)->column('id');
                foreach ($pids as $pid) {
                    $query->whereOr("pcs.associated_products",'LIKE',"%:{$pid},%");
                }
            })
            ->where([['co.sub_order_status', '=', 1], ['co.push_store_status', '=', 0]])
            ->select()->toArray();
        foreach ($orderInfo as &$val) {
            $data[] = base64_encode(json_encode(['main_order_no' => $val['main_order_no']]));
        }
        if (!empty($data)) {
            $crossPushData = array(
                'exchange_name' => 'orders',
                'routing_key'   => 'cross_auto_push',
                'data'          => $data
            );
            httpPostString(env('ITEM.QUEUE_URL'), json_encode($crossPushData));
        }
        return true;
    }

    /**
     * Description:设置订单不自动推送
     * Author: zrc
     * Date: 2023/3/8
     * Time: 17:36
     * @param $params
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function setOrderStopAutoPush($params)
    {
        $orderInfo = Db::name('order_main')
            ->alias('om')
            ->field('co.id,co.sub_order_no,co.sub_order_status,co.push_store_status,co.payment_doc,co.is_auto_push')
            ->leftJoin('cross_order co', 'co.main_order_id=om.id')
            ->where(['main_order_no' => $params['main_order_no']])
            ->find();
        if (empty($orderInfo)) $this->throwError('未获取到订单信息');
        if ($orderInfo['sub_order_status'] != 1) $this->throwError('订单状态异常');
        if ($orderInfo['push_store_status'] == 1 || $orderInfo['payment_doc'] == 1) $this->throwError('订单已推送');
        if ($orderInfo['is_auto_push'] == 0) $this->throwError('订单已设置为不自动推送');
        Db::name('cross_order')->where(['id' => $orderInfo['id']])->update(['is_auto_push' => 0, 'update_time' => time()]);
        $orderService = new OrderService();
        $remark       = array(
            'sub_order_no' => $orderInfo['sub_order_no'],
            'order_type'   => 2,
            'content'      => '订单已设置为不自动推单',
            'admin_id'     => $params['operator']
        );
        $orderService->createRemarks($remark);
        return true;
    }

    /**
     * Description:自动推单统计列表
     * Author: zrc
     * Date: 2023/3/9
     * Time: 18:13
     * @param $params
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function autoPushOrderGatherList($params)
    {
        $result['list']  = [];
        $result['total'] = 0;
        $page            = !empty($params['page']) ? $params['page'] : 1;
        $limit           = !empty($params['limit']) ? $params['limit'] : 10;
        $offset          = ($page - 1) * $limit;
        $where           = [];
        if (!empty($params['main_order_no'])) {
            $where[] = ["main_order_no", "=", $params['main_order_no']];
        }
        if (!empty($params['period'])) {
            $where[] = ["period", "=", $params['period']];
        }
        if (!empty($params['short_code'])) {
            $where[] = ["short_code", "=", $params['short_code']];
        }
        if (isset($params['store_type']) && is_numeric($params['store_type'])) {
            $where[] = ["store_type", "=", $params['store_type']];
        }
        if (isset($params['status']) && is_numeric($params['status'])) {
            $where[] = ["status", "=", $params['status']];
        }
        if (!empty($params['s_time'])) {
            $where[] = ["created_time", ">=", strtotime($params['s_time'])];
        }
        if (!empty($params['e_time'])) {
            $where[] = ["created_time", "<", strtotime($params['e_time'])];
        }
        $totalNum = Db::name('cross_auto_push_log')->where($where)->count();
        $lists    = Db::name('cross_auto_push_log')->where($where)->limit($offset, $limit)->order('id desc')->select()->toArray();
        if (count($lists) > 0) {
            foreach ($lists as $key => $val) {
                $lists[$key]['created_time'] = date('Y-m-d H:i:s', $val['created_time']);
            }
        }
        $result['list']  = $lists;
        $result['total'] = $totalNum;
        return $result;
    }

    public function autoPushOrderGatherExport($params)
    {
        //获取发起人企业微信信息
        $weChatService = new WeChatService();
        $userInfo      = $this->httpGet(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info', ['admin_id' => $params['admin_id'], 'field' => 'userid,dept_id']);
        if ($userInfo['error_code'] != 0 || !isset($userInfo['data'][$params['admin_id']])) $this->throwError('未获取到企业微信用户信息');
        $userid = $userInfo['data'][$params['admin_id']]['userid'];
        $where  = [];
        if (!empty($params['main_order_no'])) {
            $where[] = ["main_order_no", "=", $params['main_order_no']];
        }
        if (!empty($params['period'])) {
            $where[] = ["period", "=", $params['period']];
        }
        if (!empty($params['short_code'])) {
            $where[] = ["short_code", "=", $params['short_code']];
        }
        if (isset($params['store_type']) && is_numeric($params['store_type'])) {
            $where[] = ["store_type", "=", $params['store_type']];
        }
        if (isset($params['status']) && is_numeric($params['status'])) {
            $where[] = ["status", "=", $params['status']];
        }
        if (!empty($params['s_time'])) {
            $where[] = ["created_time", ">=", strtotime($params['s_time'])];
        }
        if (!empty($params['e_time'])) {
            $where[] = ["created_time", "<", strtotime($params['e_time'])];
        }
        $lists = Db::name('cross_auto_push_log')->where($where)->order('id desc')->select()->toArray();
        if (count($lists) > 0) {
            $data = [];
            foreach ($lists as $key => $val) {
                switch ($val['store_type']) {
                    case 1:
                        $val['store_type'] = '古斯缇';
                        break;
                    case 2:
                        $val['store_type'] = '南沙仓';
                        break;
                }
                switch ($val['status']) {
                    case 0:
                        $val['status'] = '暂不推送';
                        break;
                    case 1:
                        $val['status'] = '推送成功';
                        break;
                    case 2:
                        $val['status'] = '推送失败';
                        break;
                }
                $data[] = array(
                    'main_order_no' => $val['main_order_no'],
                    'period'        => $val['period'],
                    'title'         => $val['title'],
                    'short_code'    => $val['short_code'],
                    'store_type'    => $val['store_type'],
                    'status'        => $val['status'],
                    'error_msg'     => $val['error_msg'],
                    'created_time'  => date('Y-m-d H:i:s', $val['created_time']),
                );
            }
            $filename = "跨境自动推单统计导出";
            $header   = array(
                array('column' => 'main_order_no', 'name' => '主订单号', 'width' => 30),
                array('column' => 'period', 'name' => '期数', 'width' => 15),
                array('column' => 'title', 'name' => '商品名称', 'width' => 30),
                array('column' => 'short_code', 'name' => '简码', 'width' => 15),
                array('column' => 'store_type', 'name' => '仓库', 'width' => 15),
                array('column' => 'status', 'name' => '推送状态', 'width' => 15),
                array('column' => 'created_time', 'name' => '推送时间', 'width' => 15),
                array('column' => 'error_msg', 'name' => '错误信息', 'width' => 40),
            );
            try {
                $uploadUrl = exportSheelExcel($data, $header, $filename);
                if (empty($uploadUrl)) $this->throwError('生成excel文件异常');
                $file     = app()->getRootPath() . "public/storage/" . $uploadUrl;
                $media_id = weixinUpload($file, $filename . '.xlsx');
                if (empty($media_id)) $this->throwError('上传企业微信临时文件失败');
                unlink($file);
                $msgData = array(
                    'content' => $media_id,
                    'userid'  => $userid,
                    'msgtype' => 'file',
                    'agentid' => 0,
                );
                $result  = httpPostString(env('ITEM.WECHART_URL') . '/wechat/v3/wecom/app/send', json_encode($msgData));
                if ($result['error_code'] != 0) $this->throwError('发送文件到企业微信失败');
            } catch (\Exception $e) {
                $weChatService->weChatSendText($userid, '跨境自动推单统计导出失败：' . $e->getMessage());
                $this->throwError("导出失败：" . $e->getMessage());
            }
        }
        return true;
    }
}