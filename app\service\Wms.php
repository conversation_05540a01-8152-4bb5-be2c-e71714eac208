<?php


namespace app\service;


use app\BaseService;
use app\ErrorCode;
use app\service\elasticsearch\ElasticSearchService;
use app\service\Order as OrderService;
use app\service\Push as PushService;
use think\facade\Db;
use think\facade\Log;

class Wms extends BaseService
{
    /**
     * Description:萌牙撤单
     * Author: zrc
     * Date: 2021/12/17
     * Time: 11:06
     * @param $requestParams
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function withdrawOrder($requestParams)
    {
        $param = array(
            'store_code'    => trim($requestParams['store_code']),
            'orderno'       => trim($requestParams['sub_order_no']),
            'refund_status' => 1
        );
        $this->httpPost(env('ITEM.DISTRIBUTE_URL') . '/sync/receiveOrderSync', $param);
        return true;
    }

    /**
     * Description:接收萌牙订单回调处理
     * Author: zrc
     * Date: 2021/12/17
     * Time: 16:20
     * @param $requestParams
     * @return bool
     * @throws \Exception
     */
    public function WmsOrderReceive($requestParams)
    {
        $params = $requestParams;
        //写入萌牙回写日志
        $log = array(
            'type'          => $params['type'],
            'sub_order_no'  => isset($params['data']['orderno']) ? $params['data']['orderno'] : '',
            'callback_data' => json_encode($params, JSON_UNESCAPED_UNICODE),
            'created_time'  => time()
        );
        Db::name('wms_callback_log')->insert($log);
        if ($params['type'] != 4 && empty($params['data']['orderno'])) $this->throwError('订单号不能为空！', ErrorCode::PARAM_ERROR);
        //拍卖订单
        if (strpos($params['data']['orderno'], 'VHA') !== false) {
            $this->httpPost(env('ITEM.AUCTION_ORDERS_URL') . '/auction-order/v3/order/wmsOrderReceive', $params);
            return true;
        }
        if (isset($params['data']['orderno'])) {
            $es   = new ElasticSearchService();
            $arr  = array(
                'index' => ['orders'],
                'match' => [['sub_order_no.keyword' => $params['data']['orderno']]],
                'range' => [['created_time' => ['gte' => date('Y-m-d H:i:s', 1602777600)]]],
                'limit' => 1,
            );
            $data = $es->getDocumentList($arr);
            if (!isset($data['data'][0])) {
                $data['data'][0] = Db::name('tripartite_order')->where(['sub_order_no' => $params['data']['orderno']])->find();
                if (empty($data['data'][0])) {
                    $data['data'][0] = Db::name('offline_order')->where(['sub_order_no' => $params['data']['orderno']])->find();
                    if (empty($data['data'][0])) {
                        Log::error('萌牙回写运单号失败！请求参数：' . json_encode($params, JSON_UNESCAPED_UNICODE) . '失败原因：未获取到订单信息');
                        $this->throwError($params['data']['orderno'] . ':未获取到订单信息！');
                    }
                }
            }
            $orderInfo = $data['data'][0];
        }
        if ($params['type'] == 1) {//订单状态回调通知
            switch ($params['data']['code']) {
                case 1000:
                    $routing_msg = '已下发库房';
                    break;
                case 1001:
                    $routing_msg = '任务已分配';
                    break;
                case 1002:
                    $routing_msg = '拣货下架';
                    break;
                case 1003:
                    $routing_msg = '复核打包';
                    break;
                case 1004:
                    $routing_msg = '打包完成';
                    break;
                case 1005:
                    $routing_msg = '包裹出库';
                    break;
                case 1011:
                    $routing_msg                   = '订单已撤单成功';
                    $upateData['sub_order_status'] = 4;
                    break;
                case 1013:
                    $routing_msg = '订单撤单中';
                    break;
                case 1014:
                    $routing_msg = '订单撤单失败';
                    break;
                case 1015:
                    $routing_msg = '订单已合并';
                    break;
                case 1021:
                    $routing_msg = '订单终止出库';
                    break;
                case 1022:
                    $routing_msg = '订单滞留';
                    //添加订单备注
                    $remarks = array(
                        'sub_order_no' => $orderInfo['sub_order_no'],
                        'admin_id'     => 0,
                        'remarks'      => '萌牙回写路由信息：订单滞留',
                        'created_time' => time(),
                    );
                    Db::name('order_remarks')->insert($remarks);
                    break;
                case 1023:
                    $routing_msg = '订单冻结';
                    break;
            }
            $upateData['latest_routing_msg'] = $routing_msg;
            $upateData['order_no']           = $orderInfo['sub_order_no'];
            $upateData['order_type']         = $orderInfo['order_type'];
            $upateData['operator']           = 0;
            if (in_array($orderInfo['order_type'], [0, 1, 3, 4, 9])) {
                //更新订单信息
                $orderService = new OrderService();
                $updateOrder  = $orderService->updateOrder($upateData);
                if (empty($updateOrder)) $this->throwError('修改订单信息失败');
            }
            //记录订单萌牙路由
            $logData = [
                'sub_order_no' => $orderInfo['sub_order_no'],
                'routing_msg'  => $routing_msg,
                'code'         => $params['data']['code'],
                'created_time' => time()
            ];
            $result  = Db::name('order_routing')->insert($logData);
            if (empty($result)) $this->throwError('添加订单路由记录失败');
        } else if ($params['type'] == 2) {//订单运单号回调通知
            $waybillNo  = implode(',', $params['data']['waybillNo']);
            $updateData = array(
                'sub_order_status' => 2,
                'express_type'     => $params['data']['logistics_id'],
                'express_number'   => $waybillNo,
                'push_wms_status'  => 1,
                'delivery_time'    => time(),
                'update_time'      => time(),
            );
            if (in_array($orderInfo['order_type'], [0, 1, 2, 3])) $updateData['is_ts'] = 0;
            //自提确认收货
            if ($params['data']['logistics_id'] == 6) {
                $receiptPushData = array(
                    'exchange_name' => 'orders',
                    'routing_key'   => 'automatic_goods_receipt',
                    'data'          => base64_encode(json_encode(['sub_order_no_str' => $params['data']['orderno'], 'operator' => 0])),
                );
                //httpPostString(env('ITEM.QUEUE_URL'), json_encode($receiptPushData)); //萌牙同步物流至中台时，订单状态只修改为【已发货】
            }
            $order_type = config('config')['order_type'];//订单频道获取

            //查询运单号
            $express_number  = Db::name($order_type[intval($orderInfo['order_type'])]['table'])->where(['sub_order_no' => $params['data']['orderno']])->value("express_number");
            // 更新订单信息
            $result     = Db::name($order_type[intval($orderInfo['order_type'])]['table'])->where(['sub_order_no' => $params['data']['orderno']])->update($updateData);
            if (empty($result)) $this->throwError('修改订单物流信息失败');

            //添加订单备注
            $remarks = array(
                'sub_order_no' => $params['data']['orderno'],
                'admin_id'     => 0,
                'remarks'      => $params['data']['orderno'] . '回写物流信息：' . $params['data']['logistics_company'] . $waybillNo,
                'created_time' => time(),
            );
            Db::name('order_remarks')->insert($remarks);
            //三方订单回写运单号到三方平台
            if ($orderInfo['order_type'] == 7) {
                //代发仓推送处理队列
                $tr_info = Db::name('tripartite_order')->where('sub_order_no', $orderInfo['sub_order_no'])->find();
                $data = base64_encode(json_encode([
                    'main_order_no' => Db::name('order_main')->where('id', ($orderInfo['main_order_id'] ?? ''))->value('main_order_no'),
                    'store_id'      => $tr_info['store_id'] ?? '',
                    'store_name'    => $tr_info['store_name'] ?? '',
                    'orderNo'       => $orderInfo['sub_order_no'],
                    'wayBill'       => $waybillNo,
                    'expressType'   => $params['data']['logistics_id'],
                    'platform'      => $params['data']['platform'],
                    'is_up'         => !empty($express_number) ? 1 : 0
                ]));
                switch ($orderInfo['order_from_thirdparty']) {
                    case 1:
                        $pushData = array(
                            'exchange_name' => 'order_trackingnumbers',
                            'routing_key'   => 'order_trackingnumbers_jd',
                            'data'          => $data,
                        );
                        break;
                    case 2:
                    case 4:
                    case 16:
                    case 24:
                        $pushData = array(
                            'exchange_name' => 'order_trackingnumbers',
                            'routing_key'   => 'order_trackingnumbers_taobao',
                            'data'          => $data,
                        );
                        break;
                    case 3:
                        $pushData = array(
                            'exchange_name' => 'order_trackingnumbers',
                            'routing_key'   => 'order_trackingnumbers_pdd',
                            'data'          => $data,
                        );
                        break;
                    case 13:
                        $pushData = array(
                            'exchange_name' => 'order_trackingnumbers',
                            'routing_key'   => 'order_trackingnumbers_tmall',
                            'data'          => $data,
                        );
                        break;
                    case 17:
                        if (!in_array(($tr_info['store_id'] ?? ''), [
                            '02223', //渝中区微醺酒业商行 （兔子）
                        ])) {
                            $pushData = array(
                                'exchange_name' => 'order_trackingnumbers',
                                'routing_key'   => 'order_trackingnumbers_xiaohongshu',
                                'data'          => $data,
                            );
                        }
                        break;
                    case 18:
                    case 19:
                    case 25:
                        $pushData = array(
                            'exchange_name' => 'openapi',
                            'routing_key'   => 'wms.order.status.24',
                            'data'          => $data,
                        );
                        break;
                    case 21:
                        $pushData = array(
                            'exchange_name' => 'openapi',
                            'routing_key'   => 'wms.order.status.21',
                            'data'          => $data,
                        );
                        break;
                    case 26:
                        $pushData = array(
                            'exchange_name' => 'order_trackingnumbers',
                            'routing_key'   => 'order_trackingnumbers_yuou',
                            'data'          => $data,
                        );
                        break;
                    case 27:
                        $pushData = array(
                            'exchange_name' => 'order_trackingnumbers',
                            'routing_key'   => 'order_trackingnumbers_ktt',
                            'data'          => $data,
                        );
                        break;
                    case 28:
                        $pushData = array(
                            'exchange_name' => 'order_trackingnumbers',
                            'routing_key'   => 'order_trackingnumbers_tmall_kj',
                            'data'          => $data,
                        );
                        break;
                    case 29:
                        $pushData = array(
                            'exchange_name' => 'order_trackingnumbers',
                            'routing_key'   => 'order_trackingnumbers_tmall_snack',
                            'data'          => $data,
                        );
                    case 30:
                        $pushData = array(
                            'exchange_name' => 'order_trackingnumbers',
                            'routing_key'   => 'order_trackingnumbers_tzxq', // 三方店铺兔子星球运单号同步
                            'data'          => $data,
                        );
                        break;
                    case 31:
                        $pushData = array(
                            'exchange_name' => 'order_trackingnumbers',
                            'routing_key'   => 'order_trackingnumbers_mldmydm',
                            'data'          => $data,
                        );
                        break;
                }
                if (isset($pushData)) {
                    httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
                }
                //订单运单号回写T+
                $pushTplus = $this->httpPost(env('ITEM.ERP_URL') . '/erp/v3/saleOrder/wmsCallback', ['waybill_no' => $waybillNo, 'sub_order_no' => $orderInfo['sub_order_no']]);
                if ($pushTplus['error_code'] != 0) {
                    Log::error('运单号推送T+失败！请求参数：' . json_encode(['waybill_no' => $waybillNo, 'sub_order_no' => $orderInfo['sub_order_no']]) . '返回参数：' . json_encode($pushTplus));
                }
            } else if ($orderInfo['order_type'] == 8) {
                // 线下订单发货后创建应收单
                pushQueue('orders', 'create_arap_ys_order', ['sub_order_no' => $orderInfo['sub_order_no']]);
            }
            $oft = $orderInfo['order_from_thirdparty'] ?? false;
            if ((isset($orderInfo['push_t_status']) && $orderInfo['push_t_status'] != 3) || ($oft == 28)) {//订单推送erp
                $pushService = new PushService();
                if ($orderInfo['order_type'] == 8 && isset($orderInfo['document_type']) && $orderInfo['document_type'] == 0 && isset($orderInfo['corp']) && $orderInfo['corp'] != '002') { //科技样酒订单推送出库单
                    $pushService->pushTplus(['sub_order_no' => $orderInfo['sub_order_no'], 'order_type' => $orderInfo['order_type'], 'corp' => $orderInfo['corp']]);
                } else {
                    $pushService->pushTplus(['sub_order_no' => $orderInfo['sub_order_no'], 'order_type' => $orderInfo['order_type']]);
                }
            }
            if (isset($orderInfo['uid']) && !empty($orderInfo['uid']) && empty($orderInfo['express_number'])) {
                $userInfo = httpGet(env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo', ['uid' => $orderInfo['uid'], 'field' => 'nickname,applet_openid,h5_openid']);
                if ($userInfo['error_code'] == 0 && isset($userInfo['data']['list'][0])) {
                    $getTokens = $this->httpGet(env('ITEM.WECHART_URL') . '/wechat/v3/gzh/stable_accesstoken');
                    if ($getTokens['error_code'] == 0 && isset($userInfo['data']['list'][0]['h5_openid'])) {
                        $push_goods_title = $orderInfo['title'] ?? '酒云商品';
                        if (mb_strlen($push_goods_title) > 18) {
                            $push_goods_title = mb_substr($orderInfo['title'], 0, 15, 'utf-8') . '...';
                        }
                        $pushData = array(
                            'touser'        => $userInfo['data']['list'][0]['h5_openid'],
                            'template_id'   => "iemsKHfctf99eiVrmILmU66dwOdVd-CLtU13AR0yDWE",
                            'url'           => "https://uh5.vinehoo.com",
                            'miniprogram'   => [
                                'appid'    => 'wx3e0b582d1f902659',
                                'pagepath' => 'packageB/pages/order-detail/order-detail?orderNo=' . $orderInfo['sub_order_no'],
                            ],
                            "topcolor"      => "#FF0000",
                            'data'          => [
                                'time3'             => [
                                    'value' => $orderInfo['created_time'],
                                    'color' => '#173177'
                                ],
                                'thing7'            => [
                                    'value' => $push_goods_title,
                                    'color' => '#173177'
                                ],
                                'thing8'            => [
                                    'value' => $params['data']['logistics_company'],
                                    'color' => '#173177'
                                ],
                                'character_string9' => [
                                    'value' => $waybillNo,
                                    'color' => '#173177'
                                ]
                            ],
                            'client_msg_id' => $orderInfo['sub_order_no'] . 'orders_msg_send'
                        );
                        $result   = curlRequest('https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=' . $getTokens['access_token'], json_encode($pushData));
                        $log      = array(
                            'type'               => 1,
                            'openid'             => $userInfo['data']['list'][0]['h5_openid'],
                            'template_id'        => 'iemsKHfctf99eiVrmILmU66dwOdVd-CLtU13AR0yDWE',
                            'request_url'        => 'https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=' . $getTokens['access_token'],
                            'request_param_json' => json_encode($pushData, JSON_UNESCAPED_UNICODE),
                            'errcode'            => $result['errcode'],
                            'errmsg'             => $result['errmsg'],
                            'created_time'       => time(),
                        );
                        Db::name('template_message_log')->insert($log);
                    }
                    $getToken = $this->httpGet(env('ITEM.WECHART_URL') . '/wechat/v3/minapp/accesstoken');
                    if ($getToken['error_code'] == 0 && isset($userInfo['data']['list'][0]['applet_openid'])) {
                        $pushData = array(
                            'touser'            => $userInfo['data']['list'][0]['applet_openid'],
                            'template_id'       => '0tiG9uQEuyT4OqvLl5Z6_tJ2P58dyxin_zyOzQBLhuY',
                            'page'              => 'packageB/pages/order-detail/order-detail?orderNo=' . $orderInfo['sub_order_no'],
                            'miniprogram_state' => env('ORDERS.MINIPROGRAM_STATE'),
                            "topcolor"          => "#FF0000",
                            'data'              => [
                                'amount3'           => [
                                    'value' => $orderInfo['payment_amount'] . '元',
                                    'color' => '#173177'
                                ],
                                'thing2'            => [
                                    'value' => truncate_utf8_string($orderInfo['title'], 15),
                                    'color' => '#173177'
                                ],
                                'character_string1' => [
                                    'value' => $orderInfo['sub_order_no'],
                                    'color' => '#173177'
                                ]
                            ]
                        );
                        curlRequest('https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=' . $getToken['access_token'], json_encode($pushData));
                    }
                    $single = array(
                        'is_push'      => 1,
                        'uid'          => $orderInfo['uid'],
                        'title'        => "物流通知",
                        // 'content'      => "物流通知 您的" . isset($orderInfo['title']) ? $orderInfo['title'] : '商品' . "已经发货，物流单号是：" . $waybillNo . "，点击查看物流信息",
                        'content'      => "您购买的商品已发货，点击查看！",
                        'data_type'    => 20,
                        'data'         => [
                            'title'        => "物流通知",
                            // 'content'      => "物流通知 您的" . isset($orderInfo['title']) ? $orderInfo['title'] : '商品' . "已经发货，物流单号是：" . $waybillNo . "，点击查看物流信息",
                            'content'      => "您购买的商品已发货，点击查看！",
                            'cover'        => isset($orderInfo['banner_img']) ? $orderInfo['banner_img'] : '',
                            'logisticCode' => $waybillNo,
                            'expressType'  => $params['data']['logistics_id']
                        ],
                        'label'        => "LogisticsDetails",
                        'custom_param' => ['cover' => isset($orderInfo['banner_img']) ? $orderInfo['banner_img'] : '', 'logisticCode' => $waybillNo, 'expressType' => $params['data']['logistics_id']]
                    );
                    httpPostString(env('ITEM.APPPUSH_URL') . '/apppush/v3/push/single', json_encode($single, JSON_UNESCAPED_UNICODE));
                }
            }
        } else if ($params['type'] == 3) {//退货单完成回调通知
            $good = [];
            foreach ($params['data']['goods'] as $items) {
                if ($items['defectueux'] > 0 || $items['normal_num'] != $items['number']) {
                    $good[] = $items['short_code'] . ':申请退回 ' . $items['number'] . ',良品 ' . $items['normal_num'] . ',次品 ' . $items['defectueux'];
                }
            }
            if (empty($good)) {
                $str = '订单退回成功，无破损';
                //无破损退还库存（待完善，现有换库存接口扩展产品ID字段，部分退货或有次品的情况传入产品ID进行库存退还，同时根据产品库存修正套餐库存；退还商品无破损且整个套餐退回，根据下单扣的库存原样退还）

            } else {
                $str = implode(';', $good);
            }
            //添加订单备注
            $remarks = array(
                'sub_order_no' => $params['data']['orderno'],
                'admin_id'     => 0,
                'remarks'      => $str,
                'created_time' => time(),
            );
            Db::name('order_remarks')->insert($remarks);

            // 查询销售退货单
            // $sales_return = Db::name('sales_return')
            //     ->where([
            //         ['sub_order_no', '=', $params['data']['orderno']],
            //         ['dingtalk_status', '=', 1],
            //         ['push_wms_status', '>', 0]
            //     ])
            //     ->findOrEmpty();
            // if (!empty($sales_return)) {
            //     //添加订单备注
            //     $remarks = array(
            //         'sub_order_no' => $sales_return['bill_no'],
            //         'admin_id'     => 0,
            //         'remarks'      => '萌牙退货入库单，上架成功',
            //         'created_time' => time(),
            //     );
            //     Db::name('order_remarks')->insert($remarks);

            //     // 更改订单状态
            //     Db::name('sales_return')->where('id', $sales_return['id'])->update([
            //         'dingtalk_status' => 2,
            //         'update_time'     => time(),
            //         'approval_time'   => time(),
            //         'push_erp_time'   => time(),
            //     ]);
            //     // 自动推送erp
            //     (new \app\service\SalesReturn())->SalesReturnPushErp([
            //         'operator_id'   =>  0,
            //         'operator_name' => '系统',
            //         'bill_no'       => $sales_return['bill_no'],
            //     ]);
            // }

        } else if ($params['type'] == 4) {//入库单完成回调通知

        }
        return true;
    }

    /**
     * Description:推送萌牙结果处理
     * Author: zrc
     * Date: 2022/4/21
     * Time: 10:20
     * @param $requestParams
     * @return bool
     */
    public function pushWmsResultDeal($requestParams)
    {
        $params = $requestParams;
        //修改订单推送萌牙状态
        $order_type      = config('config')['order_type'];//订单频道获取
        $push_wms_status = Db::name($order_type[intval($params['order_type'])]['table'])->where(['sub_order_no' => $params['sub_order_no']])->value('push_wms_status');
        if (!in_array($push_wms_status, [1, 3])) {
            Db::name($order_type[intval($params['order_type'])]['table'])->where(['sub_order_no' => $params['sub_order_no']])->update(['push_wms_status' => $params['push_wms_status'], 'update_time' => time()]);
        }

        if ($params['order_type'] == 7 && strpos($params['msg'], "Operation timed out after") !== false) {
            //订单推送萌牙因【超时失败】时，需要在【三方订单推送萌牙失败】群里提示
            \Curl::sendWechatSender([
                'msg'          => "{$params['sub_order_no']} 推送萌芽失败: {$params['msg']}",
                'at'           => '',
                'access_token' => '5b9cd1ab-30a4-4e6a-b676-701487dc7617',
            ]);
        }

        //添加订单备注
        $remarks      = array(
            'sub_order_no' => $params['sub_order_no'],
            'order_type'   => $params['order_type'],
            'content'      => $params['msg'],
            'admin_id'     => isset($params['operator']) ? $params['operator'] : 0
        );
        $orderService = new OrderService();
        $orderService->createRemarks($remarks);
        return true;
    }
}