<?php
declare (strict_types = 1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;

/**
 * 三方订单每天10：00到18：00自动推送（30分钟执行一次）
 * Class AutomaticGoodsReceipt
 * @package app\command
 */
class TripartiteOrderPushWMSCommand extends Command
{
    protected $service;

    protected function configure()
    {
        // 指令配置
        $this->setName('TripartiteOrderPushWMSCommand')
            ->setDescription('the TripartiteOrderPushWMSCommand command');
    }

    protected function execute(Input $input, Output $output)
    {
    	// 指令输出
        $this->init();
        $this->service->exec();
    }

    protected function init(){
      $this->service = new \app\service\command\TripartiteOrderPushWMSCommand();
    }
}
