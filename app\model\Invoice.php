<?php

namespace app\model;

use think\Model;

class Invoice extends Model
{
    protected $defaultSoftDelete = 0;
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    protected $json = ['detail_json','goinvoicejson'];


    public function getApproverTimeAttr($value,$data)
    {
        return date("Y-m-d H:i:s",$value);
    }

    public function getGoinvoicejsonAttr($value,$data)
    {
        return (array)$value;
    }
}