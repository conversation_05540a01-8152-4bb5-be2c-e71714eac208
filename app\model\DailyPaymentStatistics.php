<?php

namespace app\model;

use think\Model;

class DailyPaymentStatistics extends Model
{
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_time';
    protected $updateTime = 'updated_time';
    
    // 收款商户ID映射到公司
    const MERCHANT_COMPANY_MAP = [
        1 => '重庆云酒佰酿电子商务有限公司',
        2 => '佰酿云酒（重庆）科技有限公司',
        5 => '渝中区微醺酒业商行',
        10 => '海南一花一世界科技有限公司'
    ];

    // 公司编码映射（保持向后兼容）
    const COMPANY_MAP = [
        '001' => '佰酿云酒（重庆）科技有限公司',
        '002' => '重庆云酒佰酿电子商务有限公司',
        '008' => '渝中区微醺酒业商行',
        '032' => '海南一花一世界科技有限公司'
    ];
    
    /**
     * 根据收款商户ID获取公司名称
     * @param int $merchantId
     * @return string
     */
    public static function getCompanyNameByMerchantId($merchantId)
    {
        return self::MERCHANT_COMPANY_MAP[$merchantId] ?? '';
    }

    /**
     * 验证收款商户ID是否有效
     * @param int $merchantId
     * @return bool
     */
    public static function isValidMerchantId($merchantId)
    {
        return isset(self::MERCHANT_COMPANY_MAP[$merchantId]);
    }

    /**
     * 获取所有有效的收款商户ID
     * @return array
     */
    public static function getAllMerchantIds()
    {
        return array_keys(self::MERCHANT_COMPANY_MAP);
    }

    /**
     * 获取公司名称（保持向后兼容）
     * @param string $companyCode
     * @return string
     */
    public static function getCompanyName($companyCode)
    {
        return self::COMPANY_MAP[$companyCode] ?? '';
    }

    /**
     * 验证公司编码是否有效（保持向后兼容）
     * @param string $companyCode
     * @return bool
     */
    public static function isValidCompanyCode($companyCode)
    {
        return isset(self::COMPANY_MAP[$companyCode]);
    }

    /**
     * 获取所有公司编码（保持向后兼容）
     * @return array
     */
    public static function getAllCompanyCodes()
    {
        return array_keys(self::COMPANY_MAP);
    }
}
