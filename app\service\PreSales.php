<?php


namespace app\service;


use app\BaseService;
use app\service\elasticsearch\ElasticSearchService;
use think\facade\Db;

class PreSales extends BaseService
{
    /**
     * Description:售前客户列表
     * Author: zrc
     * Date: 2023/7/26
     * Time: 18:12
     * @param $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function adminList($params)
    {
        $result = [];
        $offset = ($params['page'] - 1) * $params['limit'];
        $where  = [];
        if (!empty($params['admin_id'])) {
            $where[] = ['admin_id', '=', $params['admin_id']];
        }
        if (!empty($params['realname'])) {
            $where[] = ['realname', '=', $params['realname']];
        }
        if (!empty($params['status'])) {
            $where[] = ['status', '=', $params['status']];
        }
        $totalNum        = Db::name('presales_admin')->where($where)->count();
        $lists           = Db::name('presales_admin')->where($where)->limit($offset, $params['limit'])->order('id desc')->select()->toArray();
        $result['list']  = $lists;
        $result['total'] = $totalNum;
        return $result;
    }

    /**
     * Description:添加/修改售前客户
     * Author: zrc
     * Date: 2023/7/27
     * Time: 9:54
     * @param $params
     * @return int|string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function addUpdateAdmin($params)
    {
        $result = [];
        if (!empty($params['id'])) {//修改
            $presalesAdmin = Db::name('presales_admin')->where(['id' => $params['id']])->find();
            if (!empty($presalesAdmin)) $this->throwError('未获取到售前客服信息');
            $updateData                = [];
            $updateData['update_time'] = time();
            if (!empty($params['admin_id'])) {
                $updateData['admin_id'] = $params['admin_id'];
            }
            if (!empty($params['realname'])) {
                $updateData['realname'] = $params['realname'];
            }
            if (!empty($params['userid'])) {
                $updateData['userid'] = $params['userid'];
            }
            if (!empty($params['status'])) {
                $updateData['status'] = $params['status'] == 1 ? 1 : 2;
            }
            $result = Db::name('presales_admin')->where(['id' => $params['id']])->update($updateData);
        } else {//添加
            $isset = Db::name('presales_admin')->where(['admin_id' => $params['admin_id']])->find();
            if ($isset) $this->throwError('请勿重复添加售前客服');
            $insertData = array(
                'admin_id'        => $params['admin_id'],
                'realname'        => $params['realname'],
                'userid'          => $params['userid'],
                'status'          => 1,
                'allocation_nums' => 0,
                'created_time'    => time(),
            );
            $result     = Db::name('presales_admin')->insert($insertData);
        }
        return $result;
    }

    /**
     * Description:分配客户
     * Author: zrc
     * Date: 2023/7/27
     * Time: 13:28
     * @param $params
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function allocationCustom($params)
    {
        $time          = time();
        $presalesAdmin = Db::name('presales_admin')->field('id,admin_id,allocation_nums')->where(['status' => 1])->order('allocation_nums asc')->select()->toArray();
        if (empty($presalesAdmin)) $this->throwError('未获取到售前客服');
        Db::startTrans();
        try {
            foreach ($params['user_info'] as &$val) {
                $insertData             = array(
                    'type'           => $params['type'],
                    'user_id'        => $val['uid'],
                    'binding_status' => 1,
                    'created_time'   => $time,
                    'register_time'  => $val['created_time'],
                    'nickname'       => $val['nickname'],
                    'telephone'      => $val['telephone'],
                    'reg_from'       => $val['reg_from'],
                    'region'         => $val['region'],
                    'export_time'    => isset($val['export_time']) ? $val['export_time'] : 0,
                    'consignee'      => isset($val['consignee']) ? $val['consignee'] : '',
                    'order_nums'     => isset($val['order_nums']) ? $val['order_nums'] : 0,
                    'last_buy_time'  => isset($val['last_buy_time']) ? $val['last_buy_time'] : 0,
                    'flash_moeny'    => isset($val['flash_moeny']) ? $val['flash_moeny'] : 0,
                    'second_money'   => isset($val['second_money']) ? $val['second_money'] : 0,
                    'cross_money'    => isset($val['cross_money']) ? $val['cross_money'] : 0,
                    'tail_money'     => isset($val['tail_money']) ? $val['tail_money'] : 0,
                    'total_money'    => isset($val['total_money']) ? $val['total_money'] : 0,
                    'work_nums'      => isset($val['work_nums']) ? $val['work_nums'] : 0,
                    'tripartite_source'       => $val['tripartite_source'] ?? '', //三方来源
                );
                $insertData['admin_id'] = $presalesAdmin[0]['admin_id'];
                Db::name('presales_allocation_user')->insert($insertData);
                Db::name('presales_admin')->where(['id' => $presalesAdmin[0]['id']])->inc('allocation_nums')->update();
                $presalesAdmin[0]['allocation_nums'] = $presalesAdmin[0]['allocation_nums'] + 1;
                $allocation_nums                     = array_column($presalesAdmin, 'allocation_nums');
                array_multisort($allocation_nums, SORT_ASC, $presalesAdmin);
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->throwError($e->getMessage());
        }
        return $time;
    }

    /**
     * Description:发送分配excel到企微
     * Author: zrc
     * Date: 2023/7/27
     * Time: 17:19
     * @param $params
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function sendExcel($params)
    {
        $adminExcel         = [];
        $allocationUserInfo = Db::name('presales_allocation_user')->where(['created_time' => $params['created_time']])->order('id asc')->select()->toArray();
        foreach ($allocationUserInfo as &$val) {
            if ($params['type'] == 1) {
                $adminExcel[$val['admin_id']][] = array(
                    'created_time' => date('Y-m-d', $val['register_time']),
                    'nickname'     => $val['nickname'],
                    'user_id'      => $val['user_id'],
                    'telephone'    => $val['telephone'],
                    'reg_from'     => $val['reg_from'],
                    'region'       => $val['region'],
                    'tripartite_source'       => $val['tripartite_source'],
                );
            } else {
                $adminExcel[$val['admin_id']][] = array(
                    'created_time'  => date('Y-m-d', $val['register_time']),
                    'nickname'      => $val['nickname'],
                    'user_id'       => $val['user_id'],
                    'telephone'     => $val['telephone'],
                    'reg_from'      => $val['reg_from'],
                    'region'        => $val['region'],
                    'export_time'   => date('Y-m-d', $val['export_time']),
                    'consignee'     => $val['consignee'],
                    'order_nums'    => $val['order_nums'],
                    'last_buy_time' => date('Y-m-d', $val['last_buy_time']),
                    'flash_moeny'   => $val['flash_moeny'],
                    'second_money'  => $val['second_money'],
                    'cross_money'   => $val['cross_money'],
                    'tail_money'    => $val['tail_money'],
                    'total_money'   => $val['total_money'],
                    'work_nums'     => $val['work_nums'],
                );
            }
        }
        if ($params['type'] == 1) {
            $filename = "新客户每日分配明细" . date('Y-m-d', strtotime('-1 day')) . "日";
            $header   = array(
                array('column' => 'created_time', 'name' => '注册时间', 'width' => 15),
                array('column' => 'nickname', 'name' => '标识', 'width' => 15),
                array('column' => 'user_id', 'name' => '用户ID', 'width' => 15),
                array('column' => 'telephone', 'name' => '注册手机号', 'width' => 15),
                array('column' => 'reg_from', 'name' => '注册来源', 'width' => 15),
                array('column' => 'region', 'name' => '收货城市', 'width' => 15),
                array('column' => 'tripartite_source', 'name' => '三方来源', 'width' => 15),
            );
        } else {
            $filename = "沉默客户每周分配明细" . date('Y-m-d', time()) . "日";
            $header   = array(
                array('column' => 'export_time', 'name' => '导出时间', 'width' => 15),
                array('column' => 'nickname', 'name' => '标识', 'width' => 15),
                array('column' => 'created_time', 'name' => '注册时间', 'width' => 15),
                array('column' => 'user_id', 'name' => '用户ID', 'width' => 15),
                array('column' => 'consignee', 'name' => '收货人', 'width' => 15),
                array('column' => 'telephone', 'name' => '注册手机号', 'width' => 15),
                array('column' => 'reg_from', 'name' => '注册来源', 'width' => 15),
                array('column' => 'region', 'name' => '收货城市', 'width' => 15),
                array('column' => 'order_nums', 'name' => '有效订单', 'width' => 15),
                array('column' => 'last_buy_time', 'name' => '最后一次购买时间', 'width' => 15),
                array('column' => 'flash_moeny', 'name' => '闪购', 'width' => 15),
                array('column' => 'second_money', 'name' => '秒发', 'width' => 15),
                array('column' => 'cross_money', 'name' => '跨境', 'width' => 15),
                array('column' => 'tail_money', 'name' => '尾货', 'width' => 15),
                array('column' => 'total_money', 'name' => '购买总额', 'width' => 15),
                array('column' => 'work_nums', 'name' => '工单数量', 'width' => 15),
            );
        }
        foreach ($adminExcel as $key => $value) {
            $userid    = Db::name('presales_admin')->where(['admin_id' => $key])->value('userid');
            $uploadUrl = exportSheelExcel($value, $header, $filename);
            $file      = app()->getRootPath() . "public/storage/" . $uploadUrl;


            $media_id  = weixinUpload($file, $filename . '.xlsx');
            unlink($file);
            $msgData = array(
                'content' => $media_id,
                'userid'  => $userid,
                'msgtype' => 'file',
                'agentid' => 0,
            );
            httpPostString(env('ITEM.WECHART_URL') . '/wechat/v3/wecom/app/send', json_encode($msgData));
        }
    }

    /**
     * Description:售前数据统计
     * Author: zrc
     * Date: 2023/8/1
     * Time: 10:36
     * @param $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function preSalesDataStats($params)
    {
        //默认实时
        $stime = strtotime(date('Y-m-1', time()));
        $etime = time();
        if (isset($params['is_snapshot'])) {//快照定时任务访问
            $stime = $params['stime'];
            $etime = $params['etime'];
        } else if (!empty($params['stime'])) {//历史快照读取
            $stime = strtotime(date('Y-m-1', strtotime($params['stime'])));
            $etime = strtotime(date('Y-m-t 23:59:59', strtotime($params['stime'])));
            //历史数据读取快照数据返回
            if ($stime < strtotime(date('Y-m-1', time()))) {
                $result        = [];
                $snapshot_json = Db::name('presales_data_snapshot')->where(['date' => date('Y-m-1', strtotime($params['stime']))])->value('snapshot_json');
                if (!empty($snapshot_json)) {
                    $result = json_decode($snapshot_json, true);
                }
                return $result;
            }
        }
        $presalesAdmin          = Db::name('presales_admin')->field('admin_id,realname')->where(['status' => 1])->select()->toArray();
        $time                   = strtotime('-3 month');
        $es                     = new ElasticSearchService();
        $new_transform_total    = 0;
        $new_first_total        = 0;
        $new_second_total       = 0;
        $new_third_total        = 0;
        $new_total_total        = 0;
        $silent_transform_total = 0;
        $silent_first_total     = 0;
        $silent_second_total    = 0;
        $silent_third_total     = 0;
        $silent_total_total     = 0;
        $total_money_total      = 0;
        $total_order_total      = [];
        foreach ($presalesAdmin as &$val) {
            $total_money = 0;
            $total_order = [];
            //新用户单量统计
            $newUser         = Db::name('presales_allocation_user')
                ->field('user_id')
                ->where([['admin_id', '=', $val['admin_id']], ['type', '=', 1], ['created_time', '>', $time]])
                ->select()->toArray();
            $newUserArr      = array_column($newUser, 'user_id');
            $newGroupedArray = array_chunk($newUserArr, 500);
            $newEsData       = [];
            foreach ($newGroupedArray as &$va) {
                $newEsArr     = array(
                    'index'  => ['orders'],
                    'match'  => [['refund_status' => 0]],
                    'terms'  => [['uid' => $va]],
                    'range'  => [['payment_time' => ['gte' => date('Y-m-d H:i:s', $stime)]], ['payment_time' => ['lte' => date('Y-m-d H:i:s', $etime)]], ['sub_order_status' => ['gt' => 0]], ['sub_order_status' => ['lt' => 4]]],
                    'source' => ['sub_order_no', 'payment_amount', 'order_type', 'uid', 'created_time'],
                    'sort'   => [['created_time' => 'asc']],
                    'limit'  => 10000
                );
                $newEsDataSub = $es->getDocumentList($newEsArr);
                $newEsData    = array_merge($newEsDataSub['data'], $newEsData);
            }
            $new_created_time = array_column($newEsData, 'created_time');
            array_multisort($new_created_time, SORT_ASC, $newEsData);
            $newOrderMoney = [];
            foreach ($newEsData as $kk => $vv) {
                if (!isset($newOrderMoney[$vv['uid']])) {
                    $newOrderMoney[$vv['uid']]['total_money']        = $vv['payment_amount'];
                    $newOrderMoney[$vv['uid']]['sub_order_no_arr'][] = $vv['sub_order_no'];
                } else if (count($newOrderMoney[$vv['uid']]['sub_order_no_arr']) < 3) {
                    $newOrderMoney[$vv['uid']]['total_money']        += $vv['payment_amount'];
                    $newOrderMoney[$vv['uid']]['sub_order_no_arr'][] = $vv['sub_order_no'];
                }
            }
            $orderNewUserArr      = array_column($newEsData, 'uid');
            $val['new_transform'] = count(array_unique($orderNewUserArr));
            $newCounts            = array_count_values($orderNewUserArr);
            $new_first            = 0;
            $new_second           = 0;
            $new_third            = 0;
            foreach ($newCounts as $k => $v) {
                if ($v == 1) {
                    $new_first++;
                    $total_money += $newOrderMoney[$k]['total_money'];
                    $total_order = array_merge($total_order, $newOrderMoney[$k]['sub_order_no_arr']);
                }
                if ($v == 2) {
                    $new_first++;
                    $new_second++;
                    $total_money += $newOrderMoney[$k]['total_money'];
                    $total_order = array_merge($total_order, $newOrderMoney[$k]['sub_order_no_arr']);
                }
                if ($v >= 3) {
                    $new_first++;
                    $new_second++;
                    $new_third++;
                    $total_money += $newOrderMoney[$k]['total_money'];
                    $total_order = array_merge($total_order, $newOrderMoney[$k]['sub_order_no_arr']);
                }
            }
            $val['new_first']    = $new_first;
            $val['new_second']   = $new_second;
            $val['new_third']    = $new_third;
            $val['new_total']    = $new_first + $new_second + $new_third;
            $new_transform_total += $val['new_transform'];
            $new_first_total     += $new_first;
            $new_second_total    += $new_second;
            $new_third_total     += $new_third;
            $new_total_total     += $new_first + $new_second + $new_third;
            //沉默用户单量统计
            $silentUser         = Db::name('presales_allocation_user')
                ->field('user_id')
                ->where([['admin_id', '=', $val['admin_id']], ['type', '=', 2], ['created_time', '>', $time]])
                ->select()->toArray();
            $silentUserArr      = array_column($silentUser, 'user_id');
            $silentGroupedArray = array_chunk($silentUserArr, 500);
            $silentEsData       = [];
            foreach ($silentGroupedArray as &$va) {
                $silentEsArr     = array(
                    'index'  => ['orders'],
                    'match'  => [['refund_status' => 0]],
                    'terms'  => [['uid' => $va]],
                    'range'  => [['payment_time' => ['gte' => date('Y-m-d H:i:s', $stime)]], ['payment_time' => ['lte' => date('Y-m-d H:i:s', $etime)]], ['sub_order_status' => ['gt' => 0]], ['sub_order_status' => ['lt' => 4]]],
                    'source' => ['sub_order_no', 'payment_amount', 'order_type', 'uid', 'created_time'],
                    'sort'   => [['created_time' => 'asc']],
                    'limit'  => 10000
                );
                $silentEsDataSub = $es->getDocumentList($silentEsArr);
                $silentEsData    = array_merge($silentEsDataSub['data'], $silentEsData);
            }
            $silent_created_time = array_column($silentEsData, 'created_time');
            array_multisort($silent_created_time, SORT_ASC, $silentEsData);
            $silentOrderMoney = [];
            foreach ($silentEsData as $kk => $vv) {
                if (!isset($silentOrderMoney[$vv['uid']])) {
                    $silentOrderMoney[$vv['uid']]['total_money']        = $vv['payment_amount'];
                    $silentOrderMoney[$vv['uid']]['sub_order_no_arr'][] = $vv['sub_order_no'];
                } else if (count($silentOrderMoney[$vv['uid']]['sub_order_no_arr']) < 3) {
                    $silentOrderMoney[$vv['uid']]['total_money']        += $vv['payment_amount'];
                    $silentOrderMoney[$vv['uid']]['sub_order_no_arr'][] = $vv['sub_order_no'];
                }
            }
            $orderSilentUserArr      = array_column($silentEsData, 'uid');
            $val['silent_transform'] = count(array_unique($orderSilentUserArr));
            $silentCounts            = array_count_values($orderSilentUserArr);
            $silent_first            = 0;
            $silent_second           = 0;
            $silent_third            = 0;
            foreach ($silentCounts as $k => $v) {
                if ($v == 1) {
                    $silent_first++;
                    $total_money += $silentOrderMoney[$k]['total_money'];
                    $total_order = array_merge($total_order, $silentOrderMoney[$k]['sub_order_no_arr']);
                }
                if ($v == 2) {
                    $silent_first++;
                    $silent_second++;
                    $total_money += $silentOrderMoney[$k]['total_money'];
                    $total_order = array_merge($total_order, $silentOrderMoney[$k]['sub_order_no_arr']);
                }
                if ($v >= 3) {
                    $silent_first++;
                    $silent_second++;
                    $silent_third++;
                    $total_money += $silentOrderMoney[$k]['total_money'];
                    $total_order = array_merge($total_order, $silentOrderMoney[$k]['sub_order_no_arr']);
                }
            }
            $val['silent_first']  = $silent_first;
            $val['silent_second'] = $silent_second;
            $val['silent_third']  = $silent_third;
            $val['silent_total']  = $silent_first + $silent_second + $silent_third;
            //分享下单记录金额计算
            $val['share_money'] = 0;
            $where              = array(
                ['source_user', '=', $val['admin_id']],
                ['created_time', '>=', $stime],
                ['created_time', '<=', $etime],
            );
            $share_log          = Db::name('share_order_log')->field('sub_order_no')->where($where)->select()->toArray();
            if (!empty($share_log)) {
                $share_order_no     = array_column($share_log, 'sub_order_no');
                $share_order_no_arr = array_diff($share_order_no, $total_order);
                if (!empty($share_order_no_arr)) {
                    $shareGroupedArray = array_chunk($share_order_no_arr, 300);
                    $shareData         = [];
                    foreach ($shareGroupedArray as &$va) {
                        $shareEsArr   = array(
                            'index'  => ['orders'],
                            'match'  => [['refund_status' => 0]],
                            'terms'  => [['sub_order_no.keyword' => $va]],
                            'range'  => [['payment_time' => ['gte' => date('Y-m-d H:i:s', $stime)]], ['payment_time' => ['lte' => date('Y-m-d H:i:s', $etime)]], ['sub_order_status' => ['gt' => 0]], ['sub_order_status' => ['lt' => 4]]],
                            'source' => ['sub_order_no', 'payment_amount', 'order_type', 'uid'],
                            'sort'   => [['created_time' => 'asc']],
                            'limit'  => 10000
                        );
                        $shareDataSub = $es->getDocumentList($shareEsArr);
                        $shareData    = array_merge($shareDataSub['data'], $shareData);
                    }
                    foreach ($shareData as &$va) {
                        $val['share_money'] += $va['payment_amount'];
                    }
                    $val['share_money'] = intval($val['share_money']);
                    $total_money        = $total_money + $val['share_money'];
                }
            }
            $val['total_money']     = intval($total_money);
            $val['total_order']     = $total_order;
            $silent_transform_total += $val['silent_transform'];
            $silent_first_total     += $silent_first;
            $silent_second_total    += $silent_second;
            $silent_third_total     += $silent_third;
            $silent_total_total     += $silent_first + $silent_second + $silent_third;
            $total_money_total      += $total_money;
            //$total_order_total      = array_merge($total_order_total, $total_order);
        }
        foreach ($presalesAdmin as &$vvv) {
            $vvv['new_proportion']    = $new_total_total > 0 ? round($vvv['new_total'] / $new_total_total * 100, 2) . '%' : '0%';
            $vvv['silent_proportion'] = $silent_total_total > 0 ? round($vvv['silent_total'] / $silent_total_total * 100, 2) . '%' : '0%';
            $vvv['money_proportion']  = $total_money_total > 0 ? round($vvv['total_money'] / $total_money_total * 100, 2) . '%' : '0%';
        }
        $presalesAdmin[] = array(
            'new_transform'     => $new_transform_total,
            'new_first'         => $new_first_total,
            'new_second'        => $new_second_total,
            'new_third'         => $new_third_total,
            'new_total'         => $new_total_total,
            'new_proportion'    => '-',
            'silent_transform'  => $silent_transform_total,
            'silent_first'      => $silent_first_total,
            'silent_second'     => $silent_second_total,
            'silent_third'      => $silent_third_total,
            'silent_total'      => $silent_total_total,
            'silent_proportion' => '-',
            'total_money'       => intval($total_money_total),
            //'total_order_total' => $total_order_total,
            'money_proportion'  => '-'
        );
        return $presalesAdmin;
    }

    /**
     * Description:记录售前分享标识
     * Author: zrc
     * Date: 2023/8/23
     * Time: 9:52
     * @param $params
     * @return bool
     */
    public function recordPreSalesShare($params)
    {
        $redis = new \Redis();
        $redis->connect(env('CACHE.HOST'), env('CACHE.PORT'));
        $redis->auth(env('CACHE.PASSWORD'));
        $redis->select(8);
        $redis->set('pre_sales_' . $params['uid'] . '_' . $params['period'], $params['source_user'], 86400 * 2);
        return true;
    }

    /**
     * Description:点击数值显示统计具体信息
     * Author: zrc
     * Date: 2023/10/19
     * Time: 14:42
     * @param $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function clickNumsGetInfo($params)
    {
        //默认实时
        $stime = strtotime(date('Y-m-1', time()));
        $etime = time();
        $time  = strtotime('-3 month');
        if (!empty($params['stime']) && strtotime(date('Y-m-t 23:59:59', strtotime($params['stime']))) < time()) {
            $stime = strtotime(date('Y-m-1', strtotime($params['stime'])));
            $etime = strtotime(date('Y-m-t 23:59:59', strtotime($params['stime'])));
            $time  = strtotime('-3 month', $etime);
        }
        if (strtotime($params['stime']) > time()) {
            $result['list']  = [];
            $result['total'] = 0;
            return $result;
        }
        $es     = new ElasticSearchService();
        $result = [];
        $type   = 1;
        if (in_array($params['type'], ['new_transform', 'new_first', 'new_second', 'new_third'])) $type = 1;
        if (in_array($params['type'], ['silent_transform', 'silent_first', 'silent_second', 'silent_third'])) $type = 2;
        $newUser         = Db::name('presales_allocation_user')
            ->field('user_id')
            ->where([['admin_id', '=', $params['admin_id']], ['type', '=', $type], ['created_time', '>', $time], ['created_time', '<=', $etime]])
            ->select()->toArray();
        $newUserArr      = array_column($newUser, 'user_id');
        $newGroupedArray = array_chunk($newUserArr, 500);
        $newEsData       = [];
        foreach ($newGroupedArray as &$va) {
            $newEsArr     = array(
                'index'  => ['orders'],
                'match'  => [['refund_status' => 0]],
                'terms'  => [['uid' => $va]],
                'range'  => [['payment_time' => ['gte' => date('Y-m-d H:i:s', $stime)]], ['payment_time' => ['lte' => date('Y-m-d H:i:s', $etime)]], ['sub_order_status' => ['gt' => 0]], ['sub_order_status' => ['lt' => 4]]],
                'source' => ['sub_order_no', 'payment_amount', 'order_type', 'uid', 'created_time', 'nickname', 'period', 'title'],
                'sort'   => [['created_time' => 'asc']],
                'limit'  => 10000
            );
            $newEsDataSub = $es->getDocumentList($newEsArr);
            $newEsData    = array_merge($newEsDataSub['data'], $newEsData);
        }
        $new_created_time = array_column($newEsData, 'created_time');
        array_multisort($new_created_time, SORT_ASC, $newEsData);
        $newOrderMoney = [];
        foreach ($newEsData as $kk => $vv) {
            if (!isset($newOrderMoney[$vv['uid']])) {
                $newOrderMoney[$vv['uid']]['sub_order_no_arr'][] = array(
                    'sub_order_no' => $vv['sub_order_no'],
                    'nickname'     => $vv['nickname'],
                    'period'       => $vv['period'],
                    'title'        => $vv['title'],
                );
            } else if (count($newOrderMoney[$vv['uid']]['sub_order_no_arr']) < 3) {
                $newOrderMoney[$vv['uid']]['sub_order_no_arr'][] = array(
                    'sub_order_no' => $vv['sub_order_no'],
                    'nickname'     => $vv['nickname'],
                    'period'       => $vv['period'],
                    'title'        => $vv['title'],
                );
            }
        }
        $orderNewUserArr = array_column($newEsData, 'uid');
        if (in_array($params['type'], ['new_transform', 'silent_transform'])) {
            $userInfo = Db::table('vh_user.vh_user')->field('uid,nickname,telephone')->where([['uid', 'in', array_unique($orderNewUserArr)]])->select()->toArray();
            $encrypt  = cryptionDeal(2, array_unique(array_column($userInfo, 'telephone')), '15736175219', '宗仁川');
            $list     = [];
            foreach ($userInfo as $val) {
                $list[] = array(
                    'uid'       => $val['uid'],
                    'nickname'  => $val['nickname'],
                    'telephone' => isset($encrypt[$val['telephone']]) ? $encrypt[$val['telephone']] : '',
                );
            }
            $offset          = ($params['page'] - 1) * $params['limit'];
            $result['list']  = array_slice($list, $offset, $params['limit']);
            $result['total'] = count($list);
            return $result;
        }
        $newCounts  = array_count_values($orderNewUserArr);
        $new_first  = [];
        $new_second = [];
        $new_third  = [];
        foreach ($newCounts as $k => $v) {
            if ($v == 1) {
                $new_first[] = $newOrderMoney[$k]['sub_order_no_arr'][0];
            }
            if ($v == 2) {
                $new_first[]  = $newOrderMoney[$k]['sub_order_no_arr'][0];
                $new_second[] = $newOrderMoney[$k]['sub_order_no_arr'][1];
            }
            if ($v >= 3) {
                $new_first[]  = $newOrderMoney[$k]['sub_order_no_arr'][0];
                $new_second[] = $newOrderMoney[$k]['sub_order_no_arr'][1];
                $new_third[]  = $newOrderMoney[$k]['sub_order_no_arr'][2];
            }
        }
        $list = [];
        if (in_array($params['type'], ['new_first', 'silent_first'])) $list = $new_first;
        if (in_array($params['type'], ['new_second', 'silent_second'])) $list = $new_second;
        if (in_array($params['type'], ['new_third', 'silent_third'])) $list = $new_third;
        $offset          = ($params['page'] - 1) * $params['limit'];
        $result['list']  = array_slice($list, $offset, $params['limit']);
        $result['total'] = count($list);
        return $result;
    }
}