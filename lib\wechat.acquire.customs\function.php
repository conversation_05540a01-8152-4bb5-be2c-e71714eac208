<?php
/**
 * Created by PhpStorm.
 * User: oldSmokeGun
 * Date: 2019/7/6
 * Time: 15:42
 */

/**
 * 微信支付单报关
 * @param $outTradeNo 商户订单号
 * @param $transactionId 微信支付订单号
 * @param $store_ids 仓库编码
 * @return mixed
 * @throws \Think\Exception
 */
function custom_declare($outTradeNo, $transactionId)
{
    $config = require_once("config.php");

    // 构造要请求的参数数组
    $parameter = array(
        "appid"             => $config['appid'],
        "mch_id"            => $config['mch_id'],
        "out_trade_no"      => $outTradeNo,
        "transaction_id"    => $transactionId,
        "customs"           => $config['customs'],
        "mch_customs_no"    => $config['mch_customs_no']
    );

    ksort($parameter);

    $sign = strtoupper(md5(http_build_query($parameter) . '&key=' . $config['key']));

    $parameter['sign'] = $sign;

    $response = curl_post($config['declare_url'], $parameter);

    return $response;
}

/**
 * 微信支付单报关查询
 * @param $orderNo 商户订单号
 * @param $store_ids 仓库信息
 * @return mixed
 * @throws \Think\Exception
 */
function custom_declare_query($orderNo)
{
    $config = require_once("config.php");

    // 构造要请求的参数数组
    $parameter = array(
        "sign_type"         => 'MD5',
        "appid"             => $config['appid'],
        "mch_id"            => $config['mch_id'],
        "out_trade_no"      => $orderNo,
        "customs"           => $config['customs']
    );

    ksort($parameter);

    $sign = strtoupper(md5(http_build_query($parameter) . '&key=' . $config['key']));

    $parameter['sign'] = $sign;

    $response = curl_post($config['declare_query_url'], $parameter);

    return $response;
}

/**
 * 微信支付单重推
 * @return mixed
 * @throws \Think\Exception
 */
function custom_redeclare($orderNo)
{
    $config = require_once("config.php");

    // 构造要请求的参数数组
    $parameter = array(
        "sign_type"         => 'MD5',
        "appid"             => $config['appid'],
        "mch_id"            => $config['mch_id'],
        "out_trade_no"      => $orderNo,
        "customs"           => $config['customs'],
        "mch_customs_no"    => $config['mch_customs_no']
    );

    ksort($parameter);

    $sign = strtoupper(md5(http_build_query($parameter) . '&key=' . $config['key']));

    $parameter['sign'] = $sign;

    $response = curl_post($config['redeclare_url'], $parameter);

    return $response;
}

function curl_post($url, $data, $dataType = 'xml')
{
    $curl = curl_init();

    curl_setopt($curl, CURLOPT_TIMEOUT, 30);

    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER,false);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST,false);

    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER,true);

    if ( $dataType == 'xml' )
    {
        $xml = "<xml>";
        foreach ( $data as $key => $val )
        {
            if (is_numeric($val)){
                $xml .= "<".$key.">".$val."</".$key.">";
            }else{
                $xml .= "<".$key."><![CDATA[".$val."]]></".$key.">";
            }
        }
        $xml .= "</xml>";

        $data = $xml;
    }

    curl_setopt($curl, CURLOPT_POST,true);
    curl_setopt($curl, CURLOPT_POSTFIELDS, $data);

    $content = curl_exec($curl);

    //返回结果
    if( $content )
    {
        curl_close($curl);

        header('Content-Type: text/html; charset=utf-8');

        return $content;
    } else {
        $error = curl_errno($curl);
        curl_close($curl);
        throw new \Think\Exception("curl出错，错误码:$error");
    }

}