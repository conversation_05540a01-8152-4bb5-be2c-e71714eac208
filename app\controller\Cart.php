<?php


namespace app\controller;


use app\BaseController;
use app\ErrorCode;
use app\Request;
use app\service\Cart as CartService;
use think\facade\Validate;

class Cart extends BaseController
{
    /**
     * Description:添加到购物车
     * Author: zrc
     * Date: 2021/8/16
     * Time: 15:52
     * @param Request $request
     */
    public function addShoppingCart(Request $request)
    {
        $params        = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        $params['activity_id'] = intval($params['activity_id'] ?? 0);
        //数据验证
        $validate = Validate::rule([
            'uid|用户ID'          => 'require|number',
            'period|期数ID'       => 'require|number',
            'package_id|套餐ID'   => 'require|number',
            'periods_type|频道类型' => 'require|in:0,1,2,3,9',
            'nums|套餐数量'         => 'require|number|>:0',
            'activity_id|活动ID'   => 'number|>=:0',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        
        $cartService = new CartService();
        $result      = $cartService->addShoppingCart($params);
        return $this->success($result);
    }

    /**
     * Description:购物车列表
     * Author: zrc
     * Date: 2021/8/16
     * Time: 18:15
     * @param Request $request
     */
    public function shoppingCartList(Request $request)
    {
        $params        = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        $params['activity_id'] = intval($params['activity_id'] ?? 0);
        //数据验证
        $validate = Validate::rule([
            'uid|用户ID' => 'require|number',
            'activity_id|活动ID' => 'number|>=:0',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $cartService = new CartService();
        $result      = $cartService->shoppingCartList($params);
        return $this->success($result);
    }

    /**
     * Description:购物车商品数量变更
     * Author: zrc
     * Date: 2021/8/17
     * Time: 11:23
     * @param Request $request
     */
    public function changeNums(Request $request)
    {
        $params        = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        //数据验证
        $validate = Validate::rule([
            'uid|用户ID'  => 'require|number',
            'id|购物车ID'  => 'require|number',
            'nums|商品数量' => 'require|number|>:0',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $cartService = new CartService();
        $result      = $cartService->changeNums($params);
        return $this->success($result);
    }

    /**
     * Description:购物车商品删除
     * Author: zrc
     * Date: 2021/8/17
     * Time: 13:58
     * @param Request $request
     */
    public function delete(Request $request)
    {
        $params        = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        //数据验证
        $validate = Validate::rule([
            'uid|用户ID'  => 'require|number',
            'ids|购物车ID' => 'require'
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $cartService = new CartService();
        $result      = $cartService->delete($params);
        return $this->success($result);
    }

    /**
     * Description:购物车商品计数
     * Author: zrc
     * Date: 2021/8/17
     * Time: 14:12
     * @param Request $request
     */
    public function count(Request $request)
    {
        $params        = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        $params['activity_id'] = intval($params['activity_id'] ?? 0);
        if (empty($params['uid'])) {
            $this->throwError('请传入用户ID', ErrorCode::PARAM_ERROR);
        }
        $cartService = new CartService();
        $result      = $cartService->count($params);
        return $this->success($result);
    }

    /**
     * Description:购物车商品勾选金额计算
     * Author: zrc
     * Date: 2021/12/15
     * Time: 10:55
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function calculateGoodsMoney(Request $request)
    {
        $params = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        $params['activity_id'] = intval($params['activity_id'] ?? 0);
        if (empty($params['uid'])) $this->throwError('请传入用户ID', ErrorCode::PARAM_ERROR);
        
        if (!empty($params['items_info'])){
            $params['items_info'] = json_decode($params['items_info'], true);
            foreach ($params['items_info'] as &$val) {
                //数据验证
                $validate = Validate::rule([
                    'period|期数'       => 'require|number',
                    'package_id|套餐ID' => 'require|number',
                    'nums|套餐数量'       => 'require|number|>:0',
                ]);
                if (!$validate->check($val)) {
                    $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
                }
            }
        }
        $cartService = new CartService();
        $result = $cartService->calculateGoodsMoney($params);
        return $this->success($result);
    }
}