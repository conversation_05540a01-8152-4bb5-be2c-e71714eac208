<?php


namespace app\controller;


use app\BaseController;
use app\ErrorCode;
use app\Request;
use app\service\CrossAutoPush as CrossAutoPushService;
use think\facade\Log;
use think\facade\Validate;
use app\validate\ListPagination;

class CrossAutoPush extends BaseController
{
    /**
     * Description:配置修改
     * Author: zrc
     * Date: 2023/3/7
     * Time: 9:56
     * @param Request $request
     * @return \think\response\Json
     */
    public function autoPushConfigUpdate(Request $request)
    {
        $params               = $request->param();
        $crossAutoPushService = new CrossAutoPushService();
        $result               = $crossAutoPushService->autoPushConfigUpdate($params);
        return $this->success($result);
    }

    /**
     * Description:配置获取
     * Author: zrc
     * Date: 2023/3/7
     * Time: 9:57
     * @param Request $request
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getAutoPushConfig(Request $request)
    {
        $params               = $request->param();
        $crossAutoPushService = new CrossAutoPushService();
        $result               = $crossAutoPushService->getAutoPushConfig($params);
        return $this->success($result);
    }

    /**
     * Description:自动推单处理+简码获取可推送订单
     * Author: zrc
     * Date: 2023/3/8
     * Time: 10:16
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function crossAutoPushDeal(Request $request)
    {
        $params = file_get_contents('php://input');
        if (empty($params)) {
            $this->throwError('未获取到队列数据', ErrorCode::PARAM_ERROR);
        }
        Log::write('crossAutoPushDeal param ' . strval($params));
        $params = json_decode($params, true);
        if (empty($params['main_order_no']) && empty($params['short_code'])) $this->throwError('未获取到主订单号或简码', ErrorCode::PARAM_ERROR);
        $crossAutoPushService = new CrossAutoPushService();
        if (!empty($params['short_code'])) {//简码查询可推送订单自动推单
            $result = $crossAutoPushService->getPushOrderNoByShortCode($params);
        } else {//订单推送
            $result = $crossAutoPushService->crossAutoPushDeal($params);
        }
        return $this->success($result);
    }

    /**
     * Description:设置订单不自动推送
     * Author: zrc
     * Date: 2023/3/8
     * Time: 16:31
     * @param Request $request
     * @return \think\response\Json
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function setOrderStopAutoPush(Request $request)
    {
        $params             = $request->param();
        $params['operator'] = $request->header('vinehoo-uid');
        if (empty($params['main_order_no'])) $this->throwError('未获取到主订单号', ErrorCode::PARAM_ERROR);
        if (empty($params['operator'])) $this->throwError('未获取到后台用户ID', ErrorCode::PARAM_ERROR);
        $crossAutoPushService = new CrossAutoPushService();
        $result               = $crossAutoPushService->setOrderStopAutoPush($params);
        return $this->success($result);
    }

    /**
     * Description:自动推单统计列表/导出
     * Author: zrc
     * Date: 2023/3/9
     * Time: 18:05
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function autoPushOrderGatherList(Request $request)
    {
        $params             = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        if (empty($params['admin_id'])) $this->throwError('未获取到后台用户');
        if (empty($params['type']) || !in_array($params['type'], [1, 2])) $params['type'] = 1;//默认获取列表
        if ($params['type'] == 1) {
            $validate = new ListPagination();
            if (!$validate->goCheck($params)) {//验证参数
                $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
            }
        }
        $crossAutoPushService = new CrossAutoPushService();
        $result               = [];
        switch ($params['type']) {
            case 1:
                $result = $crossAutoPushService->autoPushOrderGatherList($params);
                break;
            case 2:
                $result = $crossAutoPushService->autoPushOrderGatherExport($params);
                break;
        }
        return $this->success($result);
    }
}