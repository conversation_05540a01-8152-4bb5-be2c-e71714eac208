<?php


namespace app\controller;


use app\BaseController;
use app\ErrorCode;
use app\model\QuoteInfo;
use app\Request;
use app\service\elasticsearch\ElasticSearchService;
use app\service\es\Es;
use app\service\Order as OrderService;
use app\validate\CrossCreateOrder;
use app\validate\FlashCreateOrder;
use app\validate\ListPagination;
use app\validate\MerchantSecondCreateOrder;
use app\validate\SecondCreateOrder;
use app\validate\TailCreateOrder;
use think\Exception;
use think\facade\Db;
use think\facade\Log;
use think\facade\Validate;

class Order extends BaseController
{
    /**
     * Description:全部订单列表
     * Author: zrc
     * Date: 2021/9/6
     * Time: 10:18
     * @param Request $request
     */
    public function list(Request $request)
    {
        $params = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        $validate = new ListPagination();
        if (!$validate->goCheck($params)) {//验证参数
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $orderService = new OrderService();
        $result = $orderService->list($params);
        return $this->success($result);
    }

    /**
     * Description:公共创建订单
     * Author: zrc
     * Date: 2021/8/9
     * Time: 12:22
     * @param Request $request
     * @return \think\Response
     * submit_type 订单提交类型：0-闪购 1-秒发 2-跨境 3-尾货 4-闪购+秒发混合
     */
    public function create(Request $request)
    {
        $params = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        $params['source_platform'] = $request->header('source-platform');
        $params['source_user'] = $request->header('source-user');
        $params['source_event'] = $request->header('source-event');
        $params['request_client'] = $request->header('vinehoo-client');
        $params['request_client_version'] = $request->header('vinehoo-client-version');
        if ($params['submit_type'] == 2 && $params['request_client'] == 'ios' && $params['request_client_version'] == 9.22) {
            $this->throwError('跨境支付通道已升级，请更新最新APP再下单', ErrorCode::PARAM_ERROR);
        }
        $userInfo = httpGet(env('ITEM.USER_CACHE_URL') . '/user/GetUserInfo', ['uid' => $params['uid'], 'field' => 'user_level,created_time,is_prohibit_buy,is_new_user,user_attribute']);
        if ($userInfo['error_code'] != 0 || !isset($userInfo['data']['list'][0])) $this->throwError('未获取到用户信息');
        $userInfo = $userInfo['data']['list'][0];
        if ($userInfo['is_prohibit_buy'] == 1) $this->throwError('您已被禁购！');
        if (!isset($params['submit_type']) || !in_array($params['submit_type'], [0, 1, 2, 3, 4, 9])) {
            $this->throwError('订单提交类型必传', ErrorCode::PARAM_ERROR);
        }
        if (!isset($params['special_type']) || !in_array($params['special_type'], [0, 1, 2, 3, 4])) {
            $this->throwError('特殊类型必传', ErrorCode::PARAM_ERROR);
        }
        //验证参数
        if ($params['submit_type'] == 0) $validate = new FlashCreateOrder();
        if ($params['submit_type'] == 1) $validate = new SecondCreateOrder();
        if ($params['submit_type'] == 2) $validate = new CrossCreateOrder();
        if ($params['submit_type'] == 3) $validate = new TailCreateOrder();
        if ($params['submit_type'] == 4) $validate = new FlashCreateOrder();
        if ($params['submit_type'] == 9) $validate = new MerchantSecondCreateOrder();
        if (!$validate->goCheck($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        //对公转账支付方式验证
        if (isset($params['pay_method'])) {
            if (!in_array($params['pay_method'], [1, 2])) $this->throwError('对公转账请传入正确的支付方式');
            if ($params['submit_type'] == 2 && $params['pay_method'] == 2) $this->throwError('跨境不支持对公转账，请使用在线支付');
        } else {
            $params['pay_method'] = 1;
        }
        // 顺丰冷链转顺丰温控包裹
        foreach ($params['items_info'] as $k => $item) {
            if ($item['express_type'] == 3) {
                $params['items_info'][$k]['express_type'] = 31;
            }
        }
        $items_info = $params['items_info'];
        //跨境黑名单验证
        if (!isset($params['is_replace_pay'])) $params['is_replace_pay'] = 0;
        //临时关闭跨境好友代付
        if ($params['is_replace_pay'] == 1) $this->throwError('该功能暂停使用！');
//        if ($params['submit_type'] == 2 && $params['is_replace_pay'] == 0) {
//            if (empty($params['realname']) || empty($params['id_card_no'])) $this->throwError('真实姓名、身份证必填');
//            $params['id_card_no'] = strtoupper($params['id_card_no']);
//            $toYear               = date('Y', time());
//            $blacklist            = Db::name('cross_quota_blacklist')->field('type,year')->where(array('id_card_no' => $params['id_card_no']))->find();
//            if ($blacklist['type'] == 2 || ($blacklist['type'] == 1 && $blacklist['year'] == $toYear)) {
//                //套餐ID获取商品套餐信息
//                $packageInfo = esGetOne($items_info[0]['package_id'], 'vinehoo.periods_set');
//                if (isset($packageInfo['price'])) {
//                    //跨境下单拦截记录收集
//                    $intercept_record = array(
//                        'uid'            => $params['uid'],
//                        'period'         => $items_info[0]['period'],
//                        'package_id'     => $items_info[0]['package_id'],
//                        'order_qty'      => $items_info[0]['nums'],
//                        'payment_amount' => $packageInfo['price'],
//                        'realname'       => $params['realname'],
//                        'id_card_no'     => $params['id_card_no'],
//                        'note'           => '跨境电子商务年度个人额度不足',
//                        'created_time'   => time(),
//                    );
//                    Db::name('cross_order_intercept_record')->insert($intercept_record);
//                }
//                $this->throwError('跨境电子商务年度个人额度不足');
//            }
//        }
        if (!isset($params['coupon_id'])) $params['coupon_id'] = 0;
        if (!isset($params['express_coupon_id'])) $params['express_coupon_id'] = 0;
        if ($params['invoice_progress'] == 1) {
            if (empty($params['invoice_id'])) {
                $this->throwError('发票抬头ID必传', ErrorCode::PARAM_ERROR);
            }
        } else {
            $params['invoice_id'] = 0;
        }
        $items = [];
        $channel = '';
        $es = new ElasticSearchService();

        $data = array(
            'coupon_id' => intval($params['coupon_id']),
            'is_use_coupon' => empty($params['coupon_id']) ? 0 : 1,
            'special_type' => intval($params['special_type']),
            'express_coupon_id' => intval($params['express_coupon_id']),
            'items_info' => $params['items_info'],
            'province_id' => intval($params['province_id']),
            'district_id' => intval($params['district_id']),
            'submit_type' => intval($params['submit_type']),
            'city_id' => intval($params['city_id']),
            'is_scan_code' => isset($params['is_scan_code']) ? $params['is_scan_code'] : 0,
            'is_generate_order_number' => 1,
            'activity_id' => $params['activity_id'] ?? 0,
        );
        $data['longitude'] = isset($params['longitude']) ? $params['longitude'] : '';
        $data['latitude'] = isset($params['latitude']) ? $params['latitude'] : '';
        $header = [
            "content-type: application/json",
            "vinehoo-client: orders",
            "vinehoo-uid:" . $params['uid']
        ];

        // 选择订单来源
        if (empty($params['source_platform']) && empty($params['source_event']) && !empty($params['activity_id'])) {
            $nowuser_mark = Db::table('vh_data_statistics.vh_nowuser_mark')
                ->where('activity_id', $params['activity_id'])
                ->field('platformmark,eventmark')->find();
            $params['source_platform'] = $nowuser_mark['platformmark'] ?? '';
            $params['source_event'] = $nowuser_mark['eventmark'] ?? '';
        }

        //主、子订单金额计算
        $is_seckill = false;
        if ((count($items_info) == 1) && $items_info[0]['periods_type'] == 0) {
            $period = Es::name(Es::PERIODS)->where([['id', '=', $items_info[0]['period']]])->field('id,is_seckill,sell_time,is_deposit')->find();
            $is_deposit = $period['is_deposit'] ?? 0;
            $p_sell_time = strtotime($period['sell_time']);

            if ($is_deposit == 0 || ($is_deposit == 1 && time() > $p_sell_time)) {
                if ($period['is_seckill'] ?? 0 == 1) {
                    $is_seckill = true;
                    $redis = new \Redis();
                    $redis->connect(env('CACHE.HOST'), env('CACHE.PORT'));
                    $redis->auth(env('CACHE.PASSWORD'));
                    $redis->select(0);
                    $seckill_uid = $redis->get("seckill_{$items_info[0]['period']}_{$params['uid']}");
                    if (!$seckill_uid) {
                        throw new Exception("非法操作!");
                    }
                }
            }
        }
        if ($is_seckill) {
            $countMoney = httpPostString(env('ITEM.CALC-ORDERS_PRICE') . '/ordersPrice/v3/order/calculateOrderMoneySecKill', json_encode($data), $header);
        } else {
            $countMoney = httpPostString(env('ITEM.CALC-ORDERS_PRICE') . '/ordersPrice/v3/order/calculateOrderMoney', json_encode($data), $header);
        }
        if (empty($countMoney)) $this->throwError('订单金额计算失败！');
        if ($countMoney['error_code'] != 0) $this->throwError($countMoney['error_msg']);
        $countMoney = $countMoney['data'];
        if ($countMoney['payment_amount'] <= 0) {
            Log::write("订单金额计算异常，请重试: param." . json_encode($data) . ' is_seckill: ' . ($is_seckill ? 1 : 0) . ' response: ' . json_encode($countMoney));
            $this->throwError('订单金额计算异常，请重试！');
        }
        // 专题期数
        $params['activity_package'] = $countMoney['activity_package'] ?? [];

        //生成主订单号
        $main_order_no = $countMoney['main_order_no'];
//        $main_order_no           = creatOrderNo(env('ORDERS.ORDER_MAIN'), $params['uid']);
        $params['main_order_no'] = $main_order_no;
        $is_deposit = 0;//默认不是订金套餐
        foreach ($items_info as &$val) {
            //验证商品参数
            $validateData = array(
                'period|商品期数'         => 'require',
                'periods_type|商品频道'   => 'require|in:0,1,2,3,9',
                'package_id|套餐ID'     => 'require|number',
                'nums|套餐份数'           => 'require|number',
                'express_type|物流类型'   => 'require|in:1,2,3,4,5,6,7,8,9,10,21,22,23,65,13,31',
                'predict_time|预计发货时间' => 'require',
                'is_ts|是否暂存' => 'require|in:0,1',
            );
            //商家秒发商品参数验证
            if ($val['periods_type'] == 9) {
                if ($params['pay_method'] == 2) $this->throwError('商家秒发不支持对公转账');
                $validateData['delivery_type|发货类型'] = 'require|in:1,2';
                $validateData['delivery_store_id|发货点ID'] = 'require|number|>:0';
                $validateData['delivery_store_name|发货点名称'] = 'require';
                $validateData['supplier_id|商家ID'] = 'require|number|>:0';
            }
            $validate = Validate::rule($validateData);
            if (!$validate->check($val)) {
                $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
            }
            /***限购验证***/
            $esPeriodsData = $es->getDocumentList(['index' => ['periods'], 'match' => [['id' => $val['period']]], 'source' => ['onsale_status', 'title', 'quota_rule', 'is_support_ts', 'payee_merchant_id', 'is_deposit_period', 'is_channel'], 'limit' => 1]);
            $period_os = $esPeriodsData['data'][0]['onsale_status'] ?? null;
            $period_title = $esPeriodsData['data'][0]['title'] ?? $val['period'];
            if (in_array($period_os, [3, 4])) $this->throwError($period_title . '已售罄。');
            if (!isset($esPeriodsData['data'][0]['quota_rule'])) $this->throwError('未获取到商品信息');

            // 小程序验证渠道商品
            /*if ($params['request_client'] == 'miniapp' && $esPeriodsData['data'][0]['is_channel'] == 1) {
                if (empty($params['auth'])) {
                    $this->throwError('非法请求。');
                }
                $decrypt   = cryptionDeal(0, [$params['auth']], $params['uid'], '前端用户');
                if (empty($decrypt[$params['auth']])) {
                    $this->throwError('非法请求。');
                }
                $de_data = json_decode($decrypt[$params['auth']]);
                if (empty($de_data['id']) || $de_data['id'] != $val['period']) {
                    $this->throwError('非法请求。');
                }
            }*/

            //是否支持暂存验证
            if ($esPeriodsData['data'][0]['is_support_ts'] == 0 && $val['is_ts'] == 1) $this->throwError('商品不支持暂存:' . $esPeriodsData['data'][0]['title']);
            $quota_rule = json_decode($esPeriodsData['data'][0]['quota_rule'], true);
            if ($quota_rule) {
                if ($quota_rule['quota_number']) {
                    if ($val['nums'] > $quota_rule['quota_number']) $this->throwError('该商品限购' . $quota_rule['quota_number'] . '份');
                }
                if ($quota_rule['quota_type'] == 0) {
                    $purchased = $es->getDocumentList(['index' => ['orders'], 'match' => [['uid' => $params['uid']], ['period' => $val['period']]], 'range' => [['sub_order_status' => ['gte' => 0]], ['sub_order_status' => ['lte' => 3]]], 'source' => ['id', 'sub_order_no'], 'limit' => 1000]);
                    if (!(isset($esPeriodsSetData['data'][0]['is_deposit']) && $esPeriodsSetData['data'][0]['is_deposit'] == 1)) {
                        $purchased_data = $purchased['data'] ?? [];
                        foreach ($purchased_data as $k => $datum) {
                            if (strpos($datum['sub_order_no'], 'VHD') !== false) {
                                unset($purchased_data[$k]);
                            }
                        }
                        $purchased['data'] = $purchased_data;
                    }
                    if (count($purchased['data']) + $val['nums'] > $quota_rule['quota_number']) $this->throwError('该商品限购' . $quota_rule['quota_number'] . '份,您已购买' . count($purchased['data']) . '份');
                }
                if (!empty($quota_rule['register_time'])) {
                    if ($quota_rule['register_time'] > $userInfo['created_time']) $this->throwError($esPeriodsData['data'][0]['title'] . ':商品限' . $quota_rule['register_time'] . '之后注册的用户购买');
                }
                if (!empty($quota_rule['rank'])) {
                    $rank = explode(',', $quota_rule['rank']);
                    $min_level = $rank[0];
                    $max_level = $rank[1];
                    if ($min_level <= $userInfo['user_level'] && $userInfo['user_level'] <= $max_level) $this->throwError($esPeriodsData['data'][0]['title'] . ':商品限等级为' . $min_level . '到' . $max_level . '的用户禁止购买');
                }
                if (!empty($quota_rule['district'])) {
                    $district = explode(',', $quota_rule['district']);
                    if (in_array($params['province_id'], $district)) $this->throwError('抱歉兔友，您的收货地区暂时无法下单');
                }
            }
            //对公转账根据收款公司判断是线下转账还是线上电汇
            if ($params['pay_method'] == 2) {
                if (!in_array($esPeriodsData['data'][0]['payee_merchant_id'], [1, 2])) $this->throwError('该商品不支持对公转账！');
                if (!isset($params['payee_merchant_id'])) {
                    $params['payee_merchant_id'] = $esPeriodsData['data'][0]['payee_merchant_id'];
                    $params['payment_method'] = 11;//默认线下转账
                } else {
                    if ($params['payee_merchant_id'] != $esPeriodsData['data'][0]['payee_merchant_id']) {
                        $params['payment_method'] = 10;//多家收款公司线上电汇
                    }
                }
            }
            //var_dump($params['payee_merchant_id']);
            /***限购验证***/
            $esPeriodsSetData = $es->getDocumentList(['index' => ['periods_set'], 'match' => [['id' => $val['package_id']]], 'source' => ['periods_type', 'is_mystery_box', 'associated_products', 'is_deposit'], 'limit' => 1]);
            if (!isset($esPeriodsSetData['data'][0])) $this->throwError('未获取到商品套餐信息');
            //订金订单处理
            if (isset($esPeriodsSetData['data'][0]['is_deposit']) && $esPeriodsSetData['data'][0]['is_deposit'] == 1) {
                //版本控制
                if ($params['request_client'] == 'ios' && $params['request_client_version'] < 9.26) {
                    $this->throwError('缴纳订金失败，请更新最新版APP', ErrorCode::PARAM_ERROR);
                }
                if ($params['request_client'] == 'android' && $params['request_client_version'] < 9.19) {
                    $this->throwError('缴纳订金失败，请更新最新版APP', ErrorCode::PARAM_ERROR);
                }
                if (count($items_info) > 1) $this->throwError('订金套餐请单独提交');
                if ($params['invoice_progress'] == 1) $this->throwError('订金订单不支持开发票');
                if ($params['pay_method'] == 2) $this->throwError('订金订单不支持对公转账');
                $is_deposit = 1;//订金套餐标识（不扣库存使用）
            }
            $params['associated_products'] = json_decode($esPeriodsSetData['data'][0]['associated_products'], true);//跨境创建订单查询代发仓库需要
            //拼团验证
            if (!isset($params['group_id'])) $params['group_id'] = 0;
            if ($params['special_type'] == 1) {
                /**拼团信息验证 start**/
                if (!empty($params['group_id'])) {
                    $groupInfo = Db::name('order_group')->where(['id' => $params['group_id']])->find();
                    if (empty($groupInfo)) $this->throwError('未获取到拼团信息');
                    if ($groupInfo['over_time'] <= time()) $this->throwError('该拼团已结束，再开一个吧~');
                    if ($groupInfo['head_uid'] == $params['uid']) $this->throwError('独乐乐不如众乐乐，不能参与自己发起的拼团哦~', 20108);
                    if (!empty($groupInfo['member_uids'])) {
                        if (in_array($params['uid'], explode(',', $groupInfo['member_uids']))) $this->throwError('您已参与拼团，请勿重复下单哦~');
                    }
                }
                //已有拼团中订单判断
                $have_group = $es->getDocumentList(['index' => ['orders'], 'match' => [['uid' => $params['uid']], ['group_status' => 1], ['period' => $val['period']], ['sub_order_status' => 1]], 'source' => ['id'], 'limit' => 1]);
                if (isset($have_group['data'][0]['id'])) $this->throwError('您已有未完成的拼团订单，请勿重复下单哦~', 20116);
                /**拼团信息验证 end**/
                //获取拼团商品信息
                $getGroupInfo = httpGet(env('ITEM.COMMODITIES_URL') . '/commodities/v3/marketing/getGroupInfo', ['period' => $val['period'], 'package_id' => $val['package_id']]);
                if ($getGroupInfo['error_code'] != 0) $this->throwError($getGroupInfo['error_msg']);
                /**拼团商品验证 start**/
                //新人参团验证
                if ($getGroupInfo['data']['is_invite'] == 1 && !empty($params['group_id'])) {
                    if (isset($userInfo['is_new_user']) && $userInfo['is_new_user'] != 1) $this->throwError('您是兔子家老朋友了，本次拼团仅限新用户参加哦~', 20115);
                }
                /**拼团商品验证 end**/
            }
            $periods_type = $esPeriodsSetData['data'][0]['periods_type'];
            if ($periods_type == 0) $channel = 'flash';
            if ($periods_type == 1) $channel = 'second';
            if ($periods_type == 2) $channel = 'cross';
            if ($periods_type == 3) $channel = 'leftover';
            if ($periods_type == 9) $channel = 'second_merchants';
            //盲盒随机出产品并记录
            $associated_products = [];
            if ($esPeriodsSetData['data'][0]['is_mystery_box'] == 1) {
                //盲盒产品库存获取，保证随机到有库存的产品去扣库存
                $getInventory = httpPostString(env('ITEM.COMMODITIES_URL') . '/commodities/v3/package/getPeriodsPackageProductInventory', json_encode([['period' => $val['period'], 'package_id' => $val['package_id'], 'periods_type' => $val['periods_type']]]));
                if ($getInventory['error_code'] == 0 && count($getInventory['data']) > 0 && isset($getInventory['data'][$val['package_id']])) {
                    $package_product = $getInventory['data'][$val['package_id']]['package_product'];
                    foreach ($package_product as $ks => $tt) {
                        $needNums = $val['nums'] * $tt['nums'];
                        $available_pro = [];
                        foreach ($tt['inventory'] as $pro_id => $item) {
                            if ($item >= $needNums) $available_pro[] = $pro_id;
                        }
                        if (empty($available_pro)) $this->throwError('商品库存不足！');
                        $associated_products[] = array(
                            'product_id' => $available_pro[array_rand($available_pro)],
                            'nums' => $tt['nums'],
                        );
                    }
                } else {//查询可用库存失败处理
                    $associated_products = json_decode($esPeriodsSetData['data'][0]['associated_products'], true);
                    foreach ($associated_products as &$v) {
                        $v['product_id'] = $v['product_id'][array_rand($v['product_id'])];
                        unset($v['isGift']);
                    }
                }
                $mystery_box_log[] = array(
                    'main_order_no' => $main_order_no,
                    'period' => $val['period'],
                    'package_id' => $val['package_id'],
                    'nums' => $val['nums'],
                    'product_info' => json_encode($associated_products, true),
                    'created_time' => time(),
                );
            }
            $items[] = array(
                'period' => $val['period'],
                'set_id' => $val['package_id'],
                'delivery_store_id' => isset($val['delivery_store_id']) ? $val['delivery_store_id'] : 0,
                'buy_num' => $val['nums'],
                'channel' => $channel,
                'mystery_box' => $associated_products,
            );

//            if((count($items_info) == 1) && $val['periods_type'] == 0){
//                $period = Es::name(Es::PERIODS)->where([['id','=',$val['period']]])->field('id,is_seckill,sell_time,is_deposit')->find();
//
//                $is_deposit = $period['is_deposit'] ?? 0;
//                $p_sell_time = strtotime($period['sell_time']);
//
//                if ($is_deposit == 0 || ($is_deposit == 1 && time() > $p_sell_time)) {
//                    if ($period['is_seckill'] ?? 0 == 1) {
//                        $redis = new \Redis();
//                        $redis->connect(env('CACHE.HOST'), env('CACHE.PORT'));
//                        $redis->auth(env('CACHE.PASSWORD'));
//                        $redis->select(0);
//                        $seckill_uid = $redis->get("seckill_{$val['period']}_{$params['uid']}");
//                        if (!$seckill_uid) {
//                            throw new Exception("非法操作!");
//                        }
//                    }
//                }
//            }
        }
        /**库存验证+扣除可用库存+拼团人数验证、累加 start**/
        if ($is_deposit == 0) {//非订金商品扣减库存
            $stock_param = array(
                'orderno' => $main_order_no,
                'groupid' => $params['group_id'] > 0 ? $params['group_id'] : 0,
                'group_status' => $params['group_id'] > 0 ? 1 : 0,
                'items' => $items
            );
            if ($params['group_id'] > 0 || $params['special_type'] != 1) {
                $stockVerify = curlRequest(env('ITEM.VINEHOO_INVENTORY_INOUT_URL') . '/inventory_service/v3/inventory/dec', json_encode($stock_param, true));
                if ($stockVerify['error_code'] != 0) {
                    if ($params['group_id'] > 0) $this->throwError('该拼团已拼满，再开一个吧~', 20107);
                    $this->throwError('商品库存不足！', 20107);
                }
            }
            if ($params['special_type'] == 1) $stock_param['group_status'] = 3;
        }
        /**库存验证+扣除可用库存+拼团人数验证、累加 end**/
        $orderService = new OrderService();
        Db::startTrans();
        try {
            //写入订单日志
            $coupon = 1;
            if ($params['coupon_id'] != 0 || $params['express_coupon_id'] != 0) {
                $coupon = 0;
            }
            $log = array(
                'main_order_no' => $main_order_no,
                'create_param' => json_encode($params, JSON_UNESCAPED_UNICODE),
                'stock_param' => isset($stock_param) ? json_encode($stock_param, true) : '',
                'dec_stock_status' => 1,
                'timeout_deal_status' => json_encode(['stock' => 0, 'coupon' => $coupon]),
                'notify_deal_status' => json_encode(['order' => 0, 'invoice' => $params['invoice_progress'] == 0 ? 1 : 0, 'group' => $params['special_type'] == 1 ? 0 : 1, 'fullgift' => 0, 'wms' => $params['special_type'] == 1 || $params['submit_type'] == 2 ? 1 : 0]),
                'created_time' => time()
            );
            $addLog = Db::name('order_deal_log')->insert($log);
            if (empty($addLog)) $this->throwError('订单日志数据写入失败');
            //拼团订单处理
            if ($params['special_type'] == 1) {
                if (empty($params['group_id'])) {
                    $groupData = array(
                        'period' => $items_info[0]['period'],
                        'package_id' => $items_info[0]['package_id'],
                        'group_limit_nums' => $getGroupInfo['data']['group_people'],
                        'group_join_nums' => 1,
                        'group_status' => 1,
                        'over_time' => time() + 86400,
                        'head_order_no' => $main_order_no,
                        'head_uid' => $params['uid'],
                        'created_time' => time(),
                    );
                    $group_id = Db::name('order_group')->insertGetId($groupData);
                    if (empty($group_id)) $this->throwError('添加拼团信息失败');
                    $params['group_id'] = $group_id;
                    //写入拼团信息到商品模块，拼团人数验证、累加需要
                    $url = env('ITEM.COMMODITIES_URL') . '/commodities/v3/group/add';
                    if ($params['submit_type'] == 9) {
                        $url = env('ITEM.VMALL_STOCK_URL') . '/vmall_inventory_service/v3/group/add';
                    }
                    $addGroup = httpPostString($url, json_encode(['group_id' => intval($group_id), 'group_limit_nums' => $getGroupInfo['data']['group_people'], 'group_join_nums' => 1]));
                    if ($addGroup['error_code'] != 0) $this->throwError($addGroup['error_msg']);
                    //添加拼团超时任务
                    $pushData = array(
                        'namespace' => 'orders',
                        'key' => 'GROUP-' . $main_order_no,
                        'data' => base64_encode(json_encode(['head_order_no' => $main_order_no, 'group_id' => $group_id])),
                        'callback' => env('ITEM.ORDERS_URL') . '/orders/v3/order/groupOrderTimeOutDeal',
                        'timeout' => '24h',
                    );
                    $addTimeOut = httpPostString(env('ITEM.TIMEING_SERVICE_URL') . '/services/v3/timing/add', json_encode($pushData));
                    //任务日志
                    $taskLog = '拼团超时任务日志：' . $main_order_no . '请求参数：' . json_encode($pushData, JSON_UNESCAPED_UNICODE) . '返回参数：' . json_encode($addTimeOut, JSON_UNESCAPED_UNICODE);
                    file_put_contents(root_path() . "/runtime/log/" . date('Ym') . "/" . date('Ymd') . 'timingLog' . '.log', $taskLog . PHP_EOL, FILE_APPEND);
                    if (!isset($addTimeOut['error_code']) || $addTimeOut['error_code'] != 0) $this->throwError('拼团创建超时任务失败');
                } else {
                    if (Db::name('order_group')
                        ->where('id', $params['group_id'])
                        ->where('group_status', '<>', 1)
                        ->count()) $this->throwError('团长已取消拼团，可重新发起新拼团！');
                }
            }
            //盲盒写入产品随机数据，方便后续退还库存，订单推送使用
            if (isset($mystery_box_log)) {
                $addMysteryBox = Db::name('order_mystery_box_log')->insertAll($mystery_box_log);
                if (empty($addMysteryBox)) $this->throwError('盲盒产品数据写入失败');
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            if (isset($stockVerify) && $stockVerify['error_code'] == 0) {
                //退还库存
                curlRequest(env('ITEM.VINEHOO_INVENTORY_INOUT_URL') . '/inventory_service/v3/inventory/inc', json_encode($stock_param, true));
            }
            $this->throwError($e->getMessage());
        }
        $params['is_deposit'] = $is_deposit == 1 ? 4 : $params['special_type'];
        $result = $orderService->createOrder($params, $countMoney, $userInfo);
        return $this->success($result);
    }

    public function balancePay(Request $request)
    {
        $param = $request->param();
        $param['p_uid'] = $request->header('vinehoo-uid');
        $recharge_balance = $bonus_balance = 0;
        $balance_logs = [];

        Db::startTrans();
        try {
            // Validate required parameters
            $validate = Validate::rule([
                'p_uid|用户ID' => 'require|number',
                'main_order_no|主订单号' => 'require',
                'balance_amount|余额付款金额' => 'require|float',
            ]);

            if (!$validate->check($param)) {
                throw new Exception($validate->getError());
            }

            $main = Db::name('order_main')->where([
                ['main_order_no', '=', $param['main_order_no']],
                ['uid', '=', $param['p_uid']],
            ])->lock(true)->find();
            if (empty($main)) throw new Exception('未查询到主订单!');
            if ($main['main_order_status'] != 0) throw new Exception('订单状态错误!');

            if (($main['recharge_balance'] == 0) && ($main['bonus_balance'] == 0)) {
                //首次支付
                if ($param['balance_amount'] > $main['cash_amount']) throw new Exception('余额付款金额错误!');

                $order_type_tables = array_column(config('config.order_type'), null, 'value');
                $order_types = explode(',', $main['order_type']);
                // Validate order types
                $valid_order_types = ['0', '1', '3'];
                foreach ($order_types as $order_type) {
                    if (!in_array($order_type, $valid_order_types)) {
                        throw new Exception('不支持的订单类型: ' . ($order_type_tables[$order_type]['label'] ?? $order_type));
                    }
                }

                // Get sub orders
                $sub_orders = [];
                $total_cash_amount = 0;

                foreach ($order_types as $order_type) {
                    $table = $order_type_tables[$order_type]['table'];
                    $sub_orders_items = Db::name($table)->where([
                        ['main_order_id', '=', $main['id']],
                        ['uid', '=', $main['uid']]
                    ])->select()->toArray();

                    foreach ($sub_orders_items as $sub_orders_item) {
                        if ($sub_orders_item['sub_order_status'] != 0) {
                            throw new Exception("{$sub_orders_item['sub_order_no']} 订单状态错误!");
                        }
                        $total_cash_amount = bcadd($total_cash_amount, $sub_orders_item['cash_amount'], 2);
                        $sub_orders[] = $sub_orders_item;
                    }
                }
                array_multisort(array_column($sub_orders,'payment_amount'), SORT_ASC, $sub_orders);

                // Verify total amounts match
                if (abs(bcsub($main['cash_amount'], $total_cash_amount, 2)) != 0) {
                    throw new Exception('订单金额不一致! 主订单金额: ' . $main['cash_amount'] . ', 子订单总金额: ' . $total_cash_amount);
                }

                $balance_result = \Curl::splitBalanceByProportion([
                    'uid' => intval($main['uid']),
                    'related_no' => $param['main_order_no'],
                    'related_type' => 2, // 商品订单
                    'operation_type' => 5, // 商品购买
                    'balance' => round($param['balance_amount'], 2),
                    'change_time' => time(),
                    'change_name' => '系统',
                    'unique_code' => "PAY" . $main['id'] . uniqid() . time(),
                ]);
                // Get the actual deducted amounts
                $recharge_balance = $balance_result['recharge_use'] ?? 0;
                $bonus_balance = $balance_result['bonus_use'] ?? 0;

                // Check if the deducted amount matches the required amount
                $total_deducted = bcadd($recharge_balance, $bonus_balance, 2);
                $is_paid = $total_deducted == $main['payment_amount'];
                $balance_logs[] = [
                    'uid' => $main['uid'],
                    'main_order_no' => $main['main_order_no'],
                    'sub_order_no' => implode(',', array_column($sub_orders, 'sub_order_no')),
                    'recharge_balance' => $recharge_balance,
                    'bonus_balance' => $bonus_balance,
                    'created_time' => time(),
                    'updated_time' => time(),
                    'remark' => '余额支付',
                    'type' => 2, //扣除
                    'status' => 1,//成功
                    'request' => "",
                ];

                // Distribute balance payments to sub orders proportionally
                $sub_orders_updates = [];
                $remaining_recharge = $recharge_balance;
                $remaining_bonus = $bonus_balance;

                for ($i = 0; $i < count($sub_orders) - 1; $i++) {
                    $sub_order = $sub_orders[$i];//子订单
                    $proportion = bcdiv($sub_order['cash_amount'], $total_cash_amount, 12); //比例

                    $sub_recharge = bcmul($recharge_balance, $proportion, 2);
                    $sub_bonus = bcmul($bonus_balance, $proportion, 2);

                    if ($is_paid) {
                        $allocate_diff = bcsub($sub_order['payment_amount'], bcadd($sub_recharge, $sub_bonus, 2), 2);
                        if ($remaining_recharge >= bcadd($allocate_diff,$sub_recharge,2)) {
                            $sub_recharge = bcadd($sub_recharge, $allocate_diff, 2);
                        } else{
                            $sub_bonus = bcadd($sub_bonus, $allocate_diff, 2);
                        }
                    }

                    $remaining_recharge = bcsub($remaining_recharge, $sub_recharge, 2);
                    $remaining_bonus = bcsub($remaining_bonus, $sub_bonus, 2);

                    $sub_orders[$i]['recharge_balance_add'] = $sub_recharge;
                    $sub_orders[$i]['bonus_balance_add'] = $sub_bonus;

                    $sub_orders_updates[] = [
                        'id' => $sub_order['id'],
                        'order_type' => $sub_order['order_type'],
                        'recharge_balance' => bcadd($sub_order['recharge_balance'], $sub_recharge, 2),
                        'bonus_balance' => bcadd($sub_order['bonus_balance'], $sub_bonus, 2),
                        'cash_amount' => bcsub($sub_order['cash_amount'], bcadd($sub_recharge, $sub_bonus, 2), 2)
                    ];
                }

                // Handle the last sub order to avoid rounding issues
                $last_index = count($sub_orders) - 1;
                if (isset($sub_orders[$last_index])) {
                    $last_order = $sub_orders[$last_index];

                    $sub_orders[$last_index]['recharge_balance_add'] = $remaining_recharge;
                    $sub_orders[$last_index]['bonus_balance_add'] = $remaining_bonus;

                    $sub_orders_updates[] = [
                        'id' => $last_order['id'],
                        'order_type' => $last_order['order_type'],
                        'recharge_balance' => bcadd($last_order['recharge_balance'], $remaining_recharge, 2),
                        'bonus_balance' => bcadd($last_order['bonus_balance'], $remaining_bonus, 2),
                        'cash_amount' => bcsub($last_order['cash_amount'], bcadd($remaining_recharge, $remaining_bonus, 2), 2)
                    ];
                }


                // Update main order
                $main_update = [
                    'recharge_balance' => bcadd($main['recharge_balance'], $recharge_balance, 2),
                    'bonus_balance' => bcadd($main['bonus_balance'], $bonus_balance, 2),
                    'cash_amount' => bcsub($main['cash_amount'], bcadd($recharge_balance, $bonus_balance, 2), 2),
                    'update_time' => time()
                ];
                if ($total_deducted == $main['payment_amount']) {
                    $main_update['payment_method'] = 13;
                    $main_update['payment_subject'] = 2;
                }
                Db::name('order_main')->where('id', $main['id'])->update($main_update);

                #拆分金额验证
                array_multisort(array_column($sub_orders_updates, 'cash_amount'), SORT_ASC, $sub_orders_updates);
                $djje_recharge = $djje_bonus = 0; //叠加金额
                foreach ($sub_orders_updates as &$item) {
                    if ($item['cash_amount'] < 0) {
                        if ($item['bonus_balance'] > 0 && $item['cash_amount'] < 0) {
                            $bonus_diff            = min(abs($item['cash_amount']), $item['bonus_balance']);
                            $item['cash_amount']   = bcadd($item['cash_amount'], $bonus_diff, 2);
                            $item['bonus_balance'] = bcsub($item['bonus_balance'], $bonus_diff, 2);
                            $djje_bonus            = bcadd($djje_bonus, $bonus_diff, 2);
                        }
                        if ($item['recharge_balance'] > 0 && $item['cash_amount'] < 0) {
                            $recharge_diff            = min(abs($item['cash_amount']), $item['recharge_balance']);
                            $item['cash_amount']      = bcadd($item['cash_amount'], $recharge_diff, 2);
                            $item['recharge_balance'] = bcsub($item['recharge_balance'], $recharge_diff, 2);
                            $djje_recharge            = bcadd($djje_recharge, $recharge_diff, 2);
                        }
                    }
                    if (($item['cash_amount'] > 0) && (($djje_recharge > 0) || ($djje_bonus > 0))) {
                        if ($djje_recharge > 0 && $item['cash_amount'] > 0) {
                            $recharge_diff            = min($item['cash_amount'], $djje_recharge);
                            $item['cash_amount']      = bcsub($item['cash_amount'], $recharge_diff, 2);
                            $item['recharge_balance'] = bcadd($item['recharge_balance'], $recharge_diff, 2);
                            $djje_recharge            = bcsub($djje_recharge, $recharge_diff, 2);
                        }

                        if ($djje_bonus > 0 && $item['cash_amount'] > 0) {
                            $bonus_diff            = min($item['cash_amount'], $djje_bonus);
                            $item['cash_amount']   = bcsub($item['cash_amount'], $bonus_diff, 2);
                            $item['bonus_balance'] = bcadd($item['bonus_balance'], $bonus_diff, 2);
                            $djje_bonus            = bcsub($djje_bonus, $bonus_diff, 2);
                        }
                    }
                }
                if (($djje_recharge != 0) || ($djje_bonus != 0)) {
                    throw new Exception("金额计算错误!");
                }

                // Update sub orders
                foreach ($sub_orders_updates as $update) {
                    $table = $order_type_tables[$update['order_type']]['table'];
                    if ($update['cash_amount'] < 1) {
                        $update['invoice_progress'] = 0; //发票开票进度 0-不开票 1-开票中 2-开票成功 3-开票失败
                    }
                    Db::name($table)->where('id', $update['id'])->update($update);
                }

                // Record balance logs
                if (!empty($balance_logs)) {
                    Db::name('balance_pay')->insertAll($balance_logs);
                }


                // Check if order is fully paid, then trigger payment callback
                $updated_main = Db::name('order_main')->where('id', $main['id'])->find();
            } else {
                $updated_main = $main;
            }
            $new_pending_amount = $updated_main['cash_amount'];


            $result = [
                'recharge_balance' => round($recharge_balance,2),
                'bonus_balance' => round($bonus_balance,2),
                'pending_amount' => round($new_pending_amount,2),
                'payment_amount' => round($updated_main['payment_amount'],2),
            ];

            if ($new_pending_amount == 0) {
                //全额支付  订单回调
                $result['is_paid'] = true;
            } else {
                $result['is_paid'] = false;
            }

            Db::commit();
            if ($result['is_paid'] === true) {
                try {
                    \Curl::queuePush([
                        'exchange_name' => 'orders',
                        'routing_key' => 'orders_balance_pay_notify_deal',
                        'data' => base64_encode(json_encode([
                            "p_notify_type" => 6,
                            "totalAmount" => $new_pending_amount,
                            "seqId" => $main['main_order_no'],
                            "main_order_no" => $main['main_order_no'],
                        ])),
                    ]);
                } catch (\Exception $ee) {
                    Log::write("发起余额支付回调失败:" . $ee->getMessage());
                }
            }
            return $this->success($result);
        } catch (\Exception $e) {
            Db::rollback();
            // Refund balance if deducted
            if ($recharge_balance > 0 || $bonus_balance > 0) {
                try {
                    // Record refund log
                    $balance_logs[] = [
                        'uid' => $param['p_uid'],
                        'main_order_no' => $param['main_order_no'],
                        'sub_order_no' => '',
                        'recharge_balance' => $recharge_balance,
                        'bonus_balance' => $bonus_balance,
                        'created_time' => time(),
                        'updated_time' => time(),
                        'remark' => '余额支付失败退款: ' . $e->getMessage(),
                        'type' => 1,
                        'status' => 0,
                        'request' => json_encode([
                            'uid' => intval($param['p_uid']),
                            'type' => 1, // 增加
                            'related_no' => $param['main_order_no'],
                            'related_type' => 2, // 商品订单
                            'operation_type' => 5, // 商品购买
                            'recharge_balance' => floatval($recharge_balance),
                            'bonus_balance' => floatval($bonus_balance),
                            'change_time' => time(),
                            'change_name' => '系统',
                            'unique_code' => "REF" . $main['id'] . uniqid() . time()
                        ]),
                    ];
                    Db::name('balance_pay')->insertAll($balance_logs);
                } catch (\Exception $refundException) {
                    Log::error('余额支付失败退款异常: ' . $refundException->getMessage());
                }
            }
//            $this->throwError($e->getMessage());
            $this->throwError("支付失败!");
        }
    }

    /**
     * Description:获取15秒未支付未取消订单
     * Author: zrc
     * Date: 2021/8/14
     * Time: 10:39
     * @param Request $request
     * @return \think\Response
     */
    public function getUnpaidOrder()
    {
        $orderService = new OrderService();
        $result = $orderService->getUnpaidOrder();
        return $this->success($result);
    }

    /**
     * Description:公共获取订单详情
     * Author: zrc
     * Date: 2021/8/14
     * Time: 14:18
     * @param Request $request
     */
    public function orderDetail(Request $request)
    {
        $params = $request->param();
        if (empty($params['order_no'])) {
            $this->throwError('请传入订单号', ErrorCode::PARAM_ERROR);
        }
        $orderService = new OrderService();
        $result = $orderService->orderDetail($params);
        return $this->success($result);
    }

    /**
     * Description:队列处理超时订单
     * Author: zrc
     * Date: 2021/8/14
     * Time: 17:16
     * @param Request $request
     */
    public function timeOutOrderDeal(Request $request)
    {
        $params = file_get_contents('php://input');
        if (empty($params)) {
            $this->throwError('未获取到队列数据', ErrorCode::PARAM_ERROR);
        }
        $orderService = new OrderService();
        $result = $orderService->timeOutOrderDeal($params);
        return $this->success($result);
    }

    /**
     * Description:拼团超时订单队列处理
     * Author: zrc
     * Date: 2022/5/13
     * Time: 10:49
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function groupOrderTimeOutDeal(Request $request)
    {
        $params = file_get_contents('php://input');
        if (empty($params)) {
            $this->throwError('未获取到队列数据', ErrorCode::PARAM_ERROR);
        }
        $orderService = new OrderService();
        $result = $orderService->groupOrderTimeOutDeal($params);
        return $this->success($result);
    }

    /**
     * Description:可开票订单列表
     * Author: zrc
     * Date: 2021/8/17
     * Time: 16:21
     * @param Request $request
     */
    public function invoiceOrderList(Request $request)
    {
        $params = $request->param();
        $validate = new ListPagination();
        if (!$validate->goCheck($params)) {//验证参数
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        if (!isset($params['type'])) $params['type'] = 0;
        $orderService = new OrderService();
        $result = $orderService->invoiceOrderList($params);
        return $this->success($result);
    }

    /**
     * Description:个人中心可开发票订单列表
     * Author: zrc
     * Date: 2022/1/26
     * Time: 10:36
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function personalInvoiceOrderList(Request $request)
    {
        $params = $request->param();
        $validate = new ListPagination();
        if (!$validate->goCheck($params)) {//验证参数
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $params['uid'] = $request->header('vinehoo-uid');
        if (empty($params['uid'])) {
            $this->throwError('未获取到用户ID', ErrorCode::PARAM_ERROR);
        }
        $orderService = new OrderService();
        $result = $orderService->personalInvoiceOrderList($params);
        return $this->success($result);
    }

    /**
     * Description:创建订单备注
     * Author: zrc
     * Date: 2021/8/19
     * Time: 17:36
     * @param Request $request
     */
    public function createRemarks(Request $request)
    {
        $params = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        //数据验证
        $validate = Validate::rule([
            'order_type|订单类型' => 'require|in:0,1,2,3,4,7,9',
            'sub_order_no|子订单号' => 'require',
            'admin_id|后台用户ID' => 'require|number',
            'content|备注内容' => 'require|max:1000',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $orderService = new OrderService();
        $result = $orderService->createRemarks($params);
        return $this->success($result);
    }

    /**
     * Description:订单备注列表
     * Author: zrc
     * Date: 2021/8/19
     * Time: 18:28
     * @param Request $request
     */
    public function remarksList(Request $request)
    {
        $params = $request->param();
        if (empty($params['sub_order_no'])) {
            $this->throwError('未获取到子订单号', ErrorCode::PARAM_ERROR);
        }
        $orderService = new OrderService();
        $result = $orderService->remarksList($params);
        return $this->success($result);
    }

    /**
     * Description:订单路由列表
     * Author: zrc
     * Date: 2022/6/29
     * Time: 15:24
     * @param Request $request
     * @return array|\think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function routingList(Request $request)
    {
        $params = $request->param();
        if (empty($params['sub_order_no'])) {
            $this->throwError('未获取到子订单号', ErrorCode::PARAM_ERROR);
        }
        $orderService = new OrderService();
        $result = $orderService->routingList($params);
        if (empty($result)) {
            return json(['error_code' => 0, 'error_msg' => '', 'data' => []]);
        }
        return $this->success($result);
    }

    /**
     * Description:公共订单修改接口
     * Author: zrc
     * Date: 2021/8/24
     * Time: 12:25
     * @param Request $request
     */
    public function update(Request $request)
    {
        $params = $request->param();
        //-1代表前端用户
        $params['operator'] = isset($params['operator']) ? $params['operator'] : ($request->header('vinehoo-uid', -1));
        //数据验证
        $validate = Validate::rule([
            'order_no|订单号' => 'require',
            'order_type|订单类型' => 'require|in:0,1,2,3,4,5,6,7,8,9,11',
            'operator|操作人ID' => 'require',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $orderService = new OrderService();
        if ($params['order_type'] == 11) {
            $result = $orderService->updateOrderAuction($params);
        } else {
            $result = $orderService->updateOrder($params);
        }
        return $this->success($result);
    }

    /**
     * Description:获取订单系统配置信息
     * Author: zrc
     * Date: 2021/9/6
     * Time: 14:10
     * @param Request $request
     */
    public function getConfig(Request $request)
    {
        $header = $request->header();
        Log::write('getConfig HEADER: ' . json_encode($header));
        $params = $request->param();
        $orderService = new OrderService();
        $result = $orderService->getConfig($params);

        $h_referer = request()->header('referer', '');
        $referer_arr = parse_url($h_referer);
        $h_host = $referer_arr['host'] ?? '';
        if ($h_host == 'os.mulando.cn') {
            //木兰朵
            $result['tripartite_store'] = [
                [
                    'value' => '320552154',
                    'label' => '木兰朵旗舰店'
                ],
                [
                    'value' => '163005133',
                    'label' => '木兰朵-拼多多旗舰店'
                ],
                [
                    'value' => '650a60e17fa15200013acf16',
                    'label' => '【小红书】木兰朵Mulando的店'
                ],
                [
                    'value' => '100620515',
                    'label' => '抖店-木兰朵酒类旗舰店'
                ],
                [
                    'value' => '381499438',
                    'label' => '抖音-木兰朵全国总仓'
                ],
                [
                    'value' => '18565487',
                    'label' => '木兰朵京东旗舰店'
                ],
                [
                    'value' => 'wx0fe6132e6a98d48a',
                    'label' => '微信小商店-木兰朵微信小店'
                ],
                [
                    'value' => '208798183',
                    'label' => '木兰朵-抖音零售店'
                ],
            ];
        }

        return $this->success($result);
    }

    public function optionsConfig(Request $request)
    {
        $data = [
            'tripartite_crop' => [
                ['value' => 0, 'label' => '未知'],
                ['value' => 1, 'label' => '重庆云酒佰酿电子商务有限公司'],
                ['value' => 2, 'label' => '佰酿云酒（重庆）科技有限公司'],
                ['value' => 3, 'label' => '木兰朵'],
                ['value' => 4, 'label' => '松鸽酒业有限公司'],
                ['value' => 5, 'label' => '渝中区微醺酒业商行'],
                ['value' => 8, 'label' => '兔子星球'],
                ['value' => 9, 'label' => '开瓶有益'],
                ['value' => 10, 'label' => '一花一世界'],
            ],
            'incoic_form' => [
                ['value' => 1, 'label' => '重庆云酒佰酿电子商务有限公司'],
                ['value' => 2, 'label' => '佰酿云酒（重庆）科技有限公司'],
                ['value' => 3, 'label' => '桃式物语'],
                ['value' => 4, 'label' => '兔子星球'],
                ['value' => 5, 'label' => '木兰朵'],
            ],
        ];
        $params = $request->param();
        $header = $request->header();
        Log::write('optionsConfig HEADER: ' . json_encode($header));
        $h_referer = request()->header('referer', '');
        $referer_arr = parse_url($h_referer);
        $h_host = $referer_arr['host'] ?? '';
        if ($h_host == 'os.mulando.cn') {
            //木兰朵
            $data = [
                'tripartite_crop' => [
                    ['value' => 3, 'label' => '木兰朵'],
                ],
                'incoic_form' => [
                    ['value' => 5, 'label' => '木兰朵'],
                ],
            ];
        }
        return $this->success($data);
    }

    /**
     * Description:订单确认收货处理
     * Author: zrc
     * Date: 2021/9/22
     * Time: 11:02
     * @param Request $request
     */
    public function goodsReceipt(Request $request)
    {
        $params = $request->param();
        $params['operator'] = isset($params['operator']) ? $params['operator'] : $request->header('vinehoo-uid');
        if (empty($params['sub_order_no_str'])) {
            $this->throwError('未获取到子订单号', ErrorCode::PARAM_ERROR);
        }
        if (!isset($params['operator'])) {
            $this->throwError('操作人ID必传', ErrorCode::PARAM_ERROR);
        }
        $orderService = new OrderService();
        $result = $orderService->goodsReceipt($params);
        return $this->success($result);
    }

    /**
     * Description:通过主订单获取订单信息（满赠需要）
     * Author: zrc
     * Date: 2021/9/23
     * Time: 15:48
     * @param Request $request
     * @return \think\Response
     */
    public function getOrderDetailByMainOrderNo(Request $request)
    {
        $params = $request->param();
        if (empty($params['main_order_no'])) {
            $this->throwError('未获取到主订单号', ErrorCode::PARAM_ERROR);
        }
        $orderService = new OrderService();
        $result = $orderService->getOrderDetailByMainOrderNo($params);
        return $this->success($result);
    }

    /**
     * Description:满赠处理创建关联子订单
     * Author: zrc
     * Date: 2021/9/24
     * Time: 9:35
     * @param Request $request
     * @return \think\Response
     */
    public function fullGiftDeal(Request $request)
    {
        $params = $request->param();
        //数据验证
        $validate = Validate::rule([
            'type|类型' => 'require',
            'related_order_no|关联子订单号' => 'require',
            'items_info|赠品信息' => 'require',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $orderService = new OrderService();
        $result = $orderService->fullGiftDeal($params);
        return $this->success($result);
    }

    /**
     * Description:修改订单支付方式、支付主体
     * Author: zrc
     * Date: 2021/9/28
     * Time: 11:16
     * @param Request $request
     * @return \think\Response
     */
    public function updatePaymentMethod(Request $request)
    {
        $params = $request->param();
        //参数验证
        $validate = Validate::rule([
            'main_order_no|主订单号' => 'require',
            'payment_method|支付方式' => 'require|in:0,1,2,3,4,5,6,7,8,9,12', //12 华为
            'payment_subject|支付主体' => 'require|in:1,2,3,4,11', //11 华为
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $orderService = new OrderService();
        $result = $orderService->updatePaymentMethod($params);
        return $this->success($result);
    }

    /**
     * Description:个人中心订单列表
     * Author: zrc
     * Date: 2021/11/29
     * Time: 16:12
     * @param Request $request
     * @return \think\response\Json|void
     * @throws \Exception
     */
    public function personalList(Request $request)
    {
        $params = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        $params['request_client'] = $request->header('vinehoo-client');
        $params['request_client_version'] = $request->header('vinehoo-client-version');
        $params['is_show_auction'] = 1;//默认展示拍卖订单
        if ($params['request_client'] == 'vinehoo-pc') {
            $params['is_show_auction'] = 0;//pc暂时不展示拍卖订单
        }
        if ($params['request_client'] == 'android' && $params['request_client_version'] <= 9.12) {
            $params['is_show_auction'] = 0;//安卓9.12及以下版本不展示拍卖订单
        }
        if ($params['request_client'] == 'ios' && $params['request_client_version'] <= 9.18) {
            $params['is_show_auction'] = 0;//ios9.18及以下版本不展示拍卖订单
        }
        //参数验证
        $validate = Validate::rule([
            'uid|用户ID' => 'require|number',
            'type|列表类型' => 'require|in:0,1,2,3,4,5,6',
            'page|页码' => 'require|number|>:0',
            'limit|每页显示条数' => 'require|number|>:0',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $orderService = new OrderService();
        $result = $orderService->personalList($params);
        return $this->success($result);
    }

    /**
     * Description:在线客服订单列表
     * Author: zrc
     * Date: 2023/9/15
     * Time: 9:41
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function onlineServiceOrderList(Request $request)
    {
        $params = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        //参数验证
        $validate = Validate::rule([
            'uid|用户ID' => 'require|number',
            'page|页码' => 'require|number|>:0',
            'limit|每页显示条数' => 'require|number|>:0',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $orderService = new OrderService();
        $result = $orderService->onlineServiceOrderList($params);
        return $this->success($result);
    }

    /**
     * Description:个人中心订单详情
     * Author: zrc
     * Date: 2021/12/2
     * Time: 16:37
     * @param Request $request
     */
    public function personalDetail(Request $request)
    {
        $params = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        if (empty($params['uid'])) {
            $this->throwError('用户ID必传', ErrorCode::PARAM_ERROR);
        }
        if (empty($params['order_no'])) $this->throwError('订单号必传', ErrorCode::PARAM_ERROR);
        $orderService = new OrderService();
        $result = $orderService->personalDetail($params);
        return $this->success($result);
    }

    /**
     * Description:已售\已购按钮获取列表
     * Author: zrc
     * Date: 2021/12/10
     * Time: 10:03
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function getSoldPurchasedOrderList(Request $request)
    {
        $params = $request->param();
        //参数验证
        $validate = Validate::rule([
            'type|类型' => 'require|in:1,2',
            'period|期数' => 'require|number',
            'periods_type|商品频道' => 'require|in:0,1,2,3,4,9',
            'page|页码' => 'require|number|>:0',
            'limit|每页显示条数' => 'require|number|>:0',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $orderService = new OrderService();
        $result = $orderService->getSoldPurchasedOrderList($params);
        return $this->success($result);
    }

    /**
     * Description:用户取消、删除订单
     * Author: zrc
     * Date: 2021/12/13
     * Time: 16:55
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function cancelDeleteOrder(Request $request)
    {
        $params = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        //参数验证
        $validate = Validate::rule([
            'uid|用户ID' => 'require|number',
            'type|类型' => 'require|in:1,2',
            'order_no|订单号' => 'require',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $orderService = new OrderService();
        $result = $orderService->cancelDeleteOrder($params);
        return $this->success($result);
    }

    /**
     * Description:订单套餐重绑
     * Author: zrc
     * Date: 2022/2/28
     * Time: 11:47
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function rebindOrderPackage(Request $request)
    {
        $params = $request->param();
        $params['admin_id'] = $request->header('vinehoo-uid');
        //参数验证
        $validate = Validate::rule([
            'admin_id|后台用户' => 'require|number',
            'sub_order_no|子订单号' => 'require',
            'order_type|订单类型' => 'require|in:0,1,3,4',
            'rebind_period|重绑期数' => 'require|number',
            'rebind_package_id|重绑套餐ID' => 'require|number',
            'remark|备注' => 'require',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $orderService = new OrderService();
        $result = $orderService->rebindOrderPackage($params);
        return $this->success($result);
    }

    /**
     * Description:获取拼团信息
     * Author: zrc
     * Date: 2022/4/2
     * Time: 9:56
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function getGroupInfo(Request $request)
    {
        $params = $request->param();
        if (empty($params['group_id'])) {
            $this->throwError('拼团ID必传', ErrorCode::PARAM_ERROR);
        }
        $orderService = new OrderService();
        $result = $orderService->getGroupInfo($params);
        return $this->success($result);
    }

    /**
     * Description:订单统计待支付数、已支付数、已发货数、待拼单数
     * Author: zrc
     * Date: 2022/4/2
     * Time: 14:33
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function orderStatistics(Request $request)
    {
        $params = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        if (empty($params['uid'])) {
            $this->throwError('用户ID必传', ErrorCode::PARAM_ERROR);
        }
        $orderService = new OrderService();
        $result = $orderService->orderStatistics($params);
        return $this->success($result);
    }

    /**
     * Description:获取订单盲盒随机信息
     * Author: zrc
     * Date: 2022/5/26
     * Time: 13:49
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function getMysteryBoxLog(Request $request)
    {
        $params = $request->param();
        if (empty($params['package_id'])) {
            $this->throwError('套餐ID必传', ErrorCode::PARAM_ERROR);
        }
        $orderService = new OrderService();
        $result = $orderService->getMysteryBoxLog($params);
        return $this->success($result);
    }

    /**
     * Description:订金订单列表
     * Author: zrc
     * Date: 2023/6/19
     * Time: 16:50
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function depositList(Request $request)
    {
        $params = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        //参数验证
        $validate = Validate::rule([
            'uid|用户ID' => 'require|number',
            'page|页码' => 'require|number|>:0',
            'limit|每页显示条数' => 'require|number|>:0',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $orderService = new OrderService();
        $result = $orderService->depositList($params);
        return $this->success($result);
    }

    /**
     * Description:订金订单详情
     * Author: zrc
     * Date: 2023/6/21
     * Time: 17:40
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function depositDetail(Request $request)
    {
        $params = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        //参数验证
        $validate = Validate::rule([
            'uid|用户ID' => 'require|number',
            'order_no|订单号' => 'require',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $orderService = new OrderService();
        $result = $orderService->depositDetail($params);
        return $this->success($result);
    }

    /**
     * Description:获取用户首三单订单个数
     * Author: zrc
     * Date: 2023/6/26
     * Time: 10:48
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function getUserTopThreeOrderNums(Request $request)
    {
        $params = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        //参数验证
        $validate = Validate::rule([
            'uid|用户ID' => 'require|number',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $orderService = new OrderService();
        $result = $orderService->getUserTopThreeOrderNums($params);
        return $this->success($result);
    }

    /**
     * Description:用户暂存订单
     * Author: zrc
     * Date: 2023/9/6
     * Time: 15:52
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function stagingOrder(Request $request)
    {
        $params = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        //参数验证
        $validate = Validate::rule([
            'uid|用户ID' => 'require|number',
            'sub_order_no|订单号' => 'require',
            'order_type|订单类型' => 'require|in:0,1,2,3',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $orderService = new OrderService();
        $result = $orderService->stagingOrder($params);
        return $this->success($result);
    }

    /**
     * Description:用户开启冷链配送创建订单
     * Author: zrc
     * Date: 2023/9/7
     * Time: 14:13
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function upgradeColdChain(Request $request)
    {
        $params = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        //参数验证
        $validate = Validate::rule([
            'uid|用户ID' => 'require|number',
            'sub_order_no|订单号' => 'require',
            'order_type|订单类型' => 'require|in:0,1,2,3',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $orderService = new OrderService();
        $result = $orderService->upgradeColdChain($params);
        return $this->success($result);
    }

    public function applyBilling(Request $request)
    {
        $params = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        //参数验证
        $validate = Validate::rule([
            'uid|用户ID' => 'require|number',
            'sub_order_no|订单号' => 'require',
            'order_type|订单类型' => 'require|in:0,1,2,3,9',
            'invoice_progress|是否开票' => 'require|in:1',
            'invoice_id|开票抬头' => 'require|number|>:0',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }

        Db::startTrans();
        try {
            $orderService = new OrderService();
            $result = $orderService->applyBilling($params);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->throwError($e->getMessage());
        }

        return $this->success([
            'msg' => '申请成功',
            'result' => $result
        ]);
    }


    public function findProduct(Request $request)
    {
        $params = $request->param();
        //参数验证
        $validate = Validate::rule([
            'short_code|简码' => 'require',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }

        Db::startTrans();
        try {
            $field = 'p.id,p.cn_product_name,u.name as unit_name,p.en_product_name,p.grape,p.short_code,p.capacity,p.carton_dimension,c.country_name_cn,r.regions_name_cn';

            $product = Db::table('vh_wiki.vh_products')->alias('p')
                ->leftJoin('vh_wiki.vh_country_base c', 'p.country_id = c.id')
                ->leftJoin('vh_wiki.vh_regions_base r', 'p.producing_area_id = r.id')
                ->leftJoin('vh_wiki.vh_product_unit_open u', 'p.product_unit = u.id')
                ->field($field)
                ->where('p.short_code', $params['short_code'])
                ->find();
            if (!empty($product)) {
                $grapes = Db::table('vh_wiki.vh_grape_type')->where('id', 'in', $product['grape'])->column('id,gname_cn,gname_en');
                $product['gname_cn'] = implode('、', array_column($grapes, 'gname_cn'));
                $product['gname_en'] = implode('、', array_column($grapes, 'gname_en'));

                $product['trade_price'] = null;
                $trade_price_json = Db::table('vh_supplychain.vh_inventory_price')->where('product_id', $product['id'])->value('trade_price_json');
                if (!empty($trade_price_json)) {
                    $trade_price = json_decode($trade_price_json, true);
                    $product['trade_price'] = $trade_price[2] ?? null;
                }
                $quote_info = (new QuoteInfo())->where('short_code', $params['short_code'])->find();
                $product['quote_image'] = $quote_info['image'] ?? '';
            }

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->throwError($e->getMessage());
        }

        return $this->success(['product' => $product ?? ((object)[])]);
    }

    public function saveQuoteInfo(Request $request)
    {
        $nums = 0;
        $now = time();
        $params = $request->param('items');
        if (empty($params)) $this->throwError('参数不能为空!');
        foreach ($params as $param) {
            //参数验证
            $validate = Validate::rule([
                'short_code|简码' => 'require',
//                'image|图片'      => 'require',
            ]);
            if (!$validate->check($param)) {
                $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
            }
        }

        Db::startTrans();
        try {
            $list = Db::name('quote_info')->where('short_code', 'in', array_column($params, 'short_code'))->column("id,short_code", 'short_code');
            foreach ($params as $param) {
                if (!isset($list[$param['short_code']])) {
                    $list[$param['short_code']] = [
                        "short_code" => $param['short_code'],
                        "image" => $param['image'] ?? '',
                    ];
                } else {
                    $list[$param['short_code']]['image'] = $param['image'] ?? '';
                }
            }

            $nums = (new QuoteInfo())->saveAll($list);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->throwError($e->getMessage());
        }

        return $this->success(compact('nums'));
    }

    public function quotationAdd(Request $request)
    {
        $params = $request->param();
        //参数验证
        $validate = Validate::rule([
            'company_id|公司ID' => 'number',  //公司ID:1=佰酿云酒(重庆)科技有限公司
            'customer_name|客户名称' => 'max:255',  //客户名称
            'customer_address|客户地址' => 'max:2048',  //客户地址
            'contacts|联系人' => 'max:255',  //联系人
            'contacts_phone|联系方式' => 'mobile|max:255',  //联系方式
            'freight|运费' => 'float|>=:0',  //运费
            'remark|备注' => 'max:65535',  //备注
            'items|产品信息' => 'require|array',  //产品信息
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }

        Db::startTrans();
        try {
            $params['create_time'] = $params['update_time'] = time();
            $params['items'] = json_encode($params['items']);
            Db::name('quotation')->insert($params);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->throwError($e->getMessage());
        }
        return $this->success();
    }

    public function getGroupShareByPeriod(Request $request)
    {
        $params = $request->param();
        //参数验证
        $validate = Validate::rule([
            'period|期数' => 'number',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }

        Db::startTrans();
        try {
            $field = 'id,period,periods_type,share_image,main_title,sub_title,share_url,share_status,mid';
            $params['period'] = $params['period'] ?? 0;
            $data = [];
            if ($params['period'] > 0) {
                $data = Db::table('vh_commodities.vh_periods_group')->where('period', $params['period'])->field($field)->find();
            }
            if (empty($data)) {
                $data = Db::table('vh_commodities.vh_periods_group')->where('share_status', 1)->field($field)->find();
            }
            if (!empty($data['share_image'])) $data['share_image'] = image_full_path($data['share_image']);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->throwError($e->getMessage());
        }
        return $this->success($data);
    }

    public function lastOrderDetail(Request $request)
    {
        $params = $request->param();
        $params['uid'] = $request->header('vinehoo-uid', null);
        //参数验证
        $validate = Validate::rule([
            'uid|用户ID' => 'require|number',  //公司ID:1=佰酿云酒(重庆)科技有限公司
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }

        Db::startTrans();
        try {
            $order = Es::name(Es::ORDERS)->where([
                ['is_delete', '==', 0],
                ['uid', '==', $params['uid']],
//                ['sub_order_status', 'in', [0, 1]]
            ])->order(['created_time' => 'DESC'])->field('sub_order_no,order_type,special_type,group_id,period')->find();
            if (!empty($order)) {
                $order['special_type'] = $order['special_type'] ?? 0;
                $order['group_id'] = $order['group_id'] ?? 0;

                $period = Es::name(Es::PERIODS)->where([['_id', '==', $order['period']]])->field('id,banner_img')->find();
                if (empty($period)) throw new Exception('未找到期数: ' . ($order['period'] ?? ''));
                $order['banner_img'] = image_full_path($period['banner_img'] ?? '');

                $order['group_last_num'] = 0;
                $order['group_status'] = 3;
                $order['share_image'] = "";
                $order['main_title'] = '';
                $order['sub_title'] = '';
                $order['share_url'] = '';

                if (($order['special_type'] == 1) && $order['group_id']) {
                    $group_info = Db::name('order_group')
                        ->where('over_time', '>=', time())
                        ->where('id', $order['group_id'])->find();
                    if (!empty($group_info)) {
                        $origin_group_last_num = bcsub($group_info['group_limit_nums'], $group_info['group_join_nums']);
                        $order['group_last_num'] = $origin_group_last_num;
                        $group_uids = $group_info['head_uid'];
                        if (!empty($group_info['member_uids'])) $group_uids .= ",{$group_info['member_uids']}";
                        $group_uids_arr = array_values(array_filter(explode(',', $group_uids)));
                        if (!in_array(request()->header('vinehoo-uid', null), $group_uids_arr)) $order['group_last_num']--;
                        $order['group_last_num'] = max($order['group_last_num'], 0);
                        $order['group_status'] = $group_info['group_status'];
                        if ($origin_group_last_num == 1 && $order['group_last_num'] == 0 && $order['group_status'] == 1) {
                            foreach (range(1, 1) as $cs) {
                                sleep(1);
                                $order['group_status'] = Db::name('order_group')->where('id', $order['group_id'])->value('group_status');
                                if ($order['group_status'] != 1) break;
                            }
                        }

                        $group_shart = Db::table('vh_commodities.vh_periods_group')->where('period', $order['period'])->find();
                        if (empty($group_shart)) {
                            $group_shart = Db::table('vh_commodities.vh_periods_group')->where('share_status', 1)->find();
                        }
                        if (!empty($group_shart)) {
                            $order['share_image'] = image_full_path(strval($group_shart['share_image']));
                            $order['main_title'] = strval($group_shart['main_title']);
                            $order['sub_title'] = strval($group_shart['sub_title']);
                            $order['share_url'] = strval($group_shart['share_url']);
                        }
                    }
                }
            }

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->throwError($e->getMessage());
        }
        return $this->success($order);
    }

    public function upTsTime(Request $request)
    {
        $params = $request->param();
        $params['uid'] = $request->header('vinehoo-uid', null);
        $params['vh_vos_name'] = base64_decode($request->header('vinehoo-vos-name'));

        //参数验证
        $validate = Validate::rule([
            'uid|用户ID' => 'require|number',  //公司ID:1=佰酿云酒(重庆)科技有限公司
            'sub_order_no|订单号' => 'require',
            'ts_time|暂存时间' => 'require|date',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }

        Db::startTrans();
        try {
            $ts_info = Db::name('order_ts')->where('sub_order_no', $params['sub_order_no'])->find();
            if (empty($ts_info)) throw new Exception('未找到暂存数据');
            Db::name('order_ts')->where('id', $ts_info['id'])->update([
                'ts_time' => strtotime($params['ts_time']),
                'uid' => $params['uid'],
                'remarks' => $ts_info['remarks'] . " {$params['vh_vos_name']}更新暂存时间",
                'update_time' => time(),
                'status' => 0,
            ]);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->throwError($e->getMessage());
        }
        return $this->success();
    }

    public function transferApprove(Request $request)
    {
        $now = time();
        $params = $request->param();
        $params['vh_uid'] = $request->header('vinehoo-uid', null);
        $admin = \Curl::adminInfo(['admin_id' => $params['vh_uid']])[$params['vh_uid']];
        $params['vh_vos_name'] = base64_decode($request->header('vinehoo-vos-name'));
        $params['status'] = 1; //状态:0=无,1=审核中,2=已通过,3=已驳回
        $params['update_time'] = $now;

        //参数验证
        $validate = Validate::rule([
            'vh_uid|用户ID' => 'require|number',
            'vh_vos_name|用户名称' => 'require',
            'main_order_no|主订单号' => 'require',
            'attachment|付款回执单附件' => 'require',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }

        Db::startTrans();
        try {
            $main_order = Db::name('order_main')->where('main_order_no', $params['main_order_no'])->find();
            if (empty($main_order)) throw new Exception('未找到订单!');
            if (!in_array($main_order['payment_method'], [11])) throw new Exception('订单支付方式不是线下支付');
            if (!in_array($main_order['main_order_status'], [0, 4])) throw new Exception('订单状态不是待支付或已取消');
            $sub_orders = Es::name(Es::ORDERS)->where([['main_order_no', '==', $main_order['main_order_no']]])->field('period')->select()->toArray();
            $period_ids = array_column($sub_orders, 'period');
            $periods = Es::name(Es::PERIODS)->where([['_id', 'in', $period_ids]])->field('payee_merchant_id,period')->select()->toArray();
            $payee_merchant_ids = array_column($periods, 'payee_merchant_id');
            $pm_temp = [
                2 => '科技',
                1 => '云酒',
                5 => '微醺',
            ];
            $pm_arr = [];
            foreach ($pm_temp as $pm_id => $pm_name) {
                if (in_array($pm_id, $payee_merchant_ids)) $pm_arr[] = $pm_name;
            }
            $payment_subject_txt = [1 => '重庆云酒佰酿电子商务有限公司', 2 => '佰酿云酒（重庆）科技有限公司'];
            $params['total_amount'] = $main_order['payment_amount'];
            if (count($pm_arr) == 3) {
                $params['payment_subject'] = '云酒+微醺+科技';
            } else {
                $params['payment_subject'] = implode('+', $pm_arr);
            }
            $transfer_log = Db::name('transfer_log')->where('main_order_no', $params['main_order_no'])->find();
            if (!empty($transfer_log)) {
                if (in_array($transfer_log['status'], [1, 2])) throw new Exception('订单转账审批审核中或已通过审核,请勿重复操作');
                Db::name('transfer_log')->where('main_order_no', $params['main_order_no'])->update($params);
            } else {
                $params['created_time'] = $now;
                Db::name('transfer_log')->where('main_order_no', $params['main_order_no'])->insert($params);
            }

            //下载文件
            $url = env("ALIURL") . $params['attachment'];
            $tmparr = parse_url($url);
            $attr_url = empty($tmparr['scheme']) ? 'http://' : $tmparr['scheme'] . '://';
            $attr_url .= $tmparr['host'] . $tmparr['path'];

            $attr_info = pathinfo($attr_url);
            $download_path = app()->getRuntimePath() . 'download' . DIRECTORY_SEPARATOR;
            if (!is_dir($download_path)) {
                if (!mkdir($download_path, 0755, true)) {
                    throw new Exception('创建附件目录失败,' . $download_path);
                }
            }

            $filePath = $download_path . $attr_info['basename'];
            \Curl::downloadGet($attr_url, $filePath);

            //上传文件到微信服务器
            $wechat_up_data = \Curl::upTempFileToWechat($filePath);

            //发起审批
            $approval_data = [
                'form_component_values' => [
                    [
                        'name' => '收款主体',
                        'value' => strval($params['payment_subject'] ?? ''),
                    ],
                    [
                        'name' => '主订单号',
                        'value' => strval($params['main_order_no'] ?? ''),
                    ],
                    [
                        'name' => '支付金额',
                        'value' => strval($params['total_amount'] ?? ''),
                    ],
                    [
                        'name' => '付款回执单附件',
                        'value' => [(object)["fileId" => $wechat_up_data['media_id']]],
                    ],
                ],
                "process_code" => (env("APP_DEBUG") === true) ? '3WMVrJ9q8P3Cg6YuHN276sFrxbXMuCYHnN3FqSQX' : 'C4UE6WHiGomgH2QL8eFCed9rucDo84a4igWbCB1qK',
                "dept_id" => 0,//部门传0默认获取自己主部门
                "originator_user_id" => $admin['userid']
            ];
            \Curl::approval($approval_data);
            unlink($filePath);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            if (!empty($filePath)) unlink($filePath);
            $this->throwError($e->getMessage());
        }
        return $this->success();
    }

    /**
     * Description:获取用户首单订单
     * Author: gangh
     * Date: 2025/1/8
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function getUserFirstOrder(Request $request)
    {
        $params = $request->param();
        //参数验证
        $validate = Validate::rule([
            'uid|用户ID' => 'require|array',
        ]);
        if (!$validate->check($params)) {
            $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
        }
        $orderService = new OrderService();
        $result = $orderService->getUserFirstOrder($params);
        return $this->success($result);
    }

}