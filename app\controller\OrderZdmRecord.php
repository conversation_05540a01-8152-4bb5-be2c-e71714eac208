<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use think\Request;
use \app\service\OrderZdmRecord as OrderZdmRecordService;
use function Complex\theta;

class OrderZdmRecord extends BaseController
{
    /**
     * 记录列表
     * @param Request $request
     * @return \think\response\Json
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function lists(Request $request)
    {
        $params = $request->param();
        $header = $request->header();
        $params['admin_id'] = $header['vinehoo-uid'];
        $result = (new OrderZdmRecordService)->list($params);
        return  $this->success($result,"添加成功");
    }


    /**
     * 子订单支付变化监听修改对应值得买记录数据
     * @param Request $request
     * @return \think\response\Json
     * @throws \Exception
     */
    public function remove(Request $request)
    {
        $params=$request->param();
        if (empty($params)) {
            $this->throwError('请求参数不能为空！');
        }
        if (isset($params['period']) && empty($params['period'])) {
            $this->throwError('期数不能为空！');
        }
        if (isset($params['period']) && empty($params['period'])) {
            $this->throwError('期数不能为空！');
        }
        if (isset($params['sub_order_no']) && empty($params['sub_order_no'])) {
            $this->throwError('子订单号不能为空！');
        }
        if (isset($params['uid']) && empty($params['uid'])) {
            $this->throwError('uid不能为空！');
        }
        $result = (new OrderZdmRecordService)->remove($params);
        return  $this->success($result,"操作成功");
    }

    /**
     * 导出
     * @param Request $request
     * @return \think\response\Json
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function export(Request $request)
    {
        $params = $request->param();
        $header = $request->header();
        $params['admin_id'] = $header['vinehoo-uid'];
        if (isset($params['admin_id']) && empty($params['admin_id'])) {
            $this->throwError('请登录后操作！');
        }
        $result = (new OrderZdmRecordService)->export($params);
        return  $this->success($result,"导出成功");
    }

    /**
     * 创建记录
     * @param Request $request
     * @return \think\response\Json
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function recordCreate(Request $request)
    {
        $params = $request->param();
        if (isset($params['uid']) && empty($params['uid'])) {
            $this->throwError("用户ID不能为空");
        }
        if (isset($params['sub_order_no']) && empty($params['sub_order_no'])) {
            $this->throwError("子订单号不能为空");
        }
        if (isset($params['period']) && empty($params['period'])) {
            $this->throwError("商品期数不能为空");
        }
        $result = (new OrderZdmRecordService)->recordCreate($params);
        return  $this->success($result,"创建成功");
    }

}
