# 支付回调中的收款统计集成

## 📋 集成概述

在支付成功回调中自动调用收款统计接口，确保收款数据的实时性和准确性。

## 🔄 集成点

### 1. 银联/微信/支付宝支付回调

**文件位置：** `app/service/Notify.php`

**集成位置：** `notify()` 方法中，订单状态更新成功后

```php
// 支付成功后调用收款统计接口
$this->callPaymentStatistics($main_order_no, $orderMain['cash_amount']);
```

**触发条件：**
- 支付验签成功
- 订单状态从待支付更新为已支付
- 数据库事务提交成功

### 2. 跨境代付支付回调

**文件位置：** `app/service/Notify.php`

**集成位置：** `payOnBehalf()` 方法中

```php
// 代付成功后也调用收款统计接口
$this->callPaymentStatistics($main_order_no, $orderMain['cash_amount']);
```

**触发条件：**
- 代付状态更新成功
- 数据库事务提交成功

### 3. 余额支付回调

**文件位置：** `app/service/Notify.php`

**集成位置：** `notify()` 方法中（`notify_type == 6`）

**队列路由：** `POST /orders/v3/order/balancePayNotifyDeal`

**触发条件：**
- 余额扣减成功
- 订单状态更新为已支付
- 队列处理成功

## 🛡️ 安全机制

### 1. 异常隔离

```php
private function callPaymentStatistics($mainOrderNo, $amount)
{
    try {
        // 调用收款统计服务
        $paymentStatisticsService = new \app\service\PaymentStatistics();
        $result = $paymentStatisticsService->writePaymentDataV2($mainOrderNo, $amount);
        
        if ($result) {
            $this->notifyLog("收款统计接口调用成功: 主订单号={$mainOrderNo}");
        } else {
            $this->notifyLog("收款统计接口调用失败: 主订单号={$mainOrderNo}");
        }
        
    } catch (\Exception $e) {
        // 记录错误日志，但不影响支付回调的主流程
        $errorMsg = "收款统计接口调用异常: 主订单号={$mainOrderNo}, 错误信息={$e->getMessage()}";
        $this->notifyLog($errorMsg);
        
        // 发送企业微信通知
        try {
            \Curl::wecomSend($errorMsg, 'LongFei', 'text');
        } catch (\Exception $notifyException) {
            $this->notifyLog("企业微信通知发送失败: " . $notifyException->getMessage());
        }
    }
}
```

### 2. 防重复机制

- 收款统计服务内部有防重复处理机制
- 通过 `vh_payment_statistics_log` 表记录处理状态
- 主订单号+收款商户ID+操作类型唯一约束

### 3. 日志记录

- 调用开始日志
- 调用成功/失败日志
- 异常详细日志
- 企业微信告警通知

## 📊 支付场景覆盖

### 支持的支付方式

| 支付方式 | 回调类型 | 集成状态 | 备注 |
|---------|---------|----------|------|
| 银联支付 | notify_type=1 | ✅ 已集成 | 主要支付方式 |
| 支付宝 | notify_type=2 | ✅ 已集成 | 移动端支付 |
| 微信支付 | notify_type=3 | ✅ 已集成 | 移动端支付 |
| 线下转账 | notify_type=4 | ✅ 已集成 | 人工确认 |
| 余额支付 | notify_type=6 | ✅ 已集成 | 队列处理 |
| 跨境代付 | payOnBehalf | ✅ 已集成 | 特殊场景 |

### 不支持的场景

| 场景 | 原因 | 备注 |
|------|------|------|
| 兔头支付 | 非现金收款 | 使用虚拟货币 |
| 礼品卡支付 | 非现金收款 | 使用虚拟卡券 |
| 酒会订单 | 独立系统 | 由酒会系统处理 |

## 🔍 测试验证

### 1. 功能测试

**测试步骤：**
1. 创建测试订单
2. 完成支付流程
3. 检查支付回调日志
4. 验证收款统计数据

**验证点：**
- 支付回调成功
- 收款统计接口调用成功
- 统计数据正确入库
- 防重复机制生效

### 2. 异常测试

**测试场景：**
1. 收款统计服务异常
2. 数据库连接异常
3. ES服务异常
4. 网络超时异常

**预期结果：**
- 支付回调主流程不受影响
- 异常信息正确记录
- 企业微信告警正常发送

### 3. 性能测试

**测试指标：**
- 支付回调响应时间增加 < 100ms
- 并发支付处理能力不下降
- 内存使用无明显增加

## 📈 监控告警

### 1. 关键指标

- **调用成功率**：> 99%
- **响应时间**：< 200ms
- **异常率**：< 1%
- **重复调用率**：< 0.1%

### 2. 告警规则

```php
// 调用失败率超过1%
if ($failureRate > 0.01) {
    $this->sendAlert("收款统计调用失败率过高: {$failureRate}%");
}

// 响应时间超过500ms
if ($responseTime > 500) {
    $this->sendAlert("收款统计调用响应时间过长: {$responseTime}ms");
}

// 连续异常超过10次
if ($consecutiveErrors > 10) {
    $this->sendAlert("收款统计连续异常: {$consecutiveErrors}次");
}
```

### 3. 日志分析

**关键日志：**
```
开始调用收款统计接口: 主订单号=VHM202412040001, 金额=100.50
收款统计接口调用成功: 主订单号=VHM202412040001
收款统计接口调用异常: 主订单号=VHM202412040001, 错误信息=...
```

**日志查询：**
```bash
# 查看今日调用情况
grep "收款统计接口" /path/to/logs/$(date +%Y%m%d).log

# 查看异常情况
grep "收款统计接口调用异常" /path/to/logs/$(date +%Y%m%d).log

# 统计成功率
grep -c "调用成功" /path/to/logs/$(date +%Y%m%d).log
```

## 🚀 部署说明

### 1. 部署步骤

1. **代码部署**：更新 `app/service/Notify.php` 文件
2. **路由更新**：添加余额支付队列路由
3. **权限检查**：确保收款统计服务可访问
4. **监控配置**：配置日志监控和告警

### 2. 回滚方案

如果出现问题，可以通过以下方式回滚：

```php
// 临时禁用收款统计调用
private function callPaymentStatistics($mainOrderNo, $amount)
{
    // 临时禁用
    return;
    
    // 原有逻辑...
}
```

### 3. 灰度发布

建议采用灰度发布策略：
1. 先在测试环境验证
2. 生产环境小流量测试
3. 逐步放开全量流量

## 🎯 总结

通过在支付回调中集成收款统计接口，实现了：

1. **实时性**：支付成功即时统计
2. **准确性**：以实际支付金额为准
3. **可靠性**：异常隔离，不影响主流程
4. **可监控**：完善的日志和告警机制

这个集成确保了收款统计数据的及时性和准确性，为财务对账和业务分析提供了可靠的数据基础。
